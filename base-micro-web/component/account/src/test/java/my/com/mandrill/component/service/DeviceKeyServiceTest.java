package my.com.mandrill.component.service;

import jakarta.persistence.EntityNotFoundException;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.repository.jpa.DeviceKeyRepository;
import my.com.mandrill.component.repository.jpa.SignatureChallengeRepository;
import my.com.mandrill.component.service.impl.AccountSystemConfigurationServiceImpl;
import my.com.mandrill.component.service.impl.DeviceKeyServiceImpl;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.test.util.ReflectionTestUtils;

import java.security.PublicKey;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class DeviceKeyServiceTest {

	@InjectMocks
	@Spy
	DeviceKeyServiceImpl deviceKeyService;

	@InjectMocks
	AesCryptoUtil aesCryptoUtil;

	@Mock
	AccountSystemConfigurationServiceImpl accountSystemConfigurationService;

	@Mock
	DeviceKeyRepository deviceKeyRepository;

	@Mock
	SignatureChallengeRepository signatureChallengeRepository;

	@Mock
	ThreadPoolTaskScheduler threadPoolTaskScheduler;

	@Mock
	ScheduledFuture<?> t;

	DeviceKey deviceKeyMock;

	List<DeviceKey> deviceKeyListMock;

	User userMock;

	SignatureChallenge signatureChallengeMock;

	PublicKey publicKeyMock;

	@BeforeEach
	void setup() {
		deviceKeyMock = new DeviceKey();
		deviceKeyMock.setDeviceId("id");

		userMock = new User();

		signatureChallengeMock = new SignatureChallenge();
		signatureChallengeMock.setSignature("signature");

		ReflectionTestUtils.setField(aesCryptoUtil, "AES_KEY", "NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc");
		ReflectionTestUtils.setField(aesCryptoUtil, "IV_KEY", "57ad289f-3def-4c");

		publicKeyMock = new PublicKey() {
			@Override
			public String getAlgorithm() {
				return null;
			}

			@Override
			public String getFormat() {
				return null;
			}

			@Override
			public byte[] getEncoded() {
				return new byte[0];
			}
		};

		deviceKeyListMock = new ArrayList<>();
		deviceKeyListMock.add(deviceKeyMock);

		t = new ScheduledFuture<>() {
			@Override
			public long getDelay(TimeUnit unit) {
				return 0;
			}

			@Override
			public int compareTo(Delayed o) {
				return 0;
			}

			@Override
			public boolean cancel(boolean mayInterruptIfRunning) {
				return false;
			}

			@Override
			public boolean isCancelled() {
				return false;
			}

			@Override
			public boolean isDone() {
				return false;
			}

			@Override
			public Object get() throws InterruptedException, ExecutionException {
				return null;
			}

			@Override
			public Object get(long timeout, TimeUnit unit)
					throws InterruptedException, ExecutionException, TimeoutException {
				return null;
			}
		};

	}

	@Test
	@Disabled
	void assertionFinish_Positive() {
	}

	@Test
	void findAllDeviceKey_Positive() {
		Mockito.when(deviceKeyRepository.findByUser(any(), any())).thenReturn(deviceKeyListMock);

		List<DeviceKey> data = deviceKeyService.findAllDeviceKey(Sort.by(Sort.Direction.ASC, "id"), new User());
		assertThat(data.size()).isEqualTo(1);
	}

	@Test
	void saveDeviceKey_Success() {
		Mockito.when(deviceKeyRepository.save(any())).thenReturn(deviceKeyMock);

		DeviceKey data = deviceKeyService.saveDeviceKey(new DeviceKey());
		assertThat(data.getDeviceId()).isEqualTo("id");
	}

	@Test
	void saveSignatureChallenge_Positive() {
		Mockito.when(signatureChallengeRepository.save(any())).thenReturn(signatureChallengeMock);

		SignatureChallenge data = deviceKeyService.saveSignatureChallenge(signatureChallengeMock);
		assertThat(data.getSignature()).isEqualTo("signature");
		verify(signatureChallengeRepository, times(1)).save(any());
	}

	@Test
	void findOptionalDeviceKeyByDeviceIdAndUser_Positivce() {
		Mockito.when(deviceKeyRepository.findByUserAndDeviceId(any(), any())).thenReturn(Optional.of(deviceKeyMock));

		Optional<DeviceKey> data = deviceKeyService.findOptionalDeviceKeyByDeviceIdAndUser("device", new User());
		assertThat(data).isPresent();
		assertThat(data.get().getDeviceId()).isEqualTo("id");
	}

	@Test
	void findSignatureChallengeByIdAndDeviceKey_ThrowSignatureChallengeNotFound_Negative() {
		Mockito.when(signatureChallengeRepository.findByIdAndDeviceKey(any(), any())).thenReturn(Optional.empty());

		Assertions.assertThrows(EntityNotFoundException.class,
				() -> deviceKeyService.findSignatureChallengeByIdAndDeviceKey("id", deviceKeyMock));
	}

	@Test
	void findSignatureChallengeByIdAndDeviceKey_DoesntThrowSignatureChallengeNotFound_Positive() {
		Mockito.when(signatureChallengeRepository.findByIdAndDeviceKey(any(), any()))
				.thenReturn(Optional.of(signatureChallengeMock));

		Assertions
				.assertDoesNotThrow(() -> deviceKeyService.findSignatureChallengeByIdAndDeviceKey("id", deviceKeyMock));
	}

	@Test
	void deleteOldestDeviceKey_DeviceKeyWasNotFound_Negative() {
		Mockito.when(deviceKeyRepository.findFirstByUserOrderByCreatedDateAsc(any())).thenReturn(Optional.empty());

		deviceKeyService.deleteOldestDeviceKey(new User());
		verify(signatureChallengeRepository, times(0)).deleteByDeviceKey(any());
		verify(deviceKeyRepository, times(0)).delete(any());
	}

	@Test
	void deleteOldestDeviceKey_DeviceKeyFound_Positive() {
		Mockito.when(deviceKeyRepository.findFirstByUserOrderByCreatedDateAsc(any()))
				.thenReturn(Optional.of(deviceKeyMock));

		deviceKeyService.deleteOldestDeviceKey(new User());
		verify(signatureChallengeRepository, times(1)).deleteByDeviceKey(any());
		verify(deviceKeyRepository, times(1)).delete(any());
	}

	@Test
	void findDeviceKeyByDeviceIdAndUser_ReturnOptionalEmpty_Negative() {
		Mockito.when(deviceKeyService.findOptionalDeviceKeyByDeviceIdAndUser(any(), any()))
				.thenReturn(Optional.empty());
		Assertions.assertThrows(EntityNotFoundException.class,
				() -> deviceKeyService.findDeviceKeyByDeviceIdAndUser("", new User()));
	}

	@Test
	void findDeviceKeyByDeviceIdAndUser_ReturnDeviceKeyData_Positive() {
		Mockito.when(deviceKeyService.findOptionalDeviceKeyByDeviceIdAndUser(any(), any()))
				.thenReturn(Optional.of(deviceKeyMock));
		assertThat(deviceKeyService.findDeviceKeyByDeviceIdAndUser("", new User()).getDeviceId()).isEqualTo("id");
	}

	@Test
	void countByUser_ReturnValidData_Positive() {
		Mockito.when(deviceKeyRepository.countByUser(any())).thenReturn(1L);
		assertThat(deviceKeyService.countByUser(new User())).isEqualTo(1L);
	}

	@Test
	void findSignatureChallengeById_ReturnValidData_Positive() {
		Mockito.when(signatureChallengeRepository.findById(any())).thenReturn(Optional.of(signatureChallengeMock));
		assertThat(deviceKeyService.findSignatureChallengeById("")).isEqualTo(Optional.of(signatureChallengeMock));
	}

}
