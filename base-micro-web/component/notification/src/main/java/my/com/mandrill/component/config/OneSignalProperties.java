package my.com.mandrill.component.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "onesignal", ignoreUnknownFields = true)
public class OneSignalProperties {

	private String basePath;

	private String appId;

	private String restApiKey;

	private Integer recipientLimit = 2000;

	private String smallIcon;

	private String androidChannelId;

}
