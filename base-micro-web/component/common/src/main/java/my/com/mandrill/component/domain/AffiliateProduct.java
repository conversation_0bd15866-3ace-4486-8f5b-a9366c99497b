package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.*;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "affiliate_products")
public class AffiliateProduct extends AuditSection implements Serializable {

	@Size(max = 255)
	@Column(name = "code", length = 255, nullable = true)
	private String code;

	@Size(max = 255)
	@Column(name = "name", length = 255, nullable = true)
	private String name;

	@Size(max = 255)
	@Column(name = "provider_id", length = 255, nullable = true)
	private String providerId;

	@Size(max = 255)
	@Column(name = "product_type", length = 255, nullable = true)
	private String productType;

	@Column(name = "url")
	private String url;

}