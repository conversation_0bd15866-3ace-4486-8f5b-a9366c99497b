package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.EpfContribution;
import my.com.mandrill.component.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import java.time.Month;
import java.time.Year;
import java.util.List;
import java.util.Optional;

@Repository
public interface EpfContributionRepository extends JpaRepository<EpfContribution, String> {

	Optional<EpfContribution> findByUserAndMonthAndYear(@NonNull User user, @NonNull Month month, @NonNull Year year);

	List<EpfContribution> findByUserId(@NonNull String userId);

	@Query("""
			   select epf from EpfContribution epf
			   where epf.user = :user
			   order by epf.year desc,
			   case
			   when epf.month = 'JANUARY' THEN 1
			   when epf.month = 'FEBRUARY' THEN 2
			   when epf.month = 'MARCH' THEN 3
			   when epf.month = 'APRIL' THEN 4
			   when epf.month = 'MAY' THEN 5
			   when epf.month = 'JUNE' THEN 6
			   when epf.month = 'JULY' THEN 7
			   when epf.month = 'AUGUST' THEN 8
			   when epf.month = 'SEPTEMBER' THEN 9
			   when epf.month = 'OCTOBER' THEN 10
			   when epf.month = 'NOVEMBER' THEN 11
			   when epf.month = 'DECEMBER' THEN 12
			   end desc limit 1
			""")
	Optional<EpfContribution> findLatest(@NonNull User user);

	EpfContribution findTopByUserIdOrderByCreatedDateDesc(@NonNull String userId);

	/**
	 * start from here currently used by test
	 */
	@Modifying
	@Query("delete from EpfContribution e where e.user.id = :userId")
	void deleteByUser(@Nullable @Param("userId") String userId);

}
