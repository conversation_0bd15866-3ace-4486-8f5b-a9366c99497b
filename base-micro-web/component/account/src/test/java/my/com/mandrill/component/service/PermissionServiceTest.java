package my.com.mandrill.component.service;

import jakarta.persistence.EntityNotFoundException;
import my.com.mandrill.component.domain.Permission;
import my.com.mandrill.component.repository.jpa.PermissionRepository;
import my.com.mandrill.component.service.impl.PermissionServiceImpl;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.service.impl.GlobalValidationServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.notNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PermissionServiceTest {

	@InjectMocks
	PermissionServiceImpl permissionService;

	@Mock
	PermissionRepository permissionRepository;

	@Mock
	GlobalValidationServiceImpl globalValidationService;

	Permission permissionMock;

	List<Permission> permissionListMock;

	Page<Permission> permissionPageMock;

	@BeforeEach
	void setup() {
		permissionMock = new Permission();

		permissionListMock = new ArrayList<>();
		permissionListMock.add(permissionMock);

		permissionPageMock = new PageImpl<>(permissionListMock);
	}

	@Test
	void createPermission_Success() {
		Mockito.when(permissionRepository.save(any())).thenReturn(permissionMock);

		Assertions.assertEquals(permissionService.createPermission(new Permission()), permissionMock);
	}

	@Test
	void checkIfPermissionExists_ThrowPermissionExist_Negative() {
		Mockito.when(permissionRepository.findByNameAndCodeAndCategory(any(), any(), any()))
				.thenReturn(Optional.of(permissionMock));

		Assertions.assertThrows(BusinessException.class, () -> permissionService.checkIfPermissionExists("", "", ""));

	}

	@Test
	void checkIfPermissionExists_DoesntThrowPermissionExist_Negative() {
		Mockito.when(permissionRepository.findByNameAndCodeAndCategory(any(), any(), any()))
				.thenReturn(Optional.empty());

		Assertions.assertDoesNotThrow(() -> permissionService.checkIfPermissionExists("", "", ""));

	}

	@Test
	void getPermissionByIsAdminFalse_Positive() {
		Mockito.when(permissionRepository.findByAdminFalseOrderByCategoryAscNameAsc()).thenReturn(permissionListMock);

		Assertions.assertEquals(permissionService.getPermissionByIsAdminFalse().size(), 1);
	}

	@Test
	void getPermissionById_ThrowPermissionNotFound_Negative() {
		Mockito.when(permissionRepository.findById(any())).thenReturn(Optional.empty());

		Assertions.assertThrows(EntityNotFoundException.class, () -> permissionService.getPermissionById("id"));
	}

	@Test
	void getPermissionById_DoesntThrowPermissionNotFound_Negative() {
		Mockito.when(permissionRepository.findById(any())).thenReturn(Optional.of(permissionMock));

		Assertions.assertDoesNotThrow(() -> permissionService.getPermissionById("id"));
	}

	@Test
	void getPermissions_Success() {
		Set<Permission> permissionSet = new HashSet<>();
		permissionSet.add(permissionMock);
		Mockito.when(permissionRepository.findById(any())).thenReturn(Optional.of(permissionMock));

		Assertions.assertEquals(permissionService.getPermissions(permissionSet).size(), 1);
	}

	@Test
	void getPermissions_Pageable_Positive() {
		Mockito.when(permissionRepository.findAllByName(any(), any())).thenReturn(permissionPageMock);
		Mockito.when(globalValidationService.validateNullToLowerCase(notNull())).thenReturn("test");
		Assertions.assertEquals(permissionService.getPermissions(Pageable.ofSize(1), "").getContent().size(), 1);
		verify(globalValidationService, times(1)).validateNullToLowerCase(any());
	}

	@Test
	void getPermissions_List_Positive() {
		Mockito.when(permissionRepository.findAll()).thenReturn(permissionListMock);

		Assertions.assertEquals(permissionService.getPermissions().size(), 1);
	}

	@Test
	void updatePermission_Positive() {
		Mockito.when(permissionRepository.save(any())).thenReturn(permissionMock);

		Permission request = new Permission();
		Assertions.assertEquals(permissionService.updatePermission(request), permissionMock);
	}

	@Test
	void deletePermissionById_Positive() {
		Mockito.doNothing().when(permissionRepository).deleteById(any());

		permissionService.deletePermissionById("id");
		verify(permissionRepository, times(1)).deleteById(any());
	}

}
