package my.com.mandrill.component.service;

import jakarta.servlet.http.HttpServletRequest;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.request.AuthenticateUserRequest;
import my.com.mandrill.utilities.feign.dto.AuthenticateDTO;
import my.com.mandrill.utilities.general.constant.AccessTypeEnum;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import my.com.mandrill.utilities.general.dto.request.HashRequest;

import java.util.List;

public interface AuthService {

	List<String> authenticateUser(AppUser user, String password, AccessTypeEnum accessType, PasscodeType passcodeType);

	AppUser processUserData(String accessType, LoginTypeEnum loginType, String username);

	AuthenticateDTO validatePassword(AuthenticateUserRequest request);

	void checkPassword(User user, String password);

	void authenticateHash(HashRequest requestBody, HttpServletRequest request);

}
