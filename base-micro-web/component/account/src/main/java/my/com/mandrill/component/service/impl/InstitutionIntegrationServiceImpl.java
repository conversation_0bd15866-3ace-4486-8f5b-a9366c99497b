package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.dto.model.ObjectDTO;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.InstitutionIntegrationService;
import my.com.mandrill.component.service.InstitutionService;
import my.com.mandrill.utilities.feign.dto.*;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.AttachmentTypeEnum;
import my.com.mandrill.utilities.general.constant.AttachmentTypeFileStorageEnum;
import my.com.mandrill.utilities.general.constant.Constant;
import my.com.mandrill.utilities.general.constant.SystemConfigurationEnum;
import my.com.mandrill.utilities.general.dto.request.ProviderProductTypeSearchRequest;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.util.CollectionUtil;
import my.com.mandrill.utilities.storage.util.LocalFileUtil;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class InstitutionIntegrationServiceImpl implements InstitutionIntegrationService {

	private final InstitutionService institutionService;

	private final ProxyFeignClient proxyFeignClient;

	@Override
	public Institution createInstitution(Institution institution) {
		if (institution.getParentInstitution() == null) {
			institution.setTier(0);
			institution.setParentInstitution(null);
			institution.setPath(null);
		}
		else {
			Institution parentInstitution = institutionService
					.getInstitutionById(institution.getParentInstitution().getId());
			Integer tier = parentInstitution.getTier() + 1;
			if (!parentInstitution.getTier().equals(0)) {
				SystemConfigurationDTO tierOneSystemConfiguration = getTierOneConfiguration(
						parentInstitution.getPath());
				if (tier > Integer.parseInt(tierOneSystemConfiguration.getValue())) {
					throw new BusinessException(ErrorCodeEnum.MAX_TIER_CONFIG_REACHED);
				}
			}
			institution.setTier(tier);
			institution.setParentInstitution(parentInstitution);
			institution.setPath(parentInstitution.getPath());
		}
		return institutionService.createInstitution(institution);
	}

	@Override
	public Institution uploadLogo(String id, AttachmentGroupDTO attachmentGroupDTO) {
		Institution institution = institutionService.getInstitutionById(id);
		attachmentGroupDTO.setType(AttachmentTypeFileStorageEnum.INSTITUTION_IMAGE.toString());
		attachmentGroupDTO.setAttachmentGroupId(institution.getAttachmentGroupId());
		attachmentGroupDTO.setClassName(Institution.class.getSimpleName());
		attachmentGroupDTO.getAttachments().stream()
				.forEach(attachmentDTO -> attachmentDTO.setType(AttachmentTypeEnum.Image.name()));
		AttachmentGroupResponse attachmentGroupResponse = proxyFeignClient.getCommonFeignClient()
				.uploadAttachment(attachmentGroupDTO);
		if (attachmentGroupResponse != null) {
			institution.setAttachmentGroupId(attachmentGroupResponse.getAttachmentGroupId());

			AttachmentResponse latestAttachment = attachmentGroupResponse.getAttachments().stream()
					.sorted(Comparator.comparing(AttachmentResponse::getCreatedDate).reversed()).toList().get(0);
			StringBuilder url = new StringBuilder();
			if (attachmentGroupResponse.getPath() != null) {
				url.append(attachmentGroupResponse.getPath() + Constant.PATH_SEPARATOR);
			}
			url.append(LocalFileUtil.formatActualFile(latestAttachment.getId(), latestAttachment.getName()));
			institution.setUrl(url.toString());
			institutionService.save(institution);
		}
		return institution;
	}

	private String getTierOneId(String path) {
		return path.split("/")[1];
	}

	/*
	 * Recursively find TierOneConfiguration TODO: TO BE REVISITED, once the packaging and
	 * licensing module is DONE
	 */
	private SystemConfigurationDTO getTierOneConfiguration(String path) {
		Institution tierOneInstitution = institutionService.getInstitutionById(getTierOneId(path));
		List<SystemConfigurationDTO> systemConfigurationList = proxyFeignClient.getCommonFeignClient()
				.getAllSystemConfigurationsNoPagination(tierOneInstitution.getId());

		return systemConfigurationList.stream()
				.filter(systemConfiguration -> systemConfiguration.getCode()
						.equals(SystemConfigurationEnum.DEFAULT_INSTITUTION_TIER_MAX.getCode())
						&& systemConfiguration.getActive())
				.findAny().orElseThrow(ExceptionPredicate.sysConfigByInstitutionIdAndCodeNotFound(
						tierOneInstitution.getId(), SystemConfigurationEnum.DEFAULT_INSTITUTION_TIER_MAX.getCode()));

	}

	@Override
	public List<ObjectDTO> findSimpleSelectionInstitution(String currentInstitutionId, List<String> productTypes,
			List<String> productCategories, Boolean rsmActive) {
		Institution currentInstitution = institutionService.getInstitutionById(currentInstitutionId);
		Sort sortByNameAsc = Sort.by(Sort.Direction.ASC, "name");

		if (CollectionUtil.areAllEmpty(productTypes, productCategories)) {
			List<Institution> childInstitutions = institutionService
					.getChildInstitutionsByPath(currentInstitution.getPath(), sortByNameAsc);
			return childInstitutions.stream().map(MapStructConverter.MAPPER::toObjectDTO).toList();
		}

		Set<String> rsmActiveProviderIds = getRsmActiveProviderIds(rsmActive, productTypes);

		List<String> propertyTypes = List.of("property");
		if (CollectionUtils.containsAny(productCategories, propertyTypes)
				|| CollectionUtils.containsAny(productTypes, propertyTypes)) {
			return findPropertySimpleSelectionInstitution(rsmActive, rsmActiveProviderIds);
		}
		List<String> providerIds = findProviderIdByProduct(productTypes, productCategories,
				rsmActiveProviderIds.stream().toList());
		if (CollectionUtils.isEmpty(providerIds)) {
			return new ArrayList<>();
		}

		List<Institution> childInstitutions = institutionService
				.getChildInstitutionsByPathAndProviderIds(currentInstitution.getPath(), providerIds, sortByNameAsc);
		return childInstitutions.stream().map(MapStructConverter.MAPPER::toObjectDTO).toList();
	}

	private List<String> findProviderIdByProduct(List<String> productTypes, List<String> productCategories,
			List<String> providerIds) {

		return proxyFeignClient.getCommonFeignClient()
				.findProviderProductTypeSearch(ProviderProductTypeSearchRequest.builder().providerIds(providerIds)
						.productTypes(productTypes).productCategories(productCategories).build())
				.stream().map(ProviderProductTypeDTO::getProviderId).toList();
	}

	private List<ObjectDTO> findPropertySimpleSelectionInstitution(Boolean rsmActive,
			Set<String> rsmActiveProviderIds) {

		if (Boolean.TRUE.equals(rsmActive)) {
			return proxyFeignClient.getPropertyFeignClient().findAllDevelopersByIds(rsmActiveProviderIds).stream()
					.map(MapStructConverter.MAPPER::toObjectDTO).toList();
		}

		return proxyFeignClient.getPropertyFeignClient().findDevelopers().stream()
				.map(MapStructConverter.MAPPER::toObjectDTO).toList();
	}

	private Set<String> getRsmActiveProviderIds(Boolean rsmActive, List<String> productTypes) {

		return Boolean.TRUE.equals(rsmActive)
				? proxyFeignClient.getMoneyXCoreFeignClient().getRsmActiveProviders(productTypes) : new HashSet<>();

	}

}
