package my.com.mandrill.component.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.util.TraceContextHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@Aspect
@Slf4j
@Component
@RequiredArgsConstructor
public class AspectLogging {

	private final ObjectMapper objectMapper;

	private final TraceContextHelper traceContextHelper;

	// pointcut for all methods with @RestController classes, excluding auth controller to
	// remove credentials
	@Pointcut("execution(public * my.com.mandrill.component.controller.*.*(..))")
	public void restControllerMethods() {
	}

	@Pointcut("execution(public * my.com.mandrill.component.service.*.*(..))")
	public void serviceMethods() {
	}

	// Logging for controller entry time and exit time
	@Around("restControllerMethods()")
	public Object logControllerExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
		if (log.isDebugEnabled()) {
			long start = System.currentTimeMillis();
			Object result;
			Object[] args = joinPoint.getArgs();
			List<Object> filteredArgs = new ArrayList<>();
			String methodName = null;
			String controllerName = null;
			try {
				for (Object arg : args) {
					if (arg instanceof HttpServletRequest) {
						continue;
					}
					if (!(arg instanceof File)) {
						filteredArgs.add(arg);
					}
					else {
						filteredArgs.add("File object");
					}
				}
				methodName = joinPoint.getSignature().getName();
				controllerName = joinPoint.getTarget().getClass().getSimpleName();
				// objectMapper converts the args to JSON string
				log.debug("Controller Method Entry: CONTROLLER_ENTER: {}.{}() - {}", controllerName, methodName,
						objectMapper.writeValueAsString(filteredArgs));
			}
			catch (Exception e) {
				log.error("Exception during logging in AOP", e);
			}
			result = joinPoint.proceed();
			long duration = System.currentTimeMillis() - start;
			log.debug("CONTROLLER_EXIT={}.{}() requestId={}, executionTime={} ms, result={}", controllerName,
					methodName, traceContextHelper.getRequestId(), duration, result);

			return result;
		}
		return joinPoint.proceed();
	}

	@Around("serviceMethods()")
	public Object logServiceExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
		if (log.isDebugEnabled()) {
			long start = System.currentTimeMillis();
			Object result;
			Object[] args = joinPoint.getArgs();
			List<Object> filteredArgs = new ArrayList<>();
			String methodName = null;
			String serviceName = null;
			try {
				for (Object arg : args) {
					if (arg instanceof File) {
						filteredArgs.add("File object");
					}
					else if (arg instanceof HttpServletRequest) {
						filteredArgs.add("HttpServletRequest");
					}
					else {
						filteredArgs.add(arg);
					}
				}
				methodName = joinPoint.getSignature().getName();
				serviceName = joinPoint.getTarget().getClass().getSimpleName();
				log.debug("Service Method Entry: SERVICE_ENTER: {}.{}() - {}", serviceName, methodName,
						objectMapper.writeValueAsString(filteredArgs));
			}
			catch (Exception e) {
				log.error("Exception during logging in AOP", e);
			}
			result = joinPoint.proceed();
			long duration = System.currentTimeMillis() - start;
			log.debug("SERVICE_EXIT={}.{}() requestId={}, executionTime={} ms, result={}", serviceName, methodName,
					traceContextHelper.getRequestId(), duration, result);

			return result;
		}
		return joinPoint.proceed();
	}

}
