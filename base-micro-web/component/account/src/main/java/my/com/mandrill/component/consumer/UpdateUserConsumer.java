package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.domain.Nationality;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.service.NationalityService;
import my.com.mandrill.component.service.UserService;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserKafkaRequest;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.model.ObjectDTO;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.service.GeneralKafkaConsumer;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.errors.SerializationException;
import org.springframework.kafka.annotation.DltHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.retrytopic.TopicSuffixingStrategy;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.support.serializer.DeserializationException;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class UpdateUserConsumer implements GeneralKafkaConsumer {

	private final UserService userService;

	private final NationalityService nationalityService;

	private final ObjectMapper objectMapper;

	private final ProxyFeignClient proxyFeignClient;

	@Override
	@RetryableTopic(attempts = "5", topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
			backoff = @Backoff(delay = 1000, multiplier = 2.0),
			exclude = { SerializationException.class, DeserializationException.class })
	@KafkaListener(topics = KafkaTopic.UPDATE_USER_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.UPDATE_USER_TOPIC, concurrency = "${base.kafka.topic.update-user.concurrency}")
	public void consume(@Payload String message, @Header(name = KafkaHeaders.RECEIVED_KEY, required = false) String key)
			throws JsonProcessingException {
		Instant now = Instant.now();
		log.info("{} Started with message: {}", getClass().getSimpleName(), message);
		UpdateUserKafkaRequest request = objectMapper.readValue(message, UpdateUserKafkaRequest.class);

		Optional<User> user = userService.findById(request.getId());
		if (Objects.nonNull(request.getNationality())) {
			Nationality nationality = nationalityService.findById(request.getNationality().getId())
					.orElseThrow(ExceptionPredicate.nationalityNotFound(request.getNationality().getId()));
			request.setNationality(ObjectDTO.builder().id(nationality.getId()).build());
			request.setIsNationalityEditable(request.getNationalityUpdateOrigin().isEditable());
		}

		if (StringUtils.isNotBlank(request.getNric()) && Objects.nonNull(request.getNricUpdateOrigin())) {
			request.setIsNricEditable(request.getNricUpdateOrigin().isEditable());
		}

		user.ifPresent(value -> userService.saveUserUpdateRequest(request, value));
		log.info("{} Ended. Took {} ms", getClass().getSimpleName(), System.currentTimeMillis() - now.toEpochMilli());
	}

	@Override
	@DltHandler
	public void handleError(String message) {
		log.error("Failed to process: {}", message);
	}

}
