package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.UserStatisticType;
import my.com.mandrill.component.domain.WeeklyUserStatistics;

import java.time.Year;
import java.time.ZoneId;
import java.util.List;
import java.util.Set;

public interface WeeklyUserStatisticsIntegrationService {

	/**
	 * Finds the weekly user statistics given a set of weeks. If `weeks` is not specified,
	 * this defaults to:
	 *
	 * <li>Week 1 - Last week of the year if `year` is in the past</li>
	 * <li>Week 1 - Current week of the year if `year` is the current year</li>
	 * <li>Empty list if `year` is a future year.</li>
	 */
	List<WeeklyUserStatistics> findForWeeks(Set<Integer> weeks, Year year, UserStatisticType type, ZoneId zone);

	void synchronize(Year year, ZoneId zone);

}
