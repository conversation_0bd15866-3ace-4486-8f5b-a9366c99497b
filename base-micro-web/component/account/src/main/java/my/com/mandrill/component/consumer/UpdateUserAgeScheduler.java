package my.com.mandrill.component.consumer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.service.UserService;
import my.com.mandrill.utilities.general.service.GeneralKafkaConsumer;
import my.com.mandrill.utilities.general.util.CalculationUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.kafka.annotation.DltHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class UpdateUserAgeScheduler implements GeneralKafkaConsumer {

	private final UserService userService;

	@Override
	@KafkaListener(topics = KafkaTopicConfig.UPDATE_APP_USER_AGE, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopicConfig.UPDATE_APP_USER_AGE)
	public void consume(@Payload String message,
			@Header(name = KafkaHeaders.RECEIVED_KEY, required = false) String key) {
		Instant now = Instant.now();
		log.info("{} Started with message: {}", getClass().getSimpleName(), message);

		List<User> users = userService.findByDobNotNull();
		for (User user : users) {
			user.setAge(CalculationUtil.calculateAgeByDob(user.getDob()));
		}
		userService.saveAll(users);

		log.info("{} Ended. Took {} ms", getClass().getSimpleName(), System.currentTimeMillis() - now.toEpochMilli());

	}

	@Override
	@DltHandler
	public void handleError(String message) {
		log.error("Message could not be processed. {}", message);
	}

}
