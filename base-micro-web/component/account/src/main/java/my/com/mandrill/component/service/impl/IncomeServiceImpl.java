package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.EmploymentType;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.IncomeType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.component.dto.model.UserIncome;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.IncomeRepository;
import my.com.mandrill.component.service.EmploymentTypeService;
import my.com.mandrill.component.service.IncomeService;
import my.com.mandrill.component.service.IncomeTypeService;
import my.com.mandrill.utilities.general.constant.Constant;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.data.domain.Sort;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class IncomeServiceImpl implements IncomeService {

	private static final String INCOME_TYPE_SALARY = "SALARY";

	private final EmploymentTypeService employmentTypeService;

	private final IncomeRepository incomeRepository;

	private final IncomeTypeService incomeTypeService;

	@Override
	public Income findByIdAndUser(String id, User user) {
		return incomeRepository.findByIdAndUser(id, user)
				.orElseThrow(ExceptionPredicate.incomeNotFoundByIdAndUser(id, user));
	}

	@Override
	public Optional<Income> findOptionalByIdAndUser(String id, User user) {
		return incomeRepository.findByIdAndUser(id, user);
	}

	@Override
	public List<Income> findByUserAndIncomeTypeAndEmploymentType(User user, IncomeType incomeType,
			EmploymentType employmentType, Sort sort) {
		return incomeRepository.findByUserAndIncomeTypeAndEmploymentType(user, incomeType, employmentType, sort);
	}

	@Override
	public void existsByUserAndIncomeTypeAndEmploymentType(User user, IncomeType incomeType,
			EmploymentType employmentType) {
		if (incomeRepository.existsByUserAndIncomeTypeAndEmploymentType(user, incomeType, employmentType)) {
			throw new BusinessException(ErrorCodeEnum.INCOME_EXISTS);
		}
	}

	@Override
	public Income findUserIncomeSalary(String userId, EmploymentType employmentType) {
		return findOptionalUserIncomeSalary(userId, employmentType)
				.orElseThrow(ExceptionPredicate.incomeNotFoundByLatest());
	}

	@Override
	public Optional<Income> findOptionalUserIncomeSalary(String userId, EmploymentType employmentType) {
		return incomeRepository.findFirstByUserIdAndIncomeTypeCodeAndEmploymentTypeOrderByCreatedDateDesc(userId,
				INCOME_TYPE_SALARY, employmentType);
	}

	@Override
	public Income create(Income income, User user) {
		income.setIncomeType(incomeTypeService.findById(income.getIncomeType().getId()));
		income.setEmploymentType(employmentTypeService.findById(income.getEmploymentType().getId()));
		income.setUser(user);

		existsByUserAndIncomeTypeAndEmploymentType(user, income.getIncomeType(), income.getEmploymentType());
		return income;
	}

	@Override
	public Income update(String id, Income income, User user) {
		Income existingIncome = findByIdAndUser(id, user);
		existingIncome.setMonthlyIncomeAmount(income.getMonthlyIncomeAmount());

		if (existingIncome.getEmploymentType() == null) {
			existingIncome.setEmploymentType(employmentTypeService.findById(income.getEmploymentType().getId()));
			existsByUserAndIncomeTypeAndEmploymentType(user, existingIncome.getIncomeType(),
					existingIncome.getEmploymentType());
		}
		return existingIncome;
	}

	@Override
	@Transactional
	public Income save(Income income) {
		return incomeRepository.save(income);
	}

	@Override
	@Transactional
	public void delete(Income income) {
		incomeRepository.delete(income);
	}

	@Override
	public Optional<Income> findByUserAndEmploymentTypeAndIncomeType(@NonNull User user,
			@NonNull EmploymentType employmentType, @NonNull IncomeType incomeType) {
		return incomeRepository.findByUserAndEmploymentTypeAndIncomeType(user, employmentType, incomeType);
	}

	@Override
	public BigDecimal findIncome(String userId) {
		Optional<BigDecimal> monthlyIncome = incomeRepository.findMonthlyIncomeByUserIdAndEmploymentTypeAndIncomeType(
				userId, Constant.FULL_TIME_EMPLOYMENT_TYPE_ID, Constant.SALARY_INCOME_TYPE_ID);
		return monthlyIncome.orElse(BigDecimal.ZERO);
	}

	@Override
	public boolean existsByIncomeTypeId(String incomeTypeId) {
		return incomeRepository.existsByIncomeTypeId(incomeTypeId);
	}

	@Override
	public List<UserIncome> findByUserDeletedFalseAndLoginTypeAndCreatedDateGreaterThanEqual(
			LoginTypeEnum loginTypeEnum, Instant time) {
		return incomeRepository.findByUserDeletedFalseAndLoginTypeAndCreatedDateGreaterThanEqual(loginTypeEnum, time);
	}

	@Override
	public Income findLastByUserAndIncomeType(String userId, String incomeTypeId) {
		return incomeRepository.findTopByUserIdAndIncomeTypeIdOrderByCreatedDateDesc(userId, incomeTypeId);
	}

	@Override
	public List<IncomeDTO> getUserCurrentIncome(String refNo) {
		return incomeRepository.findByRefNo(refNo);
	}

	@Override
	@Transactional
	public void createOrUpdateIncome(String userId, BigDecimal incomeAmount) {
		EmploymentType employmentType = employmentTypeService.findById(Constant.FULL_TIME_EMPLOYMENT_TYPE_ID);
		IncomeType incomeType = incomeTypeService.findById(Constant.SALARY_INCOME_TYPE_ID);

		Optional<Income> incomeOptional = findByUserAndEmploymentTypeAndIncomeType(new User(userId), employmentType,
				incomeType);
		Income income;
		if (incomeOptional.isEmpty()) {
			income = new Income();
			income.setUser(new User(userId));
			income.setEmploymentType(employmentType);
			income.setIncomeType(incomeType);
		}
		else {
			income = incomeOptional.get();
		}
		income.setMonthlyIncomeAmount(incomeAmount);
		incomeRepository.save(income);
	}

}
