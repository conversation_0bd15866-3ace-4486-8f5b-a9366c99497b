package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSection;
import org.hibernate.Hibernate;

import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "device_key", uniqueConstraints = { @UniqueConstraint(columnNames = { "user_id", "device_id" }) })
public class DeviceKey extends AuditSection {

	@NotNull
	@ManyToOne(optional = false)
	@JoinColumn(name = "user_id", nullable = false)
	private AppUser user;

	@NotBlank
	@Size(max = 255)
	@Column(name = "device_id", nullable = false)
	private String deviceId;

	@NotBlank
	@Size(max = 255)
	@Column(name = "device_model", nullable = false)
	private String deviceModel;

	@Size(max = 255)
	@Column(name = "public_key")
	private String publicKey;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		DeviceKey deviceKey = (DeviceKey) o;
		return getId() != null && Objects.equals(getId(), deviceKey.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}