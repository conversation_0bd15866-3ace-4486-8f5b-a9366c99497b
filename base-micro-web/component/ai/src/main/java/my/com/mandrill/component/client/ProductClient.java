package my.com.mandrill.component.client;

import my.com.mandrill.component.dto.model.ForYouDTO;
import my.com.mandrill.component.dto.model.ProductSuggestionDTO;
import my.com.mandrill.component.dto.request.RetirementProductSuggestionRequest;
import my.com.mandrill.component.dto.request.clientRequest.ProductSuggestionQuestionnaireRequest;
import my.com.mandrill.component.dto.response.clientresponse.ProductPageResponse;
import my.com.mandrill.component.dto.response.clientresponse.ProductSuggestionQuestionnaireResponse;
import my.com.mandrill.utilities.general.dto.response.AiProductDetailResponse;
import my.com.mandrill.utilities.general.dto.response.AiProductResponse;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;

@HttpExchange(contentType = MediaType.APPLICATION_JSON_VALUE, accept = MediaType.APPLICATION_JSON_VALUE)
public interface ProductClient {

	@GetExchange("/list/{type}")
	ResponseEntity<String> findProductList(@RequestParam(required = false) String page,
			@RequestParam(required = false, value = "per_page") String size, @PathVariable String type,
			@RequestParam(value = "partners_list") String issuerCodes);

	@GetExchange("/filters/{type}")
	ResponseEntity<String> findProductFilterList(@PathVariable String type,
			@RequestParam(name = "recommendation_type", required = false) String recommendationType);

	@PostExchange("/suggestions/{type}")
	ResponseEntity<String> findSuggestions(@RequestBody ProductSuggestionDTO request,
			@RequestParam(required = false) String page,
			@RequestParam(required = false, value = "per_page") String size, @PathVariable String type,
			@RequestParam(value = "partners_list") String issuerCodes,
			@RequestParam(name = "recommendation_type", required = false) String recommendationType);

	@PostExchange("/suggestions/{type}")
	ProductPageResponse<List<AiProductResponse>> findSuggestionsStruct(@RequestBody ProductSuggestionDTO request,
			@RequestParam(required = false) String page,
			@RequestParam(required = false, value = "per_page") String size, @PathVariable String type,
			@RequestParam(value = "partners_list") String issuerCodes,
			@RequestParam(name = "recommendation_type", required = false) String recommendationType);

	@GetExchange("/details/{type}")
	ResponseEntity<String> findProductDetailsList(@PathVariable String type,
			@RequestParam(value = "product_name") String productName);

	@GetExchange("/details/{type}")
	AiProductDetailResponse findProductDetailsListStruct(@PathVariable String type,
			@RequestParam(value = "product_name") String productName);

	@GetExchange("/compare/{type}")
	ResponseEntity<String> findProductCompare(@PathVariable String type,
			@RequestParam(value = "product_1_name") String productName1,
			@RequestParam(value = "product_2_name") String productName2);

	@GetExchange("/groups/{group}")
	ResponseEntity<String> findProductCategory(@PathVariable String group);

	@PostExchange("/for_you")
	ResponseEntity<String> findProductForYou(@RequestBody ForYouDTO body,
			@RequestParam(required = false, value = "top_n") String size,
			@RequestParam(value = "partners_list") String issuerCodes);

	@GetExchange("/{type}")
	ResponseEntity<String> findProduct(@PathVariable String type,
			@RequestParam(required = false, name = "entity_name") String entityName,
			@RequestParam(required = false, name = "card_type") String cardType,
			@RequestParam(required = false, name = "product_name") String productName,
			@RequestParam(required = false, name = "product_id") String productId);

	@PostExchange("/refinance/{type}")
	ResponseEntity<String> findRefinance(@PathVariable String type, @RequestBody ProductSuggestionDTO body,
			@RequestParam(required = false) String page,
			@RequestParam(required = false, value = "per_page") String size);

	@GetExchange("/groups")
	ResponseEntity<String> findProductGroups();

	@PostExchange("/retirement")
	ResponseEntity<String> findProductSuggestionForRetirement(@RequestBody RetirementProductSuggestionRequest request,
			@RequestParam(required = false) String page,
			@RequestParam(required = false, value = "per_page") String size);

	@PostExchange("/questionnaire/credit-card")
	ProductSuggestionQuestionnaireResponse findProductCCSuggestionFromQuestionnaire(
			@RequestBody ProductSuggestionQuestionnaireRequest request);

	@PostExchange("/questionnaire/casa")
	ProductSuggestionQuestionnaireResponse findProductCASASuggestionFromQuestionnaire(
			@RequestBody ProductSuggestionQuestionnaireRequest request);

}
