package my.com.mandrill.component.controller.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.model.AdminUserDTO;
import my.com.mandrill.component.dto.model.InstitutionDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.ExcelFileGenerator;
import my.com.mandrill.utilities.core.token.service.UserTokenService;
import my.com.mandrill.utilities.feign.dto.request.EmailRequest;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.util.CryptoUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Tag(name = "04-user")
@Slf4j
@RestController
@RequestMapping("/admin/user")
@RequiredArgsConstructor
public class AdminUserController {

	private final UserService userService;

	private final UserIntegrationService userIntegrationService;

	private final DeviceBindingService deviceBindingService;

	private final AccountService accountService;

	private final ValidationService validationService;

	private final ObjectMapper objectMapper;

	private final BaseProperties baseProperties;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final ExcelFileGenerator csvFileGenerator;

	private final TokenService tokenService;

	private final UserTokenService userTokenService;

	private final JSONUtil jsonUtil;

	private final AppUserService appUserService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_CREATE)")
	public ResponseEntity<AdminUserDTO> createUser(@Valid @RequestBody CreateUserRequest createUserRequest,
			@RequestParam String currentInstitutionId) {

		// Transformation
		User user = objectMapper.convertValue(createUserRequest, User.class);
		// Validation
		this.validationService.validateEmailBlacklist(user.getEmail());
		Institution ins = validationService.validateInstitution(currentInstitutionId);
		user.setInstitutions(Sets.newHashSet(ins));
		user.setCurrentInstitution(ins);
		user.setLoginType(LoginTypeEnum.ADMIN);
		user.setSecretKey(CryptoUtil.generateSecretKey());
		accountService.checkIfAdminUserExists(user);
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		validationService.validateCreateAdminUser(user);
		// Implementation
		User result = userService.adminSave(user);
		// Result
		return ResponseEntity.ok(objectMapper.convertValue(result, AdminUserDTO.class));

	}

	@PutMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<AdminUserDTO> updateUser(@Valid @RequestBody UpdateUserRequest updateUserRequest,
			@RequestParam String currentInstitutionId) {

		// Transformation
		User user = objectMapper.convertValue(updateUserRequest, User.class);
		User existingUser = userIntegrationService.findByIdAndInstitutionId(user.getId(), currentInstitutionId);
		boolean isUpdatingAuthorities = isUpdatingAuthorities(existingUser, user);
		Institution ins = validationService.validateInstitution(currentInstitutionId);
		existingUser.setCurrentInstitution(ins);
		// Validation
		if (user.getEmail() != null && !user.getEmail().equalsIgnoreCase(existingUser.getEmail())) {
			this.validationService.validateEmailBlacklist(user.getEmail());
			accountService.checkIfAdminUserExists(user);
		}
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		validationService.validateUpdateAdminUser(existingUser, user);
		validationService.validateAuthority(existingUser, user, currentInstitutionId);
		// Implementation
		User result = userIntegrationService.save(existingUser);
		if (isUpdatingAuthorities) {
			this.updateToken(user);
		}
		// Result
		return ResponseEntity.ok(objectMapper.convertValue(result, AdminUserDTO.class));
	}

	private boolean isUpdatingAuthorities(User existingUser, User requestUser) {
		return !requestUser.getAuthorities().equals(existingUser.getAuthorities());
	}

	private void updateToken(User user) {
		List<TokenServiceResponse> tokenServiceResponse = tokenService.revokedByUser(user);
		if (CollectionUtils.isNotEmpty(tokenServiceResponse)) {
			tokenServiceResponse.forEach(tokenResponse -> {
				userTokenService.deleteByTokenIds(List.of(tokenResponse.getAccessToken()));
			});
		}
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<AdminUserDTO> getUserById(@PathVariable String id,
			@RequestParam String currentInstitutionId) {
		// Validation
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		// Implementation
		User result = userIntegrationService.findByIdAndInstitutionId(id, currentInstitutionId);
		// Result
		return ResponseEntity.ok(objectMapper.convertValue(result, AdminUserDTO.class));
	}

	@GetMapping
	public ResponseEntity<UserAdminResponse> getAccount() {
		User resp = userService.getCurrentUser();
		UserAdminResponse userDTO = objectMapper.convertValue(resp, UserAdminResponse.class);
		userDTO.setInstitutions(userDTO.getInstitutions().stream().sorted(Comparator.comparing(InstitutionDTO::getTier))
				.collect(Collectors.toList()));
		return ResponseEntity.ok(userDTO);
	}

	@GetMapping("pagination/all")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Page<AdminUserDTO>> getAllUsers(Pageable pageable, @RequestParam String currentInstitutionId,
			@RequestParam(required = false) String fullName) {
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);

		fullName = StringUtils.defaultIfBlank(fullName, "").toLowerCase();
		final Page<AdminUserDTO> users = userIntegrationService.findByInstitutionsIdAndLoginTypeAndFullName(pageable,
				currentInstitutionId, LoginTypeEnum.ADMIN, fullName)
				.map(user -> objectMapper.convertValue(user, AdminUserDTO.class));
		return ResponseEntity.ok(users);
	}

	@Hidden
	@GetMapping("/refNo/{refNo}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<AdminUserDTO> getUserByRefNo(@PathVariable String refNo) {
		AppUser appUser = appUserService.findByRefNo(refNo);
		return ResponseEntity.ok(objectMapper.convertValue(appUser, AdminUserDTO.class));
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_DELETE)")
	public void deleteUser(@PathVariable String id, @RequestParam String currentInstitutionId) {
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		userIntegrationService.deleteAdminUser(id, currentInstitutionId);
	}

	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@GetMapping("/reset-password")
	public void requestResetPassword(@RequestParam String email) throws URISyntaxException {
		// for user
		UserKeyRequest keyRequest = accountService.requestEmailKey(email, LoginTypeEnum.ADMIN,
				RequestKeyType.PASSWORD_RESET);
		User user = userService.findByEmailAndLoginTypeAndActiveTrue(keyRequest.getUsername(), LoginTypeEnum.ADMIN);

		String[] keyValues = keyRequest.getKeyValue().split("_");
		String validationKey = keyValues[0];
		URIBuilder b = new URIBuilder();
		b.addParameter("key", validationKey);
		b.addParameter("username", keyRequest.getUsername());
		URI uri = b.build();
		String finalUri = baseProperties.getRequestUri().getResetPassword().concat(uri.toString());

		Map<String, Object> templateVariable = Map.of(EmailTemplateVariableEnum.USERNAME.getValue(), user.getUsername(),
				EmailTemplateVariableEnum.URL.getValue(), finalUri);

		EmailRequest emailRequest = EmailRequest.builder().to(Collections.singletonList(user.getEmail()))
				.templateVariable(templateVariable).templateName(TemplateNameEnum.EMAIL_RESET_PASSWORD.getValue())
				.build();

		try {
			kafkaTemplate.send(KafkaTopic.SEND_EMAIL_TOPIC, user.getEmail(),
					objectMapper.writeValueAsString(emailRequest));
		}
		catch (Exception e) {
			log.error("fail sending email via service: " + finalUri);
		}
	}

	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/reset-password")
	public void updateEmailVerification(@Valid @RequestBody UpdatePasswordResetEmailRequest request) {
		userService.completePasswordResetEmail(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/change-password")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void changePassword(@Valid @RequestBody ChangePasswordRequest request) {
		User user = userService.getCurrentUser();
		accountService.changePassword(user, request);
		accountService.sendEmailPasswordUpdated(user);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/generate/secret-key")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void generateSecretKey() {
		userService.generateSecretKey();
	}

	@GetMapping("/mobile-users/pagination/all")
	@PreAuthorize("hasAuthority(@authorityPermission.MOBILE_USER_READ)")
	public ResponseEntity<Page<UserDetailResponse>> getAllMobileUsers(
			@PageableDefault(sort = "fullName", direction = Sort.Direction.ASC) Pageable pageable,
			@RequestParam(required = false) String fullName, @RequestParam(required = false) String email,
			@RequestParam(required = false) String phoneNumber, @RequestParam(required = false) String refNo,
			@RequestParam(required = false) Boolean active,
			@RequestParam(required = false) PaymentAccountStatus paymentAccountStatus,
			@RequestParam(required = false) LocalDate signUpDate,
			@RequestParam(required = false) MoneyXUserDateType dateType,
			@RequestParam(required = false) LocalDate startDate, @RequestParam(required = false) LocalDate endDate,
			@RequestParam(required = false) String referralCode,
			@RequestParam(required = false) String referralCodeUsed,
			@RequestParam(required = false) String rsmRelationType) {
		var paginationRequest = MobileUserPaginationRequest.builder().fullName(fullName).refNo(refNo)
				.paymentAccountStatus(paymentAccountStatus).dateType(dateType).active(active)
				.referralCodeUsed(referralCodeUsed).rsmRelationType(rsmRelationType).referralCode(referralCode).build();
		paginationRequest.setEmail(email); // for encryption
		paginationRequest.setCombinedPhoneNumber(phoneNumber); // for encryption and
																// country code separation
		paginationRequest.setStartDate(signUpDate);
		paginationRequest.setEndDate(signUpDate);
		paginationRequest.setStartDate(startDate);
		paginationRequest.setEndDate(endDate);

		Page<UserDetailResponse> response = userService.findInternalMobileUser(pageable, paginationRequest);
		return ResponseEntity.ok(response.map(userResponse -> {
			boolean hasDeviceBinding = validationService.hasDeviceBinding(userResponse.getId());
			userResponse.setHasDeviceBinding(hasDeviceBinding);
			return userResponse;
		}));
	}

	@GetMapping("/mobile-users/export-to-csv")
	@PreAuthorize("hasAuthority(@authorityPermission.MOBILE_USER_READ)")
	public void getAllMobileUsersReport(@RequestParam(required = false) String fullName,
			@RequestParam(required = false) String email, @RequestParam(required = false) String phoneNumber,
			@RequestParam(required = false) String refNo, @RequestParam(required = false) Boolean active,
			@RequestParam(required = false) PaymentAccountStatus paymentAccountStatus,
			@RequestParam(required = false) LocalDate signUpDate,
			@RequestParam(required = false) MoneyXUserDateType dateType,
			@RequestParam(required = false) LocalDate startDate, @RequestParam(required = false) LocalDate endDate,
			@RequestParam(required = false) String referralCode,
			@RequestParam(required = false) String referralCodeUsed,
			@RequestParam(required = false) String rsmRelationType) {
		CurrentUserIdResponse currentUser = accountService.getCurrentUserIdAndRefNo();

		var paginationRequest = MobileUserPaginationRequest.builder().fullName(fullName).refNo(refNo)
				.paymentAccountStatus(paymentAccountStatus).dateType(dateType).active(active)
				.referralCodeUsed(referralCodeUsed).rsmRelationType(rsmRelationType).referralCode(referralCode).build();
		paginationRequest.setEmail(email); // for encryption
		paginationRequest.setCombinedPhoneNumber(phoneNumber); // for encryption and
																// country code separation
		paginationRequest.setStartDate(signUpDate);
		paginationRequest.setEndDate(signUpDate);
		paginationRequest.setStartDate(startDate);
		paginationRequest.setEndDate(endDate);
		paginationRequest.setFilterStartDate(startDate);
		paginationRequest.setFilterEndDate(endDate);

		userIntegrationService.generateFileReport(paginationRequest, currentUser)
				.thenAccept(fileReport -> log.info("success generate the report with key: {}", fileReport));
	}

	@PutMapping("/mobile-users/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.MOBILE_USER_UPDATE)")
	public void updateStatusInternalUserMobile(@PathVariable String id,
			@RequestBody UpdateStatusInternalMobileRequest request) {
		userService.updateStatusInternalMobile(id, request);
	}

	@GetMapping("/mobile-users/{id}/overview")
	@PreAuthorize("hasAuthority(@authorityPermission.VIEW_MOBILE_USER_OVERVIEW)")
	public ResponseEntity<UserOverviewResponse> getInternalUserOverviewById(@PathVariable String id) {
		User user = userService.findInternalMobileUserById(id);
		return ResponseEntity.ok(jsonUtil.convertValue(user, UserOverviewResponse.class));
	}

	@GetMapping("/mobile-users/{id}/personal-info")
	@PreAuthorize("hasAuthority(@authorityPermission.VIEW_MOBILE_USER_PERSONAL_INFO)")
	public ResponseEntity<UserPersonalInformationResponse> getInternalUserPersonalInfoById(@PathVariable String id) {

		return ResponseEntity.ok(userIntegrationService.getUserPersonalInformation(id));
	}

	@GetMapping("/mobile-users/{id}/payment-info")
	@PreAuthorize("hasAuthority(@authorityPermission.VIEW_MOBILE_USER_PAYMENT_EKYC_INFO)")
	public ResponseEntity<UserPaymentInformationResponse> getInternalUserPaymentInfoById(@PathVariable String id) {

		return ResponseEntity.ok(userIntegrationService.getUserPaymentInformation(id));
	}

	@GetMapping("/mobile-users/{id}/ekyc-info")
	@PreAuthorize("hasAuthority(@authorityPermission.VIEW_MOBILE_USER_PAYMENT_EKYC_INFO)")
	public ResponseEntity<UserEKycInformationResponse> getInternalUserEKycInfoById(@PathVariable String id) {

		return ResponseEntity.ok(userIntegrationService.getUserEKycInformation(id));
	}

	@ResponseStatus(HttpStatus.OK)
	@GetMapping(value = "/mobile-users/v2/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
	@PreAuthorize("hasAuthority(@authorityPermission.MOBILE_USER_READ)")
	public AccountDetailResponse getInternalUserByIdV2(@PathVariable String id) {
		return userIntegrationService.findDetailById(id);
	}

	@GetMapping("mobile-users/pagination/all/report")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_USER_LIST)")
	public ResponseEntity<Page<UserListResponse>> getMobileUsersReportPagination(
			@PageableDefault(sort = "createdDate", direction = Sort.Direction.ASC) Pageable pageable,
			@RequestParam LocalDate dateFrom, @RequestParam LocalDate dateTo,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone) {

		validationService.validateDate(dateFrom, dateTo);
		Page<UserListResponse> response = userService.findRefNoByCreatedDate(pageable, dateFrom, dateTo,
				ZoneId.of(timeZone));

		return ResponseEntity.ok(response);
	}

	@Hidden
	@PostMapping("integration/detail")
	@PreAuthorize("hasAuthority(@authorityPermission.MOBILE_USER_READ)")
	public ResponseEntity<List<InternalUserMobileResponse>> getUserDataByUserId(
			@Valid @RequestBody UserIdListRequest request) {
		return ResponseEntity.ok(userService.findAllByUserIds(request.getUserId()));
	}

	@PutMapping("/reset-binding-by-user/{userId}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void resetUserDeviceBinding(@PathVariable String userId) {
		deviceBindingService.deleteAllByUserId(userId);
	}

}
