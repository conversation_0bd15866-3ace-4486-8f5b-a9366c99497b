package my.com.mandrill.component.dto.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.component.dto.model.HomeMenuDTO;
import my.com.mandrill.component.dto.model.HomeMenuGroupDTO;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode
public class HomeMenuMapClientResponse implements Serializable {

	private HomeMenuGroupDTO menuGroup;

	private List<HomeMenuDTO> submenus;

}
