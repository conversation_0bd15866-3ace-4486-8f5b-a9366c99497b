package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.HomeSectionEnum;
import my.com.mandrill.component.domain.HomeMenuMap;
import my.com.mandrill.component.dto.model.HomeMenuMapDTO;
import my.com.mandrill.component.dto.request.HomeMenuMapCreateRequest;
import my.com.mandrill.component.dto.request.HomeMenuMapUpdateSeqRequest;
import my.com.mandrill.component.dto.response.HomeMenuMapClientResponse;
import my.com.mandrill.component.dto.response.HomeMenuProductResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface HomeMenuMapIntgService {

	HomeMenuMap create(HomeMenuMapCreateRequest homeMenuMapDTO);

	List<HomeMenuMapClientResponse> findBySectionEnum(HomeSectionEnum homeSectionEnum);

	Page<HomeMenuMap> findAll(HomeSectionEnum section, String menuCode, String groupCode, Pageable pageable);

	HomeMenuMap findById(String id);

	void deleteProcess(String id);

	HomeMenuMap update(String id, HomeMenuMapDTO homeMenuMapDTO);

	HomeMenuMap updateSeq(String id, HomeMenuMapUpdateSeqRequest request);

	List<HomeMenuProductResponse> findAllByHomeMenuCode(String menuCode);

}
