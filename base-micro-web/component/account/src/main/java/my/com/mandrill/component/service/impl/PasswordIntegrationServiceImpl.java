package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.service.PasscodeProviderService;
import my.com.mandrill.component.service.PasswordIntegrationService;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class PasswordIntegrationServiceImpl implements PasswordIntegrationService {

	private final Map<String, PasscodeProviderService> passcodeProviderService;

	public UserKeyRequest validate(String pin, AppUser user, Supplier<UserKeyRequest> supplier) {
		log.info("validating password...");
		passcodeProviderService.get(PasscodeType.PASSWORD.getProvider()).match(user, pin);

		log.info("validating success, execute post process");
		return supplier.get();
	}

}
