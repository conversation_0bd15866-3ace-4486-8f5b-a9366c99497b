package my.com.mandrill.component.controller.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Authority;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.dto.model.AuthorityDTO;
import my.com.mandrill.component.dto.response.AuthorityNoPaginationResponse;
import my.com.mandrill.component.service.AuthorityService;
import my.com.mandrill.component.service.InstitutionService;
import my.com.mandrill.component.service.ValidationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "05-authority")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/authority")
public class AdminAuthorityController {

	private final AuthorityService authorityService;

	private final InstitutionService institutionService;

	private final ValidationService validationService;

	private final ObjectMapper objectMapper;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_CREATE)")
	public ResponseEntity<AuthorityDTO> createAuthority(@RequestBody AuthorityDTO authorityDTO,
			@RequestParam String currentInstitutionId) {
		// Transformation
		Authority authority = objectMapper.convertValue(authorityDTO, Authority.class);

		// Validation
		Institution institution = validationService.validateInstitution(currentInstitutionId);
		authorityService.checkIfExists(authorityDTO.getName(), currentInstitutionId);
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		authority.setInstitution(institution);
		// Implementation
		Authority result = authorityService.create(authority);

		// Result
		return ResponseEntity.ok(MapStructConverter.MAPPER.toAuthorityDTO(result));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Page<AuthorityDTO>> getAllAuthorities(Pageable pageable,
			@RequestParam(required = false) String name, @RequestParam String currentInstitutionId) {
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		Page<Authority> authoritiesPage = authorityService.findAllByNameAndInstitutionId(pageable, name,
				currentInstitutionId);

		return ResponseEntity.ok(authoritiesPage.map(MapStructConverter.MAPPER::toAuthorityDTO));
	}

	@GetMapping("no-pagination")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<AuthorityNoPaginationResponse>> getAllAuthoritiesNoPagination(
			@RequestParam String currentInstitutionId) {
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		List<Institution> insList = institutionService.findAllByPathContainingAndActiveTrue(currentInstitutionId);
		List<Authority> authoritiesPage = authorityService
				.findAllByInstitutionIdsAndActiveTrue(insList.stream().map(Institution::getId).toList());
		return ResponseEntity.ok().body(
				authoritiesPage.stream().map(MapStructConverter.MAPPER::toAuthorityNoPaginationResponse).toList());
	}

	@GetMapping("/{authorityId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<AuthorityDTO> getAuthorityById(@PathVariable String authorityId,
			@RequestParam String currentInstitutionId) {
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		Authority authority = authorityService.findByIdAndInstitutionId(authorityId, currentInstitutionId);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toAuthorityDTO(authority));
	}

	@PutMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<AuthorityDTO> updateAuthority(@Valid @RequestBody AuthorityDTO authorityDto,
			@RequestParam String currentInstitutionId) {
		// Transformation
		Authority authority = objectMapper.convertValue(authorityDto, Authority.class);

		// Validation
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		// Implementation
		Authority result = authorityService.updateAuthority(authority, currentInstitutionId);

		// Result
		return ResponseEntity.ok(MapStructConverter.MAPPER.toAuthorityDTO(result));
	}

	@DeleteMapping("/{authorityId}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_DELETE)")
	public void deleteAuthorityById(@PathVariable String authorityId, @RequestParam String currentInstitutionId) {
		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		validationService.validateDeleteAuthority(authorityId);
		authorityService.deleteByIdAndInstitutionId(authorityId, currentInstitutionId);
	}

}
