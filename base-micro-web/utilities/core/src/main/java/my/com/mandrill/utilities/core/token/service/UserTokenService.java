package my.com.mandrill.utilities.core.token.service;

import my.com.mandrill.utilities.core.dto.model.JwtTokenRequest;
import my.com.mandrill.utilities.core.token.domain.UserToken;

import java.util.List;
import java.util.Optional;

public interface UserTokenService {

	void create(JwtTokenRequest tokenRequest, long expiration);

	Optional<UserToken> findByTokenId(String tokenId);

	void deleteByTokenIds(List<String> tokenId);

	void deleteAllToken();

}
