package my.com.mandrill.component.controller.moneyxbizintegration.admin;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.dto.response.BizPointSummaryFilterResponse;
import my.com.mandrill.component.dto.response.BizPointSummaryResponse;
import my.com.mandrill.component.service.ClosingBalanceService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("moneyxbiz-integration/admin/point-summary")
public class MoneyXBizIntegrationAdminPointSummaryController {

	private final ClosingBalanceService closingBalanceService;

	@PublicAuth
	@SecurityRequirements
	@GetMapping("pagination/all")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public Page<BizPointSummaryResponse> getAllPointSummary(@RequestParam(required = false) Integer month,
			@RequestParam(required = false) Integer year, Pageable pageable) {
		return closingBalanceService.aggregateByMonthAndYearAndSource(month, year, SourceSystemEnum.MXBIZ, pageable)
				.map(MapStructConverter.MAPPER::toBizPointSummaryResponse);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("export")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public List<BizPointSummaryResponse> getAllPointSummaryForExport(@RequestParam(required = false) Integer month,
			@RequestParam(required = false) Integer year) {
		return closingBalanceService.aggregateByMonthAndYearReportExport(month, year, SourceSystemEnum.MXBIZ).stream()
				.map(MapStructConverter.MAPPER::toBizPointSummaryResponse).toList();
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("filter")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public BizPointSummaryFilterResponse getPointSummaryFilter() {
		return closingBalanceService.getAvailableTimeFilterForBiz();
	}

}
