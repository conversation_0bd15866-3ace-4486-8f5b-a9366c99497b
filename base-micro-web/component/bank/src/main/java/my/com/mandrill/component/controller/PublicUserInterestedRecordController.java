package my.com.mandrill.component.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.request.CreateUserInterestedRecordRequest;
import my.com.mandrill.component.dto.request.PublicUserInterestRedirectRequest;
import my.com.mandrill.component.service.GuestUserInterestedRecordIntgService;
import my.com.mandrill.component.service.UserInterestRecordIntegrationService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("v1/public/user-interested")
public class PublicUserInterestedRecordController {

	private final GuestUserInterestedRecordIntgService guestUserInterestedRecordService;

	private final AccountFeignClient accountFeignClient;

	private final UserInterestRecordIntegrationService userInterestRecordIntegrationService;

	@PublicAuth
	@SecurityRequirements
	@PostMapping
	public void store(@Valid @RequestBody CreateUserInterestedRecordRequest request) {
		guestUserInterestedRecordService.store(request);
	}

	@PublicAuth
	@SecurityRequirements
	@PostMapping("/redirect")
	public void storeRedirect(@Valid @RequestBody PublicUserInterestRedirectRequest publicUserInterestRedirectRequest)
			throws JsonProcessingException {
		UserPublicWebResponse userResponse = accountFeignClient
				.registerPublicUser(publicUserInterestRedirectRequest.getUser());

		userInterestRecordIntegrationService.insertRedirectPublic(publicUserInterestRedirectRequest, userResponse);
	}

}
