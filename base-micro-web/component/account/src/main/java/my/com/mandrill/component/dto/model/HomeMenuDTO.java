package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.HomeMenuStatusEnum;
import my.com.mandrill.component.dto.request.HomeMenuProductRequest;
import my.com.mandrill.component.dto.request.ObjectRequest;
import my.com.mandrill.component.dto.response.HomeMenuProductResponse;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HomeMenuDTO implements Serializable {

	private String id;

	@NotBlank
	private String name;

	@NotBlank
	private String code;

	private String actionGroup;

	private String description;

	@NotNull
	private HomeMenuStatusEnum state;

	private ObjectRequest parentMenu;

	private HomeMenuDTO parent;

	private Integer sequence;

	private List<HomeMenuDTO> child;

	private List<String> groupIds;

	private List<HomeMenuProductResponse> homeMenuProduct;

	private boolean active = false;

}
