<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.2.final using JasperReports Library version 6.21.2-8434a0bd7c3bbc37cbf916f2968d35e4b165821a  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="user-interested-report" pageWidth="1400" pageHeight="842" orientation="Landscape" columnWidth="1360" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isIgnorePagination="true" uuid="848e4275-486e-401a-a555-2b694d76437b">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="USER_INTERESTED" uuid="cec29f00-09ac-4ca0-8087-9bc4b96fbe46">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="createdDate" class="java.time.Instant"/>
		<field name="applicationDate" class="java.time.Instant"/>
		<field name="providerName" class="java.lang.String"/>
		<field name="productType" class="java.lang.String"/>
		<field name="productName" class="java.lang.String"/>
		<field name="fullName" class="java.lang.String"/>
		<field name="phoneNumber" class="java.lang.String"/>
		<field name="email" class="java.lang.String"/>
		<field name="refNo" class="java.lang.String"/>
		<field name="productCategory" class="java.lang.String"/>
		<field name="nric" class="java.lang.String"/>
		<field name="balanceTransfer" class="java.lang.String"/>
		<field name="source" class="java.lang.String"/>
		<field name="rsmStatus" class="java.lang.Object"/>
		<field name="applicationTypeName" class="java.lang.String"/>
		<field name="rsmRelation" class="java.lang.Object"/>
		<field name="rsmCommissionAttached" class="java.lang.Boolean"/>
		<field name="rsmEligible" class="java.lang.Boolean"/>
		<field name="userRefNo" class="java.lang.String"/>
		<field name="incomeAmount" class="java.math.BigDecimal"/>
		<field name="appointmentDate" class="java.time.LocalDate"/>
		<field name="showroomAddress" class="java.lang.String"/>
		<field name="projectAddress" class="java.lang.String"/>
		<field name="unitName" class="java.lang.String"/>
		<field name="cashbackAmount" class="java.math.BigDecimal"/>
		<field name="remarks" class="java.lang.String"/>
	</subDataset>
	<parameter name="USER_INTERESTED" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="226" splitType="Stretch">
			<componentElement>
				<reportElement x="-20" y="0" width="1900" height="202" uuid="1fb8f16e-6873-42e7-8bac-f1786da7630a">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
					<datasetRun subDataset="USER_INTERESTED" uuid="9fd8c3ab-00d8-4003-bd2c-d401772e4f5b">
						<dataSourceExpression><![CDATA[$P{USER_INTERESTED}]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="104" uuid="777f86fb-95be-4574-a59f-d7b13a7f203b">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="104" height="38" uuid="9170df5a-f4f0-478b-bfca-9270dd3e5166"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Application ID]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="104" height="38" uuid="dbbcd672-046d-45f3-a667-4fcfe5101e47"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{refNo}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="104" uuid="d2ae9724-3de1-4579-ab2b-df782c0d6d0c">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="104" height="38"
											   uuid="264e284f-ad05-4118-be9a-22749d161594"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Created Date]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="104" height="38"
											   uuid="6630d6e1-e72d-4328-af95-707909d71dac"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression>
									<![CDATA[my.com.mandrill.utilities.general.util.DateUtil.instantToDateDash($F{createdDate})]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="103" uuid="470fa9af-bc08-4b96-aa28-a28094d41fa9">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="103" height="38" uuid="2f9a9057-a2b1-413e-97d5-80412fbaea57"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Application Date]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="103" height="38" uuid="2d77b344-8376-4071-8a63-abab7d7d8418"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression>
									<![CDATA[my.com.mandrill.utilities.general.util.DateUtil.instantToDateDash($F{applicationDate})]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="103" uuid="1556cdc0-22ee-4cd7-9848-f68df9fb7656">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="103" height="38" uuid="52315bc8-35f1-4e55-b40e-06d5ddb2863c"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Application Time]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="103" height="38" uuid="57af87c7-dca7-4f0d-8d70-9079c6138215"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression>
									<![CDATA[my.com.mandrill.utilities.general.util.DateUtil.instantToTimeSemiColon($F{applicationDate})]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="99" uuid="3ce396a5-6d2e-4580-b43f-f6281c4e5c21">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column14"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="99" height="38"
											   uuid="2a019062-a7b1-4c99-9ccb-428f059a3c41"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Source]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="99" height="38"
											   uuid="b97ced4f-5160-4c82-9a2a-26912cced1e8"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{source}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="99" uuid="010524bb-2a74-4c86-a599-43cf608b57d8">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column14"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="99" height="38"
											   uuid="491dee58-da3e-4d80-84e5-2f728e31c147"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Application Status]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="99" height="38"
											   uuid="eaa8ab2b-b6c6-4435-8026-6005bbd9ac5a"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String">
									<![CDATA[""+$F{rsmStatus}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="99" uuid="53af9c8e-9f11-4480-890d-989726863c2c">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column14"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="99" height="38"
											   uuid="d65433bb-10c2-471d-ada8-c95e2fd2415b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Application Type]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="99" height="38"
											   uuid="81faecbd-b0c1-422b-a5ae-afa91a3062f3"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{applicationTypeName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="99" uuid="1bd68705-618e-45c4-a8a0-bbef2d7551e4">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column14"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="99" height="38"
											   uuid="99259f2e-15a6-443c-a2c1-df026c34967b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[RSM Scenario]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="99" height="38"
											   uuid="4f4ac7ee-bdf9-41cd-96fb-3afb40b03ae8"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression class="java.lang.String">
									<![CDATA[""+$F{rsmRelation}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="99" uuid="92dfd5b8-4ce4-4329-bb55-eb79e58c1fba">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column14"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="99" height="38"
											   uuid="30a79861-f0e6-40ab-9365-1a2e405358cb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Has RSM Commission Attached]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="99" height="38"
											   uuid="c71a9276-db18-4802-ab0c-9d4b4764c889"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression>
									<![CDATA[$F{rsmCommissionAttached} == null ? "NA" : $F{rsmCommissionAttached}.equals(Boolean.TRUE) ? "Yes" : "No"]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="99" uuid="8ffe77e1-8377-4fc6-acb4-b07263841e3e">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column14"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="99" height="38"
											   uuid="981cc3c3-3a01-4675-9bb3-9e97c2d8eb0a"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[RSM Eligible]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="99" height="38"
											   uuid="df66f90d-ef50-48f2-8615-4419dfab6272"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression>
									<![CDATA[$F{rsmEligible} == null ? "NA" : $F{rsmEligible}.equals(Boolean.TRUE) ? "Yes" : "No"]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="104" uuid="0443b3eb-bb82-4e1a-82c6-850d79fe9170">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column11"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="104" height="38"
											   uuid="43f8e22a-9e2e-47e1-9c9f-5c1d0e5913aa">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Product Category]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="104" height="38"
											   uuid="723084c0-4d3b-4bbf-978e-ba00c3f535b2"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{productCategory}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="104" uuid="53ed5673-8f67-4ab7-9421-13e51725b168">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column9"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="104" height="38"
											   uuid="07c235c9-c176-4981-9338-b556b515278c"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Product Type]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="104" height="38"
											   uuid="30eddfad-9e9a-44d7-b6fd-c4307f5f52b0"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{productType}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="103" uuid="08df46c8-5b16-4dea-8f08-21748e5d784a">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column12"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="103" height="38"
											   uuid="c1bfbe01-597c-400d-82cd-ddc5548c2b61"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Provider]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="103" height="38"
											   uuid="d064dac7-f923-46c6-9fd1-80e00a15b208"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{providerName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="103" uuid="62da8dea-9316-4e83-baf4-20467b8b1aef">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column10"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="103" height="38"
											   uuid="d2e47b11-8c8e-49ad-8f01-43bfa3d1b81a">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Product Name]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="103" height="38"
											   uuid="5b427c27-439b-4ecd-a3ae-9262be28757d"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{productName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="103" uuid="172bba6a-09f7-4a9f-8848-e56339f93dd1">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column13"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="103" height="38"
											   uuid="bb7d28c2-7380-4ad2-85dc-c602a65e286b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Balance Transfer]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="103" height="38"
											   uuid="4ec34a37-81ae-4793-b42a-d2ffc2c18309"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{balanceTransfer}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="130" uuid="65bd176e-376a-4eb9-a9a0-84dea495ff75">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column15"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="130" height="38"
											   uuid="dce8875f-04ef-4e16-ac8b-9eedd6698c2b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[User Reference ID]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="130" height="38"
											   uuid="c4a91260-53f8-4df8-b0f7-51206bb9f017"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{userRefNo}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="104" uuid="f8af55e0-8f1e-4165-bc88-4318d2d34ee8">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="104" height="38" uuid="e9737792-641d-4e20-8d35-d697af076d42"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Full Name]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="104" height="38" uuid="6d989a54-30c1-425a-94b4-168f775226f7"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{fullName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="103" uuid="08c5aff1-23bd-4c9d-947b-dd2de4197095">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="103" height="38"
											   uuid="8cb275aa-ec4a-4a6c-bc41-0668be81aacd">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Phone Number]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="103" height="38"
											   uuid="9b475c93-1ebc-4b77-97ab-7db9f207dffb"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{phoneNumber}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="103" uuid="880ea598-f85e-4bad-826e-2303137fd826">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="103" height="38" uuid="8afea4b7-3377-40f4-ae5f-8d985694fa74">
									<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Email]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="103" height="38" uuid="4ff8767b-6d9d-4f07-89a4-32aacebe1dfe"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{email}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="104" uuid="f86cdc10-b8b2-43ca-9b19-6af055cf0145">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column7"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="104" height="38" uuid="fdf202c5-1816-4ed1-9bdc-9568c146b53d"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[IC Number]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="104" height="38" uuid="40bd9042-c66c-4962-8b97-41fbdda207f0"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{nric}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="f6494028-07cb-4768-bbc5-d8bcaadea364">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column8"/>
						<property name="com.jaspersoft.studio.unit.width" value="px"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<property name="com.jaspersoft.studio.unit.width" value="px"/>
							<staticText>
								<reportElement x="0" y="0" width="120" height="38" uuid="cabd2cb4-475d-4c5d-850b-4f36b73fca79"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Income Amount]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38" rowSpan="1">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="120" height="38" uuid="45ad583e-bd09-49c3-8663-9c2e1acabb97"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{incomeAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="150" uuid="5d697b40-a968-45d7-b8c7-1acb7918bb04">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column16"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="140" height="38" uuid="b2804fbb-cb0c-4a54-86a5-dd817d6cd0dc"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Showroom Visitation Date]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="140" height="38" uuid="c4a91260-53f8-4df8-b0f7-51206bb9f017"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{appointmentDate}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="140" uuid="681c5528-752a-4f31-ae67-0491c5b6c26c">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column17"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="140" height="38" uuid="d467f030-fa48-48d8-acb0-b32b266d1bc2"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Showroom Address]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="140" height="38" uuid="07cf046c-3125-4430-bf36-c8ff2aa2f136"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{showroomAddress}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="150" uuid="69cb6103-a884-43ac-9f28-0839148a3e7d">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column18"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="140" height="38" uuid="35737f71-2234-4a05-8030-d60dbb3d0118"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Project Address]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="140" height="38" uuid="1e279311-2200-4a90-8913-160fbb0e483b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{projectAddress}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="7e7b65d3-3e60-4eeb-88b8-22a14402e9c8">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column19"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="38" uuid="89665fc1-0e0a-4293-bbd3-c2f60f31e6af"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Unit]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="120" height="38" uuid="c59e61ac-4080-43fb-96f8-becc66da7fbc"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{unitName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="64a5a0fa-1ebd-4e86-a408-f5fbe0b9bb02">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column20"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="38" uuid="0f2c6c08-06fc-4fb7-83d0-5646e1a33365"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Cashback Amount]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="120" height="38" uuid="998e9e33-2ed1-402a-add5-36957967bd70"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{cashbackAmount}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="c4357320-19f6-431b-8650-f27b9caf1074">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column20"/>
						<jr:columnHeader style="Table_CH" height="38" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="38"
											   uuid="011baa9b-07de-4d2b-b8bd-1290271f2592"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Remark]]></text>
							</staticText>
						</jr:columnHeader>
						<jr:detailCell style="Table_TD" height="38">
							<textField textAdjust="StretchHeight" isBlankWhenNull="true">
								<reportElement stretchType="ContainerHeight" x="0" y="0" width="120" height="38"
											   uuid="44b479cf-bdf4-48e9-b3ea-57fdc0e6e235"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="false"/>
								</textElement>
								<textFieldExpression><![CDATA[$F{remarks}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</detail>
</jasperReport>
