package my.com.mandrill.component.service.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.WeeklyUserTarget;
import my.com.mandrill.component.dto.model.WeeklyUserTargetDTO;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.WeeklyUserTargetRepository;
import my.com.mandrill.component.service.WeeklyUserTargetService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Year;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class WeeklyUserTargetServiceImpl implements WeeklyUserTargetService {

	private final WeeklyUserTargetRepository weeklyUserTargetRepository;

	@Override
	public boolean existsByYearAndWeekNumber(Year year, int weekNumber) {
		return weeklyUserTargetRepository.existsByYearAndWeekNumber(year, weekNumber);
	}

	@Override
	public Page<WeeklyUserTargetDTO> findAllPageable(Pageable pageable, Integer weekNumber) {
		return weeklyUserTargetRepository.findAllPageable(pageable, weekNumber)
				.map(MapStructConverter.MAPPER::toWeeklyUserTargetDTO);
	}

	@Override
	public WeeklyUserTargetDTO findById(String id) {
		WeeklyUserTarget weeklyUserTarget = weeklyUserTargetRepository.findById(id)
				.orElseThrow(ExceptionPredicate.weeklyUserTargetNotFound(id));
		return MapStructConverter.MAPPER.toWeeklyUserTargetDTO(weeklyUserTarget);
	}

	@Override
	public Long findByYearAndWeekNumber(Year year, int weekNumber) {
		Optional<WeeklyUserTarget> weeklyUserTarget = weeklyUserTargetRepository.findByYearAndWeekNumber(year,
				weekNumber);

		if (weeklyUserTarget.isPresent()) {
			return weeklyUserTarget.get().getTarget();
		}
		else {
			return null;
		}
	}

	@Override
	@Transactional
	public WeeklyUserTarget save(WeeklyUserTarget weeklyUserTarget) {
		return weeklyUserTargetRepository.save(weeklyUserTarget);
	}

	@Override
	public WeeklyUserTarget update(String id, WeeklyUserTarget weeklyUserTarget) {
		WeeklyUserTarget currentRecord = weeklyUserTargetRepository.findById(id)
				.orElseThrow(ExceptionPredicate.weeklyUserTargetNotFound(id));

		Year newYear = weeklyUserTarget.getYear();
		int newWeekNumber = weeklyUserTarget.getWeekNumber();

		if (!currentRecord.getYear().equals(newYear) || currentRecord.getWeekNumber() != newWeekNumber) {
			if (weeklyUserTargetRepository.existsByYearAndWeekNumber(newYear, newWeekNumber)) {
				throw new BusinessException(ErrorCodeEnum.WEEKLY_USER_TARGET_EXISTS);
			}
		}
		currentRecord.setYear(newYear);
		currentRecord.setWeekNumber(newWeekNumber);
		currentRecord.setTarget(weeklyUserTarget.getTarget());
		currentRecord.setDescription(weeklyUserTarget.getDescription());

		return currentRecord;

	}

	@Override
	@Transactional
	public void deleteById(String id) {
		weeklyUserTargetRepository.deleteById(id);
	}

}
