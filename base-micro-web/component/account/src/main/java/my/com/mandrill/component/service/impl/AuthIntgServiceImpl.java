package my.com.mandrill.component.service.impl;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.Authority;
import my.com.mandrill.component.domain.Permission;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.AuthenticateResultDTO;
import my.com.mandrill.component.dto.request.AuthServiceRequest;
import my.com.mandrill.component.dto.request.AuthenticateUserRequest;
import my.com.mandrill.component.dto.response.TokenServiceResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.core.dto.model.JwtTokenRequest;
import my.com.mandrill.utilities.core.security.LoginDetails;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.service.UserTokenService;
import my.com.mandrill.utilities.feign.dto.AuthenticateDTO;
import my.com.mandrill.utilities.general.constant.ClientPlatformType;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.TokenStatus;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.HttpRequestUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.ValidationUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthIntgServiceImpl implements AuthIntgService {

	private final AuthenticationManager authenticationManager;

	private final AuthService authService;

	private final ValidationService validationService;

	private final BaseProperties baseProperties;

	private final DeviceBindingService deviceBindingService;

	private final TokenService tokenService;

	private final TokenProvider tokenProvider;

	private final HttpRequestUtil httpRequestUtil;

	private final UserTokenService userTokenService;

	private final JSONUtil jsonUtil;

	@Transactional(rollbackFor = Exception.class)
	@Override
	public AuthenticateResultDTO authenticateUser(AuthServiceRequest request) {
		return authenticateUserV2(request);
	}

	@Override
	public AuthenticateDTO authenticatePassword(AuthenticateUserRequest request) {
		return authService.validatePassword(request);
	}

	@Transactional
	@Override
	public AuthenticateResultDTO createSignatureToken(AppUser user, HttpServletRequest request) {
		User tempUser = jsonUtil.convertValue(user, User.class);
		TokenServiceResponse token = tokenService.createToken(AuthServiceRequest.builder().rememberMe(true)
				.username(user.getUsername()).httpDetailDTO(httpRequestUtil.parseResource(request)).build(), tempUser);
		Set<SimpleGrantedAuthority> userAuth = user.getAuthorities().stream()
				.filter(authority -> authority.getInstitution().getActive() && authority.getActive())
				.map(Authority::getPermissions).filter(Objects::nonNull).flatMap(Collection::stream)
				.map(Permission::getCode).map(SimpleGrantedAuthority::new).collect(Collectors.toSet());

		String accessToken = tokenProvider.createDefaultTokenV2(
				JwtTokenRequest.builder().accessToken(token.getAccessToken()).userRefNo(token.getUserRefNo())
						.userId(token.getUserId()).grantedAuthorities(userAuth).build());
		return AuthenticateResultDTO.builder().userId(user.getId()).accessToken(accessToken)
				.refreshToken(token.getRefreshToken()).build();
	}

	private AuthenticateResultDTO authenticateUserV2(AuthServiceRequest request) {
		this.validateRequest(request);
		Authentication authentication = this.authenticateUserCredentials(request);

		AppUser appUser = authService.processUserData(request.getAccessType().getCode(), request.getLoginType(),
				request.getUsername());
		User user = MapStructConverter.MAPPER.toUser(appUser);
		validateBindingDevice(request, user);

		TokenServiceResponse token = tokenService.createToken(request, user);
		String accessToken = tokenProvider.createDefaultTokenV2(createJwtTokenRequest(authentication, token));

		SecurityContextHolder.getContext().setAuthentication(authentication);
		return AuthenticateResultDTO.builder().refreshToken(token.getRefreshToken()).accessToken(accessToken)
				.userId(user.getId()).build();

	}

	private void validateRequest(AuthServiceRequest request) {
		if (StringUtils.isBlank(request.getUsername()) && StringUtils.isBlank(request.getPassword())) {
			throw new BusinessException(ErrorCodeEnum.USERNAME_AND_PASSWORD_MUST_BE_FILLED);
		}

		if (StringUtils.isBlank(request.getUsername())) {
			throw new BusinessException(ErrorCodeEnum.USERNAME_MUST_BE_FILLED);
		}

		if (StringUtils.isBlank(request.getPassword())) {
			throw new BusinessException(ErrorCodeEnum.PASSWORD_MUST_BE_FILLED);
		}
	}

	private JwtTokenRequest createJwtTokenRequest(Authentication authentication, TokenServiceResponse token) {
		return JwtTokenRequest.builder().accessToken(token.getAccessToken()).userRefNo(token.getUserRefNo())
				.userId(token.getUserId())
				.grantedAuthorities(authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority)
						.map(SimpleGrantedAuthority::new).collect(Collectors.toSet()))
				.build();
	}

	private Authentication authenticateUserCredentials(AuthServiceRequest request) {
		UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(
				request.getUsername(), request.getPassword());
		usernamePasswordAuthenticationToken.setDetails(
				new LoginDetails(request.getAccessType(), request.getLoginType(), request.getPasscodeType()));

		return authenticationManager.authenticate(usernamePasswordAuthenticationToken);
	}

	private void validateBindingDevice(AuthServiceRequest request, User user) {
		if (LoginTypeEnum.USER.equals(request.getLoginType())) {
			boolean isDeviceBindingRequired = validationService.getDeviceBindingValidationFlag();
			if (!isDeviceBindingRequired) {
				log.info("Device binding is not required, skipping device binding for user");
				return;
			}

			if (ClientPlatformType.WEB.equals(request.getClientPlatform())
					&& ValidationUtil.isValidWebPlatformPattern(request.getDeviceId())) {
				log.info("Web detected, revoking previous sessions, device binding skipped");
				revokePreviousSessions(user, request.getClientPlatform());
				return;
			}
			if (isUsernameNotExcluded(request.getUsername())) {
				validationService.validateLoginDeviceBinding(user.getId(), request.getDeviceId());
				deviceBindingService.process(user.getId(), request.getDeviceId());
			}
		}
	}

	private boolean isUsernameNotExcluded(String username) {
		return !baseProperties.getDeviceBindingExclusion().contains(username.substring(1));
	}

	private void revokePreviousSessions(User user, ClientPlatformType platformType) {
		List<TokenServiceResponse> tokenServiceResponse = tokenService.revokedByUser(user, platformType,
				TokenStatus.REVOKED_BY_NEW_SESSION);
		if (CollectionUtils.isNotEmpty(tokenServiceResponse)) {
			tokenServiceResponse.forEach(
					tokenResponse -> userTokenService.deleteByTokenIds(List.of(tokenResponse.getAccessToken())));
		}
		log.info("Revoked {} sessions", tokenServiceResponse.size());
	}

}
