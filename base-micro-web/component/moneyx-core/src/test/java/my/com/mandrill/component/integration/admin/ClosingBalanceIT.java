package my.com.mandrill.component.integration.admin;

import lombok.SneakyThrows;
import my.com.mandrill.component.domain.RsmClosingBalance;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.PointTransactionRepository;
import my.com.mandrill.component.repository.RsmClosingBalanceRepository;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;

public class ClosingBalanceIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private RsmClosingBalanceRepository rsmClosingBalanceRepository;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@Autowired
	private JSONUtil jsonUtil;

	@AfterEach
	public void tearDown() {
		pointTransactionRepository.deleteAll();
		rsmClosingBalanceRepository.deleteAll();
	}

	@SneakyThrows
	@Test
	public void calculate_withoutPreviousClosingBalance_success() {
		mockMvc.perform(put("/admin/closing-balance/v1/calculate-by-date")
				.param("date", LocalDate.of(2025, 4, 10).toString())
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		RsmClosingBalance result = rsmClosingBalanceRepository.findAll().get(0);
		Assertions.assertNotNull(result);
		Assertions.assertEquals(BigDecimal.valueOf(2100).setScale(2, RoundingMode.UNNECESSARY),
				result.getPointAwarded());
		Assertions.assertEquals(BigDecimal.valueOf(0).setScale(2, RoundingMode.UNNECESSARY), result.getPointSpent());
		Assertions.assertEquals(BigDecimal.valueOf(2100).setScale(2, RoundingMode.UNNECESSARY),
				result.getClosingBalance());
	}

	@SneakyThrows
	@Test
	public void calculate_withPreviousClosingBalance_success() {
		mockMvc.perform(put("/admin/closing-balance/v1/calculate-by-date")
				.param("date", LocalDate.of(2025, 4, 10).toString())
				.header("Authorization", "Bearer " + getAdminSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		RsmClosingBalance result = rsmClosingBalanceRepository.findAll().get(1);
		Assertions.assertNotNull(result);
		Assertions.assertEquals(BigDecimal.valueOf(2100).setScale(2, RoundingMode.UNNECESSARY),
				result.getPointAwarded());
		Assertions.assertEquals(BigDecimal.valueOf(0).setScale(2, RoundingMode.UNNECESSARY), result.getPointSpent());
		Assertions.assertEquals(BigDecimal.valueOf(4100).setScale(2, RoundingMode.UNNECESSARY),
				result.getClosingBalance());
	}

}
