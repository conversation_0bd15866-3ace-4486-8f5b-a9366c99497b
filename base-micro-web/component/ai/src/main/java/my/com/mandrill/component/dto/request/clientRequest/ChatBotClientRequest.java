package my.com.mandrill.component.dto.request.clientRequest;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import my.com.mandrill.component.dto.model.ChatBotUserDataDTO;
import my.com.mandrill.utilities.general.constant.DeviceOS;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChatBotClientRequest {

	@JsonProperty("user_id")
	private String userId;

	@NotBlank
	@JsonProperty("user_query")
	private String userQuery;

	@JsonProperty("session_id")
	private Integer sessionId;

	@JsonProperty("device_os")
	private DeviceOS deviceOs;

	@JsonProperty("user_data")
	private ChatBotUserDataDTO userData;

}