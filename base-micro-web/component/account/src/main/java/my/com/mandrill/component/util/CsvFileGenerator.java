package my.com.mandrill.component.util;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import my.com.mandrill.component.dto.response.InternalUserMobileResponse;
import my.com.mandrill.utilities.general.constant.EkycStatus;
import my.com.mandrill.utilities.general.constant.PaymentAccountStatus;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Component
public class CsvFileGenerator {

	private static final List<String> MOBILE_USER_REPORT_COLUMNS = List.of("User Reference ID", "Full Name",
			"Email Address", "Mobile Number", "Register Date (MYT)", "Deleted Date", "Status", "Referral Code",
			"Payment Info Status", "Payment Info Approval Date", "E-KYC Verification Status");

	private static final String ACTIVE = "Active";

	private static final String INACTIVE = "Inactive";

	private static final String DASH = "-";

	public void writeUsersToCsv(List<InternalUserMobileResponse> users, HttpServletResponse response) {
		try (Workbook workbook = new XSSFWorkbook()) {
			Sheet sheet = workbook.createSheet("Mobile Users");
			CellStyle headerStyle = createHeaderStyle(workbook);

			// Buat header row
			Row headerRow = sheet.createRow(0);
			for (int i = 0; i < MOBILE_USER_REPORT_COLUMNS.size(); i++) {
				Cell cell = headerRow.createCell(i);
				cell.setCellValue(MOBILE_USER_REPORT_COLUMNS.get(i));
				cell.setCellStyle(headerStyle);
			}

			// Format tanggal
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
					.withZone(ZoneId.of("Asia/Kuala_Lumpur"));

			// Isi data user
			int rowNum = 1;
			CellStyle rowStyle = createRowStyle(workbook);
			for (InternalUserMobileResponse user : users) {
				Row row = sheet.createRow(rowNum++);

				Cell cell0 = row.createCell(0);
				cell0.setCellValue(user.getRefNo());
				cell0.setCellStyle(rowStyle); // Terapkan style ke sel

				Cell cell1 = row.createCell(1);
				cell1.setCellValue(user.getFullName());
				cell1.setCellStyle(rowStyle);

				Cell cell2 = row.createCell(2);
				cell2.setCellValue(user.getEmail());
				cell2.setCellStyle(rowStyle);

				Cell cell3 = row.createCell(3);
				cell3.setCellValue(convertToStripOnEmptyFields(user.getPhoneCountry() + user.getPhoneNumber()));
				cell3.setCellStyle(rowStyle);

				Cell cell4 = row.createCell(4);
				cell4.setCellValue(formatter.format(user.getCreatedDate()));
				cell4.setCellStyle(rowStyle);

				Cell cell5 = row.createCell(5);
				cell5.setCellValue(
						user.getDeletedDatetime() != null ? formatter.format(user.getDeletedDatetime()) : "-");
				cell5.setCellStyle(rowStyle);

				Cell cell6 = row.createCell(6);
				cell6.setCellValue(convertToActiveOrInactive(user.getActive()));
				cell6.setCellStyle(rowStyle);

				Cell cell7 = row.createCell(7);
				cell7.setCellValue(convertToStripOnEmptyFields(user.getReferralCode()));
				cell7.setCellStyle(rowStyle);

				Cell cell8 = row.createCell(8);
				cell8.setCellValue(mapPaymentInfoStatus(user.getPaymentInfoStatus()));
				cell8.setCellStyle(rowStyle);

				Cell cell9 = row.createCell(9);
				cell9.setCellValue(convertDateToStripOnEmptyFields(user.getPaymentInfoApprovedDate(), formatter));
				cell9.setCellStyle(rowStyle);

				Cell cell10 = row.createCell(10);
				cell10.setCellValue(mapEkycStatus(user.getEkycVerificationStatus()));
				cell10.setCellStyle(rowStyle);
			}

			for (int i = 0; i < MOBILE_USER_REPORT_COLUMNS.size(); i++) {
				sheet.autoSizeColumn(i);
			}

			try (ServletOutputStream out = response.getOutputStream()) {
				workbook.write(out);
			}

		}
		catch (IOException e) {
			throw new RuntimeException("Error while writing Excel file", e);
		}
	}

	private CellStyle createHeaderStyle(Workbook workbook) {
		CellStyle style = workbook.createCellStyle();
		style.setFillForegroundColor(IndexedColors.AQUA.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);

		XSSFFont font = ((XSSFWorkbook) workbook).createFont();
		font.setBold(true);
		style.setFont(font);

		return style;
	}

	private CellStyle createRowStyle(Workbook workbook) {
		CellStyle style = workbook.createCellStyle();
		style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setBorderTop(BorderStyle.THIN);
		style.setBorderBottom(BorderStyle.THIN);
		style.setBorderLeft(BorderStyle.THIN);
		style.setBorderRight(BorderStyle.THIN);

		XSSFFont font = ((XSSFWorkbook) workbook).createFont();
		style.setFont(font);

		return style;
	}

	private String convertDateToStripOnEmptyFields(Instant date, DateTimeFormatter formatter) {
		if (Objects.nonNull(date)) {
			return formatter.format(date);
		}
		return DASH;
	}

	private String convertToActiveOrInactive(Boolean status) {
		return Boolean.TRUE.equals(status) ? ACTIVE : INACTIVE;
	}

	private String convertToStripOnEmptyFields(Object object) {
		return Objects.isNull(object) ? DASH : object.toString();
	}

	private String mapPaymentInfoStatus(PaymentAccountStatus paymentAccountStatus) {
		if (Objects.nonNull(paymentAccountStatus)) {
			return paymentAccountStatus.getName();
		}

		return DASH;
	}

	private String mapEkycStatus(EkycStatus ekycStatus) {
		if (Objects.nonNull(ekycStatus)) {
			return ekycStatus.getName();
		}

		return DASH;
	}

}
