package my.com.mandrill.utilities.general.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Instant;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExceptionUtil {

	public static void sendStackTrace(KafkaTemplate<String, String> kafkaTemplate, Exception ex,
			HttpServletRequest request, String serviceName) {
		try {
			String uri = UriComponentsBuilder.fromHttpRequest(new ServletServerHttpRequest(request)).build()
					.toUriString();
			String stacktrace = ExceptionUtils.getStackTrace(ex);
			String log = "%s - %s - %s - %s :%n%s".formatted(Instant.now(), RequestUtil.getIpAddress(request),
					serviceName, uri, stacktrace);
			kafkaTemplate.send(KafkaTopic.EXCEPTION_STACKTRACE, serviceName, log);
		}
		catch (Exception e) {
			log.error("Error happening during stacktrace recording: {}", e.getMessage());
		}
	}

	public static void sendDltStackTrace(KafkaTemplate<String, String> kafkaTemplate, String message, String topicName,
			String serviceName) {
		try {
			String log = "%s - DLT Kafka - %s - %s :%n%s".formatted(Instant.now(), serviceName, topicName, message);
			kafkaTemplate.send(KafkaTopic.EXCEPTION_STACKTRACE, topicName, log);
		}
		catch (Exception e) {
			log.error("Error happening during dlt stacktrace recording: {}", e.getMessage());
		}
	}

}
