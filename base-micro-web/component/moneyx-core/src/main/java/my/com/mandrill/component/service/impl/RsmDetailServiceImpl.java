package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.domain.RsmDetail;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.repository.RsmDetailRepository;
import my.com.mandrill.component.service.RsmDetailService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RsmDetailServiceImpl implements RsmDetailService {

	private final RsmDetailRepository rsmDetailRepository;

	@Override
	public List<RsmDetail> findByHeader(List<String> ids) {
		return rsmDetailRepository.findByHeaderIdIn(ids);
	}

	@Override
	@Transactional
	public void saveAll(List<RsmDetail> detail) {
		rsmDetailRepository.saveAll(detail);
	}

	@Override
	public void deleteAllByHeader(RsmHeader header) {
		rsmDetailRepository.deleteAllByHeader(header);
	}

}
