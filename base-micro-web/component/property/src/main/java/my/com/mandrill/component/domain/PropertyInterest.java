package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import my.com.mandrill.crypto.converter.ConfidentialDataConverter;
import my.com.mandrill.utilities.converter.ProtectedDataConverterBigDecimal;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Builder
@FieldNameConstants
@Table(name = "property_interest")
public class PropertyInterest extends AuditSection implements Serializable {

	@NotBlank
	@Column(name = "application_id")
	private String applicationId;

	@Column(name = "appointment_date")
	private LocalDate appointmentDate;

	@NotBlank
	@Column(name = "name")
	private String name;

	@Convert(converter = ConfidentialDataConverter.class)
	@NotBlank
	@Column(name = "mobile_number")
	private String mobileNumber;

	@Convert(converter = ConfidentialDataConverter.class)
	@NotBlank
	@Column(name = "email")
	private String email;

	@Column(name = "nric")
	@Convert(converter = ConfidentialDataConverter.class)
	private String nric;

	@Convert(converter = ProtectedDataConverterBigDecimal.class)
	@Column(name = "income")
	private BigDecimal income;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(optional = false, fetch = FetchType.LAZY)
	@JoinColumn(name = "project_id", nullable = false)
	private Project project;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "project_unit_id")
	private ProjectUnit projectUnit;

	@NotBlank
	@Column(name = "user_id")
	private String userId;

	@NotBlank
	@Column(name = "user_ref_no")
	private String userRefNo;

	@Enumerated(EnumType.STRING)
	@Column(name = "rsm_relation")
	private RSMRelationType rsmRelation;

	@Enumerated(EnumType.STRING)
	@Column(name = "rsm_status")
	private RSMStatus rsmStatus;

	@Column(name = "rsm_eligible")
	private boolean rsmEligible;

	@Column(name = "rsm_commission_attached")
	private boolean rsmCommissionAttached;

	@Enumerated(EnumType.STRING)
	@Column(name = "source")
	private UserInterestedSource source;

	@Column(name = "source_id", length = 36)
	private String sourceId;

	@Column(name = "remarks")
	private String remarks;

	@Column(name = "application_date")
	private Instant applicationDate;

}
