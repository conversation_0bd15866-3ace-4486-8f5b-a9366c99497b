package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.ToString;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.service.PasscodeProviderService;

import java.util.Objects;

@Data
public class UpdatePinResetRequest implements PasscodeProviderService.ResetPasscodeAware {

	@NotBlank
	private String value;

	@NotBlank
	private String key;

	@NotBlank
	@Size(min = 6, max = 6)
	@ToString.Exclude
	private String pin;

	private UsernameType usernameType;

	@NotBlank
	private String username;

	@Override
	public String getPassword() {
		return this.pin;
	}

	public UsernameType getUsernameType() {
		if (Objects.isNull(usernameType)) {
			return UsernameType.PHONE_NUMBER;
		}
		return usernameType;
	}

}
