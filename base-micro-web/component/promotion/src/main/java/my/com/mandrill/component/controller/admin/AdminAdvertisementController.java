package my.com.mandrill.component.controller.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Advertisement;
import my.com.mandrill.component.domain.AdvertisementInfo;
import my.com.mandrill.component.dto.model.AdvertisementDTO;
import my.com.mandrill.component.dto.request.AdvertisementBannerRequest;
import my.com.mandrill.component.dto.request.AdvertisementRequest;
import my.com.mandrill.component.service.AdvertisementIntegrationService;
import my.com.mandrill.component.service.AdvertisementService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.dto.AdvertisementAttachmentGroupDTO;
import my.com.mandrill.utilities.general.constant.AdvertisementStatus;
import my.com.mandrill.utilities.general.constant.AdvertisementTemplate;
import my.com.mandrill.utilities.general.util.SpringSortComparator;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;

@Tag(name = "admin-advertisement-controller")
@Slf4j
@RestController
@RequestMapping("/admin/advertisements")
@RequiredArgsConstructor
public class AdminAdvertisementController {

	private final AdvertisementService advertisementService;

	private final AdvertisementIntegrationService advertisementIntegrationService;

	private final ObjectMapper objectMapper;

	private final ValidationService validationService;

	@PutMapping("/upload-banner/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.ADVERTISEMENT_UPDATE)")
	public ResponseEntity<AdvertisementDTO> uploadBanner(@PathVariable String id,
			@RequestBody @Valid AdvertisementBannerRequest advertisementBannerRequest) {
		AdvertisementAttachmentGroupDTO advertisementAttachmentGroupDTO = MapStructConverter.MAPPER
				.toAdvertisementAttachmentGroupDTO(advertisementBannerRequest);
		return ResponseEntity.ok(MapStructConverter.MAPPER
				.toAdvertisementDTO(advertisementIntegrationService.uploadBanner(id, advertisementAttachmentGroupDTO)));
	}

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.ADVERTISEMENT_CREATE)")
	public ResponseEntity<AdvertisementDTO> create(@RequestBody @Valid AdvertisementRequest advertisementRequest) {
		Advertisement advertisement = this.objectMapper.convertValue(advertisementRequest, Advertisement.class);

		this.validationService.validateAdvertisementRequest(advertisementRequest, advertisement, null);

		Advertisement response = this.advertisementIntegrationService.create(advertisement);
		response = this.advertisementIntegrationService.addAdditionalData(response, advertisementRequest);

		if (advertisementRequest.getAttachmentGroupDTO() != null) {
			response = advertisementIntegrationService.uploadPdf(response,
					advertisementRequest.getAttachmentGroupDTO());
		}

		if (advertisementRequest.getAdvertisementImageRequest() != null) {
			response = advertisementIntegrationService.uploadPopUpImage(response,
					advertisementRequest.getAdvertisementImageRequest());
		}

		AdvertisementDTO advertisementDTO = this.objectMapper.convertValue(this.advertisementService.save(response),
				AdvertisementDTO.class);

		if (AdvertisementStatus.LIVE.equals(advertisementDTO.getStatus())) {
			// invalidate cache when new confirmed advertisement created
			advertisementIntegrationService.invalidateAdvertisementCache();
		}

		return ResponseEntity.ok(advertisementDTO);
	}

	@GetMapping("/pagination/all")
	@PreAuthorize("hasAuthority(@authorityPermission.ADVERTISEMENT_READ)")
	public ResponseEntity<Page<AdvertisementDTO>> getAll(
			@SortDefault.SortDefaults({ @SortDefault(sort = "seq", direction = Sort.Direction.ASC),
					@SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) }) Pageable pageable,
			@RequestParam(required = false) String code, @RequestParam(required = false) String name) {
		Page<Advertisement> response = advertisementService.findAll(pageable, code, name);
		Page<AdvertisementDTO> result = response.map(advertisement -> {
			AdvertisementDTO advertisementDTO = objectMapper.convertValue(advertisement, AdvertisementDTO.class);
			advertisementDTO.setAdditionalData(advertisement.getAdvertisementAdditionalData().stream()
					.map(MapStructConverter.MAPPER::toAdvertisementAdditionalDataDTO).toList());
			return advertisementDTO;
		});
		return ResponseEntity.ok(result);
	}

	@GetMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.ADVERTISEMENT_READ)")
	public ResponseEntity<AdvertisementDTO> getById(@PathVariable String id) {
		Advertisement advertisement = advertisementService.findWithDetailsById(id);
		AdvertisementDTO result = this.objectMapper.convertValue(advertisement, AdvertisementDTO.class);
		result.setAdditionalData(advertisement.getAdvertisementAdditionalData().stream()
				.map(MapStructConverter.MAPPER::toAdvertisementAdditionalDataDTO).toList());
		return ResponseEntity.ok(result);
	}

	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.ADVERTISEMENT_UPDATE)")
	public void deleteById(@PathVariable String id) {
		Advertisement advertisement = advertisementService.findById(id);
		advertisementService.softDelete(advertisement.getId());
		advertisementIntegrationService.invalidateAdvertisementCache();
	}

	@PutMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.ADVERTISEMENT_UPDATE)")
	public ResponseEntity<AdvertisementDTO> update(@PathVariable String id,
			@RequestBody @Valid AdvertisementRequest advertisementRequest) {

		Advertisement advertisement = this.objectMapper.convertValue(advertisementRequest, Advertisement.class);

		this.validationService.validateAdvertisementRequest(advertisementRequest, advertisement, id);

		Advertisement updatedAdvertisement = this.advertisementIntegrationService.update(id, advertisement);
		updatedAdvertisement = this.advertisementIntegrationService.addAdditionalData(updatedAdvertisement,
				advertisementRequest);

		if (advertisementRequest.getAttachmentGroupDTO() != null) {
			updatedAdvertisement = advertisementIntegrationService.uploadPdf(updatedAdvertisement,
					advertisementRequest.getAttachmentGroupDTO());
		}

		if (advertisementRequest.getAdvertisementImageRequest() != null) {
			updatedAdvertisement = advertisementIntegrationService.uploadPopUpImage(updatedAdvertisement,
					advertisementRequest.getAdvertisementImageRequest());
		}

		AdvertisementDTO advertisementDTO = this.objectMapper
				.convertValue(this.advertisementService.save(updatedAdvertisement), AdvertisementDTO.class);

		// invalidate cache after all operations completed
		advertisementIntegrationService.invalidateAdvertisementCache();

		return ResponseEntity.ok(advertisementDTO);
	}

	@GetMapping("/id/all")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<String>> getAllAdvertisementId(
			@SortDefault.SortDefaults({ @SortDefault(sort = "seq", direction = Sort.Direction.ASC),
					@SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) }) Sort sort) {

		return ResponseEntity.ok(advertisementService.findAllId(sort));
	}

	@GetMapping("/templates")
	@PreAuthorize("hasAuthority(@authorityPermission.ADVERTISEMENT_READ)")
	public ResponseEntity<List<AdvertisementTemplate.AdvertisementTemplateDTO>> findAllTemplate(Sort sort) {
		List<AdvertisementTemplate.AdvertisementTemplateDTO> result = Stream.of(AdvertisementTemplate.values())
				.map(AdvertisementTemplate::getObject).toList();
		try {
			Comparator<AdvertisementTemplate.AdvertisementTemplateDTO> comparator = null;
			for (Sort.Order order : sort) {
				if (order.getProperty().equals("code")) {
					comparator = SpringSortComparator.compare(order, comparator,
							Comparator.comparing(AdvertisementTemplate.AdvertisementTemplateDTO::getCode));
				}
				else if (order.getProperty().equals("description")) {
					comparator = SpringSortComparator.compare(order, comparator,
							Comparator.comparing(AdvertisementTemplate.AdvertisementTemplateDTO::getDescription));
				}
			}
			if (comparator != null) {
				result = result.stream().sorted(comparator).toList();
			}
		}
		catch (Exception e) {
			result = Stream.of(AdvertisementTemplate.values()).map(AdvertisementTemplate::getObject).toList();
		}
		return ResponseEntity.ok(result);
	}

	@GetMapping("/active/all")
	@PreAuthorize("hasAuthority(@authorityPermission.ADVERTISEMENT_READ)")
	public ResponseEntity<List<AdvertisementInfo>> getAllActiveAndWithinRange(
			@RequestParam(required = false) String name) {
		return ResponseEntity.ok(advertisementService.findAllActiveAndWithinRange(name));
	}

}
