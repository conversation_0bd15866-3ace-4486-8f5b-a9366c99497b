package my.com.mandrill.component.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.util.TraceContextHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

@Aspect
@Slf4j
@Component
@RequiredArgsConstructor
public class AspectLoggingClient {

	private final ObjectMapper objectMapper;

	private final TraceContextHelper traceContextHelper;

	// pointcut for all methods with @RestController classes
	// All testing message will be remove once approved
	@Pointcut("execution(public * my.com.mandrill.component.client.*.*(..))")
	public void restControllerMethods() {
	}

	// Logging for controller entry time and exit time
	@Around("restControllerMethods()")
	public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
		String methodName = joinPoint.getSignature().getName();
		String requestId = traceContextHelper.getRequestId();
		String platform = traceContextHelper.getPlatformClient();

		MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
		String[] paramNames = methodSignature.getParameterNames();
		Object[] args = joinPoint.getArgs();

		Map<String, Object> paramMap = new LinkedHashMap<>();
		for (int i = 0; i < paramNames.length; i++) {
			Object arg = args[i];
			try {
				paramMap.put(paramNames[i], objectMapper.writeValueAsString(arg));
			}
			catch (Throwable e) {
				paramMap.put(paramNames[i], arg.toString());
			}
		}

		log.info("[Feign Client Req] requestId={} Platform={} | method={} | request={}", requestId, platform,
				methodName, paramMap);

		Object result = joinPoint.proceed();

		String responseBody;
		try {
			responseBody = objectMapper.writeValueAsString(result);
		}
		catch (Throwable e) {
			responseBody = result.toString();
		}

		log.info("[Feign Client Res] requestId={} platform={} | method={} | response={}", requestId, platform,
				methodName, responseBody);
		return result;
	}

}
