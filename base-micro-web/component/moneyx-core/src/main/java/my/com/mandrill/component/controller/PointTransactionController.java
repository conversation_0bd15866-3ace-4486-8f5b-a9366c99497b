package my.com.mandrill.component.controller;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.dto.response.RsmPointTransactionHistoryResponse;
import my.com.mandrill.component.service.PointTransactionIntgService;
import my.com.mandrill.utilities.general.dto.response.CursorPageResponse;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/point-transaction")
public class PointTransactionController {

	private final PointTransactionIntgService pointTransactionIntgService;

	@GetMapping(value = "/histories", produces = MediaType.APPLICATION_JSON_VALUE)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<CursorPageResponse<RsmPointTransactionHistoryResponse>> getTransaction(
			@RequestParam(required = false) String cursor, @RequestParam(defaultValue = "10") Integer limit) {
		List<RsmPointTransactionHistoryResponse> histories = pointTransactionIntgService
				.findTransactionHistory(SecurityUtil.currentUserId(), cursor, limit);
		Object nextCursor = CollectionUtils.isEmpty(histories) ? null : histories.get(histories.size() - 1).getId();
		return ResponseEntity.ok(CursorPageResponse.<RsmPointTransactionHistoryResponse>builder().content(histories)
				.nextCursor(nextCursor).build());
	}

}
