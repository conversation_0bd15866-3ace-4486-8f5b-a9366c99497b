package my.com.mandrill.component.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PointTransactionType {

	CASH_WITHDRAWAL("Withdrawal In Cash"), ADMIN_FEE_WITHDRAWAL("Admin Fee Withdrawal"),
	PRODUCT_REVENUE("Product Revenue"), MX_REFERRAL_EARNING("MX RSM Earnings"), REFERRAL_EARNING("Referral Earning"),
	REFERRAL_EARNING_BIZ("MX Biz RSM Earnings"), EXPENSE_TO_MX("Expense Points to MX"),
	EXPENSE_TO_MX_USER("Expense Points to MX User"), EXPENSE_TO_MX_BIZ("Expense Points to MX Biz"),
	EXPENSE_TO_BIZ_USER("Expense Points to MX Biz User"), ACCOUNT_DELETION_EARNING("Account Deletion Earning"),
	ACCOUNT_DELETION_DEDUCTION("Account Deletion Deduction"), POINT_EXPIRY("Point Expiry");

	private final String name;

}
