package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.PasswordTransaction;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.general.constant.DeliveryType;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

@RequiredArgsConstructor
@Component("passwordPasscodeProvider")
public class PasswordProviderService implements PasscodeProviderService {

	private final UserIntegrationService userIntegrationService;

	private final PasswordEncoder passwordEncoder;

	private final KeyRequestService keyRequestService;

	private final AccountService accountService;

	private final UserService userService;

	private final PasswordTransactionService passwordTransactionService;

	@Override
	public boolean match(AppUser user, String reqPassword) {
		userIntegrationService.failLoginCheck(user, true);

		if (Objects.isNull(user.getPassword()) || !passwordEncoder.matches(reqPassword, user.getPassword())) {
			userIntegrationService.failLoginCheck(user, false);
			throw new BusinessException(ErrorCodeGlobalEnum.INVALID_PASSWORD);
		}
		return true;
	}

	public UserKeyRequest reset(ResetPasscodeAware req, UsernameType usernameType) {
		String key = req.getKey() + "_" + req.getValue();
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(key);
		keyInput.setUsername(req.getUsername());
		accountService.validateUsernameType(keyInput, usernameType);

		Optional<UserKeyRequest> optionalKeyRequest = keyRequestService
				.findValidByUsernameAndType(keyInput.getUsernameRaw(), RequestKeyType.PASSWORD_RESET);
		if (optionalKeyRequest.isEmpty()) {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}

		UserKeyRequest keyRequest = optionalKeyRequest.get();
		accountService.validateOtpVerification(optionalKeyRequest.get(), keyInput.getKeyValue());

		User user = userService.findByUsernameInputIgnoreCaseAndLoginType(keyRequest.getUsername(), LoginTypeEnum.USER,
				null);

		passwordTransactionService.validatePassword(user, req.getPassword());

		String currentPassword = user.getPassword();
		user.setPassword(passwordEncoder.encode(req.getPassword()));

		if (StringUtils.isNotBlank(currentPassword)) {
			PasswordTransaction passwordTransaction = new PasswordTransaction();
			passwordTransaction.setPasswordHash(currentPassword);
			passwordTransaction.setUser(user);
			passwordTransactionService.save(passwordTransaction);
		}
		if (DeliveryType.EMAIL.equals(keyRequest.getDeliveryType())) {
			user.setEmailVerified(true);
		}
		user.setLoginFailAttempt(0);
		userIntegrationService.save(user);

		keyRequest.setCompletionDate(Instant.now());
		keyRequest.setStatus(RequestKeyStatus.COMPLETED);
		return keyRequestService.save(keyRequest);
	}

}
