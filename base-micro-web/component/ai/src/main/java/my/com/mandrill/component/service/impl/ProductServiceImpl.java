package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.client.ProductClient;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.ProductTypeEnum;
import my.com.mandrill.component.domain.ProductGroup;
import my.com.mandrill.component.domain.ProductPlatform;
import my.com.mandrill.component.domain.ProductType;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.request.clientRequest.ProductFilterRequest;
import my.com.mandrill.component.dto.request.clientRequest.ProductSuggestionQuestionnaireRequest;
import my.com.mandrill.component.dto.response.GroupAPIResponse;
import my.com.mandrill.component.dto.response.ProductGroupResponse;
import my.com.mandrill.component.dto.response.ProductTypeResponse;
import my.com.mandrill.component.dto.response.UserDataAIResponse;
import my.com.mandrill.component.dto.response.clientresponse.ProductPageResponse;
import my.com.mandrill.component.dto.response.clientresponse.ProductSuggestionQuestionnaireResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.exception.NotSupportedException;
import my.com.mandrill.component.repository.jpa.ProductGroupRepository;
import my.com.mandrill.component.repository.jpa.ProductPlatformRepository;
import my.com.mandrill.component.repository.jpa.ProductRepository;
import my.com.mandrill.component.repository.jpa.ProductTypeRepository;
import my.com.mandrill.component.service.ProductService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.component.util.ProductPageResponseUtil;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.LoanDTO;
import my.com.mandrill.utilities.feign.dto.UserDTO;
import my.com.mandrill.utilities.feign.dto.model.CurrentUserDataForAIDTO;
import my.com.mandrill.utilities.feign.dto.model.SurveyFormUserResultDTO;
import my.com.mandrill.utilities.feign.dto.request.CommissionInfoRequest;
import my.com.mandrill.utilities.feign.dto.request.LoanEligibilityLenderProductRequest;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.SurveyFormType;
import my.com.mandrill.utilities.general.constant.SystemConfigurationEnum;
import my.com.mandrill.utilities.general.dto.response.AiProductDetailResponse;
import my.com.mandrill.utilities.general.dto.response.AiProductResponse;
import my.com.mandrill.utilities.general.dto.response.CommissionInfoResponse;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {

	public static final String LOAN_LIMIT = "KNOW_YOUR_LOAN";

	private final ProductClient productListClient;

	private final CommonFeignClient commonFeignClient;

	private final ProxyFeignClient proxyFeignClient;

	private final ProductGroupRepository productGroupRepository;

	private final ProductPlatformRepository productPlatformRepository;

	private final ProductTypeRepository productTypeRepository;

	private final RedisService redisService;

	private final ValidationService validationService;

	private final ProductRepository productRepository;

	@Override
	public ResponseEntity<String> findProductList(String page, String size, String type) {
		String issuerCodes = findAllIssuerCodes();
		return productListClient.findProductList(page, size, type, issuerCodes);
	}

	@Override
	public ResponseEntity<String> findProductFilters(String type, String recommendationType) {
		return productListClient.findProductFilterList(type, recommendationType);
	}

	@Override
	public AiProductDetailResponse findProductDetails(String type, String productName) {
		AiProductDetailResponse detailResponse = productListClient.findProductDetailsListStruct(type, productName);

		Map<String, CommissionInfoResponse> commissionInfoMap = findCommissionInfoMap(
				Collections.singletonList(detailResponse.getProductId()));
		detailResponse.setCommission(commissionInfoMap.get(detailResponse.getProductId()));
		return detailResponse;
	}

	@Override
	public ResponseEntity<String> findProductCompare(String type, String productName1, String productName2) {
		return productListClient.findProductCompare(type, productName1, productName2);
	}

	@Override
	public ResponseEntity<String> findProductCategory(String group) {
		return productListClient.findProductCategory(group);
	}

	@Override
	public ResponseEntity<String> findProduct(String type, String entityName, String cardType, String productName,
			String productId) {
		try {
			return productListClient.findProduct(type, entityName, cardType, productName, productId);
		}
		catch (Exception e) {
			log.error("AI Integration failed error: {}", e.getMessage());
			throw new BusinessException(ErrorCodeEnum.AI_INTEGRATION_FAILED_ERROR);
		}
	}

	@Override
	public ResponseEntity<String> findProductFilterLoanLimit(String type) {
		return productListClient.findProductFilterList(type, LOAN_LIMIT);
	}

	@Override
	public ResponseEntity<String> findProductRefinance(String type, ProductLoanLimitRequest body, String page,
			String size) {
		ProductSuggestionDTO request = buildLoanLimitPayload(body);

		log.debug("Refinance Loan Limit Payload: {}", request);
		return productListClient.findRefinance(type, request, page, size);
	}

	@Override
	public ResponseEntity<String> findProductSuggestionLoanLimit(String type, SuggestionLoanLimitRequest body,
			String page, String size) {
		ProductSuggestionDTO request = buildLoanLimitPayload(body);
		request.setFilters(body.getFilters());

		log.debug("Suggestion Loan Limit Payload: {}", request);
		return productListClient.findSuggestions(request, page, size, type, "", LOAN_LIMIT);
	}

	private ProductSuggestionDTO buildLoanLimitPayload(ProductLoanLimitRequest body) {
		UserDTO userDTO = proxyFeignClient.getAccountFeignClient().getAccount();

		// User Data Preparation
		UserDataAIResponse data = new UserDataAIResponse();
		data.setUserId(userDTO.getId());
		data.setUserAge(userDTO.getAge());
		data.setUserAppliedLoanAmount(body.getAppliedLoanAmount());
		data.setLoan(proxyFeignClient.getBankFeignClient().findLoanAll(Sort.unsorted()));

		// Get User's Salary
		if (userDTO.getEmploymentType() != null) {
			try {
				data.setUserIncome(proxyFeignClient.getAccountFeignClient()
						.findIncomeSalary(userDTO.getEmploymentType().getId()).getMonthlyIncomeAmount());
			}
			catch (EntityNotFoundException e) {
				data.setUserIncome(null);
			}
		}
		else {
			data.setUserIncome(null);
		}

		// Request Payload
		ProductSuggestionDTO request = new ProductSuggestionDTO();
		request.setUserData(data);

		return request;
	}

	private String findAllIssuerCodes() {
		if (aiIssuerCodeControl()) {
			return "";
		}

		List<String> filteredList = new ArrayList<>();
		String bankIssuerCode = proxyFeignClient.getBankFeignClient().getIssuerCodeByIsPartnerTruePrivate();
		String insuranceIssuerCode = proxyFeignClient.getInsuranceFeignClient().getIssuerCodeByIsPartnerTruePrivate();
		if (!bankIssuerCode.isBlank()) {
			filteredList.add(bankIssuerCode);
		}
		if (!insuranceIssuerCode.isBlank()) {
			filteredList.add(insuranceIssuerCode);
		}
		return filteredList.stream().map(Object::toString).collect(Collectors.joining(","));
	}

	private boolean aiIssuerCodeControl() {
		return !Boolean.parseBoolean(commonFeignClient.getSystemConfigurationByCodeAndInstitutionIdPrivate(
				SystemConfigurationEnum.AI_ISSUER_CODE_CONTROL.getCode(),
				SystemConfigurationEnum.DEFAULT_USER_INSTITUTION_ID.getValue()).getValue());
	}

	@Override
	public List<ProductGroupDTO> findAllProductGroups(Sort sort) {
		return productGroupRepository.findActiveProductGroupsDistinct(sort);
	}

	@Override
	public List<ProductPlatform> findAllProductPlatform(Sort sort) {
		return productPlatformRepository.findAll(sort);
	}

	@Override
	public List<ProductType> findAllProductTypeByPlatformAndGroup(String productPlatformCode, String productGroupCode,
			Boolean rsmActive, Sort sort) {
		String cacheKey = CacheKey.PRODUCT_TYPE_CACHE.formatted(productPlatformCode, productGroupCode, rsmActive)
				+ sort.hashCode();
		Optional<List<ProductType>> dataInCache = redisService.getFromValue(cacheKey, new TypeReference<>() {
		});
		if (dataInCache.isPresent()) {
			log.info("product type from AI serve by cache: {}", cacheKey);
			return dataInCache.get();
		}
		validationService.validateGetAllProductTypes(productPlatformCode, productGroupCode);

		List<ProductType> productTypes;
		if (Boolean.TRUE.equals(rsmActive)) {
			// this is to get the product types which entitled for rsm commission
			List<String> rsmActiveProductTypeIds = proxyFeignClient.getMoneyXCoreFeignClient()
					.getRsmActiveProductTypes();
			productTypes = productTypeRepository.findAllByProductPlatformCodeAndActiveTrueAndIdIn(productPlatformCode,
					rsmActiveProductTypeIds, sort);
		}
		else if (StringUtils.isBlank(productGroupCode)) {
			productTypes = productTypeRepository.findAllByProductPlatformCodeAndActiveTrue(productPlatformCode, sort);
		}
		else {
			productTypes = productTypeRepository.findAllByProductPlatformCodeAndProductGroupCodeAndActiveTrue(
					productPlatformCode, productGroupCode, sort);
		}
		redisService.putToValue(cacheKey, productTypes, Duration.ofMinutes(1));
		return productTypes;
	}

	@Override
	public ProductPageResponse<List<AiProductResponse>> findProductSuggestion(String page, String size, String type,
			ProductSuggestionRequest body) {
		if (ProductTypeEnum.contains(type)) {
			return findFromMx(page, size, type);
		}
		return findFromAi(page, size, type, body);
	}

	private ProductPageResponse<List<AiProductResponse>> findFromMx(String page, String size, String type) {
		Page<AiProductResponse> products = productRepository
				.findByType(type, PageRequest.of(Integer.parseInt(page) - 1, Integer.parseInt(size)))
				.map(MapStructConverter.MAPPER::toResponse);
		return ProductPageResponseUtil.fromSpringPage(products);
	}

	private ProductPageResponse<List<AiProductResponse>> findFromAi(String page, String size, String type,
			ProductSuggestionRequest body) {
		ProductSuggestionDTO result = new ProductSuggestionDTO();
		result.setFilters(body.getFilters());
		result.setUserData(buildUserData());

		return findFromAi(result, page, size, type);
	}

	private ProductPageResponse<List<AiProductResponse>> findFromAi(ProductSuggestionDTO request, String page,
			String size, String type) {
		String issuerCodes = findAllIssuerCodes();
		ProductPageResponse<List<AiProductResponse>> aiResult = productListClient.findSuggestionsStruct(request, page,
				size, type, issuerCodes, null);
		List<AiProductResponse> aiList = aiResult.getItems();
		Map<String, CommissionInfoResponse> commissionInfoMap = findCommissionInfoMap(
				aiList.stream().map(AiProductResponse::getProductId).toList());

		aiResult.setItems(aiList.stream().map(pr -> {
			AiProductResponse aiProductResponse = MapStructConverter.MAPPER.toAiProductResponse(pr);
			aiProductResponse.setCommission(commissionInfoMap.get(pr.getProductId()));
			return aiProductResponse;
		}).toList());
		return aiResult;
	}

	private Map<String, CommissionInfoResponse> findCommissionInfoMap(List<String> productIds) {
		List<CommissionInfoResponse> commissionInfo = proxyFeignClient.getMoneyXCoreFeignClient()
				.findCommissionInfo(CommissionInfoRequest.builder().productId(productIds).build());
		return commissionInfo.stream()
				.collect(Collectors.toMap(CommissionInfoResponse::getProductId, Function.identity()));
	}

	@Override
	public ProductPageResponse<List<AiProductResponse>> findProductSuggestionNonLogin(String page, String size,
			String type, ProductSuggestionRequest body) {
		ProductSuggestionDTO result = new ProductSuggestionDTO();
		result.setFilters(body.getFilters());
		return findFromAi(result, page, size, type);
	}

	@Override
	public ResponseEntity<String> findProductForYou(String size, ProductForYouRequest productForYouRequest) {
		ForYouDTO result = new ForYouDTO();
		result.setModule(productForYouRequest.getModule());
		result.setInterests(productForYouRequest.getInterests());
		result.setUser_data(buildUserData());
		result.setFilters(productForYouRequest.getFilters());

		String issuerCodes = findAllIssuerCodes();
		return productListClient.findProductForYou(result, size, issuerCodes);
	}

	@Override
	public ResponseEntity<String> findAllProductGroupsAI() {
		Optional<AICachedResponse> dataInCache = redisService.getFromValue(CacheKey.PRODUCT_GROUPS_CACHE,
				new TypeReference<>() {
				});
		if (dataInCache.isPresent()) {
			log.info("product group from AI serve by cache: {}", CacheKey.PRODUCT_GROUPS_CACHE);
			return ResponseEntity.status(dataInCache.get().getStatusCode()).contentType(MediaType.APPLICATION_JSON)
					.body(dataInCache.get().getBody());
		}

		ResponseEntity<String> response = productListClient.findProductGroups();
		AICachedResponse aiCachedResponse = new AICachedResponse(response.getBody(), response.getStatusCode().value());
		redisService.putToValue(CacheKey.PRODUCT_GROUPS_CACHE, aiCachedResponse, Duration.ofDays(1));

		return response;
	}

	@Override
	public ResponseEntity<String> findLoanEligibilityPurchaseProducts(String type,
			LoanEligibilityLenderProductRequest body, String page, String size) {
		ProductSuggestionDTO request = buildLoanEligibilityData(body);

		return productListClient.findSuggestions(request, page, size, type, "", LOAN_LIMIT);
	}

	@Override
	public ResponseEntity<String> findLoanEligibilityRefinanceProducts(String type,
			LoanEligibilityLenderProductRequest body, String page, String size) {
		ProductSuggestionDTO request = buildLoanEligibilityData(body);

		return productListClient.findRefinance(type, request, page, size);
	}

	private ProductSuggestionDTO buildLoanEligibilityData(LoanEligibilityLenderProductRequest body) {
		CurrentUserIdDTO userDTO = proxyFeignClient.getAccountFeignClient().getCurrentUserId();

		UserDataAIResponse data = new UserDataAIResponse();
		data.setUserId(userDTO.getId());
		data.setUserAge(userDTO.getAge());
		data.setUserAppliedLoanAmount(body.getAppliedLoanAmount());

		// set loan eligibility incomes
		data.setUserIncome(body.getTotalIncomes());

		// set loan eligibility commitments
		data.setLoan(Optional.ofNullable(body.getCommitments())
				.map(commitments -> commitments.stream().filter(Objects::nonNull)
						.map(commitment -> LoanDTO.builder().monthlyInstallment(commitment).build()).toList())
				.orElse(Collections.emptyList()));

		// Request Payload
		ProductSuggestionDTO request = new ProductSuggestionDTO();
		request.setUserData(data);

		return request;
	}

	private UserDataAIResponse buildUserData() {
		CurrentUserDataForAIDTO userData = proxyFeignClient.getAccountFeignClient()
				.getCurrentUserDataForAiIntegration();

		return MapStructConverter.MAPPER.toUserDataAIResponse(userData);
	}

	@Override
	public boolean isActiveProductTypeForPlatform(String code, String platformCode) {
		Optional<ProductType> productType = productTypeRepository.findFirstByValueAndProductPlatformCode(code,
				platformCode);
		if (productType.isPresent()) {
			return productType.get().getActive();
		}
		log.warn("configuration product-type not found, return false as default");
		return false;
	}

	@Override
	public ResponseEntity<String> findProductSuggestionForRetirement(RetirementProductSuggestionRequest request,
			String page, String size) {
		request.setUserId(SecurityUtil.currentUserId());
		return productListClient.findProductSuggestionForRetirement(request, page, size);
	}

	@Override
	public List<ProductSuggestionMatchDTO> findProductSuggestionMatch(SurveyFormType type) {
		SurveyFormUserResultDTO result = proxyFeignClient.getAnalyticFeignClient().checkSurveyFormResult(type);
		ProductSuggestionQuestionnaireRequest request = MapStructConverter.MAPPER
				.toProductSuggestionQuestionnaireRequest(result);
		ProductSuggestionQuestionnaireResponse response = switch (type) {
			case PRODUCT_CC -> productListClient.findProductCCSuggestionFromQuestionnaire(request);
			case PRODUCT_CASA -> productListClient.findProductCASASuggestionFromQuestionnaire(request);
			default -> throw new NotSupportedException("Support only CC and CASA");
		};
		return response.getMatches().stream().map(MapStructConverter.MAPPER::toProductSuggestionMatchDTO).toList();
	}

	@Override
	public List<ProductType> findDistinctByProductGroupIdAndProductPlatformCodeAndActive(String productGroupId,
			String productPlatformCode, Boolean active, Sort sort) {
		return productTypeRepository.findDistinctByProductGroupIdAndProductPlatformCodeAndActive(productGroupId,
				productPlatformCode, active, sort);
	}

	@Override
	public List<ProductType> findDistinctByProductGroupIdsAndProductPlatformCodeAndActive(List<String> productGroupIds,
			String productPlatformCode, Boolean active, Sort sort) {
		return productTypeRepository.findDistinctByProductGroupIdInAndProductPlatformCodeAndActive(productGroupIds,
				productPlatformCode, active, sort);
	}

	@Override
	public ProductGroup findProductGroupById(String id) {
		return productGroupRepository.findById(id).orElseThrow(ExceptionPredicate.productGroupNotFoundById(id));
	}

	@Override
	public ProductType findProductTypeById(String id) {
		return productTypeRepository.findById(id).orElseThrow(ExceptionPredicate.productTypeNotFoundById(id));
	}

	@Override
	public List<ProductType> findActiveByGroupIdAndPlatform(Collection<String> productGroupId,
			String productPlatformCode) {
		return productTypeRepository.findDistinctByProductGroupIdInAndProductPlatformCodeAndActive(productGroupId,
				productPlatformCode, true, Sort.by("label"));
	}

	@Override
	public List<ProductType> findActiveProductTypeAndPlatform(Collection<String> productType,
			String productPlatformCode) {
		return productTypeRepository.findDistinctByValueInAndProductPlatformCodeAndActive(productType,
				productPlatformCode, true, Sort.by("label"));
	}

	@Override
	public List<ProductSimpleSelectionDTO> findProductSimpleSelection(String productType, String providerId,
			Boolean rsmActive) {
		List<ProductSimpleSelectionDTO> products;
		if (isProperty(productType)) {
			return findPropertySelections(productType, providerId, rsmActive);
		}
		else {
			products = new ArrayList<>(fetchMxProducts(providerId, productType));
			if (shouldQueryAi(products, providerId, productType)) {
				products.addAll(fetchAiProducts(providerId, productType));
			}
		}
		if (Boolean.TRUE.equals(rsmActive)) {
			products = filterOnlyRsmEligible(products);
		}
		return products;
	}

	private boolean isProperty(String productType) {
		return Objects.equals("property", productType);
	}

	private List<ProductSimpleSelectionDTO> findPropertySelections(String productType, String providerId,
			Boolean rsmActive) {

		if (!Boolean.TRUE.equals(rsmActive)) {
			return findPropertySimpleSelectionInstitution(providerId);
		}

		Set<String> rsmIds = proxyFeignClient.getMoneyXCoreFeignClient().getRsmActiveProducts(productType);

		return CollectionUtils.isEmpty(rsmIds) ? findPropertySimpleSelectionInstitution(providerId)
				: findPropertySimpleSelectionInstitution(rsmIds);
	}

	private List<ProductSimpleSelectionDTO> fetchMxProducts(String providerId, String productType) {
		List<String> providerIds = StringUtils.isNotBlank(providerId) ? List.of(providerId) : List.of();
		List<String> productTypes = StringUtils.isNotBlank(productType) ? List.of(productType) : List.of();
		return proxyFeignClient.getCommonFeignClient().findAffiliateProduct(List.of(), providerIds, productTypes)
				.stream().map(MapStructConverter.MAPPER::toProductSimpleSelectionDTO).toList();
	}

	private boolean shouldQueryAi(List<ProductSimpleSelectionDTO> beResult, String providerId, String productType) {
		if (checkIfAffiliateProductType(beResult, productType)) {
			return false;
		}

		if (StringUtils.isBlank(providerId)) {
			return true;
		}

		Set<String> mapping = proxyFeignClient.getAccountFeignClient().getInstitutionAiMapping(providerId);
		return !CollectionUtils.isEmpty(mapping);
	}

	private List<ProductSimpleSelectionDTO> fetchAiProducts(String providerId, String productType) {
		ProductSuggestionDTO req = new ProductSuggestionDTO();
		if (StringUtils.isNotBlank(providerId)) {
			Set<String> mapping = proxyFeignClient.getAccountFeignClient().getInstitutionAiMapping(providerId);
			req.setFilters(ProductFilterRequest.builder()
					.bank(ProductFilterRequest.Bank.builder().bank(new ArrayList<>(mapping)).build()).build());
		}
		ProductPageResponse<List<AiProductResponse>> resp = productListClient.findSuggestionsStruct(req, "1", "200",
				productType, "", null);
		return resp.getItems().stream().map(MapStructConverter.MAPPER::toProductSimpleSelectionDTO).toList();
	}

	private List<ProductSimpleSelectionDTO> filterOnlyRsmEligible(List<ProductSimpleSelectionDTO> products) {
		Set<String> eligible = proxyFeignClient.getMoneyXCoreFeignClient().getRsmActiveProducts();
		return products.stream().filter(v -> eligible.contains(v.getId())).toList();
	}

	private boolean checkIfAffiliateProductType(List<ProductSimpleSelectionDTO> affiliateProducts, String productType) {
		return affiliateProducts.stream()
				.anyMatch(affiliateProduct -> productType.equals(affiliateProduct.getProductType()));
	}

	private List<ProductSimpleSelectionDTO> findPropertySimpleSelectionInstitution(String developerId) {
		return proxyFeignClient.getPropertyFeignClient().findProjects(developerId).stream()
				.map(MapStructConverter.MAPPER::toProductSimpleSelectionDTO).toList();
	}

	private List<ProductSimpleSelectionDTO> findPropertySimpleSelectionInstitution(Set<String> projectId) {
		return proxyFeignClient.getPropertyFeignClient().findProjectsByIds(projectId).stream()
				.map(MapStructConverter.MAPPER::toProductSimpleSelectionDTO).toList();
	}

	@Override
	public List<ProductGroupDTO> findProductGroupByValueIn(List<String> values, Sort sort) {
		return productGroupRepository.findByValueIn(values, sort).stream()
				.map(MapStructConverter.MAPPER::toProductGroupDTO).toList();
	}

	@Override
	public List<ProductGroupDTO> findProductGroupByIdIn(List<String> ids, Sort sort) {
		return productGroupRepository.findByIdIn(ids, sort).stream().map(MapStructConverter.MAPPER::toProductGroupDTO)
				.toList();
	}

	@Override
	public List<ProductType> findProductTypeByIdIn(List<String> ids, Sort sort) {
		return productTypeRepository.findByIdIn(ids, sort);
	}

	@Override
	public List<ProductType> findByValueInAndProductPlatformCode(List<String> values, String platformCode, Sort sort) {
		return productTypeRepository.findByValueInAndProductPlatformCode(values, platformCode, sort);
	}

	@Override
	public GroupAPIResponse findAllProductGroupByPlatformCode(String platformCode, Object o, Boolean rsmActive,
			Sort sort) {

		Optional<GroupAPIResponse> dataInCache = redisService.getFromValue(CacheKey.PRODUCT_GROUP_V2_CACHE,
				new TypeReference<>() {
				});
		if (dataInCache.isPresent()) {
			log.info("product group v2 from cache: {}", CacheKey.PRODUCT_GROUP_V2_CACHE);
			return dataInCache.get();
		}

		List<ProductGroup> productGroups = productGroupRepository.findByPlatformCode(platformCode);
		List<ProductGroupResponse> productGroupResponses = productGroups.stream().map(this::mapToProductGroupResponse)
				.toList();
		Set<ProductGroup> allGroups = getAllProductGroupsWithSubMenus(new LinkedHashSet<>(productGroups));

		List<ProductTypeResponse> productTypeResponses = new ArrayList<>(
				allGroups.stream().flatMap(group -> group.getProductTypes().stream())
						.filter(productType -> productType.getProductPlatform().getCode().equals(platformCode)
								&& Objects.nonNull(productType.getSequenceV2()))
						.map(MapStructConverter.MAPPER::toProductTypeResponse).toList());

		productTypeResponses.sort(
				Comparator.comparing(ProductTypeResponse::getSequenceV2, Comparator.nullsLast(Integer::compareTo)));

		GroupAPIResponse response = GroupAPIResponse.builder().productGroups(productGroupResponses)
				.productTypes(productTypeResponses).build();
		redisService.putToValue(CacheKey.PRODUCT_GROUP_V2_CACHE, response, Duration.ofDays(1));
		return response;
	}

	private ProductGroupResponse mapToProductGroupResponse(ProductGroup group) {
		List<ProductGroupResponse> subMenus = group.getSubMenus().stream().map(this::mapToProductGroupResponse)
				.collect(Collectors.toList());
		return new ProductGroupResponse(String.valueOf(group.getId()), group.getValue(), group.getCode(), subMenus);
	}

	private Set<ProductGroup> getAllProductGroupsWithSubMenus(Set<ProductGroup> groups) {
		Set<ProductGroup> all = new LinkedHashSet<>();
		for (ProductGroup group : groups) {
			all.add(group);
			if (group.getSubMenus() != null && !group.getSubMenus().isEmpty()) {
				all.addAll(getAllProductGroupsWithSubMenus(group.getSubMenus()));
			}
		}
		return all;
	}

}