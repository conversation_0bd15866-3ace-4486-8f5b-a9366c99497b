info:
  project:
    version: #project.version#

spring:
  application:
    name: common-component
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ************************************************************
    username: moneyx
    password: moneyx
    hikari:
      schema: common
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
      connection-timeout: 10000
      idle-timeout: 600000
  liquibase:
    default-schema: common
    change-log: classpath:liquibase/master.xml
  jackson:
    serialization:
      fail-on-empty-beans: false
  jpa:
    show-sql: false
    properties:
      hibernate:
        default_schema: common
        format_sql: false
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          lob:
            non_contextual_creation: true
    open-in-view: false
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ReactiveElasticsearchRepositoriesAutoConfiguration

  cache:
    redis:
      time-to-live: 1d
  data:
    redis:
      repositories:
        enabled: false
      timeout: 1000ms
      connect-timeout: 500ms
      host: localhost
      port: 6379
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

server:
  port: 8902

management:
  endpoints:
    web:
      exposure:
        include:
          - health
          - info
          - prometheus
  health:
    elasticsearch:
      enabled: false

##https://cloud.spring.io/spring-cloud-netflix/reference/html/appendix.html
eureka:
  client:
    enabled: true
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
  instance:
    preferIpAddress: true

##https://cloud.spring.io/spring-cloud-openfeign/reference/html/appendix.html
feign:
  client:
    config:
      default:
        loggerLevel: basic # default none | basic | headers | full
        connectTimeout: 10000 # default 10 sec
        readTimeout: 60000 # default 60 sec

security:
  jwt:
    # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 128` on your command line to generate
    base64-secret: NOIUaY1qF6H8wmQJzLWmzk8uJfJZfptuCUW2MBq4bSfobb90cTB9hP49nXiMAVUByeuEF1UhWO1Yj19ABWdZCgPcjzC0Q1Qz9qt9gXU44kLqEDmY7/JoRg5c65j31VnITVvjRSCnxj7eyqv093ETnuGy0QBrUCO624Cx7pk5QAQ=
    token-validity-in-seconds: 86400
    token-validity-in-seconds-for-remember-me: 2592000

file-storage:
  public:
    storageType: local # option: local | aws | oss
    endpoint: # oss-<region>.aliyuncs.com # storageType = oss
    accessKey: # storageType = oss | aws
    accessKeySecret: # storageType = oss | aws
    bucketName: # storageType = oss | aws
    region: # storageType = aws
    root: ${PRJA_FILE_PUBLIC} #local absolute path # storageType = local or set environment variable "PRJA_FILE_PUBLIC"
  private:
    storageType: local # option: local | aws | oss
    endpoint: # oss-<region>.aliyuncs.com # storageType = oss
    accessKey: # storageType = oss | aws
    accessKeySecret: # storageType = oss | aws
    bucketName: # storageType = oss | aws
    region: # storageType = aws
    root: ${PRJA_FILE_PRIVATE} #local absolute path # storageType = local or set environment variable "PRJA_FILE_PRIVATE"
  exception-stacktrace:
    enabled: true
    storage-type: local
    root: ./logs

base:
  internal-api-key: xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ
  request-uri:
    open-api-server: http://localhost:8762/common-component
  cdn:
    host: http://localhost
  kafka:
    topic:
      exception-stacktrace:
        partition: 10
        concurrency: 10
  api-key: 9ebbc78d-dff5-4876-bb45-13d0a6e9b8e3

integration-ai:
  statement-uri: http://localhost:5557
  qna-uri: http://localhost:5556
  ocr-credit-card-uri: http://localhost:5558
  # AI service is totally in k8s environment. There's no public IP. Please ask INFRA team give SIT k8s access and do port forwarding using kubectl command
  predict-uri: http://localhost:5704/predict
  product-uri: http://localhost:8080/product
  predict-blocking-timeout: 15s
  product-blocking-timeout: 15s
  statement-blocking-timeout: 30s
  qna-blocking-timeout: 15s
  ocr-credit-card-timeout: 15s

integration-ekyc:
  ekyc-url: https://uat.fintexvalley.com
  key: 4fc906fb-a8f6-40f7-b0a1-9902ff5e07b9
  api-signature: 6762acbb-80ef-4ea4-925f-2a44351d236b
  user-agent: moneyx
  ekyc-blocking-timeout: 15s

securities:
  crypto:
    key: NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc
    iv: 57ad289f-3def-4c

logging:
  level:
    my.com.mandrill: WARN
    org.hibernate.sql: WARN
  pattern:
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

integration-sinegy:
  base-url: https://exchange-api.sinegy.com
