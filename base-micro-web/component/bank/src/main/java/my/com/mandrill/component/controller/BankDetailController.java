package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.dto.request.BankDetailCreateRequest;
import my.com.mandrill.component.dto.request.BankDetailUpdateRequest;
import my.com.mandrill.component.dto.response.BankDetailResponse;
import my.com.mandrill.component.service.BankDetailIntegrationService;
import my.com.mandrill.component.service.BankDetailService;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.general.constant.AccountType;
import my.com.mandrill.utilities.general.dto.ValidList;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("bank-details")
public class BankDetailController {

	private final BankDetailService bankDetailService;

	private final BankDetailIntegrationService bankDetailIntegrationService;

	private final ValidationService validationService;

	private final ********************** bankIntegrationService;

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public BankDetailResponse findById(@PathVariable String id,
			@RequestParam(required = false, defaultValue = "true") boolean populateReminder) {

		BankDetail result = bankDetailService.findByIdAndBankUserId(id, SecurityUtil.currentUserId());
		BankDetailResponse resultResponse = MapStructConverter.MAPPER.toBankDetailResponse(result);
		if (populateReminder) {
			bankDetailIntegrationService.findAndSetReminderResponse(resultResponse);
		}
		return resultResponse;

	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public List<BankDetailResponse> findAll(@RequestParam List<AccountType> accountTypes) {
		List<BankDetail> result = bankDetailService.findByAccountTypeAndBankUserId(accountTypes,
				SecurityUtil.currentUserId());

		return result.stream().map(MapStructConverter.MAPPER::toBankDetailResponse).toList();
	}

	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<BankDetailResponse> update(@PathVariable String id,
			@RequestBody BankDetailUpdateRequest bankDetailUpdateRequest) {

		BankDetail bankDetail = bankDetailIntegrationService.processUpdate(
				bankDetailService.findByIdAndBankUserId(id, SecurityUtil.currentUserId()), bankDetailUpdateRequest);

		bankDetailIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.CASH_SAVINGS, NetWorthSource.BANK);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toBankDetailResponse(bankDetail));

	}

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_CREATE)")
	public ResponseEntity<BankDetailResponse> create(@RequestBody BankDetailCreateRequest bankDetailCreateRequest) {

		validationService.validateUserJourney(bankDetailCreateRequest.getUserJourneyId());
		BankDetail bankDetail = bankDetailIntegrationService.processCreate(SecurityUtil.currentUserId(),
				bankDetailCreateRequest);

		bankDetailIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.CASH_SAVINGS, NetWorthSource.BANK);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toBankDetailResponse(bankDetail));

	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@DeleteMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void delete(@PathVariable String id) {

		bankDetailIntegrationService.processDelete(id, SecurityUtil.currentUserId());

		bankDetailIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.CASH_SAVINGS, NetWorthSource.BANK);

	}

	@PostMapping("bulk")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_CREATE)")
	public List<BankDetailResponse> createBulk(@Valid @RequestBody ValidList<BankDetailCreateRequest> requests) {
		List<BankDetail> bankDetails = bankDetailIntegrationService.processCreate(SecurityUtil.currentUserId(),
				requests);

		bankDetailIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.CASH_SAVINGS, NetWorthSource.BANK);

		return bankDetails.stream().map(MapStructConverter.MAPPER::toBankDetailResponse).toList();
	}

	@Hidden
	@GetMapping("integration/count-card-added-via-campaign")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Long> countCardAddedViaCampaign(@RequestParam Instant createdDateStart,
			@RequestParam Instant createdDateEnd, @RequestParam String advertisementId) {
		return ResponseEntity
				.ok(bankDetailService.countCardAddedViaCampaign(createdDateStart, createdDateEnd, advertisementId));
	}

}
