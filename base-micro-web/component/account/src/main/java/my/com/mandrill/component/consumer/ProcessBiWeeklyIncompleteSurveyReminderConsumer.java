package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.service.UserService;
import my.com.mandrill.utilities.feign.dto.SchedulerPushNotificationDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.service.KafkaSender;
import org.springframework.kafka.annotation.DltHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessBiWeeklyIncompleteSurveyReminderConsumer {

	private static final String BI_WEEKLY_INCOMPLETE_PROFILE_REMINDER_SCHEDULER_PUSH_NOTIFICATION_TEMPLATE_ID = "6b77f146-ddfd-4722-a3e6-242c829f329d";

	private final ObjectMapper objectMapper;

	private final KafkaSender kafkaSender;

	private final UserService userService;

	@KafkaListener(topics = KafkaTopic.PROCESS_INCOMPLETE_SURVEY_REMINDER, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.PROCESS_INCOMPLETE_SURVEY_REMINDER)
	public void consume(String message) throws JsonProcessingException {
		log.info("Process Incomplete Survey Reminder Consumer Begin {}", message);
		Instant now = Instant.now();

		LocalDate today = LocalDate.now(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE));
		List<String> completedSurveyUserIds = objectMapper.readValue(message, new TypeReference<>() {
		});
		// only send reminder to those users who at least 14 days old
		List<String> incompleteSurveyUserIds = userService.findAllUserIdsByCreatedDateLessThan(
				today.minusDays(14).atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant());
		incompleteSurveyUserIds.removeAll(completedSurveyUserIds);

		if (!incompleteSurveyUserIds.isEmpty()) {
			kafkaSender.safeSend(KafkaTopic.SCHEDULER_PUSH_NOTIFICATION, UUID.randomUUID().toString(),
					new SchedulerPushNotificationDTO(incompleteSurveyUserIds,
							BI_WEEKLY_INCOMPLETE_PROFILE_REMINDER_SCHEDULER_PUSH_NOTIFICATION_TEMPLATE_ID));
		}

		log.info("Process Incomplete Survey Reminder Consumer Ended, Delay: {} ms, total: {}",
				(System.currentTimeMillis() - now.toEpochMilli()), incompleteSurveyUserIds.size());
	}

	@DltHandler
	public void dltHandler(String message) {
		log.error("Unable to process : {}", message);
	}

}
