package my.com.mandrill.component.controller.moneyxbizintegration;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.BizAccountClient;
import my.com.mandrill.component.dto.model.BizUserDTO;
import my.com.mandrill.component.dto.request.CreateBizPointWithdrawalRequest;
import my.com.mandrill.component.dto.response.BizPointAmountResponse;
import my.com.mandrill.component.dto.response.BizPointHistoryResponse;
import my.com.mandrill.component.service.PointEarningService;
import my.com.mandrill.component.service.PointTransactionService;
import my.com.mandrill.component.service.PointWithdrawalIntegrationService;
import my.com.mandrill.component.service.ReferralIntegrationService;
import my.com.mandrill.component.util.RSMCalculationUtil;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;

@RestController
@RequiredArgsConstructor
@RequestMapping("moneyxbiz-integration/mx-points")
public class MoneyXBizIntegrationMxPointController {

	private final BizAccountClient bizAccountClient;

	private final PointEarningService pointEarningService;

	private final PointTransactionService pointTransactionService;

	private final PointWithdrawalIntegrationService pointWithdrawalIntegrationService;

	private final ReferralIntegrationService referralIntegrationService;

	@PublicAuth
	@SecurityRequirements
	@GetMapping("point-histories")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public Page<BizPointHistoryResponse> gePaginatedPointHistory(@RequestParam String institutionId,
			Pageable pageable) {
		return pointTransactionService.findBizPointHistory(institutionId, pageable);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("total-earning")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public ResponseEntity<BigDecimal> getReferralSummary(@RequestParam String institutionId) {

		return ResponseEntity.ok(pointEarningService.calculateTotalEarning(institutionId));

	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("total-earning-amount")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public ResponseEntity<BizPointAmountResponse> getTotalEarningWithAmount(@RequestParam String institutionId) {
		List<String> userIds = bizAccountClient.findUsersByInstitutionId(institutionId).stream().map(BizUserDTO::getId)
				.toList();
		BigDecimal pointEarning = referralIntegrationService.getTotalAvailablePoint(userIds);
		BigDecimal pointAmount = RSMCalculationUtil.calculateRealAmount(pointEarning);
		return ResponseEntity.ok(new BizPointAmountResponse(pointEarning, pointAmount));
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("validate-withdrawal")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public void validateWithdrawal(@RequestParam String institutionId,
			@RequestParam(required = false) BigDecimal requestedAmount) {
		BigDecimal amount = ObjectUtils.defaultIfNull(requestedAmount, BigDecimal.ZERO);
		pointWithdrawalIntegrationService.validateWithdrawal(institutionId, amount);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("has-pending-withdrawal")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public boolean hasPendingWithdrawal(@RequestParam Set<String> userIds) {
		return pointWithdrawalIntegrationService.hasPendingWithdrawalForUserIdIn(userIds);
	}

	@PublicAuth
	@SecurityRequirements
	@PostMapping("request-withdrawal")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_CREATE_API)")
	public void pointWithdrawal(@Valid @RequestBody CreateBizPointWithdrawalRequest request) {
		pointWithdrawalIntegrationService.processBizPointWithdrawal(request);
	}

}
