package my.com.mandrill.component.service.impl;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Permission;
import my.com.mandrill.component.repository.jpa.PermissionRepository;
import my.com.mandrill.component.service.PermissionService;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.service.GlobalValidationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional(readOnly = true)
public class PermissionServiceImpl implements PermissionService {

	private final PermissionRepository permissionRepository;

	private final GlobalValidationService globalValidationService;

	public PermissionServiceImpl(PermissionRepository permissionRepository,
			GlobalValidationService globalValidationService) {
		this.permissionRepository = permissionRepository;
		this.globalValidationService = globalValidationService;
	}

	public List<Permission> getPermissionByIsAdminFalse() {
		return permissionRepository.findByAdminFalseOrderByCategoryAscNameAsc();
	}

	public Permission getPermissionById(String permissionId) {
		return permissionRepository.findById(permissionId)
				.orElseThrow(ExceptionPredicate.permissionByIdNotFound(permissionId));
	}

	public Set<Permission> getPermissions(Set<Permission> permissionSet) {
		return permissionSet.stream().map(permission -> permissionRepository.findById(permission.getId()))
				.flatMap(Optional::stream).collect(Collectors.toSet());
	}

	public Page<Permission> getPermissions(Pageable page, String name) {
		return permissionRepository.findAllByName(page, globalValidationService.validateNullToLowerCase(name));
	}

	@Override
	public List<Permission> getPermissions() {
		return permissionRepository.findAll();
	}

}
