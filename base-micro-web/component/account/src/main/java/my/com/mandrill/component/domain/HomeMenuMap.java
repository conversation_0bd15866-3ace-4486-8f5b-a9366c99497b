package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import lombok.*;
import my.com.mandrill.component.constant.HomeSectionEnum;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;

@Getter
@Setter
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "home_menu_map")
public class HomeMenuMap extends AuditSection implements Serializable {

	@Enumerated(EnumType.STRING)
	@Column(name = "section")
	private HomeSectionEnum section;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "home_menu_id")
	@JsonManagedReference(value = "section-homeMenu")
	private HomeMenu homeMenu;

	@Min(value = 0)
	@Column(name = "menu_sequence")
	private Integer menuSequence;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "home_menu_group_id")
	@JsonManagedReference(value = "section-homeMenuGroup")
	private HomeMenuGroup homeMenuGroup;

	@Min(value = 0)
	@Column(name = "group_sequence")
	private Integer groupSequence;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || getClass() != o.getClass())
			return false;
		HomeMenuMap that = (HomeMenuMap) o;
		return getId() != null && getId().equals(that.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}
