package my.com.mandrill.component.repository.jpa;

import jakarta.transaction.Transactional;
import my.com.mandrill.component.constant.HomeSectionEnum;
import my.com.mandrill.component.domain.HomeMenuGroup;
import my.com.mandrill.component.domain.HomeMenuMap;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HomeMenuMapRepository extends JpaRepository<HomeMenuMap, String> {

	List<HomeMenuMap> findBySection(HomeSectionEnum sectionEnum);

	Page<HomeMenuMap> findBySectionAndHomeMenuCodeContainsIgnoreCaseAndHomeMenuGroupCodeContainsIgnoreCase(
			HomeSectionEnum sectionEnum, String menuCode, String groupCode, Pageable pageable);

	HomeMenuMap findTopBySectionOrderByGroupSequenceDesc(HomeSectionEnum sectionEnum);

	HomeMenuMap findTopBySectionAndHomeMenuGroupOrderByGroupSequenceDesc(HomeSectionEnum sectionEnum,
			HomeMenuGroup groupCode);

	HomeMenuMap findTopBySectionAndHomeMenuGroupOrderByMenuSequenceDesc(HomeSectionEnum sectionEnum,
			HomeMenuGroup group);

	List<HomeMenuMap> findAllBySectionAndHomeMenuGroupAndMenuSequenceGreaterThan(HomeSectionEnum sectionEnum,
			HomeMenuGroup group, int seq);

	List<HomeMenuMap> findAllBySectionAndGroupSequenceGreaterThan(HomeSectionEnum sectionEnum, int seq);

	HomeMenuMap findTopBySectionAndHomeMenuGroupAndMenuSequenceLessThanOrderByMenuSequenceDesc(
			HomeSectionEnum sectionEnum, HomeMenuGroup group, int menuSequence);

	HomeMenuMap findTopBySectionAndHomeMenuGroupAndMenuSequenceGreaterThanOrderByMenuSequenceAsc(
			HomeSectionEnum sectionEnum, HomeMenuGroup group, int menuSequence);

	HomeMenuMap findTopBySectionAndGroupSequenceLessThanOrderByGroupSequenceDesc(HomeSectionEnum sectionEnum,
			int menuSequence);

	HomeMenuMap findTopBySectionAndGroupSequenceGreaterThanOrderByGroupSequenceAsc(HomeSectionEnum sectionEnum,
			int menuSequence);

	List<HomeMenuMap> findAllBySectionAndHomeMenuGroupAndGroupSequence(HomeSectionEnum sectionEnum, HomeMenuGroup group,
			int seq);

	@Modifying
	@Transactional
	@Query("DELETE FROM HomeMenuMap h WHERE h.id = :id")
	void deleteById(String id);

	boolean existsByHomeMenuId(String id);

	boolean existsByHomeMenuGroupId(String id);

}
