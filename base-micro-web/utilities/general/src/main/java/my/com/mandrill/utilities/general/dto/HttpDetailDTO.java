package my.com.mandrill.utilities.general.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.ClientPlatformType;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HttpDetailDTO {

	private String method;

	private String ipAddress;

	private String service;

	private String uri;

	private String userAgent;

	private ClientPlatformType clientPlatform;

}
