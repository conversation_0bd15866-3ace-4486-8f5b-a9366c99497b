package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.DashboardDateType;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.utilities.feign.dto.DocumentTypeCodeAndCountDTO;
import my.com.mandrill.utilities.general.constant.VaultTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.List;

public interface DashboardService {

	DashboardResponse getModuleRecordByDaily(LocalDate dateFrom);

	DashboardResponse getModuleRecordByWeekly(LocalDate dateFrom, LocalDate dateTo);

	DashboardResponse getModuleRecordByMonthly(YearMonth yearMonth);

	DashboardResponse getAppDownloadDaily(LocalDate dateFrom, ZoneId zoneId);

	DashboardResponse getAppDownloadRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId);

	DashboardResponse getAppDownloadMonthly(YearMonth yearMonth, ZoneId zoneId);

	DashboardResponse getAppTimeSpentDaily(LocalDate dateFrom, ZoneId zoneId);

	List<DashboardResponse> getAppTimeSpentDailyBefore(LocalDate dateFrom, ZoneId zoneId, Integer number);

	DashboardResponse getAppTimeSpentRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId);

	List<DashboardResponse> getAppTimeSpentWeeklyBefore(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId,
			Integer number);

	List<DashboardResponse> getAppTimeSpentMonthlyBefore(YearMonth yearMonth, ZoneId zoneId, Integer number);

	DashboardResponse getAppUserActivityDaily(LocalDate dateFrom, ZoneId zoneId);

	List<DashboardResponse> getAppUserActivityDailyBefore(LocalDate dateFrom, ZoneId zoneId, Integer step);

	DashboardResponse getAppUserActivityRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId);

	List<DashboardResponse> getAppUserActivityWeeklyBefore(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId,
			Integer step);

	List<DashboardResponse> getAppUserActivityMonthlyBefore(YearMonth yearMonth, ZoneId zoneId, Integer step);

	DashboardResponse getAppActiveUserDaily(LocalDate dateFrom, ZoneId zoneId);

	List<DashboardResponse> getAppActiveUserDailyBefore(LocalDate dateFrom, ZoneId zoneId, Integer step);

	DashboardResponse getAppActiveUserRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId);

	List<DashboardResponse> getAppActiveUserWeeklyBefore(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId,
			Integer step);

	List<DashboardResponse> getAppActiveUserMonthlyBefore(YearMonth yearMonth, ZoneId zoneId, Integer step);

	DashboardResponse getModuleTimeSpentDaily(LocalDate dateFrom, ZoneId zoneId);

	List<DashboardResponse> getModuleTimeSpentDailyBefore(LocalDate dateFrom, ZoneId zoneId, Integer step);

	DashboardResponse getModuleTimeSpentRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId);

	List<DashboardResponse> getModuleTimeSpentWeeklyBefore(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId,
			Integer step);

	List<DashboardResponse> getModuleTimeSpentMonthlyBefore(YearMonth yearMonth, ZoneId zoneId, Integer step);

	List<DashboardResponse> getModuleTimeSpentWeeklyBeforeLiveFeed(ZoneId zoneId, Integer step);

	List<DashboardResponse> getAppActiveUserWeeklyBeforeLiveFeed(ZoneId zoneId, Integer step);

	List<DashboardResponse> getAppDownloadRangeLiveFeed(ZoneId zoneId, Integer step);

	List<DashboardResponse> getAppDownloadRangeLiveFeedDaily(ZoneId zoneId, Integer step);

	DashboardResponse getOsDownloadDaily(LocalDate dateFrom, ZoneId zoneId);

	DashboardResponse getOsDownloadRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId);

	long getTodaysNewUsers(LocalDate dateFrom, ZoneId zoneId);

	long getTotalUser(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId);

	List<DashboardActiveUserTotalUserResponseData> getAppActiveUserTotalUserDailyBigQuery(DashboardDateType dateType,
			LocalDate dateFrom, ZoneId zoneId);

	List<DashboardActiveUserTotalUserResponseData> getAppActiveUserTotalUserWeeklyBigQuery(DashboardDateType dateType,
			LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId);

	List<DashboardActiveUserTotalUserResponseData> getAppActiveUserTotalUserMonthlyBigQuery(DashboardDateType dateType,
			YearMonth yearMonth, ZoneId zoneId);

	DashboardUserEngagementResponse getUserEngagementModuleTimeSpent(LocalDate dateFrom, LocalDate dateTo);

	DashboardRecordOrTimeSpentPerModuleResponseData getModuleRecord(LocalDate dateFrom, LocalDate dateTo,
			ZoneId zoneId);

	DashboardRecordOrTimeSpentPerModuleResponseData getModuleRecordDaily(LocalDate dateFrom, ZoneId zoneId);

	DashboardRecordOrTimeSpentPerModuleResponseData getModuleRecordWeekly(LocalDate dateFrom, LocalDate dateTo,
			ZoneId zoneId);

	DashboardRecordOrTimeSpentPerModuleResponseData getModuleRecordMonthly(YearMonth yearMonth, ZoneId zoneId);

	List<DashboardDownloadsResponseData> getAppDownloadDailyByBigQuery(LocalDate dateFrom, DashboardDateType dateType,
			ZoneId zoneId);

	List<DashboardDownloadsResponseData> getAppDownloadWeeklyByBigQuery(LocalDate dateFrom, LocalDate dateTo,
			DashboardDateType dateType, ZoneId zoneId);

	List<DashboardDownloadsResponseData> getAppDownloadMonthlyByBigQuery(YearMonth yearMonth,
			DashboardDateType dateType, ZoneId zoneId);

	long getAverageTimeSpent(LocalDate dateFrom, LocalDate dateTo);

	long getAverageSessionDurationDailyBigQuery(LocalDate dateFrom, Integer step);

	long getAverageSessionDurationWeeklyBigQuery(LocalDate dateFrom, LocalDate dateTo, Integer step);

	long getAverageSessionDurationMonthlyBigQuery(YearMonth yearMonth, Integer step);

	DashboardUserEngagementResponse getUserEngagementModuleDaily(LocalDate dateFrom, Integer step);

	DashboardUserEngagementResponse getUserEngagementModuleWeekly(LocalDate dateFrom, LocalDate dateTo, Integer step);

	DashboardUserEngagementResponse getUserEngagementModuleMonthly(YearMonth yearMonth, Integer step);

	List<DashboardUserEngagementResponseData> getUserEngagementDailyBigQuery(DashboardDateType dateType,
			LocalDate dateFrom);

	List<DashboardUserEngagementResponseData> getUserEngagementWeeklyBigQuery(DashboardDateType dateType,
			LocalDate dateFrom, LocalDate dateTo);

	List<DashboardUserEngagementResponseData> getUserEngagementMonthlyBigQuery(DashboardDateType dateType,
			YearMonth yearMonth);

	DashboardRecordOrTimeSpentPerModuleResponseData getModuleTimeSpentBigQuery(LocalDate dateFrom, LocalDate dateTo);

	DashboardRecordOrTimeSpentPerModuleResponseData getModuleTimeSpentDailyBigQuery(LocalDate dateFrom);

	DashboardRecordOrTimeSpentPerModuleResponseData getModuleTimeSpentWeeklyBigQuery(LocalDate dateFrom,
			LocalDate dateTo);

	DashboardRecordOrTimeSpentPerModuleResponseData getModuleTimeSpentMonthlyBigQuery(YearMonth yearMonth);

	DashboardStaticDataResponse getStaticData(ZoneId zoneId);

	BigDecimal getBounceRate(long downloads, long uninstall);

	List<VoucherCategoryAndCountResponse> getAllVoucherCategoryAndCountDaily(LocalDate dateFrom);

	List<VoucherCategoryAndCountResponse> getAllVoucherCategoryAndCountWeekly(LocalDate dateFrom, LocalDate dateTo);

	List<VoucherCategoryAndCountResponse> getAllVoucherCategoryAndCountMonthly(YearMonth yearMonth);

	List<VoucherCategoryAndCountResponse> getAllVoucherCategoryAndCount(LocalDate dateFrom, LocalDate dateTo);

	ReferralCountResponse countInvitedAndSucceedReferee(LocalDate dateFrom, LocalDate dateTo, String timeZone);

	ReferralCountResponse countInvitedAndSucceedRefereeDaily(LocalDate dateFrom, String timeZone);

	ReferralCountResponse countInvitedAndSucceedRefereeWeekly(LocalDate dateFrom, LocalDate dateTo, String timeZone);

	ReferralCountResponse countInvitedAndSucceedRefereeMonthly(YearMonth yearMonth, String timeZone);

	LeadGeneratedPerModuleResponse countLeadGeneratedPerModuleDaily(LocalDate dateFrom, String timeZone);

	LeadGeneratedPerModuleResponse countLeadGeneratedPerModuleWeekly(LocalDate dateFrom, LocalDate dateTo,
			String timeZone);

	LeadGeneratedPerModuleResponse countLeadGeneratedPerModuleMonthly(YearMonth yearMonth, String timeZone);

	LeadGeneratedPerModuleResponse countLeadGeneratedPerModule(LocalDate dateFrom, LocalDate dateTo, String timeZone);

	DocumentUploadedCountInVaultSubModuleResponse countDocumentUploadedDaily(LocalDate dateFrom, String timeZone);

	DocumentUploadedCountInVaultSubModuleResponse countDocumentUploadedWeekly(LocalDate dateFrom, LocalDate dateTo,
			String timeZone);

	DocumentUploadedCountInVaultSubModuleResponse countDocumentUploadedMonthly(YearMonth yearMonth, String timeZone);

	List<DocumentTypeCodeAndCountDTO> countDocumentTypeUploadedDaily(LocalDate dateFrom, String timeZone,
			VaultTypeEnum vaultType);

	List<DocumentTypeCodeAndCountDTO> countDocumentTypeUploadedWeekly(LocalDate dateFrom, LocalDate dateTo,
			String timeZone, VaultTypeEnum vaultType);

	List<DocumentTypeCodeAndCountDTO> countDocumentTypeUploadedMonthly(YearMonth yearMonth, String timeZone,
			VaultTypeEnum vaultType);

}
