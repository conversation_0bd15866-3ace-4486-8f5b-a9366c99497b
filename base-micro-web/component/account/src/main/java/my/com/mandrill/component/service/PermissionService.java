package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface PermissionService {

	List<Permission> getPermissionByIsAdminFalse();

	Permission getPermissionById(String permissionId);

	Set<Permission> getPermissions(Set<Permission> permissionSet);

	Page<Permission> getPermissions(Pageable page, String name);

	List<Permission> getPermissions();

}
