package my.com.mandrill.component.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.dto.DateDetailDTO;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashboardActiveUserTotalUserResponseData implements Serializable {

	private DateDetailDTO dateFrom;

	private DateDetailDTO dateTo;

	private DateDetailDTO yearMonth;

	private long activeUsersCount = 0L;

	private long usersCount = 0L;

}
