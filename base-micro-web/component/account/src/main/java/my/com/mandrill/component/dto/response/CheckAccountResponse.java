package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckAccountResponse {

	private boolean exists;

	private boolean preRegister;

	private boolean hasPassword;

	private boolean hasPin;

	private boolean emailVerified;

	private String phoneNumber;

	private String email;

}
