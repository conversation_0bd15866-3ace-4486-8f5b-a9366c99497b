package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.EmploymentType;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.IncomeType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.component.dto.model.UserIncome;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface IncomeRepository extends JpaRepository<Income, String> {

	@Query("""
			select i from Income i where i.user = :user
			and (:incomeType is null or i.incomeType = :incomeType)
			and (:employmentType is null or i.employmentType = :employmentType)
			""")
	List<Income> findByUserAndIncomeTypeAndEmploymentType(@NonNull User user, IncomeType incomeType,
			EmploymentType employmentType, Sort sort);

	@Query("""
			select i from Income i where i.user = :user
			and (:incomeType is null or i.incomeType = :incomeType)
			and (:employmentType is null or i.employmentType = :employmentType)
			order by i.createdDate desc limit 1
			""")
	Optional<Income> findLatestByUserAndIncomeTypeAndEmploymentType(User user, IncomeType incomeType,
			EmploymentType employmentType);

	Optional<Income> findByIdAndUser(@NonNull String id, @NonNull User user);

	boolean existsByIncomeTypeId(String incomeTypeId);

	long deleteByUserAndIdNotIn(@NonNull User user, @NonNull List<String> ids);

	boolean existsByUserAndIncomeTypeAndEmploymentType(@NonNull User user, @NonNull IncomeType incomeType,
			@NonNull EmploymentType employmentType);

	Optional<Income> findFirstByUserIdAndIncomeTypeCodeAndEmploymentTypeOrderByCreatedDateDesc(String userId,
			@NonNull String code, @NonNull EmploymentType employmentType);

	Optional<Income> findByUserAndEmploymentTypeAndIncomeType(@NonNull User user,
			@NonNull EmploymentType employmentType, @NonNull IncomeType incomeType);

	@Query("""
			SELECT i.monthlyIncomeAmount
			FROM Income i
			WHERE i.user.id = :userId AND i.employmentType.id = :employmentTypeId AND i.incomeType.id = :incomeTypeId
			""")
	Optional<BigDecimal> findMonthlyIncomeByUserIdAndEmploymentTypeAndIncomeType(String userId, String employmentTypeId,
			String incomeTypeId);

	@Query("""
			select new my.com.mandrill.component.dto.model.UserIncome(u.id, i.monthlyIncomeAmount)
			from Income i left join i.user u where u.loginType = :loginType and
			 u.deleted = false and u.createdDate >= :createdDate
			""")
	List<UserIncome> findByUserDeletedFalseAndLoginTypeAndCreatedDateGreaterThanEqual(@NonNull LoginTypeEnum loginType,
			@NonNull Instant createdDate);

	Income findTopByUserIdAndIncomeTypeIdOrderByCreatedDateDesc(@NonNull String userId,
			@NonNull String employmentTypeId);

	@Query("""
			select new my.com.mandrill.component.dto.model.IncomeDTO(i.id, new my.com.mandrill.component.dto.model.IncomeTypeDTO(it.id, it.name, it.description, it.code), new my.com.mandrill.component.dto.model.EmploymentTypeDTO(et.id, et.name, et.description, et.isLoanLimit),i.monthlyIncomeAmount)
			from Income i join i.user u
			join i.incomeType it
			join i.employmentType et
			where u.refNo = :refNo
			""")
	List<IncomeDTO> findByRefNo(@NonNull String refNo);

	/**
	 * start from here currently used by test
	 */
	@Modifying
	@Query("delete from Income e where e.user.id in :userIds")
	void deleteByUserIds(@Param("userIds") List<String> userIds);

}