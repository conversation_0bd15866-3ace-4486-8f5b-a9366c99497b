package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Property;
import my.com.mandrill.component.dto.model.PropertyDTO;
import my.com.mandrill.component.dto.response.PropertyResponse;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

public interface PropertyIntegrationService {

	Property create(Property property);

	void deleteV2(String id, String userId);

	void populateAttachment(PropertyResponse propertyResponse);

	void populateState(PropertyResponse propertyResponse);

	void populateMonthlyInstallment(PropertyResponse propertyResponse);

	void sendDashboardActivity(Instant createdDate);

	List<PropertyDTO> findByUserId(String userId, Sort sort);

	List<PropertyDTO> findDetachedEntity(String userId, List<String> linkedEntity, Sort sort);

	BigDecimal getCurrentUserLowestPropertyValue();

	void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source);

}
