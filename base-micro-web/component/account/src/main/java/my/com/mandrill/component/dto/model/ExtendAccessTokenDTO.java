package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.TokenStatus;
import my.com.mandrill.utilities.general.dto.HttpDetailDTO;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtendAccessTokenDTO implements Serializable {

	private String refreshToken;

	@JsonIgnore
	private HttpDetailDTO httpDetailDTO;

	@JsonIgnore
	private TokenStatus status;

}
