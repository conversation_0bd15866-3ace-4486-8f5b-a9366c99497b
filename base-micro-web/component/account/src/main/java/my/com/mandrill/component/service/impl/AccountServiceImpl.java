package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.*;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.ExpenseProjection;
import my.com.mandrill.component.dto.model.TermConditionAndPrivacyPolicyDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.exception.AccountComponentException;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.InvalidOtpException;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.DateUtil;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.core.constant.AuthorityRole;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.request.EmailRequest;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.request.EkycCreateRequestDTO;
import my.com.mandrill.utilities.general.dto.request.UserPublicWebRequest;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.GlobalNotSupportedException;
import my.com.mandrill.utilities.general.service.KafkaSender;
import my.com.mandrill.utilities.general.util.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static my.com.mandrill.utilities.general.util.SecurityUtil.currentUserLogin;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountServiceImpl implements AccountService {

	private static final int ATTEMPT_ZERO = 0;

	private final AuthorityService authorityService;

	private final KafkaSender kafkaSender;

	private final BaseProperties baseProperties;

	private final PasswordEncoder passwordEncoder;

	private final PopulateService populateService;

	private final RunningNumberUtil runningNumberUtil;

	private final KeyRequestService keyRequestService;

	private final UserService userService;

	private final UserIntegrationService userIntegrationService;

	private final ObjectMapper objectMapper;

	private final ProxyFeignClient proxyFeignClient;

	private final PasswordTransactionService passwordTransactionService;

	private final IncomeService incomeService;

	private final EmploymentTypeService employmentTypeService;

	private final DeleteAccountMessageService deleteAccountMessageService;

	private final ExpenseService expenseService;

	private final EntityManager entityManager;

	private final AppUserService appUserService;

	private final FeatureFlagOutbound featureFlagOutbound;

	private final JSONUtil jsonUtil;

	@Override
	public void checkIfUserExists(User user) {
		Optional<AppUser> appUser = appUserService.findSafeByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(
				user.getPhoneNumber(), user.getPhoneCountry(), user.getLoginType());
		if (appUser.isPresent()) {
			throw new BusinessException(ErrorCodeEnum.MOBILE_EXISTED);
		}
		checkIfEmailExists(user.getEmail(), user.getLoginType());
	}

	@Override
	public void checkIfEmailExists(String email, LoginTypeEnum loginType) {
		if (StringUtils.isNotBlank(email)) {
			Optional<AppUser> existEmail = appUserService.findSafeUserByEmailAndActiveAndLoginType(email, loginType);
			if (existEmail.isPresent()) {
				throw new BusinessException(ErrorCodeEnum.EMAIL_EXISTED);
			}
		}
	}

	@Override
	public void checkIfAdminUserExists(User user) {
		if (StringUtils.isBlank(user.getEmail())) {
			return;
		}
		user.setEmail(user.getEmail().toLowerCase());
		Optional<AppUser> appUser = appUserService.findSafeUserByEmailAndActiveAndLoginType(user.getEmail(),
				user.getLoginType());
		if (appUser.isPresent()) {
			throw new BusinessException(ErrorCodeEnum.EMAIL_EXISTED);
		}

	}

	@Override
	public boolean checkIfPhoneNumberExists(String fullPhoneNumber) {
		PhoneNumberUtil.ExtractedPhoneNumber extractedPhoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(fullPhoneNumber);
		return userService.existsByPhoneCountryAndPhoneNumber(extractedPhoneNumber.getPhoneCountry(),
				AesCryptoUtil.basicEncrypt(extractedPhoneNumber.getPhoneNumber()));
	}

	@Override
	public User registerUser(@Valid User user) {
		this.validateOtpKey(user.getKey(), user.getValue(), user.getPhoneCountry(), user.getPhoneNumber(),
				RequestKeyType.REGISTRATION);
		return createUser(user, false);

	}

	private void validateOtpKey(String key, String value, String phoneCountry, String phoneNumber,
			RequestKeyType requestKeyType) {
		String keyValue = key + "_" + value;
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(keyValue);
		keyInput.setUsername(phoneCountry + phoneNumber);
		Optional<UserKeyRequest> optionalKeyRequest = keyRequestService
				.findValidByUsernameAndType(keyInput.getUsernameRaw(), requestKeyType);
		if (optionalKeyRequest.isPresent()) {
			if (optionalKeyRequest.get().getType().isMultiUse()) {
				log.info("Key is multi-use, skipping revoke for key");
				return;
			}

			UserKeyRequest keyRequest = optionalKeyRequest.get();
			validateOtpVerification(keyRequest, keyValue);
			keyRequest.setCompletionDate(Instant.now());
			keyRequest.setStatus(RequestKeyStatus.COMPLETED);
			keyRequestService.save(keyRequest);
		}
		else {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}
	}

	private User createUser(User user, boolean preRegistration) {
		Authority publicAuthority = authorityService.findByName(AuthorityRole.PUBLIC);
		if (!preRegistration) {
			if (StringUtils.isNotBlank(user.getPin())) {
				user.setPin(passwordEncoder.encode(user.getPin()));
			}
			else {
				user.setPassword(passwordEncoder.encode(user.getPassword()));
			}
		}
		user.setProvider(AuthenticationProviderSource.DATABASE);
		user.setAuthorities(Collections.singleton(publicAuthority));
		user.setLoginType(LoginTypeEnum.USER);
		user.setLoginFailAttempt(ATTEMPT_ZERO);
		user.setActive(true);
		user.setPhoneVerified(true);
		user.setRefNo(runningNumberUtil.getLatestRunningNumber(RunningNumberModule.USER_REFERENCE.name(), false));
		user.setUsername(user.getRefNo());
		user.setSecretKey(CryptoUtil.generateSecretKey());
		setTermConditionVersion(user, publicAuthority.getInstitution());
		setPrivacyPolicyVersion(user, publicAuthority.getInstitution());
		setPlatformAgreementVersion(user, publicAuthority.getInstitution());
		userIntegrationService.save(user);
		return user;
	}

	@Override
	public User updateTermConditionAndPrivacyPolicyVersion(User user, TermConditionAndPrivacyPolicyDTO request) {
		Authority publicAuthority = authorityService.findByName(AuthorityRole.PUBLIC);
		if (request.isTermAndConditionRequireUpdate()) {
			setTermConditionVersion(user, publicAuthority.getInstitution());
		}
		if (request.isPrivacyPolicyRequireUpdate()) {
			setPrivacyPolicyVersion(user, publicAuthority.getInstitution());
		}
		if (request.isPlatformAgreementRequireUpdate()) {
			setPlatformAgreementVersion(user, publicAuthority.getInstitution());
		}
		userIntegrationService.save(user);
		return user;
	}

	@Override
	public TermConditionAndPrivacyPolicyDTO validateTermConditionAndPrivacyPolicy(User user) {
		Authority publicAuthority = authorityService.findByName(AuthorityRole.PUBLIC);
		String termConditionValue = proxyFeignClient.getCommonFeignClient()
				.getSystemConfigurationByCodeAndInstitutionId(SystemConfigurationEnum.TERM_CONDITION_VERSION.getCode(),
						publicAuthority.getInstitution().getId())
				.getValue();
		String privacyPolicyValue = proxyFeignClient.getCommonFeignClient()
				.getSystemConfigurationByCodeAndInstitutionId(SystemConfigurationEnum.PRIVACY_POLICY_VERSION.getCode(),
						publicAuthority.getInstitution().getId())
				.getValue();
		String platformAgreementValue = proxyFeignClient.getCommonFeignClient()
				.getSystemConfigurationByCodeAndInstitutionId(
						SystemConfigurationEnum.PLATFORM_AGREEMENT_VERSION.getCode(),
						publicAuthority.getInstitution().getId())
				.getValue();
		TermConditionAndPrivacyPolicyDTO result = new TermConditionAndPrivacyPolicyDTO();
		result.setTermAndConditionRequireUpdate(!termConditionValue.equals(user.getTermConditionVersion()));
		result.setPrivacyPolicyRequireUpdate(!privacyPolicyValue.equals(user.getPrivacyPolicyVersion()));
		result.setPlatformAgreementRequireUpdate(!platformAgreementValue.equals(user.getPlatformAgreementVersion()));
		return result;
	}

	private void setTermConditionVersion(User user, Institution institution) {
		user.setTermConditionVersion(
				proxyFeignClient.getCommonFeignClient()
						.getSystemConfigurationByCodeAndInstitutionId(
								SystemConfigurationEnum.TERM_CONDITION_VERSION.getCode(), institution.getId())
						.getValue());
	}

	private void setPrivacyPolicyVersion(User user, Institution institution) {
		user.setPrivacyPolicyVersion(
				proxyFeignClient.getCommonFeignClient()
						.getSystemConfigurationByCodeAndInstitutionId(
								SystemConfigurationEnum.PRIVACY_POLICY_VERSION.getCode(), institution.getId())
						.getValue());
	}

	private void setPlatformAgreementVersion(User user, Institution institution) {
		user.setPlatformAgreementVersion(
				proxyFeignClient.getCommonFeignClient()
						.getSystemConfigurationByCodeAndInstitutionId(
								SystemConfigurationEnum.PLATFORM_AGREEMENT_VERSION.getCode(), institution.getId())
						.getValue());
	}

	@Override
	public User getCurrentUser() {
		AppUser appUser = appUserService.findByRefNo(currentUserLogin());
		if (Objects.isNull(appUser) || Boolean.FALSE.equals(appUser.getActive())) {
			throw new BusinessException(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST);
		}
		entityManager.detach(appUser);
		User user = jsonUtil.convertValue(appUser, User.class);
		populateService.populateCountry(user);
		populateService.populateState(user);
		return user;
	}

	@Override
	public String getUserRefNo(String accessType, LoginTypeEnum loginType, String username) {
		AppUser appUser = new AppUser();
		if (AccessTypeEnum.EMAIL.getCode().equals(accessType)) {
			appUser = appUserService.findAuthByEmailAndActiveAndLoginType(username, loginType);
		}
		else if (AccessTypeEnum.MOBILE.getCode().equals(accessType) && username.length() > 3) {
			String phoneNumber = PhoneNumberUtil.getPhoneNumberWithNoCountryCode(username).getPhoneNumber();
			appUser = appUserService.findAuthByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(phoneNumber,
					username.substring(0, 3), loginType);
		}
		return appUser.getRefNo();
	}

	@Override
	public String getUserEmail(String phoneCountry, String phoneNumber, LoginTypeEnum loginType) {
		AppUser appUser = appUserService.findAuthByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(phoneNumber,
				phoneCountry, loginType);
		return EmailUtil.maskEmail(appUser.getEmail());
	}

	@Override
	public UserKeyRequest completePasswordResetEmail(UpdatePasswordResetRequest request) {
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setUsername(request.getUsername() == null ? null : request.getUsername().toLowerCase());
		keyInput.setKeyValue(request.getKey());
		Optional<UserKeyRequest> optionalKeyRequest = keyRequestService
				.findValidByUsernameAndType(keyInput.getUsernameRaw(), RequestKeyType.PASSWORD_RESET);
		if (optionalKeyRequest.isPresent()) {
			UserKeyRequest keyRequest = optionalKeyRequest.get();
			validateOtpVerification(keyRequest, request.getKey());
			AppUser appUser = appUserService.findAuthByEmailAndActiveAndLoginType(request.getUsername(),
					LoginTypeEnum.USER);
			appUser.setPassword(passwordEncoder.encode(request.getPassword()));
			appUser.setLoginFailAttempt(0);
			appUserService.save(appUser);

			keyRequest.setCompletionDate(Instant.now());
			keyRequest.setStatus(RequestKeyStatus.COMPLETED);
			keyRequestService.save(keyRequest);

			this.sendEmailPasswordUpdated(MapStructConverter.MAPPER.toUser(appUser));
			return keyRequest;
		}
		else {
			return null;
		}
	}

	private UserKeyRequest otpProcess(String username, RequestKeyType requestKeyType, DeliveryType deliveryType) {
		String key = RandomUtil.generateAlphanumeric(12) + "_" + RandomUtil.generateNumber(6);
		UserKeyRequest input = new UserKeyRequest();
		input.setUsername(username.toLowerCase());

		int keyExpiryInHour;
		if (requestKeyType.isMultiUse()) {
			keyExpiryInHour = featureFlagOutbound
					.getIntegerValue(GlobalSystemConfigurationEnum.KEY_REQUEST_MULTI_USE_VALID_HOURS);
		}
		else {
			keyExpiryInHour = featureFlagOutbound
					.getIntegerValue(GlobalSystemConfigurationEnum.KEY_REQUEST_VALID_HOURS);
		}

		Optional<UserKeyRequest> keyRequestOptional = keyRequestService
				.findValidByUsernameAndType(input.getUsernameRaw(), requestKeyType);
		if (keyRequestOptional.isPresent()) {
			UserKeyRequest keyRequest = keyRequestOptional.get();

			String maxAttempt;
			try {
				maxAttempt = proxyFeignClient.getCommonFeignClient()
						.getSystemConfigurationByCodeAndInstitutionId(
								SystemConfigurationEnum.REQUEST_OTP_ATTEMPT.getCode(),
								SystemConfigurationEnum.DEFAULT_USER_INSTITUTION_ID.getValue())
						.getValue();
			}
			catch (Exception e) {
				maxAttempt = SystemConfigurationEnum.REQUEST_OTP_ATTEMPT.getValue();
			}

			if (keyRequest.getAttempt() < Integer.parseInt(maxAttempt)) {
				keyRequest.setKeyValue(key);
				keyRequest.setInitialDate(Instant.now());
				keyRequest.setExpiresAt(Instant.now().plus(keyExpiryInHour, ChronoUnit.HOURS));
				keyRequest.setAttempt(keyRequest.getAttempt() + 1);
				keyRequest.setFailVerificationAttempt(ATTEMPT_ZERO);
				keyRequest.setDeliveryType(deliveryType);
				keyRequestService.save(keyRequest);
				return keyRequest;
			}
			else {
				throw new BusinessException(ErrorCodeEnum.REQUEST_OTP_LIMIT);
			}
		}
		else {
			UserKeyRequest keyRequest = new UserKeyRequest();
			keyRequest.setUsername(username);
			keyRequest.setKeyValue(key);
			keyRequest.setInitialDate(Instant.now());
			keyRequest.setType(requestKeyType);
			keyRequest.setAttempt(ATTEMPT_ZERO);
			keyRequest.setFailVerificationAttempt(ATTEMPT_ZERO);
			keyRequest.setDeliveryType(deliveryType);
			keyRequest.setExpiresAt(Instant.now().plus(keyExpiryInHour, ChronoUnit.HOURS));
			keyRequestService.save(keyRequest);
			return keyRequest;
		}
	}

	@Override
	public UserKeyRequest requestUpdateMobileOTP(String username, DeliveryType deliveryType) {
		return otpProcess(username, RequestKeyType.USER_UPDATE_MOBILE, deliveryType);
	}

	@Override
	public UserKeyRequest completeUpdateMobile(UpdateMobileRequest request) {
		String keyValue = request.getKey() + "_" + request.getValue();

		PhoneNumberUtil.ExtractedPhoneNumber newPhoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(request.getPhoneCountry() + request.getPhoneNumber());

		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(keyValue);
		keyInput.setUsername(newPhoneNumber.getCombinedPhoneNumber());

		Optional<UserKeyRequest> optionalKeyRequest = keyRequestService
				.findValidByUsernameAndType(keyInput.getUsernameRaw(), RequestKeyType.USER_UPDATE_MOBILE);
		if (optionalKeyRequest.isPresent()) {
			UserKeyRequest keyRequest = optionalKeyRequest.get();
			validateOtpVerification(keyRequest, keyValue);

			User user = getCurrentUser();
			user.setPhoneCountry(newPhoneNumber.getPhoneCountry());
			user.setPhoneNumber(newPhoneNumber.getPhoneNumber());
			user.setLoginFailAttempt(0);
			user.setPhoneVerified(true);
			userIntegrationService.save(user);

			keyRequest.setCompletionDate(Instant.now());
			keyRequest.setStatus(RequestKeyStatus.COMPLETED);
			keyRequestService.save(keyRequest);

			return keyRequest;
		}
		else {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}
	}

	@Override
	public void verifyMobileOTP(String username, String keyParam, String valueParam, RequestKeyType requestKeyType,
			UsernameType usernameType) {
		String key = keyParam + "_" + valueParam;
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(key);
		keyInput.setUsername(username);
		validateUsernameType(keyInput, usernameType);

		Optional<UserKeyRequest> optionalUserKeyRequest = keyRequestService
				.findValidByUsernameAndType(keyInput.getUsernameRaw(), requestKeyType);
		if (optionalUserKeyRequest.isPresent()) {
			UserKeyRequest keyRequest = optionalUserKeyRequest.get();
			validateOtpVerification(keyRequest, key);
		}
		else {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}
	}

	@Override
	public UserKeyRequest requestEmailKey(String email, LoginTypeEnum loginType, RequestKeyType requestKeyType) {
		AppUser user = appUserService.findAuthByEmailAndActiveAndLoginType(email, loginType);

		if (Objects.nonNull(user)) {
			int keyExpiryInHour = featureFlagOutbound
					.getIntegerValue(GlobalSystemConfigurationEnum.KEY_REQUEST_VALID_HOURS);

			String key = RandomUtil.generateAlphanumeric(15);
			Instant initialDate = Instant.now();

			UserKeyRequest keyRequest = new UserKeyRequest();
			keyRequest.setUsername(user.getEmail());
			keyRequest.setKeyValue(key);
			keyRequest.setInitialDate(initialDate);
			keyRequest.setType(requestKeyType);
			keyRequest.setAttempt(ATTEMPT_ZERO);
			keyRequest.setFailVerificationAttempt(ATTEMPT_ZERO);
			keyRequest.setExpiresAt(initialDate.plus(keyExpiryInHour, ChronoUnit.HOURS));
			keyRequestService.save(keyRequest);
			return keyRequest;
		}
		return null;
	}

	@Override
	public void changePassword(User user, ChangePasswordRequest request) {
		if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
			throw new BusinessException(ErrorCodeEnum.INVALID_PASSWORD);
		}
		else {
			passwordTransactionService.validatePassword(user, request.getNewPassword());
			PasswordTransaction passwordTransaction = new PasswordTransaction();
			passwordTransaction.setUser(user);
			passwordTransaction.setPasswordHash(user.getPassword());
			passwordTransactionService.save(passwordTransaction);
			user.setPassword(passwordEncoder.encode(request.getNewPassword()));
			user.setLoginFailAttempt(0);
			userIntegrationService.save(user);
		}
	}

	@Override
	public void createPin(User user, CreatePinRequest request) {
		if (Objects.nonNull(user.getPin())) {
			throw new BusinessException(ErrorCodeGlobalEnum.PIN_ALREADY_CREATED);
		}
		user.setPin(passwordEncoder.encode(request.getPin()));
		user.setLoginFailAttempt(0);
		userIntegrationService.save(user);
	}

	@Override
	public void completeEmailVerification(EmailVerificationRequest request) {
		String key = request.getKey() + "_" + request.getValue();
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(key);

		Optional<UserKeyRequest> optionalKeyRequest = keyRequestService
				.findValidByKeyValueAndType(keyInput.getKeyValueRaw(), RequestKeyType.VERIFICATION_EMAIL);

		optionalKeyRequest.filter(v -> DeliveryType.EMAIL.equals(v.getDeliveryType())).ifPresentOrElse(keyRequest -> {
			User user = userService.findByUsernameInputIgnoreCaseAndLoginType(keyRequest.getUsername(),
					LoginTypeEnum.USER, null);
			user.setEmailVerified(Boolean.TRUE);
			userService.save(user);

			keyRequest.setCompletionDate(Instant.now());
			keyRequest.setStatus(RequestKeyStatus.COMPLETED);
			keyRequestService.save(keyRequest);
		}, () -> {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		});
	}

	@Override
	public void sendEmailPasswordUpdated(User optionalUser) {
		User user = optionalUser;

		String date = DateConvertUtil.toString(user.getLastModifiedDate(), DateConvertUtil.DATETIME_FORMAT_2,
				ZoneId.of(TimeConstant.DEFAULT_TIMEZONE));

		Map<String, Object> templateVariable = Map.of(EmailTemplateVariableEnum.FIRST_NAME.getValue(),
				user.getFullName(), EmailTemplateVariableEnum.LAST_MODIFIED_DATE.getValue(), date);

		EmailRequest emailRequest = EmailRequest.builder().to(Collections.singletonList(user.getEmail()))
				.templateVariable(templateVariable).templateName(TemplateNameEnum.EMAIL_PASSWORD_UPDATED.getValue())
				.timeTriggered(Instant.now()).build();
		kafkaSender.safeSend(KafkaTopic.SEND_EMAIL_TOPIC, user.getEmail(), emailRequest);
	}

	@Override
	public void sendSmsVerification(UserKeyRequest keyRequest, String receiver) {
		if (Objects.nonNull(keyRequest)) {
			SmsOtpRequest.OTPParam otpParam = new SmsOtpRequest.OTPParam();
			otpParam.setOtp(keyRequest.getKeyValue().substring(keyRequest.getKeyValue().length() - 6));
			otpParam.setRequestDate(keyRequest.getInitialDate().atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE))
					.format(DateUtil.defaultDateFormatter));
			SmsOtpRequest smsRequest = new SmsOtpRequest();
			smsRequest.setTo(receiver);
			smsRequest.setParams(otpParam);
			kafkaSender.safeSend(KafkaTopic.SEND_SMS_TOPIC, receiver, smsRequest);
		}
	}

	@Override
	public void sendWhatsAppVerification(UserKeyRequest keyRequest, String receiver) {
		if (Objects.nonNull(keyRequest)) {
			WhatsAppOtpRequest.OTPParam otpParam = new WhatsAppOtpRequest.OTPParam();
			otpParam.setOtp(keyRequest.getKeyValue().substring(keyRequest.getKeyValue().length() - 6));
			otpParam.setRequestDate(keyRequest.getInitialDate().atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE))
					.format(DateUtil.defaultDateFormatter));

			WhatsAppOtpRequest whatsAppOtpRequest = new WhatsAppOtpRequest();
			whatsAppOtpRequest.setTo(receiver);
			whatsAppOtpRequest.setParams(otpParam);

			kafkaSender.safeSend(KafkaTopic.SEND_WHATSAPP_TOPIC, receiver, whatsAppOtpRequest);
		}
	}

	@Override
	public String getSecretKey(String refNo, String apiKey) {
		if (StringUtils.isNotBlank(baseProperties.getApiKey())) {
			if (!baseProperties.getApiKey().equals(apiKey)) {
				throw new AccessDeniedException("Not allowed to access this method");
			}
		}
		else {
			throw new AccountComponentException("api-key not configured");
		}
		return userService.findSecretKeyByRefNo(refNo);
	}

	@Override
	public ExtendedNetWorthCalculationResponse calculateExtendedNetWorth(String userId, Boolean isRevamp) {
		ExtendedNetWorthCalculationResponse netWorthCalculationResponse = new ExtendedNetWorthCalculationResponse();

		List<DetailedNetWorthDTO> incomes = new ArrayList<>();

		// calculate from bank entity
		List<DetailedNetWorthDTO> bankNetWorth = proxyFeignClient.getBankFeignClient().calculateDetailedNetWorth();
		List<DetailedNetWorthDTO> assets = new ArrayList<>(bankNetWorth.stream()
				.filter(data -> DetailedNetWorthDTO.ValueType.ASSET.equals(data.getValueType())).toList());
		List<DetailedNetWorthDTO> recurringRepayments = new ArrayList<>(bankNetWorth.stream()
				.filter(data -> DetailedNetWorthDTO.ValueType.RECURRING_REPAYMENT.equals(data.getValueType()))
				.toList());
		List<DetailedNetWorthDTO> liabilities = new ArrayList<>(bankNetWorth.stream()
				.filter(data -> DetailedNetWorthDTO.ValueType.LIABILITY.equals(data.getValueType())).toList());

		// calculate from property entity
		NetWorthDTO propertyNetWorth = proxyFeignClient.getPropertyFeignClient().calculateNetWorth();
		assets.add(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.PROPERTIES)
				.value(propertyNetWorth.getAssets()).valueType(DetailedNetWorthDTO.ValueType.ASSET).build());

		// calculate from vehicle entity
		NetWorthDTO vehicleNetWorth = proxyFeignClient.getVehicleFeignClient().calculateNetWorth();
		assets.add(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.VEHICLES).value(vehicleNetWorth.getAssets())
				.valueType(DetailedNetWorthDTO.ValueType.ASSET).build());

		// calculate from insurance entity
		List<DetailedNetWorthDTO> insuranceNetWorth = proxyFeignClient.getInsuranceFeignClient()
				.calculateNetWorthExtended();

		recurringRepayments.addAll(insuranceNetWorth.stream()
				.filter(data -> DetailedNetWorthDTO.ValueType.RECURRING_REPAYMENT.equals(data.getValueType()))
				.toList());

		List<DetailedNetWorthDTO> insuranceCoverage = insuranceNetWorth.stream()
				.filter(data -> DetailedNetWorthDTO.ValueType.INSURANCE_COVERAGE.equals(data.getValueType())).toList();
		netWorthCalculationResponse.setInsuranceCoverage(insuranceCoverage);

		netWorthCalculationResponse.setTotalInsuranceCoverage(
				insuranceCoverage.stream().map(DetailedNetWorthDTO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add));

		// calculate from retirement entity
		NetWorthDTO retirementNetWorth = proxyFeignClient.getRetirementFeignClient().calculateNetWorth();
		assets.add(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.RETIREMENT)
				.value(retirementNetWorth.getAssets()).valueType(DetailedNetWorthDTO.ValueType.ASSET).build());

		// calculate from investment entity
		NetWorthDTO investmentNetWorth = proxyFeignClient.getInvestmentFeignClient().calculateNetWorth();
		assets.add(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.INVESTMENT)
				.value(investmentNetWorth.getAssets()).valueType(DetailedNetWorthDTO.ValueType.ASSET).build());

		// calculate from utility entity
		List<DetailedNetWorthDTO> utilityNetWorth = proxyFeignClient.getUtilityFeignClient().calculateNetWorth();
		recurringRepayments.addAll(utilityNetWorth);

		// get full-time salary income
		incomes.add(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.INCOME)
				.value(incomeService.findIncome(userId)).valueType(DetailedNetWorthDTO.ValueType.INCOME).build());

		netWorthCalculationResponse.setAssets(assets);
		netWorthCalculationResponse.setLiabilities(liabilities);
		netWorthCalculationResponse.setIncomes(incomes);
		netWorthCalculationResponse.setRecurringRepayments(recurringRepayments);

		netWorthCalculationResponse.setTotalAsset(
				assets.stream().map(DetailedNetWorthDTO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add));
		netWorthCalculationResponse.setTotalLiability(
				liabilities.stream().map(DetailedNetWorthDTO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add));
		netWorthCalculationResponse.setTotalIncome(
				incomes.stream().map(DetailedNetWorthDTO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add));
		netWorthCalculationResponse.setTotalRecurringRepayment(recurringRepayments.stream()
				.map(DetailedNetWorthDTO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add));

		BigDecimal netWorth = netWorthCalculationResponse.getTotalAsset()
				.subtract(netWorthCalculationResponse.getTotalLiability());
		netWorthCalculationResponse.setNetWorth(netWorth);

		return netWorthCalculationResponse;
	}

	@Override
	public void deleteProcess(AppUser user) {
		if (proxyFeignClient.getMoneyXCoreFeignClient().existInProgressWithdrawal(user.getId())) {
			throw new BusinessException(ErrorCodeEnum.EXIST_IN_PROGRESS_WITHDRAWAL);
		}
		appUserService.processGracePeriod(user);
	}

	@Override
	public CurrentUserIdResponse getCurrentUserIdAndRefNo() {
		CurrentUserIdResponse currentUserIdResponse = userService.findRefNoAndIdByRefNo(currentUserLogin());
		if (Objects.isNull(currentUserIdResponse) || Boolean.FALSE.equals(currentUserIdResponse.getActive())) {
			throw new BusinessException(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST);
		}
		return currentUserIdResponse;
	}

	@Override
	public AccountResponse getCurrentUserIdAndRefNoV2() {
		AccountResponse currentUserIdResponse = userService.findActiveById(SecurityUtil.currentUserId());
		if (Objects.isNull(currentUserIdResponse) || Boolean.FALSE.equals(currentUserIdResponse.getActive())) {
			throw new BusinessException(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST);
		}
		return currentUserIdResponse;
	}

	@Override
	public CurrentUserIdResponse getCurrentUserIdAndRefNo(String id) {
		CurrentUserIdResponse currentUserIdResponse = userService.findRefNoAndIdByRefNo(id);
		if (Objects.isNull(currentUserIdResponse) || Boolean.FALSE.equals(currentUserIdResponse.getActive())) {
			throw new BusinessException(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST);
		}
		return currentUserIdResponse;
	}

	private boolean isBlockedCountryCode(String countryCode) {
		String whitelistedCountryCode = proxyFeignClient.getCommonFeignClient()
				.getIntegrationGlobalSystemConfigurationByCode(
						GlobalSystemConfigurationEnum.WHITELISTED_COUNTRY_CODE_FOR_REQUEST_OTP.getCode())
				.getValue();
		if (StringUtils.isBlank(whitelistedCountryCode)) {
			return false;
		}

		Set<String> codes = Set.of(whitelistedCountryCode.split(","));
		return !codes.contains(countryCode);
	}

	@Override
	public KeyResponse requestOTPByPhoneNumber(OTPRequest request) {
		PhoneNumberUtil.ExtractedPhoneNumber extractedPhoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(request.getUsername());
		if (isBlockedCountryCode(extractedPhoneNumber.getPhoneCountry())) {
			throw new BusinessException(ErrorCodeEnum.PHONE_NUMBER_NOT_ALLOWED_REQUEST_OTP);
		}
		String combinedPhoneNumber = extractedPhoneNumber.getCombinedPhoneNumber();
		User user = buildUserFromCombinedPhoneNumber(combinedPhoneNumber);
		Runnable preCheck = switch (request.getRequestKeyType()) {
			case REGISTRATION -> () -> checkIfUserExists(user);
			case VERIFIED_USER_INTEREST_REDIRECT_SUBMIT, VERIFICATION_SUBMIT_USER_INTERESTED, PRE_REGISTRATION,
					VERIFICATION_GUEST_APPLY_PRODUCT ->
				() -> {
				};
			case PASSWORD_RESET, PIN_RESET -> () -> userService
					.findByUsernameInputIgnoreCaseAndLoginType(combinedPhoneNumber, LoginTypeEnum.USER, null);
			default -> throw new GlobalNotSupportedException(request.getRequestKeyType().name());
		};
		return checkAndDeliverKeyRequest(request, user, preCheck);
	}

	@Override
	public KeyResponse checkAndDeliverKeyRequest(OTPRequest request, User user, Runnable preCheck) {
		preCheck.run();
		UserKeyRequest keyRequest = otpProcess(user.getPhoneCountry() + user.getPhoneNumber(),
				request.getRequestKeyType(), request.getDeliveryType());
		sendOtpByDeliveryType(keyRequest, user, request.getDeliveryType());
		KeyResponse keyResponse = new KeyResponse();
		keyResponse.setKey(keyRequest.getKeyValue().split("_")[0]);
		keyResponse.setExpiresAt(keyRequest.getExpiresAt());

		log.info("OTP: {}", keyRequest.getKeyValue());
		return keyResponse;
	}

	@Override
	public User buildUserFromCombinedPhoneNumber(String username) {
		User user = new User();
		user.setUsername(username);
		user.setLoginType(LoginTypeEnum.USER);
		if (username.length() > 3) {
			user.setPhoneCountry(username.substring(0, 3));
			user.setPhoneNumber(username.substring(3));
		}
		return user;
	}

	private void sendOtpByDeliveryType(UserKeyRequest keyRequest, User user, DeliveryType deliveryType) {
		if (DeliveryType.SMS.equals(deliveryType)) {
			sendSmsVerification(keyRequest,
					PhoneNumberUtil.formatPhoneNumber(user.getPhoneCountry(), user.getPhoneNumber()));
		}
		else if (DeliveryType.EMAIL.equals(deliveryType)) {
			sendEmailOneTimePassword(keyRequest.getKeyValue().split("_")[1], user.getEmail());
		}
		else if (DeliveryType.WHATSAPP.equals(deliveryType)) {
			sendWhatsAppVerification(keyRequest,
					PhoneNumberUtil.formatPhoneNumber(user.getPhoneCountry(), user.getPhoneNumber()));
		}
	}

	@Override
	public KeyResponse requestOTPByEmail(OTPRequest request) {
		// backward compatibility
		Set<RequestKeyType> forceEmail = Set.of(RequestKeyType.PASSWORD_RESET, RequestKeyType.VERIFICATION_EMAIL);
		if (forceEmail.contains(request.getRequestKeyType())) {
			request.setDeliveryType(DeliveryType.EMAIL);
		}

		AppUser appUser = appUserService.findAuthByEmailAndActiveAndLoginType(request.getUsername(),
				LoginTypeEnum.USER);
		User user = MapStructConverter.MAPPER.toUser(appUser);
		return checkAndDeliverKeyRequest(request, user, LambdaVoid.runnableNothing());
	}

	@Override
	public void sendEmailOneTimePassword(String otp, String receiver) {
		EmailRequest emailRequest = new EmailRequest();
		emailRequest.setTo(Collections.singletonList(receiver));
		Map<String, Object> templateVariable = Map.of(EmailTemplateVariableEnum.OTP.getValue(), otp);
		emailRequest.setTemplateVariable(templateVariable);
		emailRequest.setTemplateName(TemplateNameEnum.EMAIL_OTP_VERIFICATION.getValue());
		kafkaSender.safeSend(KafkaTopic.SEND_EMAIL_TOPIC, receiver, emailRequest);
	}

	@Override
	public void validateOtpVerification(UserKeyRequest existOtp, String otpNeedToValidate) {
		String maxAttempt = proxyFeignClient.getCommonFeignClient()
				.getSystemConfigurationByCodeAndInstitutionId(
						SystemConfigurationEnum.MAX_OTP_FAIL_VERIFICATION.getCode(),
						SystemConfigurationEnum.DEFAULT_USER_INSTITUTION_ID.getValue())
				.getValue();
		if (maxAttempt == null) {
			maxAttempt = SystemConfigurationEnum.MAX_OTP_FAIL_VERIFICATION.getValue();
		}
		if (existOtp.getFailVerificationAttempt() >= Integer.parseInt(maxAttempt)) {
			throw new BusinessException(ErrorCodeEnum.OTP_TRY_TIMES_EXCEEDING);
		}
		else if (!existOtp.getKeyValue().equals(otpNeedToValidate)) {
			existOtp.setFailVerificationAttempt(existOtp.getFailVerificationAttempt() + 1);
			keyRequestService.save(existOtp);
			throw new InvalidOtpException(ErrorCodeEnum.INVALID_KEY.getDescription());
		}
	}

	@Override
	public void validateUsernameType(UserKeyRequest keyRequest, UsernameType usernameType) {
		if (usernameType.equals(UsernameType.EMAIL)) {
			AppUser appUser = appUserService.findAuthByEmailAndActiveAndLoginType(keyRequest.getUsername(),
					LoginTypeEnum.USER);
			keyRequest.setUsername(appUser.getPhoneCountry().concat(appUser.getPhoneNumber()));
		}
	}

	@Override
	public PublicUserDetailRequest validateUsernameType(PublicUserDetailRequest publicUserDetailRequest,
			UsernameType usernameType) {
		if (usernameType.equals(UsernameType.EMAIL)) {
			AppUser appUser = appUserService.findAuthByEmailAndActiveAndLoginType(publicUserDetailRequest.getEmail(),
					LoginTypeEnum.USER);
			publicUserDetailRequest.setPhoneCountry(appUser.getPhoneCountry());
			publicUserDetailRequest.setPhoneNumber(appUser.getPhoneNumber());
		}
		return publicUserDetailRequest;
	}

	@Override
	public void saveDeleteAccountMessage(DeleteAccountMessage deleteAccountMessage) {
		deleteAccountMessageService.create(deleteAccountMessage);
	}

	@Override
	public IncomeAndEpfResponse findLatestIncomeAndEpf(String incomeTypeId) {
		AppUser appUser = appUserService.findByRefNo(currentUserLogin());
		BigDecimal grossIncome = appUser.getIncomes().stream()
				.filter(income -> incomeTypeId.equals(income.getIncomeType().getId()))
				.max(Comparator.comparing(Income::getCreatedDate)).map(Income::getMonthlyIncomeAmount).orElse(null);
		BigDecimal epf = appUser.getEpfContributions().stream()
				.max(Comparator.comparing(EpfContribution::getCreatedDate)).map(EpfContribution::getContribution)
				.orElse(null);
		return new IncomeAndEpfResponse(grossIncome, epf);
	}

	@Override
	public UserPublicWebResponse checkAndRegisterNewAccount(UserPublicWebRequest request) {
		PhoneNumberUtil.ExtractedPhoneNumber phoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(request.getPhoneCountry() + request.getPhoneNumber());

		if (StringUtils.isNotBlank(request.getEmail())) {
			Optional<AppUser> userByEmail = appUserService.findSafeUserByEmailAndActiveAndLoginType(request.getEmail(),
					LoginTypeEnum.USER);
			if (userByEmail.isPresent()
					&& !StringUtils.equals(phoneNumber.getPhoneNumber(), userByEmail.get().getPhoneNumber())) {
				throw new BusinessException(ErrorCodeEnum.EMAIL_EXISTED);
			}
		}

		this.validateOtpKey(request.getKey(), request.getValue(), phoneNumber.getPhoneCountry(),
				phoneNumber.getPhoneNumber(), RequestKeyType.valueOf(request.getRequestKeyType().name()));

		Optional<User> userByPhoneOpt = userService.findOptionalByPhoneCountryAndPhoneNumber(request.getPhoneCountry(),
				phoneNumber.getPhoneNumber());
		if (userByPhoneOpt.isPresent()) {
			User user = userByPhoneOpt.get();
			boolean kycSuccess = proxyFeignClient.getCommonFeignClient().getKYCStatus(user.getId());
			if (!kycSuccess) {
				user.setNric(Optional.ofNullable(request.getNric()).orElse(user.getNric()));
				user.setDob(Optional.ofNullable(request.getDob()).orElse(user.getDob()));
				user.setFullName(Optional.ofNullable(request.getFullName()).orElse(user.getFullName()));
				user.setReferralCodeUsed(request.getReferralCode());
				userService.save(user);
			}
			if (Objects.nonNull(request.getIncomeAmount())) {
				incomeService.createOrUpdateIncome(user.getId(), request.getIncomeAmount());
			}

			return toUserPublicResponse(user, true);
		}

		User newUser = objectMapper.convertValue(request, User.class);
		newUser.setReferralCode(null); // set null referral code
		newUser.setReferralCodeUsed(request.getReferralCode());
		User user = this.createUser(newUser, true);
		return toUserPublicResponse(user, false);
	}

	@Override
	public PublicUserDetailResponse getValidatedUserDetailAndKycStatus(
			PublicUserDetailRequest publicUserDetailRequest) {
		this.validateOtpKeyWithoutUpdateKeyRequestStatus(publicUserDetailRequest.getKey(),
				publicUserDetailRequest.getValue(), publicUserDetailRequest.getPhoneCountry(),
				publicUserDetailRequest.getPhoneNumber(), publicUserDetailRequest.getRequestKeyType());
		return userIntegrationService.getUserDetailAndKycStatus(publicUserDetailRequest);

	}

	@Override
	public CurrentUserDataForAIResponse getCurrentUserDataForAI(String userId) {

		BigDecimal userIncome = incomeService.findIncome(userId);

		CurrentUserDataForAIResponse response = MapStructConverter.MAPPER
				.toCurrentUserDataForAIResponse(userService.findCurrentUserDataForAiById(userId));
		// AI expecting null if no income
		response.setUserIncome(userIncome.compareTo(BigDecimal.ZERO) > 0 ? userIncome : null);
		// set expenses data
		response.setExpenses(expenseService.findProjectionByUserId(userId).stream().map(this::mapExpenses).toList());

		return response;
	}

	private UserPublicWebResponse toUserPublicResponse(User user, boolean existingUser) {
		return MapStructConverter.MAPPER.toUserPublicWebResponse(user, existingUser);
	}

	private void validateOtpKeyWithoutUpdateKeyRequestStatus(String key, String value, String phoneCountry,
			String phoneNumber, RequestKeyType requestKeyType) {
		String keyValue = key + "_" + value;
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(keyValue);
		String phoneNumberCombined = PhoneNumberUtil.getPhoneNumberWithNoCountryCode(phoneCountry + phoneNumber)
				.getCombinedPhoneNumber();
		keyInput.setUsername(phoneNumberCombined);

		Optional<UserKeyRequest> optionalKeyRequest = keyRequestService
				.findValidByUsernameAndType(keyInput.getUsernameRaw(), requestKeyType);
		if (optionalKeyRequest.isEmpty()) {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}
		UserKeyRequest userKeyRequest = optionalKeyRequest.get();
		validateOtpVerification(userKeyRequest, keyValue);
	}

	private CurrentUserDataForAIResponse.ExpenseDTO mapExpenses(ExpenseProjection expenseProjection) {

		return CurrentUserDataForAIResponse.ExpenseDTO.builder().expenseAmount(expenseProjection.getExpenseAmount())
				.expenseTypeName(expenseProjection.getExpenseTypeName()).build();

	}

	public void updateApplicant(EkycCreateRequestDTO ekycCreateRequestDTO) {
		try {
			proxyFeignClient.getCommonFeignClient().updateApplicant(ekycCreateRequestDTO);
		}
		catch (Exception ex) {
			log.error("fail to update applicant: " + ex);
		}

	}

}
