package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.CheckAccountProjection;
import my.com.mandrill.component.dto.model.UserDataForAiProjection;
import my.com.mandrill.component.dto.response.CurrentUserIdResponse;
import my.com.mandrill.component.dto.response.InternalUserMobileResponse;
import my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PaymentAccountStatus;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, String>, UserRepositoryCustom {

	List<User> findBySecretKeyNull();

	@NonNull
	@EntityGraph(value = "graph.User.load-lazy", type = EntityGraph.EntityGraphType.LOAD)
	Optional<User> findByIdAndInstitutionsId(@NonNull String id, @NonNull String institutionId);

	@EntityGraph(value = "graph.User.load-lazy", type = EntityGraph.EntityGraphType.LOAD)
	Optional<User> findByUsernameIgnoreCaseAndActiveTrueAndDeletedFalse(String username);

	@EntityGraph(value = "graph.User.load-lazy", type = EntityGraph.EntityGraphType.LOAD)
	User findByUsernameIgnoreCaseAndDeletedFalse(String username);

	Optional<User> findByEmailIgnoreCaseAndLoginTypeAndActiveTrue(String email, LoginTypeEnum loginType);

	Optional<User> findByPhoneCountryAndPhoneNumberAndLoginTypeAndActiveTrue(String phoneCountry, String phoneNumber,
			LoginTypeEnum loginType);

	Optional<User> findByEmailIgnoreCaseAndLoginType(String email, LoginTypeEnum loginType);

	Optional<User> findByPhoneCountryAndPhoneNumberAndLoginType(String phoneCountry, String phoneNumber,
			LoginTypeEnum loginType);

	Optional<User> findByPhoneNumberAndPhoneCountry(String phoneNumber, String phoneCountry);

	@EntityGraph(attributePaths = "authorities")
	Optional<User> findByRefNo(String refNo);

	Optional<User> findByIdAndInstitutionsIdAndLoginType(String username, String curIns, LoginTypeEnum loginType);

	boolean existsByUsernameAndInstitutionsIdAndLoginType(String username, String curIns, LoginTypeEnum loginType);

	boolean existsByEmailAndLoginType(String email, LoginTypeEnum loginTypeEnum);

	boolean existsByPhoneCountryAndPhoneNumberAndDeletedFalse(String phoneCountry, String phoneNumber);

	@Query("""
			select u from User u inner join u.institutions institutions
			where institutions.id = :institutionId and u.loginType = :loginType
			and (LOWER(COALESCE(u.fullName,'')) like %:fullName%)
			""")
	Page<User> findByInstitutionsIdAndLoginTypeAndFullName(@NonNull String institutionId,
			@NonNull LoginTypeEnum loginType, @Nullable String fullName, Pageable pageable);

	boolean existsByAuthoritiesId(String authorityId);

	boolean existsBySegmentId(String segmentId);

	boolean existsByEmploymentTypeId(String id);

	@Query("select u.secretKey from User u where u.refNo = :refNo")
	String findSecretKeyByRefNo(String refNo);

	@Query("select u from User u where u.deletedDatetime <= CURRENT_DATE - 30 and u.deleted = false")
	List<User> findAllByAbleToLogicalDelete();

	@Query("""
			select new my.com.mandrill.component.dto.response.InternalUserMobileResponse(u.id, u.fullName, u.phoneNumber, u.phoneCountry, u.phoneVerified, u.email, u.age, u.address1, u.address2, u.address3, u.deletedDatetime, u.active, u.refNo, u.createdDate, u.nric, u.loginFailDeviceLockLog, false, u.paymentInfoStatus, u.paymentInfoApprovedDate, u.ekycVerificationStatus, u.referralCode, u.username, u.referralCodeUsed, u.rsmRelationType, (case when u.phoneVerified = true
			                                                                                                                                                                                                                                                                                                                                                                                                                                                  and u.phoneCountry = '+60'
			                                                                                                                                                                                                                                                                                                                                                                                                                                                  and u.referralCodeTermConditionVersion is not null
			                                                                                                                                                                                                                                                                                                                                                                                                                                                  and u.referralCodeTermConditionVersion <> ''
			                                                                                                                                                                                                                                                                                                                                                                                                                                              then true else false end))
			from User u where u.loginType = 'USER'
			and (:fullName is NULL or (lower(u.fullName) like %:fullName%))
			and (:email is NULL or (u.email like :email))
			and (:phoneCountry is NULL or (u.phoneCountry like :phoneCountry))
			and (:phoneNumber is NULL or (u.phoneNumber like :phoneNumber))
			and (:refNo is NULL or (lower(u.refNo) like %:refNo%))
			and (:referralCode is NULL or (lower(u.referralCode) ilike %:referralCode%))
			and (:referralCodeUsed is NULL or (lower(u.referralCodeUsed) ilike %:referralCodeUsed%))
			and (:rsmRelationType is NULL or (lower(u.rsmRelationType) ilike %:rsmRelationType%))
			and (:active is NULL or (u.active = :active))
			and ((:dateType is NULL and u.createdDate between :startDate and :endDate) or (:dateType is not NULL and (case when :dateType = 'createdDate' then u.createdDate else u.createdDate end) between :startDate and :endDate))
			and (:paymentAccountStatus is null or u.paymentInfoStatus = :paymentAccountStatus)
			and u.deleted = false
			""")
	Page<InternalUserMobileResponse> findInternalMobileUser(String fullName, String email, String phoneCountry,
			String phoneNumber, String refNo, Boolean active, PaymentAccountStatus paymentAccountStatus,
			String referralCode, String referralCodeUsed, String rsmRelationType, String dateType, Instant startDate,
			Instant endDate, Pageable pageable);

	Optional<User> findByIdAndLoginType(String id, LoginTypeEnum loginTypeEnum);

	@Query("""
			select new my.com.mandrill.component.dto.response.CurrentUserIdResponse(u.id, u.refNo, u.active, u.loginType, u.dob, u.nric, u.passport, u.army, u.fullName, u.phoneCountry, u.phoneNumber, u.email, u.age, u.paymentInfoStatus, u.ekycVerificationStatus, u.secretKey)
			from User u where u.refNo = :refNo and u.deleted = false
			""")
	CurrentUserIdResponse findRefNoAndIdByRefNo(String refNo);

	@Cacheable(cacheNames = CacheKey.USER_BY_ID_CACHE, key = "#id")
	Optional<User> findByIdAndDeletedFalse(String id);

	List<User> findByDobNotNull();

	@Query("""
			select count(u.id) from User u
			where u.deleted = true
			and u.deletedDatetime between :startDate and :endDate
			and u.loginType = :loginType
			""")
	long countByDeletedAndLoginType(Instant startDate, Instant endDate, LoginTypeEnum loginType);

	@Query("""
			select u
			from User u
			where u.createdDate between :createdDateStart and :createdDateEnd
			and u.loginType = 'USER'
			and u.deleted = false
			""")
	Page<User> getUserListByCreatedDate(Instant createdDateStart, Instant createdDateEnd, Pageable pageable);

	@Query("select u.id from User u where u.loginType='USER' and u.active=:active and u.deleted=:deleted")
	Page<String> findByActiveAndDeleted(Boolean active, Boolean deleted, Pageable pageable);

	@Query("select u.id from User u where u.createdDate between :createdDateStart and :createdDateEnd and u.loginType = 'USER' and u.deleted = false")
	List<String> findAllUserIdsByCreatedDate(Instant createdDateStart, Instant createdDateEnd);

	@Modifying
	@Query("update User u set u.loginFailAttempt = ?1 where u.id = ?2")
	int setLoginFailAttempt(Integer failAttempt, String userId);

	@Query("select distinct u.id from User u where u.loginType = 'USER' and u.createdDate < ?1")
	List<String> findAllUserIdsByCreatedDateLessThan(Instant createdDate);

	@Query("""
			select new my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse(u.id, u.refNo, u.fullName, u.referralCode)
			from User u
			where u.loginType = 'USER'
			and u.deleted = false
			""")
	List<UserFullNameResponse> findAllUserFullNames();

	@Query("""
			select new my.com.mandrill.component.dto.response.InternalUserMobileResponse(u.id, u.fullName, u.phoneNumber, u.phoneCountry,u.phoneVerified, u.email, u.age, u.address1, u.address2, u.address3, u.deletedDatetime, u.active, u.refNo, u.createdDate, u.nric, u.loginFailDeviceLockLog, false, u.paymentInfoStatus, u.paymentInfoApprovedDate, u.ekycVerificationStatus, u.referralCode, u.username, u.referralCodeUsed, u.rsmRelationType,(case when u.phoneVerified = true
			                                                                                                                                                                                                                                                                                                                                                                                                                                                  and u.phoneCountry = '+60'
			                                                                                                                                                                                                                                                                                                                                                                                                                                                  and u.referralCodeTermConditionVersion is not null
			                                                                                                                                                                                                                                                                                                                                                                                                                                                  and u.referralCodeTermConditionVersion <> ''
			                                                                                                                                                                                                                                                                                                                                                                                                                                              then true else false end))
			from User u where
			u.id in :userIds
			""")
	List<InternalUserMobileResponse> findAllByUserIds(List<String> userIds);

	@Query("""
			select new my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse(u.id, u.refNo, u.fullName, u.referralCode)
			from User u
			where u.loginType = 'USER'
			and (:includeDeleted = true or u.deleted = false)
			and u.id in :userIds
			""")
	List<UserFullNameResponse> findUserFullNamesByUserIds(List<String> userIds, boolean includeDeleted);

	@Query("""
			select new my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse(u.id, u.refNo, u.fullName, u.referralCode)
			from User u
			where u.deleted = false
			and u.refNo in :userRefNos
			""")
	List<UserFullNameResponse> findUserFullNamesByUserRefNos(List<String> userRefNos);

	@Modifying
	@Query("update User u set u.loginFailDeviceLockLog=?1 where u.id=?2")
	int setLoginFailDeviceLockLog(String msg, String id);

	@Query("select u.refNo from User u where u.id = :id")
	String findRefNoById(String id);

	@Query("""
			select new my.com.mandrill.component.dto.model.CheckAccountProjection(u.pin, u.password, u.emailVerified, u.phoneCountry, u.phoneNumber, u.email)
			from User u
			where u.phoneCountry = :phoneCountry and u.phoneNumber = :phoneNumberRaw and u.loginType = :loginType
			""")
	Optional<CheckAccountProjection> checkByPhoneNumber(String phoneCountry, String phoneNumberRaw,
			LoginTypeEnum loginType);

	@Query("""
			select new my.com.mandrill.component.dto.model.CheckAccountProjection(u.pin, u.password, u.emailVerified, u.phoneCountry, u.phoneNumber, u.email)
			from User u
			where u.email = :emailRaw and u.loginType = :loginType
			""")
	Optional<CheckAccountProjection> checkByEmail(String emailRaw, LoginTypeEnum loginType);

	@Modifying
	@Query("update User u set u.referralCodeTermConditionVersion=?1, u.lastModifiedDate = ?2, u.lastModifiedBy = ?3 where u.id=?4")
	void updateTncVersion(String referralTncVersion, Instant lastModifiedDate, String lastModifiedBy, String userId);

	@Query(value = """
			select distinct au.id as userId, au.fullName as fullName, au.age as userAge, au.gender as gender, au.maritalStatus as maritalStatus, n.name as nationality
			from User au
			left join au.nationality n
			where au.id = :userId
			""")
	UserDataForAiProjection findCurrentUserForAiById(String userId);

	@Query("""
				SELECT DISTINCT u.Id
				FROM User u
				JOIN u.authorities a
				JOIN a.permissions p
				WHERE p.code = :code
			""")
	List<String> findUserIdsByPermission(String code);

}
