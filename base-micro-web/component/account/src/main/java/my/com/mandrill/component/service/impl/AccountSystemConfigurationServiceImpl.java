package my.com.mandrill.component.service.impl;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.service.AccountSystemConfigurationService;
import my.com.mandrill.component.service.InstitutionService;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.SystemConfigurationDTO;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import my.com.mandrill.utilities.general.constant.SystemConfigurationEnum;
import my.com.mandrill.utilities.general.service.RedisService;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AccountSystemConfigurationServiceImpl implements AccountSystemConfigurationService {

	private final CommonFeignClient commonFeignClient;

	private final InstitutionService institutionService;

	private final RedisService redisService;

	public AccountSystemConfigurationServiceImpl(CommonFeignClient commonFeignClient,
			InstitutionService institutionService, RedisService redisService) {
		this.commonFeignClient = commonFeignClient;
		this.institutionService = institutionService;
		this.redisService = redisService;
	}

	public SystemConfigurationDTO createDefaultSystemConfiguration(Institution institution) {
		SystemConfigurationDTO defaultSystemConfiguration = SystemConfigurationDTO.builder().active(true)
				.institutionId(institution.getId()).code(SystemConfigurationEnum.DEFAULT_INSTITUTION_TIER_MAX.getCode())
				.description(SystemConfigurationEnum.DEFAULT_INSTITUTION_TIER_MAX.getDescription())
				.value(SystemConfigurationEnum.DEFAULT_INSTITUTION_TIER_MAX.getValue()).build();
		return commonFeignClient.createSystemConfiguration(defaultSystemConfiguration);
	}

	public Long getFailLoginAttempt(AppUser user) {
		long result;
		try {
			String institution = institutionService.getHighestPriorityInstitution(user).getId();
			result = Long.parseLong(
					getGlobalSystemConfiguration(SystemConfigurationEnum.FAIL_LOGIN_ATTEMPT, institution).getValue());
		}
		catch (Exception e) {
			result = SystemConfigurationEnum.FAIL_LOGIN_ATTEMPT.getLong();
		}
		return result;
	}

	private SystemConfigurationDTO getGlobalSystemConfiguration(SystemConfigurationEnum systemConfiguration,
			String institution) {
		return redisService
				.getFromHash(CacheKey.SYSTEM_CONFIGURATION, systemConfiguration.getCode(), SystemConfigurationDTO.class)
				.orElseGet(() -> commonFeignClient
						.getSystemConfigurationByCodeAndInstitutionId(systemConfiguration.getCode(), institution));
	}

	@Override
	public Integer getMaximumNumberOfDevicesPerUser(User user) {
		try {
			String institution = institutionService.getInstitutionTierOneWithAuthorities(user).getId();
			SystemConfigurationDTO config = commonFeignClient.getSystemConfigurationByCodeAndInstitutionId(
					SystemConfigurationEnum.MAXIMUM_NUMBER_OF_DEVICES_PER_USER.getCode(), institution);
			log.debug("{} : {}", SystemConfigurationEnum.MAXIMUM_NUMBER_OF_DEVICES_PER_USER.getCode(), config);
			return Integer.parseInt(config.getValue());
		}
		catch (Exception e) {
			return SystemConfigurationEnum.MAXIMUM_NUMBER_OF_DEVICES_PER_USER.getInteger();
		}
	}

	@Override
	public Long getSignatureChallengeExpiredTime(User user) {
		try {
			String institution = institutionService.getInstitutionTierOneWithAuthorities(user).getId();
			SystemConfigurationDTO config = commonFeignClient.getSystemConfigurationByCodeAndInstitutionId(
					SystemConfigurationEnum.SIGNATURE_CHALLENGE_EXPIRED_TIME_IN_SECOND.getCode(), institution);
			log.debug("{} : {}", SystemConfigurationEnum.SIGNATURE_CHALLENGE_EXPIRED_TIME_IN_SECOND.getCode(), config);
			return Long.parseLong(config.getValue());
		}
		catch (Exception e) {
			return SystemConfigurationEnum.SIGNATURE_CHALLENGE_EXPIRED_TIME_IN_SECOND.getLong();
		}
	}

	@Override
	public Long getTargetedRegistered() {
		try {
			String targetedRegistered = commonFeignClient
					.getGlobalSystemConfigurationByCode(
							GlobalSystemConfigurationEnum.NUMBER_OF_TARGETED_USER_REGISTERED_CHART.getCode())
					.getValue();
			return Long.parseLong(targetedRegistered);
		}
		catch (Exception e) {
			return null;
		}
	}

}
