package my.com.mandrill.component.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.UserInterestRecord;
import my.com.mandrill.component.domain.UserInterestRecordVault;
import my.com.mandrill.component.dto.model.UserInterestRecordDTO;
import my.com.mandrill.component.dto.model.UserInterestedRedirectDTO;
import my.com.mandrill.component.dto.request.BankReportRequest;
import my.com.mandrill.component.dto.request.CreateUserInterestRecord;
import my.com.mandrill.component.dto.request.CreateUserInterestRedirectRecord;
import my.com.mandrill.component.dto.request.ReportServiceRequest;
import my.com.mandrill.component.dto.response.UserInterestRecordPageResponse;
import my.com.mandrill.component.dto.response.UserInterestRecordResponse;
import my.com.mandrill.component.service.UserInterestRecordIntegrationService;
import my.com.mandrill.component.service.UserInterestRecordService;
import my.com.mandrill.component.service.UserInterestRecordVaultService;
import my.com.mandrill.utilities.core.annotation.HasUserInterestedAccess;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.CurrentUserInstitutionsDTO;
import my.com.mandrill.utilities.feign.dto.UserDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;
import my.com.mandrill.utilities.general.util.DateUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Tag(name = "05-user-interest")
@Slf4j
@RestController
@RequestMapping("/user-interest-record")
@RequiredArgsConstructor
public class UserInterestRecordController {

	private final AccountFeignClient accountFeignClient;

	private final UserInterestRecordService userInterestRecordService;

	private final UserInterestRecordIntegrationService userInterestRecordIntegrationService;

	private final ObjectMapper objectMapper;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final UserInterestRecordVaultService userInterestRecordVaultService;

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping
	public void insert(@Valid @RequestBody CreateUserInterestRecord userInterestRecord) throws JsonProcessingException {
		UserDTO userDTO = accountFeignClient.getAccount();
		UserInterestRecord result = userInterestRecordIntegrationService.insert(userInterestRecord, userDTO);

		// only update nric if user's is null
		if (StringUtils.isBlank(userDTO.getNric()) && StringUtils.isNotBlank(result.getNric())) {
			UserDTO newNricRequest = new UserDTO();
			newNricRequest.setId(userDTO.getId());
			newNricRequest.setNric(result.getNric());
			kafkaTemplate.send(KafkaTopic.UPDATE_USER_TOPIC, newNricRequest.getId(),
					objectMapper.writeValueAsString(newNricRequest));
			log.info("Updating user's NRIC via Message queue for userId: {}. Reason: User's NRIC is blank.",
					newNricRequest.getId());
		}
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("redirect")
	public void insertRedirect(@Valid @RequestBody CreateUserInterestRedirectRecord userInterestRedirectRecord)
			throws JsonProcessingException {
		CurrentUserIdDTO userDTO = accountFeignClient.getCurrentUserId();
		userInterestRecordIntegrationService.insertRedirect(userInterestRedirectRecord, userDTO);
	}

	@Hidden
	@PostMapping("/integration")
	@HasUserInterestedAccess
	public ResponseEntity<List<UserInterestRecordResponse>> export(@Valid @RequestBody BankReportRequest request) {
		List<CurrentUserInstitutionsDTO> userInstitutions = accountFeignClient.getCurrentUserInstitutions();
		List<UserInterestRecordResponse> responses = userInterestRecordIntegrationService.export(userInstitutions,
				ReportServiceRequest.builder().productIds(request.getProductIds()).isRedirect(false)
						.providerIds(new HashSet<>(request.getProviderId())).fullName(request.getFullName())
						.startDate(request.getStartDate()).endDate(request.getEndDate())
						.productType(request.getProductType()).build());
		return ResponseEntity.ok(responses);
	}

	@PostMapping("/report-listing/pagination")
	@HasUserInterestedAccess
	public ResponseEntity<Page<UserInterestRecordPageResponse>> export(@Valid @RequestBody BankReportRequest request,
			Pageable pageable) {
		List<CurrentUserInstitutionsDTO> userInstitutions = accountFeignClient.getCurrentUserInstitutions();
		Page<UserInterestRecordPageResponse> responses = userInterestRecordIntegrationService.export(userInstitutions,
				request.getStartDate(), request.getEndDate(), request.getProviderId(), pageable);
		return ResponseEntity.ok(responses);
	}

	@Hidden
	@GetMapping("/integration/redirect")
	@HasUserInterestedAccess
	public ResponseEntity<List<UserInterestedRedirectDTO>> exportCount(
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate) {
		return ResponseEntity.ok(userInterestRecordIntegrationService.export(startDate, endDate));
	}

	@GetMapping("/report-listing/redirect/pagination")
	@HasUserInterestedAccess
	public ResponseEntity<Page<UserInterestedRedirectDTO>> exportCount(
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate, Pageable pageable) {
		return ResponseEntity.ok(userInterestRecordIntegrationService.export(startDate, endDate, pageable));
	}

	@Hidden
	@GetMapping("/integration/count")
	public ResponseEntity<Long> count(@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate) {
		return ResponseEntity.ok(userInterestRecordIntegrationService.count(startDate, endDate));
	}

	@Hidden
	@GetMapping("/integration/redirect/count")
	public ResponseEntity<Long> redirectCount(
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate) {
		return ResponseEntity.ok(userInterestRecordIntegrationService.countRedirect(startDate, endDate));
	}

	@GetMapping("/product/{id}")
	public ResponseEntity<UserInterestRecordDTO> getDataByProductId(@PathVariable String id) {
		UserInterestRecord userInterestRecord = userInterestRecordService
				.getRecordByProductId(SecurityUtil.currentUserId(), id);
		if (userInterestRecord == null) {
			return ResponseEntity.ok(null);
		}
		return ResponseEntity.ok(objectMapper.convertValue(userInterestRecord, UserInterestRecordDTO.class));
	}

	@Hidden
	@GetMapping("/integration/count-alliance-bank-campaign")
	public ResponseEntity<Long> countAllianceBankCampaign(@RequestParam LocalDate dateFrom,
			@RequestParam LocalDate dateTo,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone,
			@RequestParam(required = false) String issuerCode, @RequestParam(required = false) String productType,
			@RequestParam(required = false) UserInterestedSource source) {
		return ResponseEntity.ok(userInterestRecordIntegrationService.countAllianceBankCampaign(dateFrom, dateTo,
				ZoneId.of(timeZone), issuerCode, productType, source));
	}

	@Hidden
	@GetMapping("/integration/{vaultId}/user-interest-record")
	public String findInsurerCodeByVaultId(@PathVariable String vaultId) {
		UserInterestRecordVault data = userInterestRecordVaultService.findByVaultId(vaultId);
		return data.getUserInterestRecord().getIssuerCode();
	}

	@ServiceToServiceAccess
	@GetMapping("/private/detail/{id}")
	public UserInterestRecordRSMViewDTO findUserInterestedDetailId(@PathVariable String id) {
		return userInterestRecordIntegrationService.findById(id);
	}

	@ServiceToServiceAccess
	@PostMapping("/private/pagination")
	public Page<UserInterestRecordRSMPaginationDTO> findRsmLead(@RequestBody RSMLeadRequest request,
			Pageable pageable) {
		return userInterestRecordIntegrationService.rsmLead(pageable, request);
	}

	@ServiceToServiceAccess
	@PostMapping("/private/report")
	public List<UserInterestRecordRSMPaginationDTO> findRsmLeadReport(@RequestBody RSMLeadRequest request) {
		return userInterestRecordIntegrationService.rsmLeadReport(request);
	}

	@ServiceToServiceAccess
	@PutMapping("/private/rsm-info")
	public void updateRsmStatus(@RequestBody @Valid LeadRSMUpdateRequest request) {
		userInterestRecordIntegrationService.updateRsmInfo(request);
	}

	@ServiceToServiceAccess
	@PutMapping("/private/bulk-rsm-info")
	public void bulkUpdateRsmStatus(@RequestBody @Valid LeadRSMUpdateRequest request) {
		userInterestRecordIntegrationService.updateRsmInfo(request);
	}

	@ServiceToServiceAccess
	@GetMapping("/private/application-ids-except-user-ids")
	public Set<String> findAllApplicationIdsExcludingUserIdsIn(@RequestParam Set<String> applicationIds,
			@RequestParam Set<String> userIds) {
		return userInterestRecordIntegrationService.findAllApplicationIdsExcludingUserIdsIn(applicationIds, userIds);
	}

}
