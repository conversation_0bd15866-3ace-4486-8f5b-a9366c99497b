package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Token;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.utilities.general.constant.ClientPlatformType;
import my.com.mandrill.utilities.general.constant.TokenStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TokenRepository extends JpaRepository<Token, String> {

	Optional<Token> findByRefreshTokenAndStatus(String token, TokenStatus tokenStatus);

	Optional<Token> findByIdAndStatus(String token, TokenStatus tokenStatus);

	Page<Token> findByStatus(TokenStatus tokenStatus, Pageable pageable);

	Optional<Token> findByUserAndStatus(User user, TokenStatus tokenStatus);

	List<Token> findAllByUserAndStatus(User user, TokenStatus tokenStatus);

	List<Token> findByUserAndStatusAndPlatform(User user, TokenStatus tokenStatus, ClientPlatformType platform);

	List<Token> findAllByUser(User user);

	void deleteAllByUser(User user);

	@Query("""
					SELECT t FROM Token t
					JOIN FETCH t.user
					JOIN FETCH t.tokenTransactions
					WHERE t.id = :id
			""")
	Optional<Token> findByIdFetchAll(String id);

	@Modifying
	@Query("delete from Token t where t.user.id in :userIds")
	void deleteByUserIds(@Param("userIds") List<String> userIds);

}
