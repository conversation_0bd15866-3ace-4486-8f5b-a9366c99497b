package my.com.mandrill.utilities.core.security;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.core.config.SecurityBaseProperties;
import my.com.mandrill.utilities.core.dto.model.Request;
import my.com.mandrill.utilities.core.filter.CustomBasicAuthenticationFilter;
import my.com.mandrill.utilities.core.filter.PublicAuthenticationFilter;
import my.com.mandrill.utilities.core.security.jwt.JWTConfigurer;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.general.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.oauth2.client.OAuth2LoginConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.util.List;
import java.util.Objects;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class GlobalSecurityConfiguration {

	@Autowired
	private TokenProvider tokenProvider;

	@Autowired(required = false)
	private Customizer<OAuth2LoginConfigurer<HttpSecurity>> oAuth2Customizer;

	@Autowired
	private AuthenticationProvider authenticationProvider;

	@Autowired
	private HandlerExceptionResolver handlerExceptionResolver;

	@Autowired
	private FeatureFlagOutbound featureFlagOutbound;

	@Autowired
	private AccountFeignClient accountFeignClient;

	@Autowired
	private RedisService redisService;

	@Bean
	public AuthenticationManager authenticationManager(HttpSecurity http) throws Exception {
		AuthenticationManagerBuilder authenticationManagerBuilder = http
				.getSharedObject(AuthenticationManagerBuilder.class);
		authenticationManagerBuilder.authenticationProvider(authenticationProvider);
		return authenticationManagerBuilder.build();
	}

	@Bean
	public CustomBasicAuthenticationFilter customBasicAuthenticationFilter(AuthenticationManager authenticationManager,
			SecurityBaseProperties securityBaseProperties) {
		return new CustomBasicAuthenticationFilter(authenticationManager, securityBaseProperties);
	}

	@Bean
	public PublicAuthenticationFilter publicAuthFilter(AccountFeignClient accountFeignClient,
			HandlerExceptionResolver handlerExceptionResolver) {
		List<Request> requests = List.of(new Request("/v1/public/**", HttpMethod.GET),
				new Request("/v1/public/**", HttpMethod.POST), new Request("/v1/public/**", HttpMethod.PUT),
				new Request("/moneyxbiz-integration/**", HttpMethod.GET),
				new Request("/moneyxbiz-integration/**", HttpMethod.POST),
				new Request("/moneyxbiz-integration/**", HttpMethod.DELETE),
				new Request("/moneyxbiz-integration/**", HttpMethod.PUT));
		return new PublicAuthenticationFilter(accountFeignClient, handlerExceptionResolver, requests, redisService);
	}

	@Bean
	public SecurityFilterChain filterChain(HttpSecurity http, AuthenticationManager authenticationManager,
			SecurityBaseProperties securityBaseProperties) throws Exception {
		http.exceptionHandling().authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)).and()
				.headers().frameOptions().disable().and().sessionManagement()
				.sessionCreationPolicy(SessionCreationPolicy.STATELESS).and().csrf().disable().cors()
				.configurationSource(request -> {
					CorsConfiguration configuration = new CorsConfiguration();
					configuration.setAllowedOrigins(List.of("*"));
					configuration.setAllowedMethods(List.of("*"));
					configuration.setAllowedHeaders(List.of("*"));
					configuration.setExposedHeaders(List.of("*"));
					return configuration;
				}).and()
				.authorizeHttpRequests(authorize -> authorize.requestMatchers("/swagger-ui/**").permitAll()
						.requestMatchers("/swagger-resources/**").permitAll().requestMatchers("/v3/api-docs/**")
						.permitAll().requestMatchers("/actuator/**").permitAll()
						.requestMatchers(new AntPathRequestMatcher("/**/private/**")).permitAll()
						.requestMatchers("/sms").permitAll()

						.requestMatchers("/oauth2/**").permitAll().requestMatchers("/login/oauth2/**").permitAll()
						.requestMatchers("/authenticate").permitAll()
						.requestMatchers(HttpMethod.POST, "/authenticate/signature-challenge").permitAll()
						.requestMatchers(HttpMethod.POST, "/authenticate/hash").permitAll()
						.requestMatchers(HttpMethod.POST, "/authenticate/password").permitAll()
						.requestMatchers("/account/register").permitAll()
						.requestMatchers(HttpMethod.GET, "/account/ref-no").permitAll()
						.requestMatchers("/account/reset-password-email").permitAll()
						.requestMatchers("/account/reset-password-mobile").permitAll()
						.requestMatchers(HttpMethod.PUT, "/admin/user/reset-password").permitAll()
						.requestMatchers(HttpMethod.GET, "/admin/user/reset-password").permitAll()
						.requestMatchers(HttpMethod.GET, "/account/request-otp").permitAll()
						.requestMatchers(HttpMethod.POST, "/account/request-otp").permitAll()
						.requestMatchers(HttpMethod.GET, "/account/verify-otp").permitAll()
						.requestMatchers(HttpMethod.POST, "/account/verify-otp").permitAll()
						.requestMatchers("/account/validate-phone").permitAll().requestMatchers("/account/exist-mobile")
						.permitAll().requestMatchers(HttpMethod.GET, "/brand/**").permitAll()
						.requestMatchers(HttpMethod.GET, "/series/**").permitAll()
						.requestMatchers(HttpMethod.GET, "/model/**").permitAll()
						.requestMatchers(HttpMethod.POST, "/enquiry").permitAll()
						.requestMatchers(HttpMethod.GET, "/bid-product/history/**").permitAll()
						.requestMatchers(HttpMethod.GET, "/trade-product/search").permitAll()
						.requestMatchers(HttpMethod.GET, "/trade-product/detail/**").permitAll()
						.requestMatchers(HttpMethod.GET, "/system-config/public/**").permitAll()
						.requestMatchers("/socket").permitAll().requestMatchers("/topic/**").permitAll()
						.requestMatchers(HttpMethod.POST, "/transactions/callback").permitAll()
						.requestMatchers(HttpMethod.POST, "/transactions/response").permitAll()
						.requestMatchers("/upload-transactions/**").permitAll()
						.requestMatchers("/user-advertisements/registration").permitAll()
						.requestMatchers("/user-advertisements/registration/ios").permitAll()
						.requestMatchers("/user-advertisements/consume/ios").permitAll()
						.requestMatchers("/emails/contact-us").permitAll().requestMatchers("/emails").permitAll()
						.requestMatchers("/email-blacklist").permitAll()
						.requestMatchers(HttpMethod.GET, "/global-system-config/public/{code}").permitAll()
						.requestMatchers(HttpMethod.GET, "/global-system-config/integration/{code}").permitAll()
						.requestMatchers(HttpMethod.POST, "/ekyc/submit/result").permitAll()
						.requestMatchers(HttpMethod.POST, "/jobs/status").permitAll()
						.requestMatchers(HttpMethod.GET, "/admin/user/exist").permitAll()
						.requestMatchers(HttpMethod.POST, "/ekyc/submit/reset").permitAll()
						.requestMatchers(HttpMethod.GET, "/global-system-config/version-control").permitAll()
						.requestMatchers(HttpMethod.PUT, "/callback/e-access/visitor").permitAll()
						.requestMatchers(HttpMethod.POST, "/social-media-conversions").permitAll()
						.requestMatchers(HttpMethod.POST, "/user-interested-redirects").permitAll()
						.requestMatchers(HttpMethod.GET, "/platforms/code/*").permitAll()
						.requestMatchers(HttpMethod.POST, "/push-notification-conversions").permitAll()
						.requestMatchers(HttpMethod.PUT, "/pin/reset").permitAll()
						.requestMatchers(HttpMethod.GET, "/user-interest-record/integration/redirect/count").permitAll()
						.requestMatchers(HttpMethod.GET, "/user-interest-record/integration/count").permitAll()
						.requestMatchers(HttpMethod.GET, "/transactions/integration/*/count").permitAll()
						.requestMatchers(HttpMethod.POST, "/survey").permitAll()
						.requestMatchers(HttpMethod.GET, "/survey").permitAll()
						.requestMatchers(HttpMethod.GET, "/m-and-a-interests/integration/count").permitAll()
						.requestMatchers(HttpMethod.GET, "/admin/vouchers/voucher-details/integration/count")
						.permitAll().requestMatchers(HttpMethod.POST, "/account/email-masked").permitAll()
						.requestMatchers(HttpMethod.POST, "/prepaid-topup/callback").permitAll()
						.requestMatchers(HttpMethod.POST, "/geo/detail").permitAll()
						.requestMatchers(HttpMethod.GET, "/telco-operators/product").permitAll()
						.requestMatchers(HttpMethod.POST, "/token/extends").permitAll().anyRequest().authenticated())
				.httpBasic().disable().apply(securityConfigurerAdapter());

		http.addFilterBefore(customBasicAuthenticationFilter(authenticationManager, securityBaseProperties),
				BasicAuthenticationFilter.class);
		http.addFilterBefore(publicAuthFilter(accountFeignClient, handlerExceptionResolver),
				BasicAuthenticationFilter.class);
		if (Objects.nonNull(oAuth2Customizer)) {
			http.oauth2Login(oAuth2Customizer);
		}

		return http.build();
	}

	private JWTConfigurer securityConfigurerAdapter() {
		return new JWTConfigurer(tokenProvider, handlerExceptionResolver, featureFlagOutbound);
	}

}
