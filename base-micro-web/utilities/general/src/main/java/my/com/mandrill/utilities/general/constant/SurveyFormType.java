package my.com.mandrill.utilities.general.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SurveyFormType {

	WELCOME_APP("WELCOME_APP", "Onboarding"), PRODUCT_CC("PRODUCT_CC", "Credit Card"),
	PRODUCT_CASA("PRODUCT_CASA", "CASA"), DELETE_ACCOUNT("DELETE_ACCOUNT", "Delete Account"),
	NPS("NPS", "Net Promoter Score");

	private final String code;

	private final String name;

}
