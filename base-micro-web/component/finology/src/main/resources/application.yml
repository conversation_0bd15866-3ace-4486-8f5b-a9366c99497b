info:
  project:
    version: #project.version#

spring:
  profiles:
    active: dev
  application:
    name: finology-component
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: **************************************************************
    username: moneyx
    password: moneyx
    hikari:
      schema: finology
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
      connection-timeout: 10000
      idle-timeout: 600000
  liquibase:
    default-schema: finology
    change-log: classpath:liquibase/master.xml
  jackson:
    serialization:
      fail-on-empty-beans: false
  elasticsearch:
    uris: [ http://0.0.0.0:9200 ]
    username: elastic
    password: moneyx
  jpa:
    show-sql: false
    properties:
      hibernate:
        default_schema: finology
        format_sql: false
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          lob:
            non_contextual_creation: true
    open-in-view: false
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ReactiveElasticsearchRepositoriesAutoConfiguration

  cache:
    redis:
      time-to-live: 1d
  data:
    redis:
      repositories:
        enabled: false
      timeout: 1000ms
      connect-timeout: 500ms
      host: localhost
      port: 6379
  kafka:
    bootstrap-servers: localhost:9092

server:
  port: 8913

management:
  health:
    elasticsearch:
      enabled: false
  endpoints:
    web:
      exposure:
        include:
          - health
          - info
          - prometheus

##https://cloud.spring.io/spring-cloud-netflix/reference/html/appendix.html
eureka:
  client:
    enabled: true
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
  instance:
    preferIpAddress: true

##https://cloud.spring.io/spring-cloud-openfeign/reference/html/appendix.html
feign:
  client:
    config:
      default:
        loggerLevel: basic # default none | basic | headers | full
        connectTimeout: 10000 # default 10 sec
        readTimeout: 60000 # default 60 sec

security:
  jwt:
    # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 128` on your command line to generate
    base64-secret: NOIUaY1qF6H8wmQJzLWmzk8uJfJZfptuCUW2MBq4bSfobb90cTB9hP49nXiMAVUByeuEF1UhWO1Yj19ABWdZCgPcjzC0Q1Qz9qt9gXU44kLqEDmY7/JoRg5c65j31VnITVvjRSCnxj7eyqv093ETnuGy0QBrUCO624Cx7pk5QAQ=
    token-validity-in-seconds: 86400
    token-validity-in-seconds-for-remember-me: 2592000

file-storage:
  method: local
  local:
    root: ../../files

base:
  internal-api-key: xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ
  request-uri:
    open-api-server: http://localhost:8762/finology-component
  cache:
    ehcache:
      time-to-live-seconds: 30
      max-entries: 100
  admin-email: <EMAIL>

integration-finology:
  finology-url: https://uat-insurance-api.finology.com.my
  api-key: 129f78410aa8448f893fff22cfa905a1
  max-try-times: 3

integration-loanplus:
  url: https://uat-apix.loanplus.io
  authorization-token: 5b62afc0-fdbf-4e72-8200-5def46b7cf29
  enabled: true
  project-id: 310
  salesperson-id: 1000
  property-unit-type-id: 588

securities:
  crypto:
    key: NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc
    iv: 57ad289f-3def-4c

logging:
  pattern:
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"