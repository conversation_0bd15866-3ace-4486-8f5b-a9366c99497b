package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InterestRedirectPublicRequest implements Serializable {

	@NotNull
	private EntityName entityName;

	@NotBlank
	private String url;

	private UserInterestedSource source;

	private String campaignId;

	private String termsAndConditionVersion;

	private String productId;

}
