package my.com.mandrill.component.controller.moneyxbizintegration.admin;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.PointWithdrawalStatus;
import my.com.mandrill.component.domain.PointWithdrawal;
import my.com.mandrill.component.dto.request.BulkUpdateWithdrawalStatusRequest;
import my.com.mandrill.component.dto.request.PointWithdrawalSearchRequest;
import my.com.mandrill.component.dto.request.UpdateWithdrawalStatusRequest;
import my.com.mandrill.component.dto.response.PointWithdrawalResponse;
import my.com.mandrill.component.service.PointWithdrawalIntegrationService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import my.com.mandrill.utilities.general.constant.PointWithdrawalDateType;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("moneyxbiz-integration/admin/point-withdrawal")
public class MoneyXBizIntegrationAdminPointWithdrawalController {

	private final PointWithdrawalIntegrationService pointWithdrawalIntegrationService;

	@PublicAuth
	@SecurityRequirements
	@GetMapping("pagination/all")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public Page<PointWithdrawalResponse> getAllWithdrawalManagements(@RequestParam(required = false) String search,
			@RequestParam(required = false) PointWithdrawalStatus status,
			@RequestParam(required = false) PointWithdrawalDateType dateType,
			@RequestParam(required = false) LocalDate dateFrom, @RequestParam(required = false) LocalDate dateTo,
			Pageable pageable) {

		return pointWithdrawalIntegrationService.getAllPointWithdrawal(pageable,
				PointWithdrawalSearchRequest.builder().search(search).status(status).dateFrom(dateFrom).dateTo(dateTo)
						.dateType(dateType).source(SourceSystemEnum.MXBIZ).build());

	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("/{applicationId}")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public ResponseEntity<PointWithdrawalResponse> findByApplicationId(@PathVariable String applicationId) {
		PointWithdrawal pointWithdrawal = pointWithdrawalIntegrationService.getPointWithdrawalByRefNo(applicationId);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toPointWithdrawalResponse(pointWithdrawal));
	}

	@PublicAuth
	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping(value = "withdrawal-management/{applicationId}")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_UPDATE_API)")
	public void updateWithdrawalStatusByApplicationId(@PathVariable String applicationId,
			@Valid @RequestBody UpdateWithdrawalStatusRequest updateWithdrawalStatusRequest) {
		pointWithdrawalIntegrationService.updateWithdrawalStatusByRefNoAndSendPushNotification(applicationId,
				updateWithdrawalStatusRequest);
	}

	@PublicAuth
	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping(value = "withdrawal-management/bulk")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_UPDATE_API)")
	public void bulkUpdateWithdrawalStatusByApplicationId(
			@Valid @RequestBody BulkUpdateWithdrawalStatusRequest bulkUpdateWithdrawalStatusRequest) {
		pointWithdrawalIntegrationService
				.bulkUpdateWithdrawalStatusByRefNoAndSendPushNotification(bulkUpdateWithdrawalStatusRequest);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("withdrawal-management/export")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public List<PointWithdrawalResponse> getAllWithdrawalManagementForExport(
			@RequestParam(required = false) String search, @RequestParam(required = false) PointWithdrawalStatus status,
			@RequestParam(required = false) PointWithdrawalDateType dateType,
			@RequestParam(required = false) LocalDate dateFrom, @RequestParam(required = false) LocalDate dateTo) {
		return pointWithdrawalIntegrationService
				.getAllPointWithdrawalBiz(PointWithdrawalSearchRequest.builder().search(search).status(status)
						.dateFrom(dateFrom).dateTo(dateTo).source(SourceSystemEnum.MXBIZ).dateType(dateType).build());
	}

	@PublicAuth
	@SecurityRequirements
	@PostMapping("withdrawal-management/export")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_CREATE_API)")
	public List<PointWithdrawalResponse> getAllWithdrawalManagementForExportInRefNo(@RequestBody List<String> refNo) {
		return pointWithdrawalIntegrationService.getAllPointWithdrawalInRefNo(refNo);
	}

}
