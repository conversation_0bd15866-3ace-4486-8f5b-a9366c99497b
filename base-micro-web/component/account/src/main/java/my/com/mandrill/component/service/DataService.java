package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.*;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface DataService {

	List<BusinessNature> findAllBusinessNatureByActiveTrue();

	List<EducationLevel> findAllEducationLevel(Sort sort);

	List<EmploymentType> findAllEmploymentType();

	List<Nationality> findAllNationality();

	List<OccupationGroup> findAllOccupationGroup();

	List<Interest> findAllInterest();

	List<FinancialGoal> findAllFinancialGoal();

	List<Segment> findAllSegment();

	List<ExpenseType> findAllExpenseType();

}
