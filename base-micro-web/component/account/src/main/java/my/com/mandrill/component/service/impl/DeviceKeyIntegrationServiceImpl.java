package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.AccountSystemConfigurationService;
import my.com.mandrill.component.service.DeviceKeyIntegrationService;
import my.com.mandrill.component.service.DeviceKeyService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.CryptoUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.security.PublicKey;
import java.time.Instant;
import java.util.Base64;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceKeyIntegrationServiceImpl implements DeviceKeyIntegrationService {

	private final AccountSystemConfigurationService accountSystemConfigurationService;

	private final DeviceKeyService deviceKeyService;

	private final ThreadPoolTaskScheduler taskScheduler;

	private final JSONUtil jsonUtil;

	@Override
	public SignatureChallenge registerStart(DeviceKey request, AppUser user) {
		User tempUser = jsonUtil.convertValue(request, User.class);
		Integer maximumNumberOfDevicesPerUser = accountSystemConfigurationService
				.getMaximumNumberOfDevicesPerUser(tempUser);

		DeviceKey deviceKey = deviceKeyService.findOptionalDeviceKeyByDeviceIdAndUser(request.getDeviceId(), user)
				.orElseGet(() -> {
					if (deviceKeyService.countByUser(user) + 1 > maximumNumberOfDevicesPerUser) {
						deviceKeyService.deleteOldestDeviceKey(user);
					}
					request.setUser(user);
					return deviceKeyService.saveDeviceKey(request);
				});

		return createSignatureChallenge(deviceKey);
	}

	@Override
	public SignatureChallenge registerFinish(SignatureChallenge request, AppUser user) {
		DeviceKey deviceKey = deviceKeyService.findDeviceKeyByDeviceIdAndUser(request.getDeviceKey().getDeviceId(),
				user);
		SignatureChallenge signatureChallenge = deviceKeyService.findSignatureChallengeByIdAndDeviceKey(request.getId(),
				deviceKey);
		PublicKey publicKey = CryptoUtil.getPublicKey(request.getDeviceKey().getPublicKey());

		return signatureVerification(request.getSignature(), deviceKey, signatureChallenge, publicKey);
	}

	@Override
	public SignatureChallenge assertionStart(DeviceKey request, AppUser user) {
		DeviceKey deviceKey = deviceKeyService.findDeviceKeyByDeviceIdAndUser(request.getDeviceId(), user);
		if (StringUtils.isBlank(deviceKey.getPublicKey())) {
			throw new BusinessException(ErrorCodeEnum.DEVICE_REGISTRATION_NOT_FINISHED);
		}
		return createSignatureChallenge(deviceKey);
	}

	@Override
	public SignatureChallenge assertionFinish(SignatureChallenge request, AppUser user) {
		DeviceKey deviceKey = deviceKeyService.findDeviceKeyByDeviceIdAndUser(request.getDeviceKey().getDeviceId(),
				user);
		SignatureChallenge signatureChallenge = deviceKeyService.findSignatureChallengeByIdAndDeviceKey(request.getId(),
				deviceKey);
		PublicKey publicKey = CryptoUtil.getPublicKeyByX509(deviceKey.getPublicKey());

		return signatureVerification(request.getSignature(), deviceKey, signatureChallenge, publicKey);
	}

	@Override
	public SignatureChallenge createSignatureChallenge(DeviceKey deviceKey) {
		User tempUser = jsonUtil.convertValue(deviceKey.getUser(), User.class);
		Long expiredTime = accountSystemConfigurationService.getSignatureChallengeExpiredTime(tempUser);

		SignatureChallenge signatureChallenge = new SignatureChallenge();
		signatureChallenge.setDeviceKey(deviceKey);
		signatureChallenge.setValue(CryptoUtil.generateSecretKey());
		signatureChallenge.setExpired(false);
		signatureChallenge.setVerified(false);
		SignatureChallenge persisted = deviceKeyService.saveSignatureChallenge(signatureChallenge);

		// TODO: use scheduler consumer to create schedule
		taskScheduler.schedule(() -> expireSignatureChallenge(persisted.getId()),
				Instant.now().plusSeconds(expiredTime));

		return persisted;
	}

	@Override
	public void expireSignatureChallenge(String id) {
		Optional<SignatureChallenge> signatureChallenge = deviceKeyService.findSignatureChallengeById(id);
		if (signatureChallenge.isPresent() && (Boolean.FALSE.equals(signatureChallenge.get().getVerified()))) {
			signatureChallenge.get().setExpired(true);
			deviceKeyService.saveSignatureChallenge(signatureChallenge.get());
			log.info("signature challenge [{}] expired", signatureChallenge.get().getId());
		}
	}

	private SignatureChallenge signatureVerification(String signature, DeviceKey deviceKey,
			SignatureChallenge signatureChallenge, PublicKey publicKey) {
		if (signatureChallenge.getExpired() || signatureChallenge.getVerified()) {
			throw new BusinessException(ErrorCodeEnum.SIGNATURE_CHALLENGE_EXPIRED);
		}

		if (!CryptoUtil.verifySignature(signatureChallenge.getValue().getBytes(Charset.defaultCharset()), publicKey,
				Base64.getDecoder().decode(signature))) {
			throw new BusinessException(ErrorCodeEnum.SIGNATURE_NOT_VERIFIED);
		}

		deviceKey.setPublicKey(Base64.getEncoder().encodeToString(publicKey.getEncoded()));
		deviceKeyService.saveDeviceKey(deviceKey);
		signatureChallenge.setSignature(signature);
		signatureChallenge.setVerified(true);

		return deviceKeyService.saveSignatureChallenge(signatureChallenge);
	}

}
