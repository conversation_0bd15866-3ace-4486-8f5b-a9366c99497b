package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Authority;
import my.com.mandrill.component.domain.Permission;
import my.com.mandrill.component.domain.PublicAuthentication;
import my.com.mandrill.component.dto.model.PublicAuthenticationDTO;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.PublicAuthenticationRepository;
import my.com.mandrill.component.service.PublicAuthenticationService;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.util.CryptoUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class PublicAuthenticationServiceImpl implements PublicAuthenticationService {

	private final PublicAuthenticationRepository publicAuthenticationRepository;

	@Override
	@Transactional
	@Caching(evict = {
			@CacheEvict(value = CacheKey.PUBLIC_AUTHENTICATION_CACHE_KEY, key = "#publicAuthentication.identifier"), })
	public PublicAuthentication save(PublicAuthentication publicAuthentication) {
		return publicAuthenticationRepository.save(publicAuthentication);
	}

	@Override
	@Cacheable(cacheNames = CacheKey.PUBLIC_AUTHENTICATION_CACHE_KEY, key = "#identifier")
	public Optional<PublicAuthentication> findOptionalByIdentifier(String identifier) {
		return publicAuthenticationRepository.findByIdentifier(identifier);
	}

	@Override
	public List<PublicAuthentication> findAll() {
		return publicAuthenticationRepository.findAll();
	}

	@Override
	public PublicAuthentication findById(String id) {
		return publicAuthenticationRepository.findById(id).orElseThrow(ExceptionPredicate.publicAuthNotFound(id));
	}

	@Override
	public boolean existsByIdentifier(String identifier) {
		return publicAuthenticationRepository.existsByIdentifier(identifier);
	}

	private final JSONUtil jsonUtil;

	@Transactional
	@Override
	public PublicAuthenticationDTO findPublicKeyByIdentifier(String identifier) {
		PublicAuthentication authentication = publicAuthenticationRepository.findByIdentifier(identifier)
				.orElseThrow(ExceptionPredicate.publicAuthNotFound(identifier));

		if (authentication.getSecretKey() == null) {
			String secretKey = CryptoUtil.generateSecretKey();
			authentication.setSecretKey(secretKey);
			publicAuthenticationRepository.save(authentication);
		}
		List<String> permissions = authentication.getAuthorities().stream()
				.filter(authority -> authority.getInstitution().getActive() && authority.getActive())
				.map(Authority::getPermissions).filter(Objects::nonNull).flatMap(Collection::stream)
				.map(Permission::getCode).toList();
		PublicAuthenticationDTO result = MapStructConverter.MAPPER.toPublicAuthenticationDTO(authentication);
		result.setPermissions(permissions);
		return result;
	}

}
