package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExpenseDTO;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.utilities.feign.dto.CountryDTO;
import my.com.mandrill.utilities.feign.dto.StateDTO;

public interface PopulateService {

	void populateCountry(User user);

	void populateState(User user);

	void populateAuthoritiesByInstitution(User user, final String institutionId);

	void populateIncomeReminder(IncomeDTO incomeDTO);

	void populateExpenseReminder(ExpenseDTO expenseDto);

	CountryDTO getCountryById(String countryId);

	StateDTO getStateById(String stateId);

}
