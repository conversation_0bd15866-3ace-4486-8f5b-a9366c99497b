package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NPSResultEnum;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.SurveyFormActiveDTO;
import my.com.mandrill.component.dto.model.SurveyQuestionInfoDTO;
import my.com.mandrill.component.dto.model.SurveyReportByActiveDTO;
import my.com.mandrill.component.dto.model.SurveyReportDTO;
import my.com.mandrill.component.dto.request.SurveyEventRequest;
import my.com.mandrill.component.dto.request.SurveyHeaderServiceRequest;
import my.com.mandrill.component.dto.response.SurveySalaryRangeDataResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.*;
import my.com.mandrill.component.service.SurveyService;
import my.com.mandrill.utilities.feign.dto.IncomeDTO;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class SurveyServiceImpl implements SurveyService {

	private final SurveyFormRepository surveyFormRepository;

	private final SurveyHeaderRepository surveyHeaderRepository;

	private final SurveyDetailRepository surveyDetailRepository;

	private final SurveyQuestionRepository surveyQuestionRepository;

	private final SurveyAnswerRepository surveyAnswerRepository;

	private final UserSurveyRepository userSurveyRepository;

	private final KafkaTemplate<String, Object> kafkaTemplate;

	private final JSONUtil jsonUtil;

	@Override
	@Transactional
	public SurveyHeader saveSurveyHeader(SurveyHeader surveyHeader) {
		return surveyHeaderRepository.save(surveyHeader);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public SurveyHeader saveSurveyHeaderV2(SurveyHeaderServiceRequest request) {
		if (SurveyFormType.NPS.equals(request.getSurveyForm().getFormType())
				&& SurveyAction.DISMISSED.equals(request.getAction())) {
			processNpsSurvey(request, null);
			return null;
		}
		SurveyHeader surveyHeader = createNewSurveyHeader(request);
		surveyHeader.setSurveyDetails(request.getSurveyDetails().stream().map(surveyDetailRequest -> {
			SurveyDetail surveyDetail = new SurveyDetail();
			surveyDetail.setSurveyAnswer(this.findSurveyAnswerById(surveyDetailRequest.getSurveyAnswer()));
			surveyDetail.setSurveyQuestion(this.findSurveyQuestionById(surveyDetailRequest.getSurveyQuestion()));
			surveyDetail.setSurveyAnswerInput(surveyDetailRequest.getSurveyAnswerInput());
			surveyDetail.setSurveyHeader(surveyHeader);
			return surveyDetail;
		}).toList());
		SurveyHeader savedSurveyHeader = surveyHeaderRepository.save(surveyHeader);

		if (SurveyFormType.NPS.equals(savedSurveyHeader.getSurveyForm().getFormType())) {
			processNpsSurvey(request, surveyHeader);
		}
		return savedSurveyHeader;
	}

	private SurveyHeader createNewSurveyHeader(SurveyHeaderServiceRequest request) {
		SurveyHeader surveyHeader = new SurveyHeader();
		surveyHeader.setUserEmail(request.getCurrentUser().getEmail());
		surveyHeader.setUserFullname(request.getCurrentUser().getFullName());
		surveyHeader.setUserId(request.getCurrentUser().getId());
		surveyHeader.setUserNric(request.getCurrentUser().getNric());
		surveyHeader.setUserPhoneNumber(request.getCurrentUser().getPhoneNumber());
		surveyHeader.setUserRef(request.getCurrentUser().getRefNo());
		surveyHeader.setSurveyForm(request.getSurveyForm());
		surveyHeader.setUserAge(request.getCurrentUser().getAge());
		surveyHeader.setMaritalStatus(request.getCurrentUser().getMaritalStatus());
		surveyHeader.setCityId(request.getCityId());
		surveyHeader.setCityName(request.getCityName());
		surveyHeader.setPostcode(request.getPostcode());
		surveyHeader.setIncome(Optional.ofNullable(request.getCurrentUser().getIncome())
				.map(IncomeDTO::getMonthlyIncomeAmount).orElse(null));
		surveyHeader.setDevice(request.getDevice());
		return surveyHeader;
	}

	private void processNpsSurvey(SurveyHeaderServiceRequest request, SurveyHeader surveyHeader) {
		String userId = request.getCurrentUser().getId();
		SurveyFormType formType = request.getSurveyForm().getFormType();
		userSurveyRepository.findByUserIdAndSurveyType(userId, formType)
				.filter(userSurvey -> Instant.now().isBefore(userSurvey.getNextSurveyDate())).ifPresent(userSurvey -> {
					throw new BusinessException(ErrorCodeEnum.CANNOT_SUBMIT_SURVEY_YET);
				});

		SurveyEventRequest.SurveyEventRequestBuilder eventRequestBuilder = SurveyEventRequest.builder().userId(userId)
				.action(request.getAction()).formType(formType).surveyDate(LocalDate.now());

		if (SurveyAction.SUBMITTED.equals(request.getAction())) {
			eventRequestBuilder.result(calculateNpsResult(request, surveyHeader));
		}

		kafkaTemplate.send(KafkaTopic.SURVEY_EVENT_TOPIC, userId,
				jsonUtil.convertToString(eventRequestBuilder.build()));
	}

	private NPSResultEnum calculateNpsResult(SurveyHeaderServiceRequest request, SurveyHeader surveyHeader) {
		SurveyDetail surveyDetailSlider = surveyHeader.getSurveyDetails().stream().filter(
				surveyDetail -> SurveyAnswerType.MCQ_SLIDER.equals(surveyDetail.getSurveyQuestion().getAnswerType()))
				.findFirst().orElse(null);
		if (Objects.isNull(surveyDetailSlider)) {
			return null;
		}

		int answer = Integer.parseInt(surveyDetailSlider.getSurveyAnswerInput());
		NPSResultEnum npsResult = null;
		if (answer > request.getPassiveUpperLimit() && answer <= request.getPromoterUpperLimit()) {
			npsResult = NPSResultEnum.PROMOTERS;
		}
		else if (answer > request.getDetractorUpperLimit() && answer <= request.getPassiveUpperLimit()) {
			npsResult = NPSResultEnum.PASSIVE;
		}
		else if (answer >= 0 && answer <= request.getDetractorUpperLimit()) {
			npsResult = NPSResultEnum.DETRACTOR;
		}

		return npsResult;
	}

	@Override
	@Transactional
	public SurveyHeader updateToBeContacted(String surveyHeaderId, boolean toBeContacted) {
		SurveyHeader surveyHeader = surveyHeaderRepository.findById(surveyHeaderId)
				.orElseThrow(ExceptionPredicate.surveyHeaderNotFound(surveyHeaderId));

		surveyHeader.setToBeContacted(toBeContacted);
		return surveyHeaderRepository.save(surveyHeader);
	}

	@Override
	@Transactional
	public SurveyDetail saveSurveyDetail(SurveyDetail surveyDetail) {
		return surveyDetailRepository.save(surveyDetail);
	}

	@Override
	public SurveyForm findById(String id) {
		return surveyFormRepository.findById(id).orElseThrow(ExceptionPredicate.surveyFormNotFound(id));
	}

	@Override
	public SurveyQuestion findSurveyQuestionById(String id) {
		return surveyQuestionRepository.findById(id).orElseThrow(ExceptionPredicate.surveyQuestionNotFound(id));
	}

	@Override
	public SurveyAnswer findSurveyAnswerById(String id) {
		return surveyAnswerRepository.findById(id).orElseThrow(ExceptionPredicate.surveyAnswerNotFound(id));
	}

	@Override
	public Page<SurveyForm> findAll(Pageable pageable, String name) {
		return this.surveyFormRepository.findByNameContainsIgnoreCaseAndDeletedFalse(Strings.isBlank(name) ? "" : name,
				pageable);
	}

	@Override
	@Transactional
	public SurveyForm updateSurveyForm(SurveyForm surveyForm) {
		SurveyForm result = this.surveyFormRepository.save(surveyForm);
		// put in the same transaction to ensure only 1 form active at a time
		inactivateOtherForm(result);

		return result;
	}

	@Override
	@Transactional
	public SurveyForm saveAllSurveyForm(SurveyForm surveyForm) {
		SurveyForm form = this.surveyFormRepository.save(surveyForm);
		// put in the same transaction to ensure only 1 form active at a time
		inactivateOtherForm(form);

		return form;
	}

	@Override
	@Transactional
	public void deleteById(String id) {

		this.surveyFormRepository.deleteSurveyFormById(id);

	}

	@Override
	public SurveyForm findByActive(boolean active, SurveyFormType formType) {
		return this.surveyFormRepository
				.findByActiveAndDeletedFalseAndStatusAndFormType(active, SurveyFormStatus.CONFIRM, formType)
				.orElseThrow(ExceptionPredicate.activeFormNotFound());
	}

	@Override
	public boolean existSurveyHeaderByUserid(String userId) {
		return this.surveyHeaderRepository.existsByUserId(userId);
	}

	@Override
	public SurveyQuestionInfoDTO findSurveyQuestionInfoById(String id) {
		return this.surveyQuestionRepository.findSurveyQuestionInfoById(id);
	}

	private void inactivateOtherForm(SurveyForm surveyForm) {
		// if form is active change all to inactive
		if (surveyForm.isActive()) {
			this.surveyFormRepository.updateSurveyFormByIdAAndFormType(surveyForm.getId(), surveyForm.getFormType());
		}
	}

	@Override
	public List<SurveyReportDTO> getSurveyReportData(Instant dateStart, Instant dateEnd, Set<String> formName) {
		return surveyDetailRepository.getSurveyReportData(dateStart, dateEnd, formName);
	}

	@Override
	public Page<SurveyReportDTO> getSurveyReportData(Instant dateStart, Instant dateEnd, Set<String> formNameSet,
			Pageable pageable) {
		return surveyDetailRepository.getSurveyReportData(dateStart, dateEnd, formNameSet, pageable);
	}

	@Override
	public List<SurveyReportByActiveDTO> getSurveyReportDataByActive(Instant dateStart, Instant dateEnd,
			Set<String> formName) {
		return surveyAnswerRepository.getSurveyReportDataByActive(dateStart, dateEnd, formName);
	}

	@Override
	public List<SurveyForm> getAllSurveyFormByStatus(SurveyFormStatus status) {
		return surveyFormRepository.findByStatus(status);
	}

	@Override
	public boolean existsByNameAllIgnoreCase(String name) {
		return surveyFormRepository.existsByNameAllIgnoreCaseAndDeletedFalse(name);
	}

	@Override
	public List<SurveyFormActiveDTO> getSurveyFormCurrentActiveFlagByName(Set<String> name) {
		return surveyFormRepository.getSurveyFormCurrentActiveFlagByName(name);
	}

	@Override
	public List<String> findAllSurveyHeaderUserIds() {
		return surveyHeaderRepository.findAllSurveyHeaderUserIds();
	}

	@Override
	public SurveySalaryRangeDataResponse findSalaryRangeData(String userId, List<String> salaryRangeQuestionIds) {
		return surveyDetailRepository.findSalaryRangeData(userId, salaryRangeQuestionIds);
	}

	@Override
	public SurveyDetail findSurveyDetailById(String id) {
		return surveyDetailRepository.findById(id).orElseThrow(ExceptionPredicate.surveyDetailNotFound(id));
	}

	@Override
	public Optional<SurveyHeader> findByFormAndUser(String formId, String userId) {
		return surveyHeaderRepository.findFirstBySurveyFormIdAndUserIdOrderByCreatedDateDesc(formId, userId);
	}

	@Override
	public List<SurveyDetail> findSurveyDetailByHeaderId(String headerId) {
		return surveyDetailRepository.findBySurveyHeaderId(headerId);
	}

	@Override
	public UserSurvey findUserSurveyByUserIdAndSurveyFormType(SurveyFormType surveyFormType) {
		return userSurveyRepository.findByUserIdAndSurveyType(SecurityUtil.currentUserId(), surveyFormType)
				.orElseThrow(ExceptionPredicate.userSurveyNotFound(surveyFormType));
	}

	@Override
	public Page<SurveyHeader> findBySurveyFormFormType(Pageable pageable, SurveyFormType surveyFormType,
			Instant dateFrom, Instant dateTo) {
		return surveyHeaderRepository.findBySurveyFormFormType(pageable, surveyFormType, dateFrom, dateTo);
	}

	@Override
	public List<SurveyHeader> findBySurveyFormFormType(SurveyFormType surveyFormType, Instant dateFrom,
			Instant dateTo) {
		return surveyHeaderRepository.findBySurveyFormFormType(surveyFormType, dateFrom, dateTo);
	}

	@Override
	public Optional<SurveyHeader> findSurveyHeaderByFormTypeAndUserId(SurveyFormType formType, String userId) {
		return surveyHeaderRepository.findFirstBySurveyFormFormTypeAndUserIdOrderByCreatedDateDesc(formType, userId);
	}

}
