package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Insurance;
import my.com.mandrill.component.dto.request.ObjectRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.InsuranceRepository;
import my.com.mandrill.component.service.InsuranceService;
import my.com.mandrill.utilities.feign.dto.DetailedNetWorthDTO;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.VaultLinkDTO;
import my.com.mandrill.utilities.feign.dto.request.UpdateVehicleInsuranceRenewalRequest;
import my.com.mandrill.utilities.general.constant.InsuranceTypeEnum;
import my.com.mandrill.utilities.general.constant.ReminderFrequency;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.data.domain.Sort;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class InsuranceServiceImpl implements InsuranceService {

	private final InsuranceRepository insuranceRepository;

	@Override
	public Insurance findById(String id, String userId) {
		return insuranceRepository.findByIdAndUserId(id, userId).orElseThrow(ExceptionPredicate.insuranceNotFound(id));
	}

	@Transactional
	@Override
	public Insurance save(Insurance insurance) {
		return insuranceRepository.save(insurance);
	}

	@Override
	public List<Insurance> findAll(String userId, Sort sort) {
		return insuranceRepository.findByUserId(userId, sort);
	}

	@Transactional
	@Override
	public void delete(Insurance insurance) {
		insuranceRepository.delete(insurance);
	}

	@Override
	public Insurance findByTypeAndEntityIdAndUser(InsuranceTypeEnum type, String entityId, String userId) {
		return insuranceRepository.findByUserIdAndTypeAndEntityId(userId, type.getCode(), entityId)
				.orElseThrow(ExceptionPredicate.insuranceNotFound(type, entityId));
	}

	@Override
	public long count(String userId) {
		return insuranceRepository.countByUserId(userId);
	}

	@Override
	public List<String> findAllEntityIdByUserIdAndTypeAndEntityIdNotNull(String userId, InsuranceTypeEnum type) {
		return insuranceRepository.findAllEntityIdByUserIdAndTypeAndEntityIdIsNotNull(userId, type.getCode());
	}

	@Override
	public void linkVault(VaultLinkDTO vaultLinkDTO, Insurance existingInsurance) {
		existingInsurance.setAttachmentGroupId(vaultLinkDTO.getAttachmentGroupId());
		save(existingInsurance);
	}

	@Override
	public Insurance findByAttachmentGroupId(String attachmentGroupId, String userId) {
		return insuranceRepository.findByAttachmentGroupIdAndUserId(attachmentGroupId, userId);
	}

	@Override
	public List<Insurance> findByAttachmentGroupIdNull(String userId) {
		return insuranceRepository.findByAttachmentGroupIdNullAndUserId(userId);
	}

	@Override
	public boolean existsByUserIdAndAttachmentGroupId(String userId, @NonNull String attachmentGroupId) {
		return insuranceRepository.existsByUserIdAndAttachmentGroupId(userId, attachmentGroupId);
	}

	@Override
	public NetWorthDTO calculateNetWorthByUserId(String userId, boolean isRevampVersion) {
		BigDecimal totalAsset = new BigDecimal(0);
		BigDecimal totalLiabilities = new BigDecimal(0);

		List<String> insuranceTypeEnums = new ArrayList<>();
		if (isRevampVersion) { // for net-worth calculation on revamp version, we'll take
								// all types
			insuranceTypeEnums = Arrays.stream(InsuranceTypeEnum.values()).map(InsuranceTypeEnum::getCode).toList();
		}
		else { // this is the existing types pre-revamp
			insuranceTypeEnums.add(InsuranceTypeEnum.PERSONAL_ACCIDENT.getCode());
			insuranceTypeEnums.add(InsuranceTypeEnum.LIFE.getCode());
		}

		List<Insurance> insuranceListPersonalIncident = insuranceRepository.findByUserIdAndTypeIn(userId,
				insuranceTypeEnums);
		for (Insurance insurance : insuranceListPersonalIncident) {
			if (insurance.getCoverageLimit() != null) {
				BigDecimal coverageLimit = new BigDecimal(insurance.getCoverageLimit());
				totalAsset = totalAsset.add(coverageLimit);
			}
			// on revamp, we'll need to take the premium to be displayed as recurring
			// payment
			// for simplicity's sake, we'll use the existing unused liabilities attribute
			if (isRevampVersion && insurance.getPremium() != null
					&& ReminderFrequency.MONTHLY.equals(insurance.getPaymentFrequency())) {
				BigDecimal premium = new BigDecimal(insurance.getPremium());
				totalLiabilities = totalLiabilities.add(premium);
			}
		}
		NetWorthDTO netWorthDTO = new NetWorthDTO();
		netWorthDTO.setAssets(totalAsset);
		netWorthDTO.setLiabilities(totalLiabilities);
		return netWorthDTO;
	}

	@Override
	public List<DetailedNetWorthDTO> extendedNetWorthByUserId(String userId) {
		List<Insurance> insurances = insuranceRepository.findByUserId(userId, Sort.unsorted());

		Map<NetWorthType, BigDecimal> totalMonthlyInstallmentByInsuranceType = new EnumMap<>(NetWorthType.class);
		Map<NetWorthType, BigDecimal> totalCoverageByInsuranceType = new EnumMap<>(NetWorthType.class);
		Map<String, NetWorthType> netWorthTypeMap = Map.ofEntries(
				Map.entry(InsuranceTypeEnum.MEDICAL_HEALTH.getCode(),
						NetWorthType.MEDICAL_HEALTH_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.LIFE.getCode(), NetWorthType.LIFE_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.PROPERTY.getCode(), NetWorthType.PROPERTY_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.VEHICLE.getCode(), NetWorthType.VEHICLE_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.TRAVEL.getCode(), NetWorthType.TRAVEL_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.PERSONAL_ACCIDENT.getCode(),
						NetWorthType.PERSONAL_ACCIDENT_INSURANCE_MONTHLY_REPAYMENT),
				Map.entry(InsuranceTypeEnum.OTHERS.getCode(), NetWorthType.OTHERS_INSURANCE_MONTHLY_REPAYMENT));
		for (Insurance insurance : insurances) {
			if (netWorthTypeMap.containsKey(insurance.getType())
					&& ReminderFrequency.MONTHLY.equals(insurance.getPaymentFrequency())
					&& Objects.nonNull(insurance.getPremium())) {
				totalMonthlyInstallmentByInsuranceType.merge(netWorthTypeMap.get(insurance.getType()),
						new BigDecimal(insurance.getPremium()), BigDecimal::add);
			}
			if (netWorthTypeMap.containsKey(insurance.getType()) && Objects.nonNull(insurance.getCoverageLimit())) {
				totalCoverageByInsuranceType.merge(netWorthTypeMap.get(insurance.getType()),
						new BigDecimal(insurance.getCoverageLimit()), BigDecimal::add);
			}
		}
		List<DetailedNetWorthDTO> result = new ArrayList<>();
		result.addAll(totalMonthlyInstallmentByInsuranceType.entrySet().stream()
				.map(k -> DetailedNetWorthDTO.builder().netWorthType(k.getKey()).value(k.getValue())
						.valueType(DetailedNetWorthDTO.ValueType.RECURRING_REPAYMENT).build())
				.toList());

		result.addAll(totalCoverageByInsuranceType.entrySet().stream()
				.map(k -> DetailedNetWorthDTO.builder().netWorthType(k.getKey()).value(k.getValue())
						.valueType(DetailedNetWorthDTO.ValueType.INSURANCE_COVERAGE).build())
				.toList());

		return result;
	}

	public Optional<Insurance> findByTypeAndEntityIdAndUserOptional(InsuranceTypeEnum type, String entityId,
			String userId) {
		return insuranceRepository.findByUserIdAndTypeAndEntityId(userId, type.getCode(), entityId);
	}

	@Override
	public List<Insurance> findDetached(String userId, InsuranceTypeEnum type) {
		return insuranceRepository.findByUserIdAndEntityIdAndType(userId, null, type.getCode());
	}

	@Override
	@Transactional
	public void attachEntity(ObjectRequest request, String id, String userId) {
		Insurance insurance = findById(id, userId);

		List<Insurance> insurances = insuranceRepository.findByUserIdAndEntityIdAndType(userId, request.getId(),
				insurance.getType());
		if (!insurances.isEmpty()) {
			throw new BusinessException(ErrorCodeEnum.ENTITY_HAS_BEEN_ATTACHED);
		}
		insurance.setEntityId(request.getId());
		insuranceRepository.save(insurance);
	}

	@Override
	@Transactional
	public Insurance updateInsuranceRoadTaxFinology(
			UpdateVehicleInsuranceRenewalRequest updateVehicleInsuranceRenewalRequest) {
		Insurance insurance = insuranceRepository
				.findByUserIdAndTypeAndEntityId(updateVehicleInsuranceRenewalRequest.getUserId(),
						InsuranceTypeEnum.VEHICLE.getCode(), updateVehicleInsuranceRenewalRequest.getVehicleId())
				.orElseThrow(ExceptionPredicate.insuranceNotFound(InsuranceTypeEnum.VEHICLE,
						updateVehicleInsuranceRenewalRequest.getVehicleId()));
		insurance.setRenewalDate(updateVehicleInsuranceRenewalRequest.getRenewalDate());
		return insuranceRepository.save(insurance);
	}

	@Override
	public List<Insurance> findExpiring(InsuranceTypeEnum insuranceTypeEnum) {
		LocalDate currentDate = LocalDate.ofInstant(Instant.now(), TimeConstant.DEFAULT_ZONE_ID);
		LocalDate startDate = currentDate.minusMonths(3);
		LocalDate endDate = currentDate.plusMonths(2);

		return insuranceRepository.findByTypeAndRenewalDateIsAfterAndRenewalDateIsBeforeAndEntityIdIsNotNull(
				insuranceTypeEnum.getCode(), startDate, endDate);
	}

}
