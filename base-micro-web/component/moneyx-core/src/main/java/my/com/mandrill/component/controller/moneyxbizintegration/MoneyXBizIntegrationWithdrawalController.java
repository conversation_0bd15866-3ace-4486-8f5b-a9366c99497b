package my.com.mandrill.component.controller.moneyxbizintegration;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.service.PointWithdrawalIntegrationService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("moneyxbiz-integration/withdrawal")
public class MoneyXBizIntegrationWithdrawalController {

	private final PointWithdrawalIntegrationService pointWithdrawalIntegrationService;

	@PublicAuth
	@SecurityRequirements
	@GetMapping("in-progress-withdrawal-exists/{userId}")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public Boolean getWithdrawalStatus(@PathVariable String userId) {
		return pointWithdrawalIntegrationService.isWithdrawalProcessExist(userId);
	}

}
