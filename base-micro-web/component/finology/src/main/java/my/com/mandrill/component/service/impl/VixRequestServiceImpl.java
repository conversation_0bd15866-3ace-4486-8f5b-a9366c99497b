package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.VixRequest;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.VixRequestRepository;
import my.com.mandrill.component.service.VixRequestService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Slf4j
public class VixRequestServiceImpl implements VixRequestService {

	private final VixRequestRepository vixRequestRepository;

	@Override
	@Transactional
	public VixRequest save(VixRequest vixRequest) {
		return vixRequestRepository.save(vixRequest);
	}

	@Override
	public VixRequest findById(String id) {
		return vixRequestRepository.findById(id).orElseThrow(ExceptionPredicate.vixRequestNotFound(id));
	}

	@Override
	public VixRequest findByUserIdAndRefCode(String userId, String refCode) {
		return vixRequestRepository.findByUserIdAndRefCode(userId, refCode)
				.orElseThrow(ExceptionPredicate.refCodeNotFound(refCode));
	}

	@Override
	public Optional<VixRequest> findOptionalByIdAndVehicleRegistrationNumber(String userId, String vixNo) {
		return vixRequestRepository.findFirstByUserIdAndVehicleRegistrationNoOrderByCreatedDateDesc(userId, vixNo);
	}

}
