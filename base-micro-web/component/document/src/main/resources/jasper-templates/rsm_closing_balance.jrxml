<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="RSM Closing Balance" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="c36e85b0-4d0a-4515-a02e-145b3320d2ac">
    <!-- Parameters -->
    <parameter name="ReportTitle" class="java.lang.String"/>

    <!-- Fields -->
    <field name="closingDate" class="java.time.Instant"/>
    <field name="pointAwarded" class="java.math.BigDecimal"/>
    <field name="pointSpent" class="java.math.BigDecimal"/>
    <field name="closingBalance" class="java.math.BigDecimal"/>
    <field name="totalExpiredPoint" class="java.math.BigDecimal"/>

    <!-- Column Header Section -->
    <columnHeader>
        <band height="20">
            <staticText>
                <reportElement x="0" y="0" width="136" height="20" uuid="54a3a679-7d24-4606-b16f-223e73a2c723"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <text><![CDATA[Closing Date]]></text>
            </staticText>
            <staticText>
                <reportElement x="136" y="0" width="136" height="20" uuid="5f7e0ec9-9f87-4961-b568-ec138b49c413"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <text><![CDATA[Point Awarded]]></text>
            </staticText>
            <staticText>
                <reportElement x="272" y="0" width="136" height="20" uuid="1d3a18cb-8f9e-4483-b6ef-c2a14a8ec5c9"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <text><![CDATA[Point Spent]]></text>
            </staticText>
            <staticText>
                <reportElement x="408" y="0" width="136" height="20" uuid="f76c8dc6-db50-4c5e-aa31-f218a49ddc2c"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <text><![CDATA[Closing Balance]]></text>
            </staticText>
            <staticText>
                <reportElement x="544" y="0" width="136" height="20" uuid="5a1ae5bb-e95f-44d6-b1b1-cdf1d8cf1b3d"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <text><![CDATA[Total Point Expired]]></text>
            </staticText>
        </band>
    </columnHeader>

    <!-- Detail Section -->
    <detail>
        <band height="20">
            <textField pattern="yyyy/MM/dd">
                <reportElement x="0" y="0" width="136" height="20" uuid="731ef80d-bfb1-4126-ac44-b3b9cb9e8bc0"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[
                    java.time.ZonedDateTime.ofInstant(
                        $F{closingDate},
                        java.time.ZoneId.of("Asia/Kuala_Lumpur")
                    ).toLocalDate().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                ]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="136" y="0" width="136" height="20" uuid="94a0ba14-049c-4ee1-a844-442c89d06db3"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[
                    new java.text.DecimalFormat("#,##0").format($F{pointAwarded})
                ]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="272" y="0" width="136" height="20" uuid="7e56e445-318d-42d7-984d-d458323deb39"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[
                    new java.text.DecimalFormat("#,##0").format($F{pointSpent})
                ]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="408" y="0" width="136" height="20" uuid="90b69c04-39da-468e-afe5-9c822afbac70"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[
                    new java.text.DecimalFormat("#,##0").format($F{closingBalance})
                ]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="544" y="0" width="136" height="20" uuid="5a1ae5bb-e95f-44d6-b1b1-cdf1d8cf1b3d"/>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[
                    new java.text.DecimalFormat("#,##0").format($F{totalExpiredPoint})
                ]]></textFieldExpression>
            </textField>
        </band>
    </detail>
</jasperReport>