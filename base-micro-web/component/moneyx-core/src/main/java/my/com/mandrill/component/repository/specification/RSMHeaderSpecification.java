package my.com.mandrill.component.repository.specification;

import jakarta.persistence.criteria.Predicate;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.request.RSMHeaderSearchRequest;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class RSMHeaderSpecification {

	public static Specification<RsmHeader> withFilters(RSMHeaderSearchRequest request) {
		return (root, query, builder) -> {

			List<Predicate> predicates = new ArrayList<>();

			// filter by applicationId or fullName or userRefNo
			if (Objects.nonNull(request.getSearch())) {
				predicates.add(builder.or(
						builder.like(builder.lower(root.get("productName")),
								"%" + request.getSearch().toLowerCase() + "%"),
						builder.like(builder.lower(root.get("rewardName")),
								"%" + request.getSearch().toLowerCase() + "%"),
						builder.like(builder.lower(root.get("commissionId")),
								"%" + request.getSearch().toLowerCase() + "%")));
			}

			// filter by status
			if (Objects.nonNull(request.getIsActive())) {
				predicates.add(builder.equal(root.get("isActive"), request.getIsActive()));
			}

			// filter by source
			if (Objects.nonNull(request.getProductCategoryId())) {
				predicates.add(builder.equal(root.get("productCategoryId"), request.getProductCategoryId()));
			}

			// filter by created date
			if (Objects.nonNull(request.getProductTypeId())) {
				predicates.add(builder.equal(root.get("productTypeId"), request.getProductTypeId()));
			}

			if (Objects.nonNull(request.getProviderId())) {
				predicates.add(builder.equal(root.get("productProviderId"), request.getProviderId()));
			}

			if (Objects.nonNull(request.getSource())) {
				predicates.add(builder.equal(root.get("source"), request.getSource()));
			}

			if (StringUtils.isNotBlank(request.getProductId())) {
				predicates.add(builder.equal(root.get("productId"), request.getProductId()));
			}
			if (Objects.nonNull(request.getFrequency())) {
				predicates.add(builder.equal(root.get("frequency"), request.getFrequency()));
			}

			if (Objects.nonNull(request.getStartDate()) && Objects.nonNull(request.getEndDate())) {
				Instant start = request.getStartDate().atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant();
				Instant end = request.getEndDate().atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID)
						.toInstant();

				if (Boolean.TRUE.equals(request.getIsInternal())) {
					predicates.add(builder.between(root.get("startDate"), start, end));
					predicates.add(builder.between(root.get("endDate"), start, end));
				}
				else if (Boolean.FALSE.equals(request.getIsInternal())) {
					predicates.add(builder.between(root.get("externalStartDate"), start, end));
					predicates.add(builder.between(root.get("externalEndDate"), start, end));
				}
			}

			return builder.and(predicates.toArray(new Predicate[0]));

		};
	}

}
