package my.com.mandrill.component.repository;

import my.com.mandrill.component.domain.UserInterestedRedirect;
import my.com.mandrill.component.dto.UserInterestedRedirectForExportDTO;
import my.com.mandrill.component.dto.model.UserInterestedRedirectEntityAndCountDTO;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Repository
public interface UserInterestedRedirectRepository
		extends JpaRepository<UserInterestedRedirect, String>, JpaSpecificationExecutor<UserInterestedRedirect> {

	UserInterestedRedirect findFirstByCampaignIdAndUserIdOrderByCreatedDateDesc(String campaignId, String userId);

	@Query("""
			SELECT new my.com.mandrill.component.dto.UserInterestedRedirectForExportDTO(
			ui.userId,
			ui.entityName,
			CAST(FUNCTION('timezone', 'Asia/Kuala_Lumpur', FUNCTION('timezone', 'UTC', ui.createdDate)) as string),
			ui.fullName,
			ui.email,
			ui.phoneNumber,
			ui.nric,
			ui.refNo,
			ui.userType)
			FROM UserInterestedRedirect ui
			where
			ui.entityName = :entityName
			AND ui.createdDate >= :startDate and ui.createdDate <= :endDate
			ORDER BY ui.createdDate DESC
			""")
	List<UserInterestedRedirectForExportDTO> findAllByEntityNameForTotalClick(EntityName entityName, Instant startDate,
			Instant endDate);

	@Query("""
			SELECT new my.com.mandrill.component.dto.UserInterestedRedirectForExportDTO(
			ui.userId,
			ui.entityName,
			CAST(DATE(FUNCTION('timezone', 'Asia/Kuala_Lumpur', FUNCTION('timezone', 'UTC', ui.createdDate))) as string),
			ui.fullName,
			ui.email,
			ui.phoneNumber,
			ui.nric,
			ui.refNo,
			ui.userType)
			FROM UserInterestedRedirect ui
			where
			ui.entityName = :entityName
			AND ui.createdDate >= :startDate and ui.createdDate <= :endDate
			GROUP BY DATE(FUNCTION('timezone', 'Asia/Kuala_Lumpur', FUNCTION('timezone', 'UTC', ui.createdDate))),
			ui.userId,
			ui.entityName,
			ui.fullName,
			ui.email,
			ui.phoneNumber,
			ui.nric,
			ui.refNo,
			ui.userType
			ORDER BY DATE(FUNCTION('timezone', 'Asia/Kuala_Lumpur', FUNCTION('timezone', 'UTC', ui.createdDate))) DESC
			""")
	List<UserInterestedRedirectForExportDTO> findAllByEntityNameForUniqueClick(EntityName entityName, Instant startDate,
			Instant endDate);

	@Query("""
			select new my.com.mandrill.component.dto.model.UserInterestedRedirectEntityAndCountDTO(uir.entityName, count(uir))
			from UserInterestedRedirect uir
			where uir.createdDate between :createdDateStart and :createdDateEnd
			group by uir.entityName
			""")
	List<UserInterestedRedirectEntityAndCountDTO> getEntityNameAndCountBetweenCreatedDate(Instant createdDateStart,
			Instant createdDateEnd);

	@Query("""
			select new my.com.mandrill.component.dto.model.UserInterestedRedirectEntityAndCountDTO(uir.entityName, count(uir))
			from UserInterestedRedirect uir
			where uir.createdDate between :createdDateStart and :createdDateEnd
			and uir.campaignId in :campaignIdList
			group by uir.entityName
			""")
	List<UserInterestedRedirectEntityAndCountDTO> getEntityNameAndCountBetweenCreatedDate(Instant createdDateStart,
			Instant createdDateEnd, List<String> campaignIdList);

	@Query("""
			SELECT new my.com.mandrill.component.dto.UserInterestedRedirectForExportDTO(
			ui.userId,
			ui.entityName,
			CAST(FUNCTION('timezone', 'Asia/Kuala_Lumpur', FUNCTION('timezone', 'UTC', ui.createdDate)) as string),
			ui.fullName,
			ui.email,
			ui.phoneNumber,
			ui.nric,
			ui.refNo,
			ui.userType)
			FROM UserInterestedRedirect ui
			where
			ui.entityName = :entityName
			AND ui.createdDate >= :startDate and ui.createdDate <= :endDate
			""")
	Page<UserInterestedRedirectForExportDTO> findAllByEntityNameForTotalClick(EntityName entityName, Instant startDate,
			Instant endDate, Pageable pageable);

	@Query("""
			SELECT new my.com.mandrill.component.dto.UserInterestedRedirectForExportDTO(
			ui.userId,
			ui.entityName,
			CAST(FUNCTION('timezone', 'Asia/Kuala_Lumpur', FUNCTION('timezone', 'UTC', ui.createdDate)) as string),
			ui.fullName,
			ui.email,
			ui.phoneNumber,
			ui.nric,
			ui.refNo,
			ui.userType)
			FROM UserInterestedRedirect ui
			where
			ui.entityName in :entityName
			AND ui.createdDate >= :startDate and ui.createdDate <= :endDate
			""")
	Page<UserInterestedRedirectForExportDTO> findAllByEntityNameForTotalClick(List<EntityName> entityName,
			Instant startDate, Instant endDate, Pageable pageable);

	List<UserInterestedRedirect> findByFullNameNullOrNricNullOrPhoneNumberNullOrEmailNullOrRefNoNull();

	@Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END " + "FROM UserInterestedRedirect e "
			+ "WHERE e.id IN :ids AND (e.rsmStatus = :status OR e.rsmCommissionAttached = true)")
	boolean existsAllByIdInAndRsmStatusAndRsmCommissionAttachedIsTrue(Set<String> ids, RSMStatus status);

	@Modifying
	@Query("UPDATE UserInterestedRedirect uir SET uir.rsmStatus = ?1 WHERE uir.id IN ?2")
	void bulkUpdateStatusByIdIn(RSMStatus status, Set<String> ids);

}
