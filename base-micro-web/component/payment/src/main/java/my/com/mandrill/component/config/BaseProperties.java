package my.com.mandrill.component.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "base")
public class BaseProperties {

	private final RequestUri requestUri = new RequestUri();

	private final PaymentGateway paymentGateway = new PaymentGateway();

	@Getter
	@Setter
	public static class PaymentGateway {

		private GKash gKash = new GKash();

		private ATXPayGate atxPayGate = new ATXPayGate();

		private ATXPayHub atxPayHub = new ATXPayHub();

		private ATXValidateNumber atxValidateNumber = new ATXValidateNumber();

		@Getter
		@Setter
		public static class GKash {

			private Boolean enabled;

			private String paymentVersion;

			private String refundVersion;

			private String queryVersion;

			private String serverUrl;

			private String responseUrl;

			private String blockingTimeout = "PT120S";

			private String defaultMerchant;

		}

		@Getter
		@Setter
		public static class ATXPayGate {

			private String serverUrl;

			private String redirectUrl;

			private String defaultMerchant;

			private String callbackUrl;

			private String blockingTimeout = "PT120S";

			private String accessKey;

		}

		@Getter
		@Setter
		public static class ATXPayHub {

			private String serverUrl;

			private String terminalId;

			private String authKey;

			private String blockingTimeout = "PT120S";

		}

		@Getter
		@Setter
		public static class ATXValidateNumber {

			private String serverUrl;

			private String accessKey;

			private String blockingTimeout = "PT120S";

		}

	}

	@Getter
	@Setter
	public static class RequestUri {

		private String openApiServer;

	}

}