package my.com.mandrill.component.dto.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.ExpenseTypeEnum;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode
public class ExpenseTypeDTO implements Serializable {

	private String id;

	private String code;

	private String name;

	private String description;

	private ExpenseTypeEnum type;

	private Boolean isUtility;

	public ExpenseTypeDTO(String id, String code, String name) {
		this.id = id;
		this.code = code;
		this.name = name;
	}

}
