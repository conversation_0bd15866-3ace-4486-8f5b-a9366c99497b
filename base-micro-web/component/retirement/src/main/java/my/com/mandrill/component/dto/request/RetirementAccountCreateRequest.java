package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RetirementAccountCreateRequest {

	@NotBlank
	private String accountTypeId;

	private String providerId;

	@Digits(integer = 10, fraction = 2)
	private BigDecimal savingsAmount = BigDecimal.ZERO;

	private BigDecimal monthlyIncome;

	private BigDecimal employeeContributionPercentage;

	private BigDecimal employerContributionPercentage;

	private BigDecimal monthlyContribution;

	private LocalDate latestContributionDate;

}
