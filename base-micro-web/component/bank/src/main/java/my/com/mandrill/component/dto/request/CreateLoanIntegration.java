package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.LoanPaymentStatus;
import my.com.mandrill.utilities.general.dto.request.ObjectRequest;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Month;
import java.time.Year;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateLoanIntegration implements Serializable {

	@NotNull
	private ObjectRequest provider;

	@NotNull
	private Short duration;

	@NotNull
	private Month repaymentStartMonth;

	@NotNull
	@Schema(implementation = Short.class, example = "1997")
	@JsonFormat(shape = JsonFormat.Shape.NUMBER_INT, pattern = "yyyy")
	private Year repaymentStartYear;

	@NotNull
	@Digits(integer = 15, fraction = 2)
	private BigDecimal monthlyInstallment;

	@Digits(integer = 5, fraction = 2)
	private BigDecimal interestRate;

	@NotNull
	private EntityName entityName;

	@NotBlank
	@Size(max = 36)
	private String entityId;

	@Digits(integer = 15, fraction = 2)
	private BigDecimal amount;

	@Max(100)
	@Digits(integer = 5, fraction = 2)
	private BigDecimal percentage;

	@NotNull
	@Digits(integer = 15, fraction = 2)
	private BigDecimal loanBalance;

	private LoanPaymentStatus paymentStatus = LoanPaymentStatus.ON_GOING;

}
