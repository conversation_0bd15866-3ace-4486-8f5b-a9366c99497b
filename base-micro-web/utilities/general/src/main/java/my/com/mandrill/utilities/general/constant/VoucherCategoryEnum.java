package my.com.mandrill.utilities.general.constant;

import lombok.*;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum VoucherCategoryEnum {

	PROPERTY("PROPERTY", "Property"), SERVICE("SERVICE", "Service"), E_SHOP("E_SHOP", "E_Shop"),
	VOUCHER("VOUCHER", "Voucher");

	private final String code;

	private final String name;

	public static List<VoucherCategoryEnum.VoucherCategoryEnumDTO> findAll() {
		return Arrays.stream(VoucherCategoryEnum.values()).map(
				i -> VoucherCategoryEnum.VoucherCategoryEnumDTO.builder().code(i.getCode()).name(i.getName()).build())
				.toList();
	}

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class VoucherCategoryEnumDTO implements Serializable {

		private String code;

		private String name;

	}

}
