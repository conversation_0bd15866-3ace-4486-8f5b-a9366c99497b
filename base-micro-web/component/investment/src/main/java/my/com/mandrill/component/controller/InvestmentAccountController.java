package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.dto.request.InvestmentAccountRequest;
import my.com.mandrill.component.dto.response.InvestmentAccountResponse;
import my.com.mandrill.component.service.InvestmentAccountIntegrationService;
import my.com.mandrill.component.service.InvestmentAccountService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/investment-accounts")
@RequiredArgsConstructor
public class InvestmentAccountController {

	private final InvestmentAccountIntegrationService investmentAccountIntegrationService;

	private final InvestmentAccountService investmentAccountService;

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_CREATE)")
	public ResponseEntity<Void> create(@RequestBody @Valid InvestmentAccountRequest investmentAccountRequest) {
		investmentAccountIntegrationService.create(investmentAccountRequest);

		investmentAccountIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.INVESTMENT, NetWorthSource.INVESTMENT);

		return ResponseEntity.ok().build();
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<Void> update(@RequestBody @Valid InvestmentAccountRequest investmentAccountRequest,
			@PathVariable String id) {
		investmentAccountIntegrationService.update(id, investmentAccountRequest);

		investmentAccountIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.INVESTMENT, NetWorthSource.INVESTMENT);

		return ResponseEntity.ok().build();
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<InvestmentAccountResponse>> getAccountInvestments() {
		return ResponseEntity.ok(investmentAccountIntegrationService.getInvestmentList());
	}

	@Hidden
	@GetMapping("integration/net-worth")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public NetWorthDTO calculateNetWorth() {
		String userId = SecurityUtil.currentUserId();
		return investmentAccountIntegrationService.calculateNetWorth(userId);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@DeleteMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_DELETE)")
	public ResponseEntity<Void> delete(@PathVariable String id) {
		investmentAccountIntegrationService.deleteInvestmentAccount(id);

		investmentAccountIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.INVESTMENT, NetWorthSource.INVESTMENT);

		return ResponseEntity.ok().build();
	}

	@ServiceToServiceAccess
	@GetMapping("/private/count")
	public ResponseEntity<Long> countUser() {
		return ResponseEntity.ok(investmentAccountIntegrationService.count());
	}

}
