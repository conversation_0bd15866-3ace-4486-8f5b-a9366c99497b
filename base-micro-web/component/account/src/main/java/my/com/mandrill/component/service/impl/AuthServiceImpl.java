package my.com.mandrill.component.service.impl;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.request.AuthenticateUserRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.AppUserService;
import my.com.mandrill.component.service.AuthService;
import my.com.mandrill.component.service.PasscodeProviderService;
import my.com.mandrill.component.service.PublicAuthenticationService;
import my.com.mandrill.utilities.feign.dto.AuthenticateDTO;
import my.com.mandrill.utilities.general.constant.AccessTypeEnum;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import my.com.mandrill.utilities.general.dto.request.HashRequest;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.HashUtil;
import my.com.mandrill.utilities.general.util.PhoneNumberUtil;
import my.com.mandrill.utilities.general.util.RequestUtil;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

	private final PasswordEncoder passwordEncoder;

	private final PublicAuthenticationService publicAuthenticationService;

	private final AppUserService appUserService;

	private final Map<String, PasscodeProviderService> passcodeCheckerService;

	@Override
	public AppUser processUserData(String accessType, LoginTypeEnum loginType, String username) {
		AppUser appUser = null;
		try {
			if (AccessTypeEnum.EMAIL.getCode().equals(accessType)) {
				appUser = appUserService.findAuthByEmailAndActiveAndLoginType(username, loginType);

			}
			else if (AccessTypeEnum.MOBILE.getCode().equals(accessType) && username.length() > 3) {
				String phoneNumber = PhoneNumberUtil.getPhoneNumberWithNoCountryCode(username).getPhoneNumber();
				appUser = appUserService.findAuthByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(phoneNumber,
						username.substring(0, 3), loginType);

			}
		}
		catch (EntityNotFoundException ignore) {
			throw new BusinessException(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST);

		}

		return appUser;
	}

	@Override
	public AuthenticateDTO validatePassword(AuthenticateUserRequest request) {
		AppUser appUser = this.processUserData(request.accessType(), request.loginType(), request.username());
		List<String> permissions = this.authenticateUser(appUser, request.password(),
				AccessTypeEnum.valueOf(request.accessType()), request.passcodeType());
		return AuthenticateDTO.builder().permissions(permissions).refNo(appUser.getRefNo()).build();
	}

	@Override
	public List<String> authenticateUser(AppUser user, String password, AccessTypeEnum accessType,
			PasscodeType passcodeType) {
		passcodeCheckerService.get(passcodeType.getProvider()).match(user, password);
		if (user.getPhoneVerified().equals(false)) {
			return new ArrayList<>();
		}
		if (AccessTypeEnum.EMAIL.equals(accessType) && user.getEmailVerified().equals(false)) {
			return new ArrayList<>();
		}
		if (user.getDeletedDatetime() != null) {
			throw new BusinessException(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST);
		}
		if (Boolean.FALSE.equals(user.getActive())) {
			throw new BadCredentialsException("");
		}

		return user.getAuthorities().stream()
				.filter(authority -> authority.getInstitution().getActive() && authority.getActive())
				.map(Authority::getPermissions).filter(Objects::nonNull).flatMap(Collection::stream)
				.map(Permission::getCode).toList();
	}

	@Override
	public void checkPassword(User user, String password) {
		if (!passwordEncoder.matches(password, user.getPassword())) {
			throw new BusinessException(ErrorCodeEnum.INVALID_PASSWORD);
		}
	}

	@Override
	public void authenticateHash(HashRequest requestBody, HttpServletRequest request) {
		log.info("Request: {}", requestBody);

		Optional<PublicAuthentication> publicAuthentication = publicAuthenticationService
				.findOptionalByIdentifier(requestBody.getIdentifier());
		if (publicAuthentication.isPresent()) {
			Instant now = Instant.now();
			if (Duration.between(requestBody.getTimestamp(), now).getSeconds() >= 60) {
				log.warn(
						"Attempting to authenticate hash failed from ipAddress: {}. Reason: Timestamp is over 60 seconds",
						RequestUtil.getIpAddress(request));
				throw new BusinessException(ErrorCodeEnum.INVALID_HASH);
			}

			String hash = HashUtil.sha512Hex(publicAuthentication.get().getApiKey(),
					publicAuthentication.get().getIdentifier(), requestBody.getTimestamp().toString());

			if (!hash.equals(requestBody.getHash())) {
				log.warn("Attempting to authenticate hash failed from ipAddress: {}. Reason: Wrong hash output",
						RequestUtil.getIpAddress(request));
				throw new BusinessException(ErrorCodeEnum.INVALID_HASH);
			}
		}
		else {
			log.warn("Attempting to authenticate hash failed from ipAddress: {}. Reason: Identifier not found",
					RequestUtil.getIpAddress(request));
			throw new BusinessException(ErrorCodeEnum.INVALID_HASH);
		}
	}

}
