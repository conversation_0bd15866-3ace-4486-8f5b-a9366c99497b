package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.service.UserService;
import my.com.mandrill.utilities.feign.dto.SchedulerPushNotificationDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.service.KafkaSender;
import org.springframework.kafka.annotation.DltHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class DailyWelcomeNotificationScheduler {

	private static final String DAILY_WELCOME_SCHEDULER_PUSH_NOTIFICATION_TEMPLATE_ID = "49ee34db-d7a2-4091-9edd-c92a4d2afc41";

	private final UserService userService;

	private final KafkaSender kafkaSender;

	@KafkaListener(topics = KafkaTopicConfig.CREATE_WELCOME_NOTIFICATION_9PM, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopicConfig.CREATE_WELCOME_NOTIFICATION_9PM)
	public void consume(String message) {
		log.info("Daily 9pm Welcome Push Notification Begin {}", message);
		Instant now = Instant.now();

		Instant cutoffTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(13, 0)).toInstant(ZoneOffset.UTC);

		// get the new users in past 24 hours from cutoff time 9pm
		List<String> userIds = userService.findAllUserIdsByCreatedDate(cutoffTime.minus(24, ChronoUnit.HOURS),
				cutoffTime);

		if (!userIds.isEmpty()) {
			kafkaSender.safeSend(KafkaTopic.SCHEDULER_PUSH_NOTIFICATION, UUID.randomUUID().toString(),
					new SchedulerPushNotificationDTO(userIds, DAILY_WELCOME_SCHEDULER_PUSH_NOTIFICATION_TEMPLATE_ID));
		}

		log.info("Daily 9pm Welcome Push Notification Ended, Delay: {} ms, total: {}",
				(System.currentTimeMillis() - now.toEpochMilli()), userIds.size());
	}

	@DltHandler
	public void dltHandler(String message) {
		log.error("Unable to process : {}", message);
	}

}
