package my.com.mandrill.component.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Expense;
import my.com.mandrill.component.domain.ExpenseType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExpenseDTO;
import my.com.mandrill.component.dto.request.ReminderIntegrationRequest;
import my.com.mandrill.component.service.ExpenseIntegrationService;
import my.com.mandrill.component.service.ExpenseService;
import my.com.mandrill.component.service.ExpenseTypeService;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.feign.dto.ObjectRequest;
import my.com.mandrill.utilities.feign.dto.ReminderRequest;
import my.com.mandrill.utilities.feign.dto.ReminderResponse;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.DeliveryType;
import my.com.mandrill.utilities.general.constant.ReminderFrequency;
import my.com.mandrill.utilities.general.constant.ReminderType;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExpenseIntegrationServiceImpl implements ExpenseIntegrationService {

	private final ExpenseService expenseService;

	private final ExpenseTypeService expenseTypeService;

	private final ProxyFeignClient proxyFeignClient;

	private void processReminder(Expense expense, Boolean isReminder,
			ReminderIntegrationRequest reminderIntegrationRequest) {
		ReminderType reminderType = ReminderType.EXPENSE;
		if (Boolean.TRUE.equals(isReminder) && Objects.nonNull(reminderIntegrationRequest)) {
			ReminderRequest reminderRequest = MapStructConverter.MAPPER.toReminderRequest(reminderIntegrationRequest);
			ObjectRequest data = new ObjectRequest();
			data.setId(expense.getId());
			reminderRequest.setData(data);
			reminderRequest.setReminderType(reminderType);
			reminderRequest.setExpense(MapStructConverter.MAPPER.toExpenseDTO(expense));
			reminderRequest.setDeliveryType(DeliveryType.PUSH);
			reminderRequest.setReminderFrequency(ReminderFrequency.MONTHLY);

			ReminderResponse response;
			try {
				response = proxyFeignClient.getNotificationFeignClient().findByReminderTypeAndDataId(reminderType,
						expense.getId());
				proxyFeignClient.getNotificationFeignClient().update(reminderRequest, response.getId());
			}
			catch (EntityNotFoundException e) {
				proxyFeignClient.getNotificationFeignClient().integration(reminderRequest);
			}
		}
		else if (Boolean.FALSE.equals(isReminder)) {
			deleteReminder(expense);
		}
	}

	private void deleteReminder(Expense expense) {
		proxyFeignClient.getNotificationFeignClient().deleteByReminderTypeAndDataId(ReminderType.EXPENSE,
				expense.getId());
	}

	@Override
	public Expense save(Expense expense, User user) {
		expense.setExpenseType(getExpenseType(expense, user));

		Boolean isReminder = expense.getIsReminder();
		ReminderIntegrationRequest reminderIntegrationRequest = expense.getReminder();

		expense.setUser(user);
		Expense persisted = expenseService.save(expense);
		processReminder(persisted, isReminder, reminderIntegrationRequest);

		return persisted;
	}

	private ExpenseType getExpenseType(Expense expense, User user) {
		if (Objects.nonNull(expense.getId())) {
			return expenseService.findByIdAndUser(expense.getId(), user).getExpenseType();
		}
		return expenseTypeService.findById(expense.getExpenseType().getId());
	}

	@Override
	public void delete(String id, User user) {
		expenseService.findOptionalByIdAndUser(id, user).ifPresent(expense -> {
			expenseService.deleteByUserAndId(user, id);
			deleteReminder(expense);
		});
	}

	@Override
	public List<ExpenseDTO> getUserCurrentExpenses() {
		String refNo = SecurityUtil.currentUserLogin();
		return expenseService.getUserCurrentExpenses(refNo);
	}

}
