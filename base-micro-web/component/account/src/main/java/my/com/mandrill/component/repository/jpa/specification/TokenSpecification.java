package my.com.mandrill.component.repository.jpa.specification;

import jakarta.persistence.criteria.Predicate;
import my.com.mandrill.component.domain.TokenTransaction;
import my.com.mandrill.component.dto.request.TokenSearchRequest;
import my.com.mandrill.utilities.general.constant.TokenStatus;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;

public class TokenSpecification {

	private TokenSpecification() {
	}

	public static Specification<TokenTransaction> withFilters(TokenSearchRequest request) {

		return (root, query, criteriaBuilder) -> {
			List<Predicate> predicates = new ArrayList<>();

			if (StringUtils.isNotBlank(request.getAccessToken())) {
				predicates.add(criteriaBuilder.equal(root.get("token").get("id"), request.getAccessToken()));
			}
			if (StringUtils.isNotBlank(request.getAccessToken())) {
				predicates.add(criteriaBuilder.equal(root.get("token").get("refreshToken"), request.getRefreshToken()));
			}
			if (StringUtils.isNotBlank(request.getUsername())) {
				predicates.add(criteriaBuilder.equal(root.get("user").get("username"), request.getUsername()));
			}
			if (StringUtils.isNotBlank(request.getService())) {
				predicates.add(criteriaBuilder.equal(root.get("service"), request.getService()));
			}
			if (StringUtils.isNotBlank(request.getUri())) {
				predicates.add(criteriaBuilder.equal(root.get("uri"), request.getUri()));
			}
			if (StringUtils.isNotBlank(request.getMethod())) {
				predicates.add(criteriaBuilder.equal(root.get("method"), request.getMethod()));
			}
			if (StringUtils.isNotBlank(request.getDeviceId())) {
				predicates.add(criteriaBuilder.equal(root.get("token").get("deviceId"), request.getDeviceId()));
			}
			if (StringUtils.isNotBlank(request.getStatus())) {
				predicates.add(criteriaBuilder.equal(root.get("token").get("status"),
						TokenStatus.valueOf(request.getStatus())));
			}

			return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
		};
	}

}
