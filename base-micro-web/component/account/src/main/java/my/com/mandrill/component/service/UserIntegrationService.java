package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.request.MobileUserPaginationRequest;
import my.com.mandrill.component.dto.request.PublicUserDetailRequest;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.UnaryOperator;

public interface UserIntegrationService {

	void failLoginCheck(AppUser user, boolean loginStatus);

	void failLoginCheck(AppUser user, boolean loginStatus, PasscodeType passcodeType);

	User save(User user);

	User findByIdAndInstitutionId(String id, String institutionId);

	Page<User> findByInstitutionsIdAndLoginTypeAndFullName(Pageable pageable, String institutionId,
			LoginTypeEnum loginType, String fullName);

	AppUser sensitiveFieldUpdateCheck(AppUser user, String key, UnaryOperator<AppUser> saveFunc);

	PublicUserDetailResponse getUserDetailAndKycStatus(PublicUserDetailRequest publicUserDetailRequest);

	AccountDetailResponse findDetailById(String id);

	void deleteAdminUser(String id, String constitutionId);

	CompletableFuture<String> generateFileReport(MobileUserPaginationRequest request,
			CurrentUserIdResponse currentUser);

	List<String> findUserIdsByPermission(String permission);

	UserPersonalInformationResponse getUserPersonalInformation(String id);

	UserPaymentInformationResponse getUserPaymentInformation(String id);

	UserEKycInformationResponse getUserEKycInformation(String id);

}
