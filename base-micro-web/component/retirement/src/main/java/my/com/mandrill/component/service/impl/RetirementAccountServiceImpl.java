package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.domain.RetirementAccount;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.RetirementAccountRepository;
import my.com.mandrill.component.service.RetirementAccountService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class RetirementAccountServiceImpl implements RetirementAccountService {

	private final RetirementAccountRepository retirementAccountRepository;

	@Transactional
	@Override
	public RetirementAccount save(RetirementAccount retirementAccount) {
		return retirementAccountRepository.save(retirementAccount);
	}

	@Override
	public RetirementAccount findByIdAndUserId(String id, String userId) {
		return retirementAccountRepository.findByIdAndUserId(id, userId)
				.orElseThrow(ExceptionPredicate.retirementAccountNotFound(id, userId));
	}

	@Override
	public List<RetirementAccount> findAllByUserId(String userId) {
		return retirementAccountRepository.findAllByUserId(userId);
	}

	@Override
	public List<RetirementAccount> findAllByAccountTypeIdAndMonthlyContributionNotNull(String accountTypeId, long limit,
			long offset) {
		return retirementAccountRepository.findAllByAccountTypeIdAndMonthlyContributionNotNull(accountTypeId, limit,
				offset);
	}

	@Override
	public boolean existsByUserIdAndAccountTypeIdAndRetirementProviderId(String userId, String accountTypeId,
			String providerId) {
		return retirementAccountRepository.existsByUserIdAndAccountTypeIdAndRetirementProviderId(userId, accountTypeId,
				providerId);
	}

	@Override
	public boolean existsByIdAndUserId(String id, String userId) {
		return retirementAccountRepository.existsByIdAndUserId(id, userId);
	}

	@Transactional
	@Override
	public void deleteById(String id) {
		retirementAccountRepository.deleteById(id);
	}

}
