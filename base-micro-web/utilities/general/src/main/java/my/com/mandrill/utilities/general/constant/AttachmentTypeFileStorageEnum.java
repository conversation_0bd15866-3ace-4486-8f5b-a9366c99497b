package my.com.mandrill.utilities.general.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AttachmentTypeFileStorageEnum {

	BANK_LOGO(FileMethod.PUBLIC.getMethod(), "bank-logo"), BANK_ICON(FileMethod.PUBLIC.getMethod(), "bank-icon"),
	UTILITY_LOGO(FileMethod.PUBLIC.getMethod(), "utility-logo"),
	UTILITY_BILL(FileMethod.PRIVATE.getMethod(), "utility-doc"),
	SALES_AND_PURCHASE_AGREEMENT(FileMethod.PRIVATE.getMethod(), "sales-and-purchase-agreement"),
	CREDIT_CARD(FileMethod.PRIVATE.getMethod(), "credit-card"),
	INSURANCE_LOGO(FileMethod.PUBLIC.getMethod(), "insurance-logo"),
	PROPERTY_DOC(FileMethod.PRIVATE.getMethod(), "property-doc"), BANK_DOC(FileMethod.PRIVATE.getMethod(), "bank-doc"),
	VEHICLE_DOC(FileMethod.PRIVATE.getMethod(), "vehicle-doc"),
	IDENTITY_DOC(FileMethod.PRIVATE.getMethod(), "identity-doc"),
	LEGAL_DOC(FileMethod.PRIVATE.getMethod(), "legal-doc"),
	INSURANCE_DOC(FileMethod.PRIVATE.getMethod(), "insurance-doc"),
	ADVERTISEMENT_IMAGE(FileMethod.PUBLIC.getMethod(), "advertisement-image"),
	ADVERTISEMENT_PDF(FileMethod.PUBLIC.getMethod(), "advertisement-pdf"),
	BANNER_IMAGE(FileMethod.PUBLIC.getMethod(), "banner-image"),
	INSTITUTION_IMAGE(FileMethod.PUBLIC.getMethod(), "institution-image"),
	PUSH_NOTIFICATION_IMAGE(FileMethod.PUBLIC.getMethod(), "push-notification-image"),
	VOUCHER_IMAGE(FileMethod.PUBLIC.getMethod(), "voucher-image"),
	RETIREMENT_EPF_DOC(FileMethod.PRIVATE.getMethod(), "retirement-epf-doc"),
	VOUCHER_PDF(FileMethod.PUBLIC.getMethod(), "voucher-pdf"), BLOG_IMAGE(FileMethod.PUBLIC.getMethod(), "blog-image"),
	HOME_MENU_PRODUCT_IMAGE(FileMethod.PUBLIC.getMethod(), "home-menu-product-image"),
	PURCHASE_PROPERTY_IMAGE(FileMethod.PUBLIC.getMethod(), "purchase-property-image"),
	RETIREMENT_PRODUCT_LOGO(FileMethod.PUBLIC.getMethod(), "retirement-product-logo"),
	WEB_CAMPAIGN_IMAGE(FileMethod.PUBLIC.getMethod(), "web-campaign-image"),
	RETIREMENT_PROVIDER_LOGO(FileMethod.PUBLIC.getMethod(), "retirement-provider-logo"),
	INVESTMENT_PROVIDER_LOGO(FileMethod.PUBLIC.getMethod(), "investment-provider-logo"),
	SAVING_GOAL_IMAGE(FileMethod.PUBLIC.getMethod(), "saving-goal-image"),
	BANK_STATEMENT_IMAGE(FileMethod.PUBLIC.getMethod(), "bank-statement-image"),
	MESSAGE_TEMPLATE_HEADER_IMAGE(FileMethod.PUBLIC.getMethod(), "message-template-header-image"),
	PROJECT_BROCHURE_PDF(FileMethod.PUBLIC.getMethod(), "project-brochure-pdf"),
	DOWNLOAD_USER_MOBILE(FileMethod.PRIVATE.getMethod(), "download-user-mobile");

	private final String method;

	private final String basePath;

	public static AttachmentTypeFileStorageEnum fromString(String attachmentTypeString) {
		for (AttachmentTypeFileStorageEnum attachmentType : AttachmentTypeFileStorageEnum.values()) {
			if (attachmentType.name().equals(attachmentTypeString)) {
				return attachmentType;
			}
		}
		throw new IllegalArgumentException(attachmentTypeString + " is not found in AttachmentTypeFileStorageEnum.");
	}

}
