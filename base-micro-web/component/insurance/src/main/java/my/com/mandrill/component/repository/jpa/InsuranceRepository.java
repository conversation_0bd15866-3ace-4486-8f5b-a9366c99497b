package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Insurance;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface InsuranceRepository extends JpaRepository<Insurance, String> {

	long countByUserId(@NonNull String userId);

	List<Insurance> findByUserId(@NonNull String userId, Sort sort);

	List<Insurance> findByUserIdAndTypeIn(String userId, List<String> insuranceTypeEnums);

	Optional<Insurance> findByIdAndUserId(@NonNull String id, @NonNull String userId);

	Optional<Insurance> findByUserIdAndTypeAndEntityId(@NonNull String userId, @NonNull String type,
			@NonNull String entityId);

	@Query("SELECT i.entityId FROM Insurance i where i.userId = ?1 and i.type = ?2 and entityId is not null")
	List<String> findAllEntityIdByUserIdAndTypeAndEntityIdIsNotNull(@NonNull String userId, @NonNull String type);

	Insurance findByAttachmentGroupIdAndUserId(String attachmentGroupId, String userId);

	List<Insurance> findByAttachmentGroupIdNullAndUserId(String userId);

	boolean existsByUserIdAndAttachmentGroupId(String userId, @NonNull String attachmentGroupId);

	List<Insurance> findByUserIdAndEntityIdAndType(@NonNull String userId, String entityId, @NonNull String type);

	List<Insurance> findByTypeAndRenewalDateIsAfterAndRenewalDateIsBeforeAndEntityIdIsNotNull(String type,
			LocalDate startDate, LocalDate endDate);

}