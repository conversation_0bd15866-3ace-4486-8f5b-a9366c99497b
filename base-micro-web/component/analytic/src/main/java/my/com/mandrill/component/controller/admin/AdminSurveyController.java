package my.com.mandrill.component.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.SurveyAnswer;
import my.com.mandrill.component.domain.SurveyForm;
import my.com.mandrill.component.domain.SurveyQuestion;
import my.com.mandrill.component.dto.model.ObjectDTO;
import my.com.mandrill.component.dto.model.SurveyFormDTO;
import my.com.mandrill.component.dto.request.SurveyFormCreateRequest;
import my.com.mandrill.component.service.SurveyIntegrationService;
import my.com.mandrill.component.service.SurveyService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.general.constant.SurveyAnswerTag;
import my.com.mandrill.utilities.general.constant.SurveyFormStatus;
import my.com.mandrill.utilities.general.constant.SurveyFormType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/admin/survey")
@RequiredArgsConstructor
public class AdminSurveyController {

	private final SurveyIntegrationService surveyIntegrationService;

	private final SurveyService surveyService;

	private final ValidationService validationService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.SURVEY_CREATE)")
	public ResponseEntity<SurveyFormDTO> createSurveyForm(
			@Valid @RequestBody SurveyFormCreateRequest createSurveyFormRequest) {

		SurveyForm surveyForm = MapStructConverter.MAPPER.toSurveyForm(createSurveyFormRequest);
		validationService.validateCreateSurveyForm(surveyForm);
		SurveyForm result = surveyIntegrationService.createSurveyForm(surveyForm);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toSurveyFormDTO(sortSequence(result)));

	}

	@DeleteMapping("{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.SURVEY_DELETE)")
	public void deleteSurveyForm(@PathVariable String id) {
		this.surveyIntegrationService.deleteSurveyForm(id);
	}

	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.SURVEY_UPDATE)")
	public ResponseEntity<SurveyFormDTO> updateSurveyForm(
			@Valid @RequestBody SurveyFormCreateRequest createSurveyFormRequest, @PathVariable String id) {

		SurveyForm surveyForm = MapStructConverter.MAPPER.toSurveyForm(createSurveyFormRequest);
		surveyForm.setId(id);

		surveyForm = this.validationService.validateUpdateSurveyForm(surveyForm);

		SurveyForm updateForm = this.surveyIntegrationService.updateSurveyForm(surveyForm);

		SurveyForm resultForm = this.surveyService.findById(updateForm.getId());

		return ResponseEntity.ok(MapStructConverter.MAPPER.toSurveyFormDTO(sortSequence(resultForm)));
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.SURVEY_READ)")
	public ResponseEntity<SurveyFormDTO> getSurveyFormById(@PathVariable String id) {
		SurveyForm surveyForm = this.surveyService.findById(id);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toSurveyFormDTO(sortSequence(surveyForm)));

	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.SURVEY_READ)")
	public ResponseEntity<Page<SurveyFormDTO>> getAllSurveyForm(Pageable pageable,
			@RequestParam(required = false) String code, @RequestParam(required = false) String name) {
		Page<SurveyForm> surveyFormPage = this.surveyService.findAll(pageable, name);
		return ResponseEntity.ok(surveyFormPage.map(MapStructConverter.MAPPER::toSurveyFormDTO));
	}

	@GetMapping("/names")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.REPORT_SURVEY_READ)")
	public ResponseEntity<Set<String>> getAllSurveyName() {
		Set<String> surveyFormNames = surveyIntegrationService.getAllSurveyFormNameByStatus(SurveyFormStatus.CONFIRM);
		return ResponseEntity.ok(surveyFormNames);
	}

	@GetMapping("/form-types")
	@PreAuthorize("hasAuthority(@authorityPermission.SURVEY_READ)")
	public Set<ObjectDTO> getFormTypes() {
		return Arrays.stream(SurveyFormType.values()).map(MapStructConverter.MAPPER::toObjectDto)
				.collect(Collectors.toSet());
	}

	@GetMapping("/tags")
	@PreAuthorize("hasAuthority(@authorityPermission.SURVEY_READ)")
	public Set<ObjectDTO> getSurveyTags() {
		return Arrays.stream(SurveyAnswerTag.values()).map(MapStructConverter.MAPPER::toObjectDto)
				.collect(Collectors.toSet());
	}

	private SurveyForm sortSequence(SurveyForm form) {
		SurveyForm result = form;
		Comparator<SurveyQuestion> compareQuestion = Comparator.comparing(SurveyQuestion::getSeq);
		List<SurveyQuestion> sortedQuestion = form.getSurveyQuestions().stream().sorted(compareQuestion)
				.collect(Collectors.toList());

		sortedQuestion.forEach(surveyQuestion -> {
			List<SurveyAnswer> surveyAnswer = surveyQuestion.getSurveyAnswers();
			Comparator<SurveyAnswer> compareAnswer = Comparator.comparing(SurveyAnswer::getSeq);
			List<SurveyAnswer> sortedAnswer = surveyAnswer.stream().sorted(compareAnswer).collect(Collectors.toList());
			surveyQuestion.setSurveyAnswers(sortedAnswer);
		});
		result.setSurveyQuestions(sortedQuestion);
		return result;
	}

}
