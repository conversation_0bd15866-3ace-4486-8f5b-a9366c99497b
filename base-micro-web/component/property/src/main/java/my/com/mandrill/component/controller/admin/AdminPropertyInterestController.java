package my.com.mandrill.component.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.dto.model.PropertyInterestPageDTO;
import my.com.mandrill.component.service.PropertyInterestIntegrationService;
import my.com.mandrill.component.service.PropertyInterestService;
import my.com.mandrill.component.service.PurchasePropertyIntgService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.request.CreateManualLeadRequest;
import my.com.mandrill.utilities.feign.dto.request.DateRangeRequest;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@RequiredArgsConstructor
@RequestMapping("admin/v1/purchase-property/interest")
public class AdminPropertyInterestController {

	private final PropertyInterestIntegrationService propertyInterestIntegrationService;

	private final PropertyInterestService propertyInterestService;

	private final PurchasePropertyIntgService purchasePropertyIntgService;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.REPORT_READ_PURCHASE_PROPERTY)")
	public Page<PropertyInterestPageDTO> index(DateRangeRequest query, Pageable pageable) {
		return propertyInterestIntegrationService.find(query, pageable).map(propertyInterestPageDTO -> {
			propertyInterestPageDTO.setMobileNumber(propertyInterestPageDTO.getMobileNumber().replace("+", ""));
			return propertyInterestPageDTO;
		});
	}

	@GetMapping("export")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.REPORT_READ_PURCHASE_PROPERTY)")
	public List<PropertyInterestPageDTO> export(DateRangeRequest query) {
		return propertyInterestIntegrationService.export(query);
	}

	@ServiceToServiceAccess
	@PostMapping("/private/pagination")
	public Page<UserInterestRecordRSMPaginationDTO> findRsmLead(@RequestBody RSMLeadRequest request,
			Pageable pageable) {
		return propertyInterestIntegrationService.rsmLead(pageable, request);
	}

	@ServiceToServiceAccess
	@GetMapping("/private/detail/{id}")
	public UserInterestRecordRSMViewDTO findRsmLeadDetail(@PathVariable String id) {
		return propertyInterestService.findRsmLeadDetail(id);
	}

	@ServiceToServiceAccess
	@PutMapping("/private/rsm-info")
	public void updateRsmStatus(@RequestBody @Valid LeadRSMUpdateRequest request) {
		propertyInterestService.updateRsmInfo(request);
	}

	@ServiceToServiceAccess
	@PostMapping("/private/report")
	public List<UserInterestRecordRSMPaginationDTO> findRsmLeadReport(@RequestBody RSMLeadRequest request) {
		return propertyInterestIntegrationService.rsmLeadReport(request);
	}

	@ServiceToServiceAccess
	@GetMapping("/private/application-ids-except-user-ids")
	Set<String> findAllApplicationIdsExcludingUserIdsIn(@RequestParam Set<String> applicationIds,
			@RequestParam Set<String> userIds) {
		return propertyInterestService.findAllApplicationIdsExcludingUserIdsIn(applicationIds, userIds);
	}

	@ServiceToServiceAccess
	@PostMapping("/private/integration/manual")
	public void createManualLead(@Valid @RequestBody CreateManualLeadRequest createManualLeadRequest) {
		purchasePropertyIntgService.createManualLead(createManualLeadRequest);
	}

}
