package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.RetirementAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RetirementAccountRepository extends JpaRepository<RetirementAccount, String> {

	Optional<RetirementAccount> findByIdAndUserId(String id, String userId);

	List<RetirementAccount> findAllByUserId(String userId);

	boolean existsByUserIdAndAccountTypeIdAndRetirementProviderId(String userId, String accountTypeId,
			String providerId);

	boolean existsByIdAndUserId(String id, String userId);

	@Query("select ra from RetirementAccount ra where ra.accountType.id = :accountTypeId and ra.monthlyContribution is not null order by ra.createdDate desc limit :limit offset :offset")
	List<RetirementAccount> findAllByAccountTypeIdAndMonthlyContributionNotNull(String accountTypeId, long limit,
			long offset);

}
