package my.com.mandrill.utilities.feign.client;

import my.com.mandrill.utilities.feign.dto.model.PointsDisbursementExportDTO;
import my.com.mandrill.utilities.feign.dto.model.PointsWithdrawalExportDTO;
import my.com.mandrill.utilities.feign.dto.model.ReferralCodeDto;
import my.com.mandrill.utilities.feign.dto.request.CommissionInfoRequest;
import my.com.mandrill.utilities.feign.dto.request.FindRelationTypeRequest;
import my.com.mandrill.utilities.feign.dto.response.PaymentAccountDetailResponse;
import my.com.mandrill.utilities.general.constant.PointDisbursementDateType;
import my.com.mandrill.utilities.general.constant.PointEarningStatus;
import my.com.mandrill.utilities.general.constant.PointWithdrawalDateType;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.dto.response.CommissionInfoResponse;
import my.com.mandrill.utilities.general.dto.response.RsmClosingBalanceResponse;
import my.com.mandrill.utilities.general.util.DateUtil;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@FeignClient("moneyx-core-component")
public interface MoneyXCoreFeignClient {

	@GetMapping("/account/v1/private/exists-referral-code")
	Boolean existsReferralCode(@RequestParam("referralCode") String referralCode,
			@RequestParam("source") String source);

	@GetMapping("/admin/account/private/get-payment-account-by-user-id")
	PaymentAccountDetailResponse getPaymentAccountByUserId(@RequestParam("userId") String userId);

	@GetMapping("/admin/closing-balance/private/export")
	List<RsmClosingBalanceResponse> exportClosingBalance(@RequestParam("month") Integer month,
			@RequestParam("year") Integer year);

	@GetMapping("/admin/v1/account/withdrawal-management/export")
	List<PointsWithdrawalExportDTO> getAllWithdrawalManagementForExport(@RequestParam(required = false) String search,
			@RequestParam(required = false) String status,
			@RequestParam(required = false) PointWithdrawalDateType dateType,
			@RequestParam(required = false) @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate dateFrom,
			@RequestParam(required = false) @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate dateTo);

	@GetMapping("/admin/v1/account/point-disbursement/export")
	List<PointsDisbursementExportDTO> getAllPointDisbursementsForExport(@RequestParam(required = false) String search,
			@RequestParam(required = false) RSMRelationType rsmScenario,
			@RequestParam(required = false) String rsmFocalType,
			@RequestParam(required = false) PointEarningStatus status,
			@RequestParam(required = false) PointDisbursementDateType dateType,
			@RequestParam(required = false) @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate dateFrom,
			@RequestParam(required = false) @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate dateTo);

	@PostMapping("/v1/private/referral/find-relation-type")
	RSMRelationType findRelationType(@RequestBody FindRelationTypeRequest request);

	@GetMapping("/v1/private/referral/referred-user-id/{userId}")
	ReferralCodeDto getReferralCodeOfReferrerByReferredId(@PathVariable String userId);

	@GetMapping("v1/private/withdrawal/has-pending/{userId}")
	Boolean existInProgressWithdrawal(@PathVariable String userId);

	@GetMapping("/admin/v1/rsm-commission-header/private/products/types")
	List<String> getRsmActiveProductTypes();

	@GetMapping("/admin/v1/rsm-commission-header/private/products")
	Set<String> getRsmActiveProducts();

	@GetMapping("/admin/v1/rsm-commission-header/private/products/by-type")
	Set<String> getRsmActiveProducts(@RequestParam(required = false) String productTypeName);

	@GetMapping("/admin/v1/rsm-commission-header/private/providers/by-type")
	Set<String> getRsmActiveProviders(@RequestParam(required = false) List<String> productTypeNames);

	@GetMapping("/account/v1/private/exists-by-user-id")
	String existsReferralCodeByUserId(@RequestParam("userId") String userId);

	@PostMapping("/v1/private/commission/find")
	List<CommissionInfoResponse> findCommissionInfo(@RequestBody CommissionInfoRequest request);

}