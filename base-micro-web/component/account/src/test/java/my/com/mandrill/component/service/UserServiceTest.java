package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.component.service.impl.PopulateServiceImpl;
import my.com.mandrill.component.service.impl.UserServiceImpl;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {

	@InjectMocks
	UserServiceImpl userService;

	@Mock
	UserRepository userRepository;

	@Mock
	PasswordEncoder passwordEncoder;

	@Mock
	PopulateServiceImpl populateService;

	@InjectMocks
	AesCryptoUtil aesCryptoUtil;

	@Mock
	RunningNumberUtil runningNumberUtil;

	User userMock;

	@BeforeEach
	void setup() {
		ReflectionTestUtils.setField(aesCryptoUtil, "AES_KEY", "NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc");
		ReflectionTestUtils.setField(aesCryptoUtil, "IV_KEY", "57ad289f-3def-4c");

		userMock = new User();
		userMock.setPassword("anyany");
	}

	@Test
	void findByIdAndInstitutionId() {
		// Mockito.when(userRepository.findByIdAndInstitutionsId(any(),
		// any())).thenReturn(Optional.of(userMock));
		// Mockito.doNothing().when(populateService).populateCountry(any());
		// Mockito.doNothing().when(populateService).populateState(any());
		//
		// User user = userService.findByIdAndInstitutionId("", "");
		// assertThat(user).isEqualTo(userMock);
	}

	@Test
	void save_Success() {
		Mockito.when(userRepository.saveAndFlush(any())).thenReturn(userMock);

		User user = userService.save(new User());

		assertThat(user).isNotNull();

	}

	@Test
	void adminSave_Success() {
		Mockito.when(userRepository.saveAndFlush(any())).thenReturn(userMock);
		Mockito.when(passwordEncoder.encode(any())).thenReturn("anyany");

		User req = new User();
		req.setPassword("any");

		User res = userService.adminSave(req);
		assertThat(res.getPassword()).isEqualTo("anyany");

	}

	@Test
	void delete_Success() {
		Mockito.when(userRepository.findByIdAndInstitutionsIdAndLoginType(any(), any(), any()))
				.thenReturn(Optional.of(userMock));

		userService.delete("", "");
		verify(userRepository, times(1)).findByIdAndInstitutionsIdAndLoginType(any(), any(), any());
	}

	@Test
	void findBYRefNo_Success() {
		Mockito.when(userRepository.findByRefNo(any())).thenReturn(Optional.of(userMock));
		User user = userService.findByRefNo("");
		assertThat(user).isEqualTo(userMock);
	}

	@Test
	void getRefNoByUsernameAndLoginType_Success() {
		Mockito.when(userRepository.findRefNoByUsernameAndLoginType(any(), any())).thenReturn("test");

		String s = userService.getRefNoByUsernameAndLoginType("", LoginTypeEnum.USER);
		assertThat(s).isEqualTo("test");
	}

	@Test
	void findByEmailAndLoginTypeAndActiveTrue_Success() {
		Mockito.when(userRepository.findByEmailIgnoreCaseAndLoginTypeAndActiveTrue(any(), any()))
				.thenReturn(Optional.of(userMock));

		User u = userService.findByEmailAndLoginTypeAndActiveTrue("", LoginTypeEnum.USER);
		assertThat(u).isEqualTo(userMock);
	}

	@Test
	void findByPhoneCountryAndPhoneNumberAndLoginTypeAndActiveTrue_Success() {
		Mockito.when(userRepository.findByPhoneCountryAndPhoneNumberAndLoginTypeAndActiveTrue(any(), any(), any()))
				.thenReturn(Optional.of(userMock));

		User u = userService.findByPhoneCountryAndPhoneNumberAndLoginTypeAndActiveTrue("", "", LoginTypeEnum.USER);
		assertThat(u).isEqualTo(userMock);
	}

}
