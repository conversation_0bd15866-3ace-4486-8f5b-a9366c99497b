package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Expense;
import my.com.mandrill.component.domain.ExpenseType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExpenseDTO;
import my.com.mandrill.component.dto.model.ExpenseProjection;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.ExpenseRepository;
import my.com.mandrill.component.service.ExpenseService;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ExpenseServiceImpl implements ExpenseService {

	private final ExpenseRepository expenseRepository;

	@Override
	public Expense findByIdAndUser(String id, User user) {
		return expenseRepository.findByIdAndUser(id, user)
				.orElseThrow(ExceptionPredicate.expenseNotFoundByIdAndUser(id, user));
	}

	@Override
	public Optional<Expense> findOptionalByIdAndUser(String id, User user) {
		return expenseRepository.findByIdAndUser(id, user);
	}

	@Override
	public List<Expense> findByUserAndExpenseType(User user, ExpenseType expenseType, Sort sort) {
		return expenseRepository.findByUserAndExpenseType(user, expenseType, sort);
	}

	@Override
	@Transactional
	public Expense save(Expense expense) {
		return expenseRepository.save(expense);
	}

	@Override
	@Transactional
	public long deleteByUserAndId(User user, String id) {
		return expenseRepository.deleteByUserAndId(user, id);
	}

	@Override
	public List<ExpenseDTO> getUserCurrentExpenses(String refNo) {
		return expenseRepository.findByRefNo(refNo);
	}

	@Override
	public List<ExpenseProjection> findProjectionByUserId(String userId) {
		return expenseRepository.findProjectionByUserId(userId);
	}

}
