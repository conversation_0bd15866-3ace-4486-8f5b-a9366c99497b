package my.com.mandrill.utilities.core.token.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.core.dto.model.JwtTokenRequest;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.repository.UserTokenRepository;
import my.com.mandrill.utilities.core.token.service.UserTokenService;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserTokenServiceImpl implements UserTokenService {

	private final UserTokenRepository userTokenRepository;

	@Override
	public void create(JwtTokenRequest tokenRequest, long expiration) {
		userTokenRepository.save(UserToken.builder().id(tokenRequest.getAccessToken())
				.permissions(tokenRequest.getGrantedAuthorities().stream().map(GrantedAuthority::getAuthority)
						.collect(Collectors.toSet()))
				.userRefNo(tokenRequest.getUserRefNo()).userId(tokenRequest.getUserId()).expiration(expiration)
				.build());
	}

	@Override
	public Optional<UserToken> findByTokenId(String tokenId) {
		return userTokenRepository.findById(tokenId);
	}

	@Override
	public void deleteByTokenIds(List<String> tokenId) {
		Iterable<UserToken> userToken = userTokenRepository.findAllById(tokenId);
		userTokenRepository.deleteAll(userToken);
	}

	@Override
	public void deleteAllToken() {
		userTokenRepository.deleteAll();
	}

}
