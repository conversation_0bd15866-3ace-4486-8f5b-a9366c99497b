package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.InvestmentAccount;
import my.com.mandrill.component.dto.request.InvestmentAccountRequest;
import my.com.mandrill.component.dto.response.InvestmentAccountResponse;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;

import java.util.List;

public interface InvestmentAccountIntegrationService {

	InvestmentAccount create(InvestmentAccountRequest investmentAccountRequest);

	InvestmentAccount update(String id, InvestmentAccountRequest investmentAccountRequest);

	List<InvestmentAccountResponse> getInvestmentList();

	NetWorthDTO calculateNetWorth(String userId);

	Long count();

	void deleteInvestmentAccount(String id);

	void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source);

}
