package my.com.mandrill.component.service.impl;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.CustomOAuth2User;
import my.com.mandrill.component.dto.model.CustomOAuth2UserDetails;
import my.com.mandrill.component.service.AccountService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional(readOnly = true)
public class OAuth2UserServiceImpl extends DefaultOAuth2UserService {

	private final AccountService accountService;

	public OAuth2UserServiceImpl(AccountService accountService) {
		this.accountService = accountService;
	}

	@Override
	public OAuth2User loadUser(OAuth2UserRequest oAuth2UserRequest) throws OAuth2AuthenticationException {
		OAuth2User oAuth2User = super.loadUser(oAuth2UserRequest);

		return processOAuth2User(oAuth2UserRequest, oAuth2User);
	}

	private OAuth2User processOAuth2User(OAuth2UserRequest oAuth2UserRequest, OAuth2User oAuth2User) {
		CustomOAuth2User customOAuth2User = new CustomOAuth2User(oAuth2UserRequest, oAuth2User);
		if (StringUtils.isEmpty(customOAuth2User.getEmail())) {
			throw new OAuth2AuthenticationException(
					new OAuth2Error("OA001", "Email not found from OAuth2 provider", null));
		}

		User user = accountService.findOrRegisterOAuthUser(
				oAuth2UserRequest.getClientRegistration().getRegistrationId(), customOAuth2User);

		return CustomOAuth2UserDetails.create(user, oAuth2User.getAttributes());
	}

}
