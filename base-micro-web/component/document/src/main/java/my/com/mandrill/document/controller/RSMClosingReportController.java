package my.com.mandrill.document.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.document.dto.model.DocumentOutput;
import my.com.mandrill.document.service.RsmClosingReportService;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.ByteArrayInputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("reports/rsm-closing-balance")
public class RSMClosingReportController {

	private final RsmClosingReportService rsmClosingReportService;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.POINT_SUMMARY_REPORT)")
	public ResponseEntity<StreamingResponseBody> rsmClosingReport(@RequestParam(required = false) Integer month,
			@RequestParam(required = false) Integer year) {
		DocumentOutput output = rsmClosingReportService.populateDataClosingBalance(month, year);
		StreamingResponseBody streamingResponseBody = out -> {
			ByteArrayInputStream inputStream = new ByteArrayInputStream(
					Files.readAllBytes(Paths.get(output.getFile().getAbsolutePath())));
			IOUtils.copy(inputStream, out);
		};
		return ResponseEntity.ok()
				.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + output.getFileName())
				.header(HttpHeaders.CONTENT_LENGTH, String.valueOf(output.getFile().length()))
				.contentType(output.getDocumentType().getMediaType()).body(streamingResponseBody);

	}

}
