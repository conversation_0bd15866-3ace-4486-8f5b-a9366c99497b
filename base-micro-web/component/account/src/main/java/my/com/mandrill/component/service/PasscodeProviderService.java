package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.UserKeyRequest;

public interface PasscodeProviderService {

	boolean match(AppUser user, String reqPassword);

	UserKeyRequest reset(ResetPasscodeAware req, UsernameType usernameType);

	interface ResetPasscodeAware {

		String getKey();

		String getValue();

		String getUsername();

		String getPassword();

	}

}
