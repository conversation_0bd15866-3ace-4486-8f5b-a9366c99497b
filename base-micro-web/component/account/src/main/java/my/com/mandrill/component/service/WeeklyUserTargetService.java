package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.WeeklyUserTarget;
import my.com.mandrill.component.dto.model.WeeklyUserTargetDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Year;

public interface WeeklyUserTargetService {

	boolean existsByYearAndWeekNumber(Year year, int weekNumber);

	Page<WeeklyUserTargetDTO> findAllPageable(Pageable pageable, Integer weekNumber);

	WeeklyUserTargetDTO findById(String id);

	Long findByYearAndWeekNumber(Year year, int weekNumber);

	WeeklyUserTarget save(WeeklyUserTarget weeklyUserTarget);

	WeeklyUserTarget update(String id, WeeklyUserTarget weeklyUserTarget);

	void deleteById(String id);

}
