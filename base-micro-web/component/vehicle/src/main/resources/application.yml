info:
  project:
    version: #project.version#

spring:
  application:
    name: vehicle-component
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: *************************************************************
    username: moneyx
    password: moneyx
    hikari:
      schema: vehicle
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
      connection-timeout: 10000
      idle-timeout: 600000
  liquibase:
    default-schema: vehicle
    change-log: classpath:liquibase/master.xml
  jackson:
    serialization:
      fail-on-empty-beans: false
  elasticsearch:
    uris: [ http://0.0.0.0:9200 ]
    username: elastic
    password: moneyx
  jpa:
    show-sql: false
    properties:
      hibernate:
        default_schema: vehicle
        format_sql: false
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          lob:
            non_contextual_creation: true
    open-in-view: false
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.data.elasticsearch.ReactiveElasticsearchRepositoriesAutoConfiguration

  cache:
    redis:
      time-to-live: 1d
  data:
    redis:
      repositories:
        enabled: false
      timeout: 1000ms
      connect-timeout: 500ms
      host: localhost
      port: 6379
  kafka:
    bootstrap-servers: localhost:9092

server:
  port: 8908

management:
  health:
    elasticsearch:
      enabled: false
  endpoints:
    web:
      exposure:
        include:
          - health
          - info
          - prometheus

##https://cloud.spring.io/spring-cloud-netflix/reference/html/appendix.html
eureka:
  client:
    enabled: true
    serviceUrl:
      defaultZone: http://localhost:8761/eureka
  instance:
    preferIpAddress: true

##https://cloud.spring.io/spring-cloud-openfeign/reference/html/appendix.html
feign:
  client:
    config:
      default:
        loggerLevel: basic # default none | basic | headers | full
        connectTimeout: 10000 # default 10 sec
        readTimeout: 60000 # default 60 sec

security:
  jwt:
    # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 128` on your command line to generate
    base64-secret: NOIUaY1qF6H8wmQJzLWmzk8uJfJZfptuCUW2MBq4bSfobb90cTB9hP49nXiMAVUByeuEF1UhWO1Yj19ABWdZCgPcjzC0Q1Qz9qt9gXU44kLqEDmY7/JoRg5c65j31VnITVvjRSCnxj7eyqv093ETnuGy0QBrUCO624Cx7pk5QAQ=
    token-validity-in-seconds: 86400
    token-validity-in-seconds-for-remember-me: 2592000

base:
  internal-api-key: xseBs7rmRosoMGMWhHRRzuL6olSn2RCJ
  request-uri:
    open-api-server: http://localhost:8762/vehicle-component
  cdn:
    host: http://localhost:8762/vehicle-component/image
  cache:
    ehcache:
      time-to-live-seconds: 30
      max-entries: 100
  admin-email: <EMAIL>
  upload:
    base-path: upload-transaction/errors

logging:
  level:
    my.com.mandrill: WARN
    org.hibernate.sql: WARN
  pattern:
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"
