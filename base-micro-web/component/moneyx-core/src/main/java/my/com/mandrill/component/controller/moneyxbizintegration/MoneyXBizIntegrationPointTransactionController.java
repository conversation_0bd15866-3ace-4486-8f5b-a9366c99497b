package my.com.mandrill.component.controller.moneyxbizintegration;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.service.PointTransactionIntgService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("moneyxbiz-integration/point-transaction")
public class MoneyXBizIntegrationPointTransactionController {

	private final PointTransactionIntgService pointTransactionIntgService;

	@PublicAuth
	@SecurityRequirements
	@PutMapping("account-deletion")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_UPDATE_API)")
	public void handleAccountDeletionForPointTransaction(@RequestParam List<String> userIds) {
		pointTransactionIntgService.handleAccountDeletion(userIds, SourceSystemEnum.MXBIZ);
	}

}
