package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.CheckAccountResponse;
import my.com.mandrill.component.dto.response.PreRegisterResponse;
import my.com.mandrill.component.service.AccountIntgService;
import my.com.mandrill.component.service.AccountService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import my.com.mandrill.utilities.general.util.HttpRequestUtil;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/v1/public/account")
@RequiredArgsConstructor
@PublicAuth
@SecurityRequirements
public class PublicAccountController {

	private final AccountIntgService accountIntgService;

	private final AccountService accountService;

	@PostMapping("check")
	public CheckAccountResponse checkAccountExists(@Valid @RequestBody CheckAccountRequest request) {
		return accountIntgService.checkAccount(request);
	}

	@PostMapping("pre-register")
	public PreRegisterResponse preRegister(@Valid @RequestBody PreSignUpRequest request) {
		return accountIntgService.preRegister(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("finish-register")
	public void finishRegister(@Valid @RequestBody FinishSignUpRequest request) {
		accountIntgService.finishRegister(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/verify-email")
	public void updatePhoneVerification(@Valid @RequestBody EmailVerificationRequest request) {
		accountService.completeEmailVerification(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("/create-pin")
	public void createPin(@Valid @RequestBody PublicCreatePinRequest request) {
		accountIntgService.createPin(request);
	}

	@GetMapping("/my-ip")
	public String getMyIp(HttpServletRequest request) {
		return HttpRequestUtil.getClientIp(request);
	}

}
