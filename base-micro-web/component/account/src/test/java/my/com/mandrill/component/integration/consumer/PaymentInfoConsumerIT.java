package my.com.mandrill.component.integration.consumer;

import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.PaymentAccountStatus;
import my.com.mandrill.utilities.general.dto.request.PaymentInfoUpdateKafkaRequest;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "spring.kafka.bootstrap-servers")
public class PaymentInfoConsumerIT extends BaseIntegrationTest {

	private static final String USER_ID = "0ab87a42-995f-416d-8fef-e1bd8b3882c2";

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private UserRepository userRepository;

	@Test
	public void paymentAccount_approved_success() {
		PaymentInfoUpdateKafkaRequest request = PaymentInfoUpdateKafkaRequest.builder().userId(USER_ID)
				.paymentAccountStatus(PaymentAccountStatus.APPROVED).approvedDate(Instant.now()).build();
		this.sendKafkaPaymentInfo(request);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(PaymentAccountStatus.APPROVED, userOptional.get().getPaymentInfoStatus());
			Assertions.assertNotNull(userOptional.get().getPaymentInfoApprovedDate());
		});
	}

	@Test
	public void paymentAccount_removed_success() {
		PaymentInfoUpdateKafkaRequest request = PaymentInfoUpdateKafkaRequest.builder().userId(USER_ID)
				.paymentAccountStatus(PaymentAccountStatus.YET_TO_SUBMIT).build();
		this.sendKafkaPaymentInfo(request);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(PaymentAccountStatus.YET_TO_SUBMIT, userOptional.get().getPaymentInfoStatus());
			Assertions.assertNull(userOptional.get().getPaymentInfoApprovedDate());
		});
	}

	@Test
	public void paymentAccount_submitted_success() {
		PaymentInfoUpdateKafkaRequest request = PaymentInfoUpdateKafkaRequest.builder().userId(USER_ID)
				.paymentAccountStatus(PaymentAccountStatus.SUBMITTED).build();
		this.sendKafkaPaymentInfo(request);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(PaymentAccountStatus.SUBMITTED, userOptional.get().getPaymentInfoStatus());
			Assertions.assertNull(userOptional.get().getPaymentInfoApprovedDate());
		});
	}

	@Test
	public void paymentAccount_rejected_success() {
		PaymentInfoUpdateKafkaRequest request = PaymentInfoUpdateKafkaRequest.builder().userId(USER_ID)
				.paymentAccountStatus(PaymentAccountStatus.REJECTED).build();
		this.sendKafkaPaymentInfo(request);
		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Optional<User> userOptional = userRepository.findById(USER_ID);
			Assertions.assertTrue(userOptional.isPresent());
			Assertions.assertEquals(PaymentAccountStatus.REJECTED, userOptional.get().getPaymentInfoStatus());
			Assertions.assertNull(userOptional.get().getPaymentInfoApprovedDate());
		});
	}

	private void sendKafkaPaymentInfo(PaymentInfoUpdateKafkaRequest request) {
		kafkaTemplate.send(KafkaTopic.PAYMENT_INFO_UPDATE_TOPIC, USER_ID, jsonUtil.convertToString(request));
	}

}
