package my.com.mandrill.component.controller.moneyxbizintegration.admin;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.dto.request.PointDisbursementSearchRequest;
import my.com.mandrill.component.dto.response.BizPointDisbursementResponse;
import my.com.mandrill.component.service.PointEarningService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import my.com.mandrill.utilities.general.constant.PointDisbursementDateType;
import my.com.mandrill.utilities.general.constant.PointEarningStatus;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import my.com.mandrill.utilities.general.util.DateUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("moneyxbiz-integration/admin/point-disbursement")
public class MoneyXBizIntegrationAdminPointDisbursementController {

	private final PointEarningService pointEarningService;

	@PublicAuth
	@SecurityRequirements
	@GetMapping("pagination/all")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public Page<BizPointDisbursementResponse> getAllPointDisbursements(@RequestParam(required = false) String search,
			@RequestParam(required = false) RSMRelationType rsmScenario,
			@RequestParam(required = false) RSMFocalType rsmFocalType,
			@RequestParam(required = false) PointEarningStatus status,
			@RequestParam(required = false) PointDisbursementDateType dateType,
			@RequestParam(required = false) LocalDate dateFrom, @RequestParam(required = false) LocalDate dateTo,
			Pageable pageable) {
		return pointEarningService.getAllPointDisbursements(pageable,
				PointDisbursementSearchRequest.builder().search(search).rsmScenario(rsmScenario)
						.rsmFocalType(rsmFocalType).status(status).source(SourceSystemEnum.MXBIZ).dateType(dateType)
						.dateFrom(dateFrom).dateTo(dateTo).isMoneyXFocalTypeExcluded(true).build())
				.map(MapStructConverter.MAPPER::toBizPointDisbursementResponse);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("export")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public List<BizPointDisbursementResponse> getAllPointDisbursementsForExport(
			@RequestParam(required = false) String search, @RequestParam(required = false) RSMRelationType rsmScenario,
			@RequestParam(required = false) RSMFocalType rsmFocalType,
			@RequestParam(required = false) PointEarningStatus status,
			@RequestParam(required = false) PointDisbursementDateType dateType,
			@RequestParam(required = false) @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate dateFrom,
			@RequestParam(required = false) @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate dateTo) {
		return pointEarningService.getAllPointDisbursements(PointDisbursementSearchRequest.builder().search(search)
				.rsmScenario(rsmScenario).rsmFocalType(rsmFocalType).status(status).source(SourceSystemEnum.MXBIZ)
				.dateType(dateType).dateFrom(dateFrom).dateTo(dateTo).isMoneyXFocalTypeExcluded(true).build()).stream()
				.map(MapStructConverter.MAPPER::toBizPointDisbursementResponse).toList();
	}

}
