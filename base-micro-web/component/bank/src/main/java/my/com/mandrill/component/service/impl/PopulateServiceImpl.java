package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.dto.model.ProductConfigurationTemplatePageDTO;
import my.com.mandrill.component.service.LoanService;
import my.com.mandrill.component.service.PopulateService;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.VaultProductResponse;
import my.com.mandrill.utilities.feign.dto.VaultSubProductDTO;
import my.com.mandrill.utilities.feign.dto.model.VaultTypeRequestDTO;
import my.com.mandrill.utilities.general.constant.LoanPaymentStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PopulateServiceImpl implements PopulateService {

	private final CommonFeignClient commonFeignClient;

	private final LoanService loanService;

	@Override
	public void populateDocumentAndVaultName(ProductConfigurationTemplatePageDTO template) {
		List<VaultProductResponse> vaultSubType = commonFeignClient.findSubTypes(
				List.of(new VaultTypeRequestDTO(template.getVaultTypeId(), template.getDocumentTypeId())));
		if (!vaultSubType.isEmpty()) {
			VaultProductResponse category = vaultSubType.get(0);
			template.setVaultTypeName(category.getVaultName());
			template.setDocumentTypeName(
					Optional.ofNullable(category.getDocumentType()).map(VaultSubProductDTO::getName).orElse(null));
		}
	}

	@Override
	public Loan populateLoanPaymentStatus(Loan data) {
		BigDecimal endingBalance = loanService.calculateLoanEndingBalance(data);
		LoanPaymentStatus newStatus = BigDecimal.ZERO.compareTo(endingBalance) >= 0 ? LoanPaymentStatus.FULLY_PAID
				: LoanPaymentStatus.ON_GOING;

		boolean needsUpdate = !endingBalance.equals(data.getLoanBalance()) || data.getPaymentStatus() != newStatus;

		if (needsUpdate) {
			data.setLoanBalance(endingBalance);
			data.setPaymentStatus(newStatus);
			return data;
		}
		return null;
	}

}
