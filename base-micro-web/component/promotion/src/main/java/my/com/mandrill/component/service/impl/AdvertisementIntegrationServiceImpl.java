package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Advertisement;
import my.com.mandrill.component.domain.AdvertisementAdditionalData;
import my.com.mandrill.component.domain.AdvertisementDetail;
import my.com.mandrill.component.domain.AdvertisementPredefinedProduct;
import my.com.mandrill.component.dto.model.AdvertisementAdditionalDataDTO;
import my.com.mandrill.component.dto.model.AdvertisementRequestDTO;
import my.com.mandrill.component.dto.request.AdvertisementRequest;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.dto.*;
import my.com.mandrill.utilities.feign.dto.model.AiProductCreditCardDTO;
import my.com.mandrill.utilities.feign.dto.response.AdvertisementAttachmentPopUpResponseDTO;
import my.com.mandrill.utilities.feign.dto.response.AdvertisementResponse;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.AttachmentTypeFileStorageEnum;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.UserInterestProductTypeEnum;
import my.com.mandrill.utilities.general.dto.request.CacheRequest;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.storage.util.LocalFileUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.Duration;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdvertisementIntegrationServiceImpl implements AdvertisementIntegrationService {

	private final AdvertisementService advertisementService;

	private final ProxyFeignClient proxy;

	private final AdvertisementDetailService advertisementDetailService;

	private final AdvertisementProductService advertisementProductService;

	private final AdvertisementDataService advertisementDataService;

	private final ObjectMapper objectMapper;

	private final RedisService redisService;

	@Override
	public Advertisement uploadBanner(String id, AdvertisementAttachmentGroupDTO advertisementAttachmentGroupDTO) {
		Advertisement advertisement = advertisementService.findById(id);
		advertisement.setId(id);
		advertisementAttachmentGroupDTO.setAttachmentGroupId(advertisement.getAttachmentGroupId());
		advertisementAttachmentGroupDTO.setClassName(EntityName.ADVERTISEMENT.getCode());
		advertisementAttachmentGroupDTO.setType(AttachmentTypeFileStorageEnum.ADVERTISEMENT_IMAGE.toString());

		AdvertisementDTO advertisementDTOResponse = proxy.getCommonFeignClient()
				.uploadAdvertisementBanner(advertisementAttachmentGroupDTO);
		if (advertisementDTOResponse.getBannerImage() != null) {
			advertisement.setBannerImage(advertisementDTOResponse.getBannerImage());
		}
		if (advertisementDTOResponse.getImageEn() != null) {
			advertisement.setImageEn(advertisementDTOResponse.getImageEn());
		}
		if (advertisementDTOResponse.getImageCn() != null) {
			advertisement.setImageCn(advertisementDTOResponse.getImageCn());
		}
		if (advertisementDTOResponse.getImageMy() != null) {
			advertisement.setImageMy(advertisementDTOResponse.getImageMy());
		}
		if (StringUtils.isBlank(advertisement.getAttachmentGroupId())) {
			advertisement.setAttachmentGroupId(advertisementDTOResponse.getAttachmentGroupId());
		}
		advertisementService.save(advertisement);
		invalidateAdvertisementCache();

		return advertisement;
	}

	@Override
	public Advertisement update(String id, Advertisement advertisement) {
		Advertisement existAdvertisement = advertisementService.findById(id);
		if (advertisement.getCode() != null
				&& !existAdvertisement.getCode().equalsIgnoreCase(advertisement.getCode())) {
			advertisementService.existByCode(advertisement.getCode());
		}

		existAdvertisement.setCode(advertisement.getCode());
		existAdvertisement.setJourneyConfigurationGroup(advertisement.getJourneyConfigurationGroup());
		existAdvertisement.setEntityName(advertisement.getEntityName());
		existAdvertisement.setName(advertisement.getName());
		existAdvertisement.setDescription(advertisement.getDescription());
		existAdvertisement.setSeq(advertisement.getSeq());
		existAdvertisement.setStartDate(advertisement.getStartDate());
		existAdvertisement.setEndDate(advertisement.getEndDate());
		existAdvertisement.setStatus(advertisement.getStatus());
		existAdvertisement.setIsHavePopup(advertisement.getIsHavePopup());
		existAdvertisement.setTermsAndConditionVersion(advertisement.getTermsAndConditionVersion());
		existAdvertisement.setTemplate(advertisement.getTemplate());
		existAdvertisement.getAdvertisementPredefinedProducts().clear();
		existAdvertisement.setAdvertisementPredefinedProducts(advertisement.getAdvertisementPredefinedProducts());
		existAdvertisement.getAdvertisementPredefinedProducts().forEach(
				advertisementPredefinedProduct -> advertisementPredefinedProduct.setAdvertisement(existAdvertisement));
		existAdvertisement.setIsBalanceTransfer(advertisement.getIsBalanceTransfer());

		return existAdvertisement;
	}

	@Override
	public Advertisement create(Advertisement advertisement) {
		if (advertisement.getCode() != null) {
			advertisementService.existByCode(advertisement.getCode());
		}
		return advertisement;
	}

	@Override
	public AdvertisementResponse populateByLanguage(Advertisement advertisement, String language) {
		AdvertisementResponse advertisementResponse = MapStructConverter.MAPPER.toAdvertisementResponse(advertisement);
		Optional<AdvertisementDetail> advertisementDetailOptional = advertisementDetailService
				.findOptionalByAdvertisementIdAndLanguage(advertisement.getId(), language);
		if (advertisementDetailOptional.isPresent()) {
			AdvertisementDetail advertisementDetail = advertisementDetailOptional.get();
			advertisementResponse.setTitle(advertisementDetail.getTitle());
			advertisementResponse.setSubtitle(advertisementDetail.getSubtitle());
			advertisementResponse.setFooterImage(advertisementDetail.getFooterImage());
			advertisementResponse.setFullImage(advertisementDetail.getFullImage());
			advertisementResponse.setTncLabel(advertisementDetail.getTncLabel());
			advertisementResponse.setTncDocUrl(advertisementDetail.getTncDocUrl());
		}

		List<AdvertisementAdditionalData> data = advertisementDataService.findByAdvertisement(advertisement);
		advertisementResponse.setAdditionalData(MapStructConverter.MAPPER.toMap(data));
		return advertisementResponse;
	}

	@Override
	public AdvertisementResponse populateByProductWithDetails(Advertisement advertisement,
			AdvertisementRequestDTO advertisementRequestDTO) {

		AdvertisementResponse advertisementResponse = new AdvertisementResponse();
		if (advertisement != null) {
			List<AdvertisementPredefinedProduct> advertisementPredefinedProducts = advertisementProductService
					.findByAdvertisementOrderBySequenceAsc(advertisement);

			List<AiProductCreditCardDTO> productDetailResults = new ArrayList<>();
			for (AdvertisementPredefinedProduct advertisementPredefinedProduct : advertisementPredefinedProducts) {

				UserInterestProductTypeEnum productType = Optional
						.ofNullable(advertisementPredefinedProduct.getProductType())
						.orElse(UserInterestProductTypeEnum.CREDIT_CARD);

				if (StringUtils.isNotBlank(advertisementRequestDTO.getPlatform())
						&& !proxy.getAiFeignClient().findStatusProductTypeForPlatform(productType.getCode(),
								advertisementRequestDTO.getPlatform())) {
					log.warn("product ignored by platform: {}, {}", productType.getCode(),
							advertisementRequestDTO.getPlatform());
					continue;
				}

				List<AiProductCreditCardDTO> aiProductDetail = proxy.aiFindProductCreditCard(productType,
						advertisementPredefinedProduct.getProductId());

				if (!aiProductDetail.isEmpty()) {
					productDetailResults.add(aiProductDetail.get(0));
				}
			}
			advertisementResponse = populateByLanguage(advertisement, advertisementRequestDTO.getLanguage());
			advertisementResponse.setPredefinedProducts(productDetailResults);
		}

		return advertisementResponse;
	}

	@Override
	public Advertisement uploadPdf(Advertisement advertisement, AttachmentGroupDTO attachmentGroupDTO) {
		attachmentGroupDTO.setAttachmentGroupId(advertisement.getPdfAttachmentGroupId());
		attachmentGroupDTO.setClassName(Advertisement.class.getSimpleName());
		AttachmentGroupResponse attachmentGroupResponse = proxy.getCommonFeignClient()
				.uploadAttachment(attachmentGroupDTO);
		if (attachmentGroupResponse != null) {
			AttachmentResponse latestAttachment = attachmentGroupResponse.getAttachments().stream()
					.sorted(Comparator.comparing(AttachmentResponse::getCreatedDate).reversed()).toList().get(0);
			StringBuilder url = new StringBuilder();
			if (attachmentGroupResponse.getPath() != null) {
				url.append(attachmentGroupResponse.getPath() + File.separator);
			}
			url.append(LocalFileUtil.formatActualFile(latestAttachment.getId(), latestAttachment.getName()));
			advertisement.setPdfAttachmentGroupId(attachmentGroupResponse.getAttachmentGroupId());
			advertisement.setPdfUrl(url.toString());
			advertisementService.save(advertisement);
		}
		return advertisement;
	}

	@Override
	public Advertisement uploadPopUpImage(Advertisement advertisement,
			AdvertisementRequest.AdvertisementImageRequest popUpRequest) {
		AdvertisementAttachmentPopUpGroupDTO attachmentPopUpGroupDTO = objectMapper.convertValue(popUpRequest,
				AdvertisementAttachmentPopUpGroupDTO.class);
		attachmentPopUpGroupDTO.setAttachmentGroupId(advertisement.getPopUpAttachmentGroupId());
		attachmentPopUpGroupDTO.setClassName(Advertisement.class.getSimpleName());
		attachmentPopUpGroupDTO.setPopUpImageCn(popUpRequest.getPopUpImageCn());
		attachmentPopUpGroupDTO.setPopUpImageMy(popUpRequest.getPopUpImageMy());
		attachmentPopUpGroupDTO.setPopUpImageEn(popUpRequest.getPopUpImageEn());
		attachmentPopUpGroupDTO.setFullImageCn(popUpRequest.getFullImageCn());
		attachmentPopUpGroupDTO.setFullImageMy(popUpRequest.getFullImageMy());
		attachmentPopUpGroupDTO.setFullImageEn(popUpRequest.getFullImageEn());
		AdvertisementAttachmentPopUpResponseDTO response = proxy.getCommonFeignClient()
				.uploadPopUpImage(attachmentPopUpGroupDTO);
		if (response.getPopUpImageEnUrl() != null) {
			advertisement.setPopUpImageEn(response.getPopUpImageEnUrl());
		}
		if (response.getPopUpImageMyUrl() != null) {
			advertisement.setPopUpImageMy(response.getPopUpImageMyUrl());
		}
		if (response.getPopUpImageCnUrl() != null) {
			advertisement.setPopUpImageCn(response.getPopUpImageCnUrl());
		}
		if (response.getAttachmentGroupId() != null) {
			advertisement.setPopUpAttachmentGroupId(response.getAttachmentGroupId());
		}
		if (response.getFullImageCnUrl() != null) {
			advertisement.setFullImageCn(response.getFullImageCnUrl());
		}
		if (response.getFullImageMyUrl() != null) {
			advertisement.setFullImageMy(response.getFullImageMyUrl());
		}
		if (response.getFullImageEnUrl() != null) {
			advertisement.setFullImageEn(response.getFullImageEnUrl());
		}
		return advertisementService.save(advertisement);
	}

	@Override
	public boolean checkNowIsInCampaignPeriodById(String id) {
		return advertisementService.checkNowIsInCampaignPeriodById(id);
	}

	@Override
	public Advertisement addAdditionalData(Advertisement advertisement, AdvertisementRequest advertisementRequest) {

		advertisement.getAdvertisementAdditionalData().clear();

		for (AdvertisementAdditionalDataDTO addon : advertisementRequest.getAdditionalData()) {
			if (AdvertisementAdditionalData.IS_BALANCE_TRANSFER_KEY.equals(addon.getKey())
					&& Objects.nonNull(advertisement.getIsBalanceTransfer())) {
				addon.setValue(String.valueOf(advertisement.getIsBalanceTransfer()));
			}
			advertisement.getAdvertisementAdditionalData().add(AdvertisementAdditionalData.builder().key(addon.getKey())
					.value(addon.getValue()).advertisement(advertisement).build());
		}

		return advertisementService.save(advertisement);
	}

	@Override
	public List<Advertisement> excludeProductException(List<Advertisement> advertisements, String platform) {
		if (platform == null || platform.isEmpty()) {
			return advertisements;
		}

		advertisements.removeIf(advertisement -> advertisement.getEntityName().equals(EntityName.PREDEFINED_PRODUCT)
				&& shouldExcludeAdvertisement(advertisement, platform));

		return advertisements;
	}

	@Override
	public List<AdvertisementResponse> getActiveAdvertisements(String language, String platform, Sort sort) {

		String cacheKey = CacheKey.ACTIVE_ADVERTISEMENTS.formatted(language, platform, sort.toString());
		Optional<List<AdvertisementResponse>> cachedAdvertisements = redisService.getFromValue(cacheKey,
				new TypeReference<>() {
				});
		if (cachedAdvertisements.isPresent()) {
			log.info("advertisement serve by cache: {}", cacheKey);
			return cachedAdvertisements.get();
		}

		List<Advertisement> results = advertisementService.findAvailableByActive(sort);
		results = excludeProductException(results, platform);

		List<AdvertisementResponse> response = results.stream()
				.map(advertisement -> populateByLanguage(advertisement, language)).toList();

		redisService.putToValue(cacheKey, response, Duration.ofHours(1));

		return response;
	}

	private boolean shouldExcludeAdvertisement(Advertisement advertisement, String platform) {

		List<AdvertisementPredefinedProduct> advertisementPredefinedProducts = advertisementProductService
				.findByAdvertisementOrderBySequenceAsc(advertisement);

		long platformMatchCount = advertisementPredefinedProducts.stream().filter(product -> !proxy.getAiFeignClient()
				.findStatusProductTypeForPlatform(product.getProductType().getCode(), platform)).count();

		return platformMatchCount == advertisementPredefinedProducts.size()
				&& !advertisementPredefinedProducts.isEmpty();
	}

	@Override
	public void invalidateAdvertisementCache() {
		String cachePattern = CacheKey.ACTIVE_ADVERTISEMENTS.formatted("*", "*", "*");
		int deletedCount = redisService.deleteCache(CacheRequest.builder().patterns(Set.of(cachePattern)).build());
		log.info("{} advertisement cache invalidated", deletedCount);
	}

}
