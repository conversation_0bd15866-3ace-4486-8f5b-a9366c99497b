package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.constant.TncTypeEnum;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.model.CheckAccountProjection;
import my.com.mandrill.component.dto.model.UserDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.CheckAccountResponse;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.dto.response.PreRegisterResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.feign.client.MoneyXCoreFeignClient;
import my.com.mandrill.utilities.feign.dto.model.ReferralCodeDto;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserKafkaRequest;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.request.UserPublicWebRequest;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import my.com.mandrill.utilities.general.dto.response.UserRefereeUpdateRequest;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.PhoneNumberException;
import my.com.mandrill.utilities.general.service.KafkaSender;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountIntgServiceImpl implements AccountIntgService {

	private final AccountService accountService;

	private final UserService userService;

	private final JSONUtil jsonUtil;

	private final ValidationService validationService;

	private final DeviceBindingService deviceBindingService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final MoneyXCoreFeignClient moneyXCoreFeignClient;

	private final KeyRequestService keyRequestService;

	private final PasswordEncoder passwordEncoder;

	private final ProxyFeignClient proxyFeignClient;

	private final RedisService redisService;

	private final AppUserService appUserService;

	private final PopulateService populateService;

	private final CaptchaService captchaService;

	private final KafkaSender kafkaSender;

	private final FeatureFlagOutbound featureFlagOutbound;

	@Override
	public KeyResponse requestOtp(OTPRequest request) {
		if (!captchaService.verify(MapStructConverter.MAPPER.toCaptchaVerifyData(request))) {
			throw new BusinessException(ErrorCodeGlobalEnum.REQUEST_VERIFICATION_FAILED);
		}
		String key = String.format(CacheKey.REQUEST_OTP_CONCURRENT_CACHE_KEY, request.getUsername(),
				request.getRequestKeyType());
		return redisService.executeSingleProcess(key, request.getUsername(), () -> {
			this.validateValidReferralCode(request.getReferralCode());
			return switch (request.getUsernameType()) {
				case PHONE_NUMBER -> accountService.requestOTPByPhoneNumber(request);
				case EMAIL -> accountService.requestOTPByEmail(request);
			};
		}, ErrorCodeGlobalEnum.TOO_MANY_REQUEST, TimeUnit.SECONDS);
	}

	@Override
	public void register(SignUpRequest signUpRequest) {
		String code = this.validateValidReferralCode(signUpRequest.getReferralCode());
		// Transformation
		User user = jsonUtil.convertValue(signUpRequest, User.class);
		user.setReferralCode(null); // set null referral code
		user.setReferralCodeUsed(code);
		user.setLoginType(LoginTypeEnum.USER);
		user.setPhoneNumber(PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(signUpRequest.getPhoneCountry() + signUpRequest.getPhoneNumber())
				.getPhoneNumber());
		// Validation
		this.validationService.validateRegister(user);
		accountService.checkIfUserExists(user);
		boolean toValidateDeviceBinding = validationService.getDeviceBindingValidationFlag();
		if (toValidateDeviceBinding) {
			validationService.validateRegisterDeviceBinding(signUpRequest.getDeviceId());
		}
		// Implementation
		User result = accountService.registerUser(user);
		if (toValidateDeviceBinding) {
			deviceBindingService.process(result.getId(), signUpRequest.getDeviceId());
		}
		sentEventReferralFriendRelation(code, user);
		if (StringUtils.isBlank(signUpRequest.getReferralCode())) {
			sendEventToUpdateUserRSMRelationTypeToNonReferral(user.getId());
		}
	}

	private String validateValidReferralCode(String referralCode) {
		if (StringUtils.isBlank(referralCode)) {
			return null;
		}

		String phoneReferralCode = getReferralCode(referralCode);
		if (PhoneNumberUtil.isValidPhoneNumber(phoneReferralCode)) {
			PhoneNumberUtil.ExtractedPhoneNumber extracted = PhoneNumberUtil
					.getPhoneNumberWithNoCountryCode(phoneReferralCode);

			String encryptedPhoneNumber = AesCryptoUtil.basicEncrypt(extracted.getPhoneNumber());

			User user = userService.findByPhoneCountryAndPhoneNumber(extracted.getPhoneCountry(), encryptedPhoneNumber)
					.orElseThrow(() -> new BusinessException(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE));

			if (BooleanUtil.or(StringUtils.isBlank(user.getReferralCodeTermConditionVersion()),
					!user.getPhoneVerified()))
				throw new BusinessException(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE);

			String code = moneyXCoreFeignClient.existsReferralCodeByUserId(user.getId());
			if (StringUtils.isBlank(code)) {
				throw new BusinessException(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE);
			}
			return code;
		}

		Boolean existInBiz = moneyXCoreFeignClient.existsReferralCode(referralCode, SourceSystemEnum.MXBIZ.name());
		Boolean existInApp = moneyXCoreFeignClient.existsReferralCode(referralCode, SourceSystemEnum.MXAPP.name());
		if (BooleanUtil.and(BooleanUtils.isFalse(existInBiz), BooleanUtils.isFalse(existInApp))) {
			throw new BusinessException(ErrorCodeGlobalEnum.INVALID_REFERRAL_CODE);
		}

		return referralCode;
	}

	private String getReferralCode(String referralCode) {
		StringBuilder builder = new StringBuilder(referralCode);
		builder.deleteCharAt(0);
		builder.insert(0, Constant.LOCAL_MALAYSIAN_PHONE_COUNTRY);
		return builder.toString();
	}

	@Override
	public CheckAccountResponse checkAccount(CheckAccountRequest checkAccountRequest) {
		Optional<CheckAccountProjection> userOpt = userService.checkAccountByUsername(checkAccountRequest.getUsername(),
				checkAccountRequest.getUsernameType());

		if (userOpt.isEmpty()) {
			return new CheckAccountResponse();
		}
		CheckAccountProjection user = userOpt.get();

		return CheckAccountResponse.builder().exists(true)
				.preRegister(
						BooleanUtil.and(StringUtils.isBlank(user.getPassword()), StringUtils.isBlank(user.getPin())))
				.hasPassword(StringUtils.isNotBlank(user.getPassword())).emailVerified(user.isEmailVerified())
				.email(EmailUtil.maskEmail(user.getEmail()))
				.phoneNumber(MaskingUtil.maskPhoneNumber(user.getPhoneCountry() + user.getPhoneNumber()))
				.hasPin(StringUtils.isNotBlank(user.getPin())).build();
	}

	@Override
	public PreRegisterResponse preRegister(PreSignUpRequest signUpRequest) {
		String code = this.validateValidReferralCode(signUpRequest.getReferralCode());

		UserPublicWebRequest request = MapStructConverter.MAPPER.toUserPublicWebRequest(signUpRequest);
		request.setRequestKeyType(RequestKeyType.PRE_REGISTRATION);
		UserPublicWebResponse userPublicWebResponse = accountService.checkAndRegisterNewAccount(request);

		ReferralCodeDto referralCodeDto = moneyXCoreFeignClient
				.getReferralCodeOfReferrerByReferredId(userPublicWebResponse.getId());
		if (referralCodeDto == null) {
			Optional<User> user = userService.findById(userPublicWebResponse.getId());
			user.ifPresent(value -> sentEventReferralFriendRelation(code, value));
		}

		if (StringUtils.isBlank(signUpRequest.getReferralCode())) {
			sendEventToUpdateUserRSMRelationTypeToNonReferral(userPublicWebResponse.getId());
		}

		int expiryInHours = featureFlagOutbound.getIntegerValue(GlobalSystemConfigurationEnum.KEY_REQUEST_VALID_HOURS);
		UserKeyRequest keyRequest = keyRequestService.createRequestKey(userPublicWebResponse.getId(),
				RequestKeyType.CREATE_PASSWORD, expiryInHours);

		PreRegisterResponse preRegisterResponse = new PreRegisterResponse();
		preRegisterResponse.setKey(keyRequest.getKeyValue());
		preRegisterResponse.setUserId(userPublicWebResponse.getId());
		return preRegisterResponse;

	}

	@Override
	public void finishRegister(FinishSignUpRequest request) {
		this.validateValidReferralCode(request.getReferralCode());

		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(request.getKey());
		Optional<UserKeyRequest> keyRequestOpt = keyRequestService.findValidByKeyValueAndType(keyInput.getKeyValueRaw(),
				RequestKeyType.CREATE_PASSWORD);

		if (keyRequestOpt.isEmpty()) {
			log.error("[CREATE-PASSWORD] invalid request checking, data not found or not status pending");
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}

		Optional<User> existingPreUser = userService.findById(keyRequestOpt.get().getUsername());
		if (existingPreUser.isEmpty()) {
			log.error("[CREATE-PASSWORD] invalid request checking, key found but username invalid");
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}
		User user = existingPreUser.get();
		if (StringUtils.isNotBlank(user.getPassword()) || StringUtils.isNotBlank(user.getPin())) {
			throw new BusinessException(ErrorCodeEnum.NOT_PRE_REGISTRATION);
		}

		if (PasscodeType.PASSWORD.equals(request.getPasscodeType())) {
			user.setPassword(passwordEncoder.encode(request.getPassword()));
		}
		else if (PasscodeType.PIN.equals(request.getPasscodeType())) {
			user.setPin(passwordEncoder.encode(request.getPassword()));
		}
		user.setLoginFailAttempt(0);
		userService.save(user);
		log.info("CREATE PASSWORD COMPLETED");

		UserKeyRequest keyRequest = keyRequestOpt.get();
		keyRequest.setStatus(RequestKeyStatus.COMPLETED);
		keyRequest.setCompletionDate(Instant.now());
		keyRequestService.save(keyRequest);
	}

	@Override
	public void updateTncAgreement(TncAgreementRequest request) {
		if (TncTypeEnum.REFERRAL_CODE_TERM_AND_CONDITION.equals(request.getType())) {
			String referralCodeTnc = proxyFeignClient.getCommonFeignClient()
					.getSystemConfigurationByCodeAndInstitutionId(
							SystemConfigurationEnum.valueOf(request.getType().name()).getCode(),
							Constant.DEFAULT_INSTITUTION_ID)
					.getValue();

			userService.updateReferralTnc(referralCodeTnc);
		}
	}

	private void sentEventReferralFriendRelation(String referralCode, User user) {
		if (StringUtils.isNotBlank(referralCode)) {
			kafkaSender.safeSend(KafkaTopic.USER_REFEREE_UPDATE_TOPIC, user.getId(),
					UserRefereeUpdateRequest.builder().referredId(user.getId()).referrerCode(referralCode)
							.signUpDate(user.getCreatedDate()).source(SourceSystemEnum.MXAPP).build());
		}
	}

	@Override
	public UserDTO getAccount() {
		AppUser user = appUserService.findByRefNo(SecurityUtil.currentUserLogin());

		boolean hasMobileReferralCode = BooleanUtil.and(user.getPhoneVerified(), user.getPhoneCountry().equals("+60"),
				StringUtils.isNotBlank(user.getReferralCodeTermConditionVersion()));

		UserDTO userDTO = MapStructConverter.MAPPER.toUserDTO(user);
		userDTO.setHasMobileReferralCode(hasMobileReferralCode);
		userDTO.setReferralCodePhoneNumber(extractPhoneNumber(user));
		userDTO.setCountry(populateService.getCountryById(user.getCountryId()));
		userDTO.setState(populateService.getStateById(user.getStateId()));
		return userDTO;
	}

	private String extractPhoneNumber(AppUser user) {
		try {
			PhoneNumberUtil.ExtractedPhoneNumber extracted = PhoneNumberUtil
					.getPhoneNumberWithNoCountryCode(user.getPhoneCountry() + user.getPhoneNumber());
			return extracted.getLocalFormatPhoneNumber();
		}
		catch (PhoneNumberException e) {
			return null;
		}
	}

	private void sendEventToUpdateUserRSMRelationTypeToNonReferral(String userId) {
		kafkaTemplate.send(KafkaTopic.UPDATE_USER_TOPIC, jsonUtil.convertToString(UpdateUserKafkaRequest.builder()
				.id(userId).rsmRelationType(RSMRelationType.NON_REFERRAL.name()).build()));
	}

	@Override
	public void createPin(PublicCreatePinRequest request) {

		Optional<User> userOpt = switch (request.getUsernameType()) {
			case PHONE_NUMBER -> {
				PhoneNumberUtil.ExtractedPhoneNumber phoneNumber = PhoneNumberUtil
						.getPhoneNumberWithNoCountryCode(request.getUsername());
				yield userService.findOptionalByPhoneCountryAndPhoneNumber(phoneNumber.getPhoneCountry(),
						phoneNumber.getPhoneNumber());
			}
			case EMAIL -> userService.findByEmail(request.getUsername());
		};

		if (userOpt.isEmpty()) {
			throw new BusinessException(ErrorCodeGlobalEnum.ACCOUNT_DOES_NOT_EXIST);
		}

		User user = userOpt.get();

		if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
			throw new BusinessException(ErrorCodeEnum.INVALID_PASSWORD);
		}

		user.setPin(passwordEncoder.encode(request.getPin()));
		user.setLoginFailAttempt(0);
		userService.save(user);

	}

}
