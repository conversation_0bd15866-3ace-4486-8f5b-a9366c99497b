package my.com.mandrill.component.repository;

import my.com.mandrill.component.domain.RsmDetail;
import my.com.mandrill.component.domain.RsmHeader;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;

public interface RsmDetailRepository extends JpaRepository<RsmDetail, String> {

	List<RsmDetail> findByHeaderIdIn(Collection<String> ids);

	void deleteAllByHeader(RsmHeader header);

}
