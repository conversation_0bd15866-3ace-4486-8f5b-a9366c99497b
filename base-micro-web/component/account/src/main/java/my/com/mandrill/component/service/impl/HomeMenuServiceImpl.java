package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.HomeMenuStatusEnum;
import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.domain.HomeMenuInfo;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.HomeMenuRepository;
import my.com.mandrill.component.service.HomeMenuService;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class HomeMenuServiceImpl implements HomeMenuService {

	private final HomeMenuRepository homeMenuRepository;

	@Override
	@Transactional
	public HomeMenu save(HomeMenu homeMenu) {
		return homeMenuRepository.save(homeMenu);
	}

	@Override
	public HomeMenu findById(String id) {
		return homeMenuRepository.findById(id).orElseThrow(ExceptionPredicate.homeMenuNotFound(id));
	}

	@Override
	@Transactional
	public void delete(HomeMenu homeMenu) {
		homeMenuRepository.delete(homeMenu);
	}

	@Override
	public boolean existsByCode(String code) {
		return homeMenuRepository.existsByCodeIgnoreCase(code);
	}

	@Override
	public Page<HomeMenu> findAll(String code, Pageable pageable) {
		return homeMenuRepository.findByCodeContainsIgnoreCase(StringUtils.defaultIfBlank(code, StringUtils.EMPTY),
				pageable);
	}

	@Override
	public List<HomeMenuInfo> findEnabledState(String name) {
		return homeMenuRepository.findByState(HomeMenuStatusEnum.ENABLED,
				StringUtils.defaultIfBlank(name, StringUtils.EMPTY).toLowerCase());
	}

	@Override
	public List<HomeMenu> findWithoutParentId() {
		return homeMenuRepository.findAllByParentIdIsNull();
	}

	@Override
	public Page<HomeMenu> findAllWithoutParentId(String code, Pageable pageable) {
		return homeMenuRepository.findAllByCodeContainsIgnoreCaseAndParentIdIsNull(
				StringUtils.defaultIfBlank(code, StringUtils.EMPTY), pageable);
	}

	@Override
	public Page<HomeMenu> findAllWithParentId(String code, Pageable pageable) {
		return homeMenuRepository.findAllByCodeContainsIgnoreCaseAndParentIdIsNotNull(
				StringUtils.defaultIfBlank(code, StringUtils.EMPTY), pageable);
	}

	@Override
	public Page<HomeMenu> findAllProducts(String code, Pageable pageable) {
		return homeMenuRepository.findByCodeContainsIgnoreCaseAndHomeMenuProductNotEmpty(
				StringUtils.defaultIfBlank(code, StringUtils.EMPTY), pageable);
	}

	@Override
	public List<HomeMenu> findAllWithParent() {
		return homeMenuRepository.findAllWithParent();
	}

}
