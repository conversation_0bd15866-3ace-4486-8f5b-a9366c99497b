package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Advertisement;
import my.com.mandrill.component.dto.model.AdvertisementRequestDTO;
import my.com.mandrill.component.dto.request.AdvertisementRequest;
import my.com.mandrill.utilities.feign.dto.AdvertisementAttachmentGroupDTO;
import my.com.mandrill.utilities.feign.dto.AttachmentGroupDTO;
import my.com.mandrill.utilities.feign.dto.response.AdvertisementResponse;
import org.springframework.data.domain.Sort;

import java.util.List;

public interface AdvertisementIntegrationService {

	Advertisement uploadBanner(String id, AdvertisementAttachmentGroupDTO advertisementAttachmentGroupDTO);

	Advertisement update(String id, Advertisement advertisement);

	Advertisement create(Advertisement advertisement);

	AdvertisementResponse populateByLanguage(Advertisement advertisement, String language);

	AdvertisementResponse populateByProductWithDetails(Advertisement advertisement,
			AdvertisementRequestDTO advertisementRequestDTO);

	Advertisement uploadPdf(Advertisement advertisement, AttachmentGroupDTO attachmentGroupDTO);

	Advertisement uploadPopUpImage(Advertisement advertisement,
			AdvertisementRequest.AdvertisementImageRequest popUpRequest);

	boolean checkNowIsInCampaignPeriodById(String id);

	Advertisement addAdditionalData(Advertisement advertisement, AdvertisementRequest advertisementRequest);

	List<Advertisement> excludeProductException(List<Advertisement> advertisements, String platform);

	List<AdvertisementResponse> getActiveAdvertisements(String language, String platform, Sort sort);

	void invalidateAdvertisementCache();

}
