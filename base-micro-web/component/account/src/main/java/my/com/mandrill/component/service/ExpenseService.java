package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Expense;
import my.com.mandrill.component.domain.ExpenseType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExpenseDTO;
import my.com.mandrill.component.dto.model.ExpenseProjection;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;

public interface ExpenseService {

	Expense findByIdAndUser(String id, User user);

	Optional<Expense> findOptionalByIdAndUser(String id, User user);

	List<Expense> findByUserAndExpenseType(User user, ExpenseType expenseType, Sort sort);

	Expense save(Expense expense);

	long deleteByUserAndId(User user, String id);

	List<ExpenseDTO> getUserCurrentExpenses(String refNo);

	List<ExpenseProjection> findProjectionByUserId(String userId);

}
