package my.com.mandrill.component.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.MessageTemplate;
import my.com.mandrill.component.dto.request.SavingGoalSharingTemplateRequest;
import my.com.mandrill.component.dto.response.GetMessageTemplateResponse;
import my.com.mandrill.component.dto.response.UserShareMessageHeaderResponse;
import my.com.mandrill.component.service.MessageTemplateIntegrationService;
import my.com.mandrill.component.service.MessageTemplateService;
import my.com.mandrill.utilities.general.constant.ChannelCodeEnum;
import my.com.mandrill.utilities.general.constant.MessageTemplateCodeEnum;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("message-template")
public class MessageTemplateController {

	private final MessageTemplateService messageTemplateService;

	private final MessageTemplateIntegrationService messageTemplateIntegrationService;

	@GetMapping("/code/{code}")
	public ResponseEntity<List<GetMessageTemplateResponse>> getMessageTemplateByCode(
			@PathVariable MessageTemplateCodeEnum code) {
		List<MessageTemplate> messageTemplates = messageTemplateService.findByActiveCode(code);
		List<GetMessageTemplateResponse> getMessageTemplateResponse = messageTemplates.stream()
				.map(MapStructConverter.MAPPER::toGetMessageTemplateResponse).toList();
		return ResponseEntity.ok(getMessageTemplateResponse);
	}

	@GetMapping("/code/{code}/channel/{channelCode}")
	public ResponseEntity<GetMessageTemplateResponse> getMessageTemplateByCodeAndChannel(
			@PathVariable MessageTemplateCodeEnum code, @PathVariable ChannelCodeEnum channelCode) {
		MessageTemplate messageTemplate = messageTemplateService.findByActiveCodeAndChannel(code, channelCode);
		GetMessageTemplateResponse getMessageTemplateResponse = MapStructConverter.MAPPER
				.toGetMessageTemplateResponse(messageTemplate);
		return ResponseEntity.ok(getMessageTemplateResponse);
	}

	@PostMapping("saving-goal-sharing")
	public ResponseEntity<List<GetMessageTemplateResponse>> findAllSavingGoalSharingTemplates(
			@RequestBody SavingGoalSharingTemplateRequest request) {

		List<MessageTemplate> messageTemplates = messageTemplateService
				.findByActiveCode(MessageTemplateCodeEnum.SHARE_SAVING_GOAL_COMPLETION);

		List<GetMessageTemplateResponse> getMessageTemplateResponses = messageTemplates.stream()
				.map(messageTemplate -> messageTemplateIntegrationService
						.processSavingGoalSavingTemplate(messageTemplate, request))
				.toList();

		return ResponseEntity.ok(getMessageTemplateResponses);

	}

	@GetMapping("/share-message/{code}")
	public ResponseEntity<UserShareMessageHeaderResponse> getMessageTemplateByCode(@PathVariable String code) {
		return ResponseEntity.ok(messageTemplateIntegrationService.getShareMessageTemplate(code));
	}

	@GetMapping("/product-share-message")
	public List<GetMessageTemplateResponse> getProductShareMessage(@RequestParam String productName,
			@RequestParam String productType) {
		return messageTemplateIntegrationService.getShareProductMessage(SecurityUtil.currentUserId(), productName,
				productType);
	}

}
