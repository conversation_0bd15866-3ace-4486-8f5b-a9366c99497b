package my.com.mandrill.component.config;

import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.request.ScreenRequest;
import my.com.mandrill.component.dto.request.VixCallRequest;
import my.com.mandrill.utilities.feign.dto.request.ScreenInternalRequest;
import my.com.mandrill.utilities.general.service.LazyLoadingAwareMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MapStructConverter extends LazyLoadingAwareMapper {

	MapStructConverter MAPPER = Mappers.getMapper(MapStructConverter.class);

	CoverTypeDTO toCoverTypeDTO(CoverType coverType);

	VehicleTypeDTO toVehicleTypeDTO(VehicleType vehicleType);

	TransactionTypeDTO toTransactionTypeDTO(TransactionType transactionType);

	UsageTypeDTO toUsageTypeDTO(UsageType usageType);

	RelationshipDTO toRelationshipDTO(Relationship relationship);

	@Mapping(target = "coverType", source = "extraCoverage.coverType")
	ExtraCoverageDTO toExtraCoverageDTO(ExtraCoverage extraCoverage);

	ScreenRequest toScreenRequest(ScreenInternalRequest screenInternalRequest);

	LoanPlusTransactionDTO toLoanPlusTransactionDTO(LoanPlusTransaction loanPlusTransaction);

	@Mapping(target = "vixRequest", source = "vixResponse.vixRequest")
	@Mapping(target = "vixRequest.vehicleTypeId", source = "vixResponse.vixRequest.vehicleType.id")
	@Mapping(target = "vixRequest.postcode", source = "vixResponse.vixRequest.postcode")
	VixResponseDTO toVixResponseDTO(VixResponse vixResponse);

	@Mapping(target = "vehicleNo", source = "priceQuotationResponse.priceQuotationRequest.vehicleInfoRegistrationNo")
	PriceQuotationResponseDetailDTO toPriceQuotationResponseDetailDTO(PriceQuotationResponseDetail entity);

	@Mapping(target = "vehicleTypeId", source = "vehicleType.id")
	VixCallRequest toVixCallRequest(VixRequest data);

}