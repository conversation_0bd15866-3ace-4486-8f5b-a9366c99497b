<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <include file="liquibase/changelog/account-component_20230216_0001_initial_tables.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230216_0002_initial_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230220_0001_update_sys_admin_mobile.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230222_0001_delete_country_and_state.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230224_0001_delete_system_configuration.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230301_darmawan_0001_added_permission_data.xml"
             relativeToChangelogFile="false"/>
    <include
            file="liquibase/changelog/account-component_20230228_wuikeat_0001_create_expense_type_and_expense_table.xml"
            relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230228_wuikeat_0002_insert_expense_table_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230228_0001_delete_load_new_employment_types.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230228_0002_income_journey.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230228_andy_0001_registration_flow.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230302_andy_0001_registration_flow_update.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230303_andy_0001_update_sys_admin_username_mobile.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230309_andy_0001_segment_add_tooltip.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230315_wuikeat_0001_add_column_for_expense_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230315_wuikeat_0002_create_table_for_my_bank_module.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230316_wuikeat_0001_insert_data_for_my_bank_picklist.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230316_andy_0001_authority_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230316_andy_0002_authority_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230322_darmawan_0001_alter_bank_list_attachment_group.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230323_muhdlaziem_0001_added_scheduler_permissions_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230328_wuikeat_0001_alter_entity_bank_and_bank_details.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230328_wuikeat_0002_rename_bank_list_bank_muamalat.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230328_andy_0001_segment_permission.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230329_muhdlaziem_0001_utility_permissions_data.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230329_muhdlaziem_0002_expense_type_add_column.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230330_andy_0001_income_type_permission.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230403_andy_0001_segment_update_uc.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230403_andy_0002_permission_delete.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230403_jose_0001_alter_table_bank_list.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230404_jose_0001_bank_list_add_column.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230410_andy_0001_master_config_permission.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230412_wuikeat_0001_delete_bank_detail.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230412_wuikeat_0002_drop_bank_detail_foreign_key.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230412_wuikeat_0003_drop_bank_related_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230412_wuikeat_0004_drop_credit_card_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230412_wuikeat_0005_drop_bank_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230412_wuikeat_0006_drop_bank_list_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230412_wuikeat_0007_drop_bank_detail.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230413_agustri_0001_add_permission.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230418_agustri_0001_add_expense_type_constrain.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230503_muhdlaziem_0001_utility_type_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20230427_agustri_0001_add_permission_employment_type.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230427_agustri_0001_add_permission_country.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230427_agustri_0001_add_permission_currency.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230503_agustri_0001_add_field_active_currency.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/account-component_20230516_muhdlaziem_0001_running_number_include_day.xml"/>
    <include file="liquibase/changelog/account-component_20230520_agustri_0001_authority_upload_vehicle.xml"/>
    <include file="liquibase/changelog/account-component_20230524_andy_0001_ref_no_update.xml"/>
    <include file="liquibase/changelog/account-component_20230525_agustri_0001_authority_bank_list.xml"/>
    <include file="liquibase/changelog/account-component_20230601_agustri_0001_email_blacklist.xml"/>
    <include file="liquibase/changelog/account-component_20230606_andy_0001_email_update.xml"/>
    <include file="liquibase/changelog/account-component_20230602_agustri_0001_authority_provider.xml"/>
    <include file="liquibase/changelog/account-component_20230615_muhdlaziem_0001_user_secret_key.xml"/>
    <include file="liquibase/changelog/account-component_20230619_agustri_0001_authority_property_type.xml"/>
    <include file="liquibase/changelog/account-component_20230619_agustri_0002_authority_property_sub_type.xml"/>
    <include file="liquibase/changelog/account-component_20230625_andy_0001_encryption_update.xml"/>
    <include file="liquibase/changelog/account-component_20230625_andy_0002_encryption_update.xml"/>
    <include file="liquibase/changelog/account-component_20230627_maulana_0001_password_transaction.xml"/>
    <include file="liquibase/changelog/account-component_20230708_andy_0001_encryption_update.xml"/>
    <include file="liquibase/changelog/account-component_20230708_andy_0002_encryption_update.xml"/>
    <include file="liquibase/changelog/account-component_20230712_andy_0001_institution_update_and_user.xml"/>
    <include file="liquibase/changelog/account-component_20230712_andy_0002_institution_update_and_user.xml"/>
    <include file="liquibase/changelog/account-component_20230712_andy_0003_institution_update_and_user.xml"/>
    <include file="liquibase/changelog/account-component_20230705_muhdlaziem_0001_digital_signature_auth.xml"/>
    <include file="liquibase/changelog/account-component_20230713_agustri_0001_dashboard_activity.xml"/>
    <include file="liquibase/changelog/account-component_20230721_maulana_0001_alter_institution.xml"/>
    <include file="liquibase/changelog/account-component_20230721_muhdlaziem_0001_advertisement_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20230721_andy_0001_app_user_deleted_date.xml"/>
    <include file="liquibase/changelog/account-component_20230721_wuikeat_0001_add_flag_for_others.xml"/>
    <include file="liquibase/changelog/account-component_20230725_muhdlaziem_0001_report_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20230731_andy_0001_app_user_deleted_flag.xml"/>
    <include file="liquibase/changelog/account-component_20230801_muhdlaziem_0001_dashboard_activity_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20230809_maulana_0001_permission_mobile_user.xml"/>
    <include
            file="liquibase/changelog/account-component_20230822_andy_0001_app_user_term_condition_privacy_policy.xml"/>
    <include file="liquibase/changelog/account-component_20230904_maulana_0001_permission_platform.xml"/>
    <include file="liquibase/changelog/account-component_20230911_andy_0001_business_nature_update.xml"/>
    <include file="liquibase/changelog/account-component_20230914_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20230919_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20230922_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20230930_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20231005_anto_0001_live_dashboard_permissions.xml"/>
    <include
            file="liquibase/changelog/account-component_20231008_wuikeat_0001_drop_email_constraint_insert_email_and_logintype_constraint.xml"/>
    <include file="liquibase/changelog/account-component_20231017_andy_0001_app_user_platform_agreement.xml"/>
    <include file="liquibase/changelog/account-component_20231017_andy_0001_update_app_user_platform_agreement.xml"/>
    <include file="liquibase/changelog/account-component_20231019_muhdlaziem_0001_pull_big_query_data_permission.xml"/>
    <include file="liquibase/changelog/account-component_20230911_andy_0001_business_nature_update.xml"/>
    <include file="liquibase/changelog/account-component_20230914_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20230919_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20230922_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20230930_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20231005_anto_0001_live_dashboard_permissions.xml"/>
    <include
            file="liquibase/changelog/account-component_20231008_wuikeat_0001_drop_email_constraint_insert_email_and_logintype_constraint.xml"/>
    <include file="liquibase/changelog/account-component_20230911_andy_0001_business_nature_update.xml"/>
    <include file="liquibase/changelog/account-component_20230912_muhdlaziem_0001_income_employment_type.xml"/>
    <include file="liquibase/changelog/account-component_20230914_andy_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20230914_wuikeat_0001_create_epf_contribution_table.xml"/>
    <include file="liquibase/changelog/account-component_20230918_maulana_0001_alter_income_type.xml"/>
    <include file="liquibase/changelog/account-component_20230918_maulana_0002_alter_table_employment_type.xml"/>
    <include
            file="liquibase/changelog/account-component_20231009_maulana_0001_new_column_fail_verification_attempt.xml"/>
    <include file="liquibase/changelog/account-component_20231023_clement_0001_create_global_system_config.xml"/>
    <include
            file="liquibase/changelog/account-component_20231030_chooiyie_0001_business_nature_add_column_for_active.xml"/>
    <include file="liquibase/changelog/account-component_20231030_chooiyie_0002_add_permission_business_nature.xml"/>
    <include file="liquibase/changelog/account-component_20231108_maulana_0001_add_passport_and_army_on_app_user.xml"/>
    <include file="liquibase/changelog/account-component_20231108_maulana_0002_id_type_table.xml"/>
    <include file="liquibase/changelog/account-component_20231108_maulana_0003_seed_id_type.xml"/>
    <include file="liquibase/changelog/account-component_20231109_loongyeat_0001_add_permission_banner.xml"/>
    <include file="liquibase/changelog/account-component_20231126_andy_0001_alter_institution.xml"/>
    <include file="liquibase/changelog/account-component_20231129_andy_0001_add_permission_voucher.xml"/>
    <include file="liquibase/changelog/account-component_20231207_weishun_0001_app_user_add_DOB.xml"/>
    <include file="liquibase/changelog/account-component_20231213_muhdlaziem_0001_encrypt_app_user_dob.xml"/>
    <include file="liquibase/changelog/account-component_20231213_muhdlaziem_0002_loan_plus_permission.xml"/>
    <include file="liquibase/changelog/account-component_20231221_muhdlaziem_0001_running_number_tz.xml"/>
    <include file="liquibase/changelog/account-component_20231226_muhdlaziem_0001_payment_transaction_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240104_maulana_0001_payment_finology_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240101_muhdlaziem_0001_running_number_refactor.xml"/>
    <include file="liquibase/changelog/account-component_20240105_andy_0001_update_script.xml"/>
    <include file="liquibase/changelog/account-component_20240108_kuswandi_0001_migration_permission.xml"/>
    <include
            file="liquibase/changelog/account-component_20240102_chooiyie_0001_downloads_statistics_report_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20240112_kuswandi_0001_edge_prop_transaction_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240122_loongyeat_0001_add_is_nric_editable.xml"/>
    <include file="liquibase/changelog/account-component_20240126_wuikeat_0001_m_and_a_interest_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240130_loongyeat_0001_add_is_nationality_editable.xml"/>
    <include file="liquibase/changelog/account-component_20240202_muhdlaziem_0001_public_auth.xml"/>
    <include file="liquibase/changelog/account-component_20240204_muhdlaziem_0001_public_auth_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240219_loongyeat_0001_create_weekly_user_statistics.xml"/>
    <include file="liquibase/changelog/account-component_20240222_loongyeat_0001_add_weekly_user_target_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20240222_loongyeat_0002_create_weekly_user_target.xml"/>
    <include file="liquibase/changelog/account-component_20240223_loongyeat_0001_weekly_user_statistics_change_year_type.xml"/>
    <include file="liquibase/changelog/account-component_20240223_loongyeat_0002_weekly_user_statistics_add_is_synchronized.xml"/>
    <include file="liquibase/changelog/account-component_20240321_kuswandi_001_delete_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240321_chooiyie_0001_user_list_report_permissions.xml"/>
    <include file="/liquibase/changelog/account-component_20240418_maulana_0001_permission_push_notification.xml"/>
    <include file="liquibase/changelog/account-component_20240402_chooiyie_0001_add_monthly_user_target_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20240402_chooiyie_0002_create_monthly_user_target.xml"/>
    <include file="liquibase/changelog/account-component_20240424_kuswandi_0001_mpin.xml"/>
    <include file="liquibase/changelog/account-component_20240423_wuikeat_0001_loan_plus_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240419_hobas_0001_property_voucher_report_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20240502_mengfoong_0001_add_permission_survey.xml"/>
    <include file="liquibase/changelog/account-component_20240509_mengfoong_0001_permissions_survey_update.xml"/>
    <include file="liquibase/changelog/account-component_20240423_maulana_0001_add_permission_for_stash_away_and_e_will.xml"/>
    <include file="liquibase/changelog/account-component_20240528_chooiyie_0001_survey_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240530_mengfoong_0001_create_delete_account_message.xml"/>
    <include file="liquibase/changelog/account-component_20240606_maulana_0001_init_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240607_maulana_0001_init_home_menu_group.xml"/>
    <include file="/liquibase/changelog/account-component_20240607_maulana_0002_seed_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240612_maulana_0001_seed_permission_dynamic_menu.xml"/>
    <include file="/liquibase/changelog/account-component_20240619_weishun_0001_vehicle_application_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240627_maulana_0001_update_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240719_andy_0001_device_binding.xml"/>
    <include file="liquibase/changelog/account-component_20240807_khairunnisa_0001_add_permission_for_credit_bureau.xml"/>
    <include file="liquibase/changelog/account-component_20240806_maulana_0001_add_secret_key_on_institutions.xml"/>
    <include file="liquibase/changelog/account-component_20240806_kuswandi_0001_seed_permission_user_interest.xml"/>
    <include file="liquibase/changelog/account-component_20240809_maulana_0001_add_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240812_maulana_0001_add_renewable_energy_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240809_junyew_0001_add_permission_for_milieu_solar.xml"/>
    <include file="liquibase/changelog/account-component_20240813_maulana_0001_permissions_telco_and_denom.xml"/>
    <include file="liquibase/changelog/account-component_20240809_farhanah_0001_product_configuration_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240813_farhanah_0001_product_config_template_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240813_jumari_0001_add_permission_for_easiwill_ctos.xml"/>
    <include file="liquibase/changelog/account-component_20240816_kuswandi_0001_add_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240819_maulana_0001_fix_permissions_home_menu_create.xml"/>
    <include file="liquibase/changelog/account-component_20240821_khairunnisa_0001_delete_update_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240806_mengfoong_0001_alter_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240806_mengfoong_0001_permissions_update.xml"/>
    <include file="liquibase/changelog/account-component_20240808_mengfoong_0001_add_permission_revamp.xml"/>
    <include file="liquibase/changelog/account-component_20240808_mengfoong_0001_delete_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240821_chooiyie_0001_purchase_vehicle_statistics_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20240823_maulana_0001_atx_transaction_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20240826_maulana_0001_add_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240828_khairunnisa_0001_add_permission_for_solaroo_report.xml"/>
    <include file="liquibase/changelog/account-component_20240909_andy_0001_home_module_product.xml"/>
    <include file="liquibase/changelog/account-component_20240903_kuswandi_0001_update_menu.xml"/>
    <include file="liquibase/changelog/account-component_20240923_chooiyie_0001_alter_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20241008_kuswandi_0001_permissions_blog_category.xml"/>
    <include file="liquibase/changelog/account-component_20241001_monikav_0001_blog_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20241022_kuswandi_0001_add_institution.xml"/>
    <include file="liquibase/changelog/account-component_20241024_kuswandi_0001_permissions_update.xml"/>
    <include file="liquibase/changelog/account-component_20241022_monikav_0001_developer_permissions.xml"/>
    <include file="liquibase/changelog/account-component_20241023_kuswandi_0001_permissions_property_project.xml"/>
    <include file="liquibase/changelog/account-component_20241025_farhanah_0001_permissions_property_unit.xml"/>
    <include file="liquibase/changelog/account-component_20241105_kuswandi_0001_permissions_update.xml"/>
    <include file="liquibase/changelog/account-component_20241101_farahy_0001_insert_phonenumber_and_logintype_constraint.xml"/>
    <include file="liquibase/changelog/account-component_20241108_kuswandi_0001_permissions_property_report.xml"/>
    <include file="/liquibase/changelog/account-component_20241108_deni_0001_initial_table_token.xml"/>
    <include file="/liquibase/changelog/account-component_20241108_deni_0002_initial_table_token_transaction.xml"/>
    <include file="/liquibase/changelog/account-component_20241108_deni_0003_token_permissions.xml"/>
    <include file="/liquibase/changelog/account-component_20241108_deni_0004_alter_table_token_transaction.xml"/>
    <include file="liquibase/changelog/account-component_20241112_kuswandi_0001_add_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20241111_monika_0001_alter_home_menu_product_add_column.xml"/>
    <include file="liquibase/changelog/account-component_20241112_monika_0001_alter_home_menu_product_add_logo.xml"/>
    <include file="liquibase/changelog/account-component_20241108_kuswandi_0001_permissions_property_report.xml"/>
    <include file="liquibase/changelog/account-component_20241114_monikav_0001_projectunit_upload_permission.xml"/>
    <include file="liquibase/changelog/account-component_20241113_deniarianto_0001_alter_token_transaction.xml"/>
    <include file="liquibase/changelog/account-component_20241117_deniarianto_0001_alter_token_device_id.xml"/>
    <include file="liquibase/changelog/account-component_20241118_monika_0001_alter_home_menu_product_add_sequence.xml"/>
    <include file="liquibase/changelog/account-component_20241112_farhanah_0001_upload_purchase_property_permission.xml"/>
    <include file="liquibase/changelog/account-component_20241118_kuswandi_0001_permissions_prs.xml"/>
    <include file="liquibase/changelog/account-component_20241129_kuswandi_0001_add_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20241129_kuswandi_0001_add_institution.xml"/>
    <include file="liquibase/changelog/account-component_20241202_monikav_0001_web_campaign_permission.xml"/>
    <include file="liquibase/changelog/account-component_20241204_kuswandi_0001_alter_users_table.xml"/>
    <include file="liquibase/changelog/account-component_20241206_monika_0001_alter_public_auth_add_secret_key.xml"/>
    <include file="liquibase/changelog/account-component_20241216_kuswandi_0001_permissions_cgs_int_my.xml"/>
    <include file="liquibase/changelog/account-component_20241216_kuswandi_0002_add_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20241218_monikav_0001_investment_permission.xml"/>
    <include file="liquibase/changelog/account-component_20241218_chooiyie_0001_add_retirement_provider_permission.xml"/>
    <include file="liquibase/changelog/account-component_20241223_monika_0001_investment_permissions_update.xml"/>
    <include file="liquibase/changelog/account-component_20241220_kuswandi_0001_re_order_home_menu_map.xml"/>
    <include file="liquibase/changelog/account-component_20241230_kuswandi_0001_re_name_home_menu_map.xml"/>
    <include file="liquibase/changelog/account-component_20241231_kuswandi_0001_home_menu_group.xml"/>
    <include file="liquibase/changelog/account-component_20250115_deniarianto_0001_alter_app_user_payment_info.xml"/>
    <include file="liquibase/changelog/account-component_20250120_deniarianto_0001_add_column_ekyc_status_on_user.xml"/>
    <include file="liquibase/changelog/account-component_20250121_kuswandi_0001_public_auth.xml"/>
    <include file="liquibase/changelog/account-component_20250120_monikav_0001_payment_account_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250120_deniarianto_0002_add_column_referrer_code_on_user.xml"/>
    <include file="liquibase/changelog/account-component_20250127_kuswandi_0001_public_auth.xml"/>
    <include file="liquibase/changelog/account-component_20250127_monikav_0001_dws_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250124_chooiyie_0001_add_permission_rsm_header.xml"/>
    <include file="liquibase/changelog/account-component_20250128_monika_0001_alter_home_menu_product_add_parentid.xml"/>
    <include file="liquibase/changelog/account-component_20250128_monika_0001_institution_update_octowill.xml"/>
    <include
            file="liquibase/changelog/account-component_20250128_diknes_0001_permissions_points_withdrawal_report.xml"/>
    <include file="liquibase/changelog/account-component_20250203_kuswandi_0001_permissions_rsm_leadgen.xml"/>
    <include
            file="liquibase/changelog/account-component_20250205_diknes_0001_permissions_points_disbursement_report.xml"/>
    <include file="liquibase/changelog/account-component_20250210_monika_0001_remove_unique_constraints.xml"/>
    <include file="liquibase/changelog/account-component_20250211_deniarianto_0001_rsm_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250213_chooiyie_0001_permissions_update.xml"/>
    <include file="liquibase/changelog/account-component_20250218_kuswandi_0001_alter_app_user_key_request.xml"/>
    <include file="liquibase/changelog/account-component_20250224_deniarianto_0001_add_referral_tnc_on_user.xml"/>
    <include file="liquibase/changelog/account-component_20250225_monika_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20250226_monika_0001_institution_update_remove_ai_mapping.xml"/>
    <include
            file="liquibase/changelog/account-component_20250227_diknes_0001_rsm__message_template_read_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250227_kuswandi_0001_add_institution.xml"/>
    <include file="liquibase/changelog/account-component_20250227_monika_0001_institution_update.xml"/>
    <include file="liquibase/changelog/account-component_20250227_weishun_0001_update_rsm_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250303_deniarianto_0001_update_stashaway_name.xml"/>
    <include file="liquibase/changelog/account-component_20250304_deniarianto_0001_update_stashaway_name.xml"/>
    <include file="liquibase/changelog/account-component_20250310_chooiyie_0001_fsm_one_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250320_chooiyie_0001_nps_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250402_chooiyie_0001_up_by_cgs_my_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250403_chooiyie_0001_up_by_cgs_my_institution.xml"/>
    <include
            file="liquibase/changelog/account-component_20250327_diknes_0001_update_points_withdrawal_management_permission_naming.xml"/>

    <include file="liquibase/changelog/account-component_20250409_chooiyie_0001_update_admin_portal_menu.xml"/>
    <include file="liquibase/changelog/account-component_20250409_chooiyie_0002_update_admin_portal_menu.xml"/>
    <include file="liquibase/changelog/account-component_20250409_kuswandi_0001_remove_nonnull_constraints.xml"/>
    <include file="liquibase/changelog/account-component_20250402_chooiyie_0001_up_by_cgs_my_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250403_chooiyie_0001_up_by_cgs_my_institution.xml"/>
    <include file="liquibase/changelog/account-component_20250410_chooiyie_0001_update_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20250410_chooiyie_0001_update_admin_portal_menu.xml"/>
    <include file="liquibase/changelog/account-component_20250414_kuswandi_0001_download_transaction_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250416_deniarianto_0001_add_column_referral_code_used.xml"/>
    <include file="liquibase/changelog/account-component_20250506_deniarianto_0001_add_dashboard_module_revamp.xml"/>
    <include file="liquibase/changelog/account-component_20250508_chooiyie_0001_update_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20250508_chooiyie_0002_update_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20250520_diknes_0001_apex_securities_report_permission.xml"/>
    <include file="liquibase/changelog/account-component_20250520_diknes_0002_apex_securities_institution.xml"/>
    <include file="liquibase/changelog/account-component_20250520_diknes_0003_update_apex_securities_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20250513_kuswandi_0001_add_blood_type_on_app_user.xml"/>
    <include file="liquibase/changelog/account-component_20250515_deniarianto_0001_add_group_action_on_home_menu.xml"/>
    <include file="liquibase/changelog/account-component_20250519_kuswandi_0001_add_platform.xml"/>
    <include file="liquibase/changelog/account-component_20250526_deniarianto_0001_add_featured_v2.xml"/>
    <include file="liquibase/changelog/account-component_20250529_deniarianto_0001_update_investments_action_group.xml"/>
    <include file="liquibase/changelog/account-component_20250603_kuswandi_0001_institution.xml"/>
    <include file="liquibase/changelog/account-component_20250609_diknes_0001_permissions_rsm_manual_create_lead.xml"/>
    <include file="liquibase/changelog/account-component_20250611_deniarianto_0001_update_menu_map_group_sequence.xml"/>
    <include file="liquibase/changelog/account-component_20250611_kuswandi_0001_app_user_key_request.xml"/>
    <include file="liquibase/changelog/account-component_20250618_kuswandi_0001_institution.xml"/>
    <include file="liquibase/changelog/account-component_20250618_kuswandi_0001_menu_map.xml"/>
    <include file="liquibase/changelog/account-component_20250619_diknes_0001_update_apex_securities_institution.xml"/>
    <include file="liquibase/changelog/account-component_20250623_deniarianto_0001_public_auth_authority.xml"/>
</databaseChangeLog>
