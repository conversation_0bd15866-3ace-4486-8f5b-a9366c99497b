package my.com.mandrill.component.integration.admin;

import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.RsmDetailRepository;
import my.com.mandrill.component.repository.RsmHeaderRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class RsmHeaderIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private RsmHeaderRepository rsmHeaderRepository;

	@Autowired
	private RsmDetailRepository rsmDetailRepository;

	@AfterEach
	public void tearDown() {
		rsmDetailRepository.deleteAll();
		rsmHeaderRepository.deleteAll();
	}

	@Test
	void createSuccess() throws Exception {
		mockMvc.perform(
				post("/admin/v1/rsm-commission-header").header("Authorization", "Bearer " + getAdminSecretToken())
						.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(status().isOk());

		long total = rsmHeaderRepository.count();
		Assertions.assertEquals(1, total);
	}

	@Test
	void updateStatusSuccess() throws Exception {
		mockMvc.perform(put("/admin/v1/rsm-commission-header/01JP9MTBH8W6SPSK8271K0F27V/status/true")
				.header("Authorization", "Bearer " + getAdminSecretToken())).andExpect(status().isOk());

		Optional<RsmHeader> rsmHeader = rsmHeaderRepository.findById("01JP9MTBH8W6SPSK8271K0F27V");
		Assertions.assertTrue(rsmHeader.isPresent());
		Assertions.assertTrue(rsmHeader.get().getIsActive());
	}

	@Test
	void findAllWithPaginationSuccess() throws Exception {
		mockMvc.perform(
				get("/admin/v1/rsm-commission-header").header("Authorization", "Bearer " + getAdminSecretToken()))
				.andExpect(status().isOk()).andExpect(content().json(getExpectationString(), false));
	}

	@Test
	void findByIdSuccess() throws Exception {
		mockMvc.perform(get("/admin/v1/rsm-commission-header/01JP9MTBH8W6SPSK8271K0F27V").header("Authorization",
				"Bearer " + getAdminSecretToken())).andExpect(status().isOk())
				.andExpect(content().json(getExpectationString(), false));
	}

	@Test
	void findByIdNoRsmDetail() throws Exception {
		mockMvc.perform(get("/admin/v1/rsm-commission-header/01JP9MTBH8W6SPSK8271K0F27V").header("Authorization",
				"Bearer " + getAdminSecretToken())).andExpect(status().isNotFound())
				.andExpect(jsonPath("$.errorCode").value("GBL0007"));
	}

	@Test
	void searchRsmHeaderSuccess() throws Exception {
		mockMvc.perform(get("/admin/v1/rsm-commission-header/search").queryParam("isActive", "false")
				.queryParam("productCategoryId", "3495b901-d62f-4034-8b47-c1b7881a0677")
				.queryParam("productId", "6d09ada4-575e-441d-823e-9baeb9aa11d4")
				.queryParam("providerId", "47524a7d-7244-4da0-a17c-53a961466f8b")
				.header("Authorization", "Bearer " + getAdminSecretToken())).andExpect(status().isOk())
				.andExpect(content().json(getExpectationString(), false));
	}

}
