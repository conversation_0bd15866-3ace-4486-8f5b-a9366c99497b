package my.com.mandrill.component.controller.admin;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.ExpenseType;
import my.com.mandrill.component.dto.model.ExpenseTypeDTO;
import my.com.mandrill.component.dto.request.ExpenseTypeRequest;
import my.com.mandrill.component.service.ExpenseTypeService;
import my.com.mandrill.utilities.general.constant.ExpenseTypeEnum;
import my.com.mandrill.utilities.general.service.GlobalValidationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Tag(name = "14-expense-type")
@Slf4j
@RestController
@RequestMapping("/admin/expense-types")
@RequiredArgsConstructor
public class AdminExpenseTypeController {

	private final ExpenseTypeService expenseTypeService;

	private final GlobalValidationService globalValidationService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.EXPENSE_TYPE_CREATE)")
	public ResponseEntity<ExpenseTypeDTO> createExpenseType(@Valid @RequestBody ExpenseTypeRequest expenseTypeRequest) {
		// Transformation
		ExpenseType expenseType = MapStructConverter.MAPPER.toExpenseType(expenseTypeRequest);

		// Implementation
		ExpenseType result = expenseTypeService.create(expenseType);

		// Result
		return ResponseEntity.ok(MapStructConverter.MAPPER.toExpenseTypeDTO(result));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.EXPENSE_TYPE_READ)")
	public ResponseEntity<Page<ExpenseTypeDTO>> getAllExpenseTypes(Pageable pageable,
			@RequestParam(required = false) String name) {
		Page<ExpenseType> expenseTypePage = expenseTypeService.findAllByName(pageable,
				globalValidationService.validateNullToLowerCase(name));

		return ResponseEntity.ok(expenseTypePage.map(MapStructConverter.MAPPER::toExpenseTypeDTO));
	}

	@GetMapping("/{expenseTypeId}")
	@PreAuthorize("hasAuthority(@authorityPermission.EXPENSE_TYPE_READ)")
	public ResponseEntity<ExpenseTypeDTO> getExpenseTypeById(@PathVariable String expenseTypeId) {
		ExpenseType expenseType = expenseTypeService.findById(expenseTypeId);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toExpenseTypeDTO(expenseType));
	}

	@PutMapping("/{expenseTypeId}")
	@PreAuthorize("hasAuthority(@authorityPermission.EXPENSE_TYPE_UPDATE)")
	public ResponseEntity<ExpenseTypeDTO> updateExpenseType(@Valid @RequestBody ExpenseTypeRequest expenseTypeRequest,
			@PathVariable String expenseTypeId) {
		// Transformation
		ExpenseType expenseType = MapStructConverter.MAPPER.toExpenseType(expenseTypeRequest);
		expenseType.setId(expenseTypeId);

		// Implementation
		ExpenseType result = expenseTypeService.update(expenseType);

		// Result
		return ResponseEntity.ok(MapStructConverter.MAPPER.toExpenseTypeDTO(result));
	}

	@DeleteMapping("/{expenseTypeId}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.EXPENSE_TYPE_DELETE)")
	public void deleteExpenseTypeById(@PathVariable String expenseTypeId) {
		expenseTypeService.deleteById(expenseTypeId);
	}

	@GetMapping("expense-type-type-enum")
	public ResponseEntity<List<ExpenseTypeEnum.ExpenseTypeEnumDTO>> getActivityTypeEnum() {
		return ResponseEntity.ok(Stream.of(ExpenseTypeEnum.values()).map(ExpenseTypeEnum::getObject)
				.sorted(Comparator.comparing(ExpenseTypeEnum.ExpenseTypeEnumDTO::getCode)).toList());
	}

}
