package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.DeliveryType;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

@Entity
@Table(name = "app_user_key_request")
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class UserKeyRequest extends AuditSection implements Serializable {

	@NotNull
	@Column(name = "username", length = 100, nullable = false)
	private String username;

	@NotNull
	@Column(name = "key_value", length = 100, nullable = false)
	private String keyValue;

	@NotNull
	@Column(name = "type", length = 50, nullable = false)
	@Enumerated(EnumType.STRING)
	private RequestKeyType type;

	@NotNull
	@Column(name = "initial_date", nullable = false)
	private Instant initialDate;

	@NotNull
	@Column(name = "status", length = 50, nullable = false)
	@Enumerated(EnumType.STRING)
	private RequestKeyStatus status = RequestKeyStatus.PENDING;

	@Column(name = "completion_date")
	private Instant completionDate;

	@Column(nullable = false, columnDefinition = "INT(2) DEFAULT 0")
	private Integer attempt;

	@Column(nullable = false, columnDefinition = "INT(2) DEFAULT 0", name = "fail_verification_attempt")
	private Integer failVerificationAttempt;

	@Enumerated(EnumType.STRING)
	@Column(name = "delivery_type")
	private DeliveryType deliveryType;

	@Column(name = "expires_at")
	private Instant expiresAt;

	public String getKeyValue() {
		return this.keyValue == null ? null : AesCryptoUtil.basicDecrypt(this.keyValue);
	}

	public void setKeyValue(String keyValue) {
		this.keyValue = keyValue == null ? null : AesCryptoUtil.basicEncrypt(keyValue);
	}

	public String getKeyValueRaw() {
		return this.keyValue;
	}

	public String getUsername() {
		return this.username == null ? null : AesCryptoUtil.basicDecrypt(this.username);
	}

	public void setUsername(String username) {
		this.username = username == null ? null : AesCryptoUtil.basicEncrypt(username);
	}

	public String getUsernameRaw() {
		return this.username;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		UserKeyRequest that = (UserKeyRequest) o;
		return getId() != null && Objects.equals(getId(), that.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}
