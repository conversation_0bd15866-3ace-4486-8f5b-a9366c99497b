package my.com.mandrill.component.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import my.com.mandrill.component.dto.model.TopUpTransactionHistoryDTO;
import my.com.mandrill.component.dto.request.PrepaidTopupCallbackRequest;
import my.com.mandrill.component.dto.request.PrepaidTopupInquiryRequest;
import my.com.mandrill.component.dto.response.PrepaidTopupInquiryResponse;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import org.springframework.data.domain.Sort;

import java.time.Instant;
import java.util.List;

public interface PrepaidTopupIntegrationService {

	PrepaidTopupInquiryResponse inquiry(CurrentUserIdDTO userId, PrepaidTopupInquiryRequest inquiryRequest)
			throws JsonProcessingException;

	void callbackLock(PrepaidTopupCallbackRequest request) throws JsonProcessingException;

	void callback(PrepaidTopupCallbackRequest request) throws JsonProcessingException;

	List<TopUpTransactionHistoryDTO> findAll(String userId, Sort sort);

	TopUpTransactionHistoryDTO findById(String userId, String id);

	void resolveFailedTransaction(Instant pointer);

}
