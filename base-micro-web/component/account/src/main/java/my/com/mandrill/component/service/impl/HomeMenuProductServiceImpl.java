package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.domain.HomeMenuProduct;
import my.com.mandrill.component.repository.jpa.HomeMenuProductRepository;
import my.com.mandrill.component.service.HomeMenuProductService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class HomeMenuProductServiceImpl implements HomeMenuProductService {

	private final HomeMenuProductRepository homeMenuProductRepository;

	@Override
	@Transactional
	public void deleteByHomeMenu(HomeMenu homeMenu) {
		homeMenuProductRepository.deleteByHomeMenu(homeMenu);
	}

	@Override
	public List<HomeMenuProduct> findAllByHomeMenuCode(String menuCode) {
		return homeMenuProductRepository.findAllByHomeMenuCode(menuCode);
	}

	@Override
	public List<HomeMenuProduct> findAllByParentId(String parentId) {
		return homeMenuProductRepository.findAllByParentId(parentId);
	}

}
