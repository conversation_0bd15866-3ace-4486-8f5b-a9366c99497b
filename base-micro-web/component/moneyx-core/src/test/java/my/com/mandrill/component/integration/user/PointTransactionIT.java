package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import my.com.mandrill.component.dto.response.RsmPointTransactionHistoryResponse;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.PointTransactionRepository;
import my.com.mandrill.component.repository.PointWithdrawalRepository;
import my.com.mandrill.utilities.general.dto.response.CursorPageResponse;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

public class PointTransactionIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@Autowired
	private PointWithdrawalRepository pointWithdrawalRepository;

	@AfterEach
	public void clear() {
		pointTransactionRepository.deleteAll();
		pointWithdrawalRepository.deleteAll();
	}

	@SneakyThrows
	@Test
	public void histories_withoutAdminFee_pageZero_success() {
		MvcResult mvcResult = mockMvc.perform(get("/point-transaction/histories").param("limit", "2")
				.header("Authorization", "Bearer " + getUserSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		CursorPageResponse<RsmPointTransactionHistoryResponse> response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		CursorPageResponse<RsmPointTransactionHistoryResponse> expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(expectation, response);
	}

	@SneakyThrows
	@Test
	public void histories_withoutAdminFee_pageOne_success() {
		MvcResult mvcResult = mockMvc.perform(get("/point-transaction/histories").param("limit", "2")
				.param("cursor", "01JS1FMWTKJW68V5X233CV170D").header("Authorization", "Bearer " + getUserSecretToken())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		CursorPageResponse<RsmPointTransactionHistoryResponse> response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		CursorPageResponse<RsmPointTransactionHistoryResponse> expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(expectation, response);
	}

}
