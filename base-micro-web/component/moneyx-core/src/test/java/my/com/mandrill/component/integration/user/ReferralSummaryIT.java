package my.com.mandrill.component.integration.user;

import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.*;
import my.com.mandrill.utilities.general.util.RequestUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class ReferralSummaryIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private ReferreRepository referreRepository;

	@Autowired
	private RsmHeaderRepository rsmHeaderRepository;

	@Autowired
	private RsmDetailRepository rsmDetailRepository;

	@Autowired
	private PointEarningRepository pointEarningRepository;

	@Autowired
	private PointWithdrawalRepository pointWithdrawalRepository;

	@Autowired
	private PointAllocationRepository pointAllocationRepository;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@AfterEach
	void tearDown() throws Exception {
		pointWithdrawalRepository.deleteAll();
		pointEarningRepository.deleteAll();
		rsmDetailRepository.deleteAll();
		rsmHeaderRepository.deleteAll();
		referreRepository.deleteAll();
		pointTransactionRepository.deleteAll();
		pointAllocationRepository.deleteAll();
	}

	@Test
	void referralSummaryWithValueSuccess() throws Exception {
		String expected = getExpectationString();
		mockMvc.perform(get("/account/v1/referral-summary").header("Authorization", "Bearer " + getUserSecretToken()))
				.andDo(print()).andExpect(status().isOk()).andExpect(content().json(expected));
	}

	@Test
	void referralSummary_unauthorized_error() throws Exception {
		mockMvc.perform(get("/account/v1/referral-summary")).andDo(print()).andExpect(status().isUnauthorized());
	}

	@Test
	void existReferralCodeSuccess() throws Exception {
		mockMvc.perform(get("/account/v1/private/exists-referral-code").queryParam("referralCode", "QNu8k")
				.queryParam("source", "MXAPP").header(RequestUtil.INTERNAL_API_KEY, "123")).andDo(print())
				.andExpect(status().isOk()).andExpect(jsonPath("$").value(true));
	}

	@Test
	void existReferralCode_invalidParam_error() throws Exception {
		mockMvc.perform(get("/account/v1/private/exists-referral-code").queryParam("referralCode", "")
				.queryParam("source", "").header(RequestUtil.INTERNAL_API_KEY, "123")).andDo(print())
				.andExpect(status().isBadRequest());
	}

	@Test
	void earningHistorySuccess() throws Exception {
		String expected = getExpectationString();
		mockMvc.perform(get("/account/v1/earning-histories").header("Authorization", "Bearer " + getUserSecretToken()))
				.andDo(print()).andExpect(status().isOk()).andExpect(content().json(expected));
	}

	@Test
	void totalAvailablePointSuccess() throws Exception {
		mockMvc.perform(
				get("/account/v1/total-available-mxpoint").header("Authorization", "Bearer " + getUserSecretToken()))
				.andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$").value("179995.0"));
	}

	@Test
	void totalAvailablePointSuccessV2() throws Exception {
		mockMvc.perform(
				get("/account/v2/total-available-mxpoint").header("Authorization", "Bearer " + getUserSecretToken()))
				.andDo(print()).andExpect(status().isOk())
				.andExpect(jsonPath("$.totalAvailablePoint").value("124995.0"))
				.andExpect(jsonPath("$.totalExpiringPointEndOfMonth").value("60000.0"));
	}

	@Test
	void totalAvailablePointSuccessV2_expendMoreThanAvailablePoint_returnZero() throws Exception {
		mockMvc.perform(
				get("/account/v2/total-available-mxpoint").header("Authorization", "Bearer " + getUserSecretToken()))
				.andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$.totalAvailablePoint").value("59995.0"))
				.andExpect(jsonPath("$.totalExpiringPointEndOfMonth").value("0"));
	}

}
