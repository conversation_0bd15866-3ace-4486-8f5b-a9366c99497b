package my.com.mandrill.component.service.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.UserStatisticType;
import my.com.mandrill.component.domain.WeeklyUserStatistics;
import my.com.mandrill.component.repository.jpa.WeeklyUserStatisticsRepository;
import my.com.mandrill.component.service.WeeklyUserStatisticsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Year;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class WeeklyUserStatisticsServiceImpl implements WeeklyUserStatisticsService {

	private final WeeklyUserStatisticsRepository weeklyUserStatisticsRepository;

	@Override
	public Optional<WeeklyUserStatistics> findByWeekAndYearAndType(Integer week, Year year, UserStatisticType type) {
		return weeklyUserStatisticsRepository.findByWeekAndYearAndType(week, year, type);
	}

	@Override
	@Transactional
	public WeeklyUserStatistics save(WeeklyUserStatistics statistics) {
		return weeklyUserStatisticsRepository.save(statistics);
	}

}
