package my.com.mandrill.component.config;

import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.model.clientmodel.ProductSimplePaginationDTO;
import my.com.mandrill.component.dto.model.clientmodel.ProductSuggestionQuestionnaireDTO;
import my.com.mandrill.component.dto.request.VehicleApplicationRequest;
import my.com.mandrill.component.dto.request.clientRequest.ProductSuggestionQuestionnaireRequest;
import my.com.mandrill.component.dto.request.clientRequest.UserSegmentClientRequest;
import my.com.mandrill.component.dto.response.KyllDataAIResponse;
import my.com.mandrill.component.dto.response.ProductTypeResponse;
import my.com.mandrill.component.dto.response.UserDataAIResponse;
import my.com.mandrill.utilities.feign.dto.AffiliateProductDTO;
import my.com.mandrill.utilities.feign.dto.model.CurrentUserDataForAIDTO;
import my.com.mandrill.utilities.feign.dto.model.StandardObjectDTO;
import my.com.mandrill.utilities.feign.dto.model.SurveyFormUserResultDTO;
import my.com.mandrill.utilities.feign.dto.model.VehicleApplicationReportDTO;
import my.com.mandrill.utilities.feign.dto.request.UserSegmentRequest;
import my.com.mandrill.utilities.general.dto.response.AiProductHighlightResponse;
import my.com.mandrill.utilities.general.dto.response.AiProductResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MapStructConverter {

	MapStructConverter MAPPER = Mappers.getMapper(MapStructConverter.class);

	VehicleApplication toVehicleApplication(VehicleApplicationRequest vehicleApplicationRequest);

	VehicleApplicationReportDTO toVehicleApplicationReportDTO(VehicleApplication vehicleApplication);

	UserSegmentClientRequest toUserSegmentClientRequest(UserSegmentRequest userSegmentRequest);

	VehicleLoanCalculation toVehicleLoanCalculation(VehicleLoanCalculationDTO vehicleLoanCalculationDTO);

	KyllDataAIResponse toKyllDataAIResponse(KyllDataDTO kyllDataDTO);

	@Mapping(source = "preferences", target = "selectedOptions")
	@Mapping(source = "salary", target = "income")
	@Mapping(source = "salary", target = "deposit")
	ProductSuggestionQuestionnaireRequest toProductSuggestionQuestionnaireRequest(SurveyFormUserResultDTO data);

	@Mapping(source = "type", target = "productType")
	@Mapping(source = "logo", target = "logoUrl")
	ProductSuggestionMatchDTO toProductSuggestionMatchDTO(ProductSuggestionQuestionnaireDTO data);

	ProductTypeDTO toProductTypeDTO(ProductType data);

	ProductGroupDTO toProductGroupDTO(ProductGroup data);

	@Mapping(source = "productId", target = "id")
	@Mapping(target = "source", constant = "AI")
	ProductSimpleSelectionDTO toProductSimpleSelectionDTO(ProductSimplePaginationDTO.Product data);

	@Mapping(target = "source", constant = "MX")
	ProductSimpleSelectionDTO toProductSimpleSelectionDTO(AffiliateProductDTO data);

	@Mapping(target = "source", constant = "MX")
	ProductSimpleSelectionDTO toProductSimpleSelectionDTO(StandardObjectDTO data);

	UserDataAIResponse toUserDataAIResponse(CurrentUserDataForAIDTO currentUserDataForAIDTO);

	ChatBotUserDataDTO toChatBotUserDataDTO(CurrentUserDataForAIDTO currentUserDataForAIDTO);

	@Mapping(source = "highlights", target = "highlights")
	@Mapping(source = "id", target = "productId")
	AiProductResponse toResponse(Product product);

	@Named("mapHighlights")
	static AiProductHighlightResponse mapHighlights(List<ProductHighlight> highlights) {
		if (highlights == null)
			return null;

		List<String> defaultList = highlights.stream().filter(h -> "default".equalsIgnoreCase(h.getCategory()))
				.map(ProductHighlight::getHighlightText).collect(Collectors.toList());

		List<String> suggestedList = highlights.stream().filter(h -> "suggested".equalsIgnoreCase(h.getCategory()))
				.map(ProductHighlight::getHighlightText).collect(Collectors.toList());

		return new AiProductHighlightResponse(defaultList, suggestedList);
	}

	@Mapping(target = "defaultHighlights", source = "highlights", qualifiedByName = "mapDefault")
	@Mapping(target = "suggestedHighlights", source = "highlights", qualifiedByName = "mapSuggested")
	default AiProductHighlightResponse toHighlightResponse(List<ProductHighlight> highlights) {
		return mapHighlights(highlights);
	}

	@Mapping(target = "productGroupId", source = "productType.productGroup.id")
	ProductTypeResponse toProductTypeResponse(ProductType productType);

	AiProductResponse toAiProductResponse(AiProductResponse product);

	ProductSimpleSelectionDTO toProductSimpleSelectionDTO(AiProductResponse data);

}