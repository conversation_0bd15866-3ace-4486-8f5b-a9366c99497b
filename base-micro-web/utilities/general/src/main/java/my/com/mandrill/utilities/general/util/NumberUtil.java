package my.com.mandrill.utilities.general.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.function.IntUnaryOperator;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NumberUtil {

	public static final DecimalFormat ZERO_DECIMAL = new DecimalFormat("0");

	static {
		ZERO_DECIMAL.setRoundingMode(RoundingMode.HALF_UP);
	}

	public static int[] parseRange(String range) {

		// used for min value rounding, eg: 2500 for 2501, as AI is expecting this
		IntUnaryOperator roundToNearestThousand = value -> value % 100 == 1 ? value - 1 : value;

		int min = -1; // Default -1 if the value don't exist
		int max = -1;

		// Pattern to parse RM x,xxx or RMxxxx
		Pattern pattern = Pattern.compile("RM\\s?(\\d{1,3}(,\\d{3})*|\\d+)");

		Matcher matcher = pattern.matcher(range);

		if (range.contains("Less than")) {
			if (matcher.find()) {
				min = 0;
				max = Integer.parseInt(matcher.group(1).replace(",", ""));
			}
		}
		else if (range.contains("More than") || range.contains("and above")) {
			if (matcher.find()) {
				min = roundToNearestThousand.applyAsInt(Integer.parseInt(matcher.group(1).replace(",", "")));
				// set max as 2x of min, AI expecting min = 15000, max = 30000
				max = min * 2;
			}
		}
		else if (range.contains("-") || range.contains("–")) {
			if (matcher.find()) {
				min = roundToNearestThousand.applyAsInt(Integer.parseInt(matcher.group(1).replace(",", "")));
			}
			if (matcher.find()) {
				max = Integer.parseInt(matcher.group(1).replace(",", ""));
			}
		}

		return new int[] { min, max };
	}

	public static boolean isBetween(BigDecimal value, BigDecimal lowerBound, BigDecimal upperBound) {
		return value.compareTo(lowerBound) >= 0 && value.compareTo(upperBound) <= 0;
	}

	public static Integer incrementOrDefault(Integer current, int incrementBy, int defaultValue) {
		return current == null ? defaultValue : current + incrementBy;
	}

}