package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.core.dto.model.Request;
import my.com.mandrill.utilities.core.filter.PublicAuthenticationFilter;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.general.service.RedisService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.util.List;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class PublicAuthenticationConfig {

	private final RedisService redisService;

	@Bean
	public PublicAuthenticationFilter publicAuthenticationFilter(AccountFeignClient accountFeignClient,
			HandlerExceptionResolver handlerExceptionResolver) {
		List<Request> requests = List.of(new Request("/account/request-otp", HttpMethod.POST));
		return new PublicAuthenticationFilter(accountFeignClient, handlerExceptionResolver, requests, redisService);
	}

}
