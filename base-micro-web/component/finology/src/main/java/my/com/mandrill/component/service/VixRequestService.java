package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.VixRequest;

import java.util.Optional;

public interface VixRequestService {

	VixRequest save(VixRequest vixRequest);

	VixRequest findById(String id);

	VixRequest findByUserIdAndRefCode(String userId, String refCode);

	Optional<VixRequest> findOptionalByIdAndVehicleRegistrationNumber(String userId, String vixNo);

}
