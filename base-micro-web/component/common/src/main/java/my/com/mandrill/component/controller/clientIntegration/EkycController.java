package my.com.mandrill.component.controller.clientIntegration;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constants.EkycAction;
import my.com.mandrill.component.domain.Ekyc;
import my.com.mandrill.component.dto.request.EkycCallbackRequest;
import my.com.mandrill.component.dto.request.EkycCallbackResetRequest;
import my.com.mandrill.component.dto.request.EkycSubmitICMobileRequest;
import my.com.mandrill.component.dto.request.EkycSubmitPhotoMobileRequest;
import my.com.mandrill.component.dto.response.EkycCallbackResponse;
import my.com.mandrill.component.dto.response.EkycMobileResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.general.constant.EkycStatus;
import my.com.mandrill.utilities.general.dto.request.EkycCreateRequestDTO;
import my.com.mandrill.utilities.general.dto.request.UserPublicWebRequest;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.security.GeneralSecurityException;
import java.time.Instant;

@Slf4j
@RestController
@RequestMapping("ekyc")
@RequiredArgsConstructor
public class EkycController {

	private final AccountFeignClient accountFeignClient;

	private final ValidationService validationService;

	private final ********************** ekycIntegrationService;

	private final ObjectMapper objectMapper;

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	@PostMapping("submit/ic")
	public void submitEkycIC(@RequestBody EkycSubmitICMobileRequest body) throws Exception {
		CurrentUserIdDTO currentUser = accountFeignClient.getCurrentUserId();
		// validate user not yet
		Ekyc ekyc = validationService.validateEkycApplicant(currentUser.getId());
		if (ekyc == null) {
			ekyc = ekycIntegrationService.createApplicant(currentUser);
		}
		ekyc.setOverallStatus(EkycStatus.RUNNING);
		ekycIntegrationService.uploadIc(ekyc, body, currentUser.getId());
		// if not new applicant directly to submitIC
		ekycIntegrationService.submitIC(body, ekyc);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	@PostMapping("submit/photo")
	public void submitEkycPhoto(@RequestBody EkycSubmitPhotoMobileRequest body) throws Exception {
		String userId = SecurityUtil.currentUserId();
		Ekyc ekyc = validationService.validateEkycApplicant(userId);
		if (ekyc == null) {
			throw new BusinessException(ErrorCodeEnum.EKYC_PHOTO_REQUIRE_IC);
		}

		ekycIntegrationService.uploadPhoto(ekyc, body, userId);
		// send Photo
		ekycIntegrationService.submitPhoto(body, ekyc);
	}

	@ResponseStatus(HttpStatus.ACCEPTED)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	@GetMapping
	public ResponseEntity<EkycMobileResponse> getCurrentEkycStatus() throws Exception {

		Ekyc ekyc = validationService.validateEkycApplicant(SecurityUtil.currentUserId());

		return ResponseEntity.ok(objectMapper.convertValue(ekyc, EkycMobileResponse.class));
	}

	// TODO create MQ to update the message to ekycTrasaction
	@ResponseStatus(HttpStatus.ACCEPTED)
	@SecurityRequirements
	@PostMapping("submit/result")
	public ResponseEntity<EkycCallbackResponse> callbackResult(@RequestBody EkycCallbackRequest request)
			throws Exception {
		EkycCallbackResponse ekycCallbackResponse = new EkycCallbackResponse();

		try {
			Ekyc ekyc = validationService.findByApplicantId(request.getApplicantId());
			ekycIntegrationService.saveEkycTransaction(ekyc, request.toString(), EkycAction.CALLBACK_RESULT);

			ekycIntegrationService.updateStatus(request, ekyc);
		}
		catch (GeneralSecurityException e) {
			ekycCallbackResponse.setErrorMessage(e.getMessage());
			ekycCallbackResponse.setKycStatusUpdated(false);
		}
		catch (Exception e) {
			ekycCallbackResponse.setErrorMessage(e.getMessage());
			ekycCallbackResponse.setKycStatusUpdated(false);
		}

		ekycCallbackResponse.setResultTimeStamp(Instant.now());
		ekycCallbackResponse.setApplicantId(request.getApplicantId());
		return ResponseEntity.ok(ekycCallbackResponse);
	}

	@ResponseStatus(HttpStatus.ACCEPTED)
	@SecurityRequirements
	@PostMapping("submit/reset")
	public ResponseEntity<EkycCallbackResponse> callbackResetAttempt(@RequestBody EkycCallbackResetRequest request)
			throws Exception {
		EkycCallbackResponse ekycCallbackResponse = new EkycCallbackResponse();
		try {
			Ekyc ekyc = validationService.findByApplicantId(request.getApplicantId());
			ekycIntegrationService.saveEkycTransaction(ekyc, request.toString(), EkycAction.RESET_ATTEMPT);

			ekycIntegrationService.resetAttempt(request, ekyc);
		}
		catch (GeneralSecurityException e) {
			ekycCallbackResponse.setErrorMessage(e.getMessage());
			ekycCallbackResponse.setKycStatusUpdated(false);
		}
		catch (Exception e) {
			ekycCallbackResponse.setErrorMessage(e.getMessage());
			ekycCallbackResponse.setKycStatusUpdated(false);
		}

		ekycCallbackResponse.setResultTimeStamp(Instant.now());
		ekycCallbackResponse.setApplicantId(request.getApplicantId());
		return ResponseEntity.ok(ekycCallbackResponse);
	}

	@Hidden
	@GetMapping("integration/count-completed")
	public ResponseEntity<Long> countCompleted() {
		return ResponseEntity.ok(ekycIntegrationService.countCompleted(SecurityUtil.currentUserId()));
	}

	@ServiceToServiceAccess
	@GetMapping("private/get-kyc-status/{userId}")
	public ResponseEntity<Boolean> getKYCStatus(@PathVariable String userId) {
		return ResponseEntity.ok(ekycIntegrationService.getKycOverallStatus(userId));
	}

	@PutMapping("/integration/update-applicant")
	public void updateApplicant(@RequestBody EkycCreateRequestDTO request) {
		ekycIntegrationService.updateApplicant(request);
	}

}
