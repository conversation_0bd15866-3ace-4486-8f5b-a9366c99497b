package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.UserInterestedRedirect;
import my.com.mandrill.component.dto.model.UserInterestedRedirectEntityAndCountDTO;
import my.com.mandrill.component.dto.request.UserInterestedRedirectForLoggedInUserRequest;
import my.com.mandrill.component.dto.request.UserInterestedRedirectPublicRequest;
import my.com.mandrill.component.dto.request.UserInterestedRedirectRequest;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.UserInterestedRedirectExportDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.request.CreateManualLeadRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.feign.dto.response.InternalUserMobileResponse;
import my.com.mandrill.utilities.general.constant.EntityName;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface UserInterestedRedirectIntgService {

	void process(UserInterestedRedirectRequest request);

	void process(CurrentUserIdDTO userDTO, UserInterestedRedirectForLoggedInUserRequest request);

	void createManualLead(CreateManualLeadRequest createManualLeadRequest);

	UserInterestedRedirect findByCampaignIdAndUserId(String campaignId, String userId);

	UserInterestedRedirectExportDTO export(EntityName entityName, LocalDate startDate, LocalDate endDate);

	List<UserInterestedRedirectEntityAndCountDTO> getEntityNameAndCount(LocalDate dateFrom, LocalDate dateTo,
			String timeZone);

	List<UserInterestedRedirectEntityAndCountDTO> getCampaignEntityNameAndCount(LocalDate dateFrom, LocalDate dateTo,
			String timeZone);

	Page<UserInterestedRedirectExportDTO.TotalNumberClick> totalNumberClick(EntityName entityName, LocalDate startDate,
			LocalDate endDate, Pageable pageable);

	Page<UserInterestedRedirectExportDTO.TotalNumberClick> totalNumberClick(List<EntityName> entityNames,
			LocalDate startDate, LocalDate endDate, Pageable pageable);

	void migrateToUserInterestedRedirect();

	void populateUserData(Map<String, List<UserInterestedRedirect>> userInterestedRedirectMap,
			List<InternalUserMobileResponse> userData);

	void processPublicUser(UserInterestedRedirectPublicRequest request);

	UserInterestRecordRSMViewDTO findRsmLeadDetail(String id);

	List<UserInterestRecordRSMPaginationDTO> findRsmLeadReport(RSMLeadRequest request);

	Page<UserInterestRecordRSMPaginationDTO> findRsmLead(Pageable pageable, RSMLeadRequest request);

}
