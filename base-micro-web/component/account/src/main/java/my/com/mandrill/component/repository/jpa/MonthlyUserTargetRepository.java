package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.MonthlyUserTarget;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Year;

@Repository
public interface MonthlyUserTargetRepository extends JpaRepository<MonthlyUserTarget, String> {

	boolean existsByYearAndMonth(Year year, int month);

	@Query("""
			select mut from MonthlyUserTarget mut
			where (:month is NULL
			or mut.month = :month)
			and (:year is NULL
			or mut.year = :year)
			""")
	Page<MonthlyUserTarget> findAllPageable(Pageable pageable, Year year, Integer month);

	@Query("""
			select mut.target from MonthlyUserTarget mut
			where mut.month = :month
			and mut.year = :year
			""")
	Long findTargetByYearAndMonth(Year year, int month);

}
