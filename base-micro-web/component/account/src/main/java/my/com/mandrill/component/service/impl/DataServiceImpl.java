package my.com.mandrill.component.service.impl;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.repository.jpa.*;
import my.com.mandrill.component.service.DataService;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional(readOnly = true)
public class DataServiceImpl implements DataService {

	private final BusinessNatureRepository businessNatureRepository;

	private final EducationLevelRepository educationLevelRepository;

	private final EmploymentTypeRepository employmentTypeRepository;

	private final NationalityRepository nationalityRepository;

	private final OccupationGroupRepository occupationGroupRepository;

	private final InterestRepository interestRepository;

	private final FinancialGoalRepository financialGoalRepository;

	private final ExpenseTypeRepository expenseTypeRepository;

	private final SegmentRepository segmentRepository;

	public DataServiceImpl(BusinessNatureRepository businessNatureRepository,
			EducationLevelRepository educationLevelRepository, EmploymentTypeRepository employmentTypeRepository,
			NationalityRepository nationalityRepository, OccupationGroupRepository occupationGroupRepository,
			InterestRepository interestRepository, FinancialGoalRepository financialGoalRepository,
			ExpenseTypeRepository expenseTypeRepository, SegmentRepository segmentRepository) {
		this.businessNatureRepository = businessNatureRepository;
		this.educationLevelRepository = educationLevelRepository;
		this.employmentTypeRepository = employmentTypeRepository;
		this.nationalityRepository = nationalityRepository;
		this.occupationGroupRepository = occupationGroupRepository;
		this.interestRepository = interestRepository;
		this.financialGoalRepository = financialGoalRepository;
		this.expenseTypeRepository = expenseTypeRepository;
		this.segmentRepository = segmentRepository;
	}

	@Override
	public List<BusinessNature> findAllBusinessNatureByActiveTrue() {
		return businessNatureRepository.findAllByActiveTrue();
	}

	@Override
	public List<EducationLevel> findAllEducationLevel(Sort sort) {
		return educationLevelRepository.findAll(sort);
	}

	@Override
	public List<EmploymentType> findAllEmploymentType() {
		return employmentTypeRepository.findAll();
	}

	@Override
	public List<Nationality> findAllNationality() {
		return nationalityRepository.findAll();
	}

	@Override
	public List<OccupationGroup> findAllOccupationGroup() {
		return occupationGroupRepository.findAll();
	}

	@Override
	public List<Interest> findAllInterest() {
		return interestRepository.findAll();
	}

	@Override
	public List<FinancialGoal> findAllFinancialGoal() {
		return financialGoalRepository.findAll();
	}

	@Override
	public List<Segment> findAllSegment() {
		return segmentRepository.findAll();
	}

	@Override
	public List<ExpenseType> findAllExpenseType() {
		return expenseTypeRepository.findAll();
	}

}
