package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.RegistrationSource;
import my.com.mandrill.utilities.general.constant.PasscodeType;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FinishSignUpRequest {

	public static final int PASSWORD_MIN_LENGTH = 4;

	public static final int PASSWORD_MAX_LENGTH = 100;

	@NotBlank
	@Size(min = PASSWORD_MIN_LENGTH, max = PASSWORD_MAX_LENGTH)
	private String password;

	@NotBlank
	private String key;

	private RegistrationSource source;

	private String referralCode;

	@Builder.Default
	private PasscodeType passcodeType = PasscodeType.PASSWORD;

}
