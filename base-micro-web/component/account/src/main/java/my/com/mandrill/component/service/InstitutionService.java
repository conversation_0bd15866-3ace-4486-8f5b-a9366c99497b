package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.response.CurrentUserInstitutionResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface InstitutionService {

	Institution createInstitution(Institution institution);

	Page<Institution> getAllInstitutions(Pageable pageable, String name, String parentId);

	Institution getInstitutionById(String institutionId);

	List<Institution> getAllInstitutionsNoPagination();

	List<Institution> getAllInstitutionsNoPaginationWithSorting(Sort sort);

	Institution getInstitutionByIdAndActiveTrue(String institutionId);

	Institution updateInstitution(Institution institution);

	List<Institution> getInstitutionsByParentId(String parentId);

	Institution getInstitutionTierOneWithAuthorities(User user);

	Institution getInstitutionByAiMapping(String aiMapping);

	List<Institution> getListInstitutionByAiMapping();

	List<Institution> findAllByPathContainingAndActiveTrue(String path);

	boolean existsByName(String name);

	Optional<Institution> findByIdAndActiveTrue(String currentInstitutionId);

	Institution save(Institution institution);

	List<CurrentUserInstitutionResponse> getCurrentUserInstitution();

	String getInstitutionSecretKey(String aiMappings, String apiKey);

	Set<String> getInstitutionAiMapping(String institutionId);

	List<Institution> getChildInstitutionsByPath(String path, Sort sort);

	List<Institution> getChildInstitutionsByPathAndProviderIds(String path, List<String> providerIds, Sort sort);

	List<Institution> findInstitutionById(List<String> ids);

	List<Institution> findByIdIn(List<String> ids, Sort sort);

	Institution getHighestPriorityInstitution(AppUser appUser);

}
