package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.dto.model.BankDTO;
import my.com.mandrill.component.dto.request.BankDetailCreateRequest;
import my.com.mandrill.component.dto.request.BankDetailUpdateRequest;
import my.com.mandrill.component.dto.response.BankDetailResponse;

import java.util.List;

public interface BankDetailIntegrationService {

	BankDTO processReminderResponse(BankDTO bankDTO);

	BankDetail processUpdate(BankDetail existingBankDetail, BankDetailUpdateRequest bankDetailUpdateRequest);

	BankDetail processCreate(String userId, BankDetailCreateRequest bankDetailCreateRequest);

	List<BankDetail> processCreate(String userId, List<BankDetailCreateRequest> bankDetailCreateRequest);

	BankDetailResponse findAndSetReminderResponse(BankDetailResponse bankDetailResponse);

	void processDelete(String id, String userId);

	void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source);

}
