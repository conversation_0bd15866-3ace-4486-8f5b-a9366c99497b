package my.com.mandrill.component.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.HomeSectionEnum;
import my.com.mandrill.component.domain.HomeMenuMap;
import my.com.mandrill.component.dto.model.HomeMenuMapDTO;
import my.com.mandrill.component.dto.request.HomeMenuMapCreateRequest;
import my.com.mandrill.component.dto.request.HomeMenuMapUpdateSeqRequest;
import my.com.mandrill.component.dto.response.HomeMenuMapResponse;
import my.com.mandrill.component.dto.response.HomeSectionResponse;
import my.com.mandrill.component.service.HomeMenuMapIntgService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("admin/home-menu-map")
public class AdminHomeMenuMapController {

	private final HomeMenuMapIntgService homeMenuMapIntgService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_CREATE)")
	public ResponseEntity<HomeMenuMapResponse> create(
			@Valid @RequestBody HomeMenuMapCreateRequest homeMenuMapCreateRequest) {
		HomeMenuMap homeMenuMap = homeMenuMapIntgService.create(homeMenuMapCreateRequest);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toHomeMenuMapResponse(homeMenuMap));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<Page<HomeMenuMapResponse>> findAll(@RequestParam(required = false) HomeSectionEnum section,
			@RequestParam(required = false) String menuCode, @RequestParam(required = false) String groupCode,
			Pageable pageable) {
		Page<HomeMenuMap> data = homeMenuMapIntgService.findAll(section, menuCode, groupCode, pageable);
		return ResponseEntity.ok(data.map(MapStructConverter.MAPPER::toHomeMenuMapResponse));
	}

	@GetMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<HomeMenuMapResponse> findById(@PathVariable("id") String id) {
		HomeMenuMap data = homeMenuMapIntgService.findById(id);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toHomeMenuMapResponse(data));
	}

	@DeleteMapping("/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_DELETE)")
	public void delete(@PathVariable("id") String id) {
		homeMenuMapIntgService.deleteProcess(id);
	}

	@PutMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_UPDATE)")
	public ResponseEntity<HomeMenuMapResponse> update(@PathVariable("id") String id,
			@RequestBody HomeMenuMapDTO homeMenuMapDTO) {
		HomeMenuMap data = homeMenuMapIntgService.update(id, homeMenuMapDTO);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toHomeMenuMapResponse(data));
	}

	@PutMapping("/update-seq/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_UPDATE)")
	public ResponseEntity<HomeMenuMapResponse> updateSeq(@PathVariable("id") String id,
			@RequestBody HomeMenuMapUpdateSeqRequest request) {
		HomeMenuMap data = homeMenuMapIntgService.updateSeq(id, request);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toHomeMenuMapResponse(data));
	}

	@GetMapping("/section")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<List<HomeSectionResponse>> findAllSection() {
		return ResponseEntity.ok(Arrays.stream(HomeSectionEnum.values()).sorted(Comparator.comparing(Enum::name))
				.map(homeSectionEnum -> HomeSectionResponse.builder().name(homeSectionEnum.name()).build()).toList());
	}

}
