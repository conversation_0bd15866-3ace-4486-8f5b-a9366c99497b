package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.DashboardActivity;
import my.com.mandrill.component.dto.model.DashboardBarChartProjection;
import my.com.mandrill.utilities.general.constant.DashboardCategory;
import my.com.mandrill.utilities.general.constant.DashboardType;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface DashboardActivityService {

	DashboardActivity save(DashboardActivity dashboardActivity);

	List<DashboardBarChartProjection> findBarChartByCategoryAndDateBetween(DashboardCategory dashboardCategory,
			Instant dateStart, Instant dateEnd);

	Optional<DashboardActivity> findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(
			DashboardCategory dashboardCategory, DashboardType dashboardType, Instant dateStart, Instant dateEnd);

	long countByDateBetweenAndLoginType(Instant dateStart, Instant dateEnd, LoginTypeEnum loginTypeEnum);

}
