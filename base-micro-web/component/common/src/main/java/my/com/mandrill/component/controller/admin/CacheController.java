package my.com.mandrill.component.controller.admin;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.general.dto.request.CacheRequest;
import my.com.mandrill.utilities.general.service.RedisService;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/private/cache")
@RequiredArgsConstructor
public class CacheController {

	private final RedisService redisService;

	@ServiceToServiceAccess
	@DeleteMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Integer> deleteCache(@RequestBody CacheRequest cacheRequest) {
		return ResponseEntity.ok(redisService.deleteCache(cacheRequest));
	}

	@ServiceToServiceAccess
	@GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<List<String>> getByCursorPaginate(
			@RequestParam(required = false, defaultValue = "*") String patterns,
			@RequestParam(required = false, defaultValue = "1") int page,
			@RequestParam(required = false, defaultValue = "100") int size, @RequestParam(required = false,
					defaultValue = "my.com.mandrill.utilities.core.token.domain.Token") List<String> exclude) {
		return ResponseEntity.ok(redisService.getKeysByPattern(patterns, page, size, exclude));
	}

}
