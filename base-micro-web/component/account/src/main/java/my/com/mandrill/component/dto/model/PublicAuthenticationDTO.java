package my.com.mandrill.component.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
public class PublicAuthenticationDTO implements Serializable {

	private String id;

	private String apiKey;

	private String secretKey;

	private String identifier;

	private Instant createdDate;

	private Instant lastModifiedDate;

	private List<String> permissions;

}
