package my.com.mandrill.component.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.ProductSuggestionCategory;
import my.com.mandrill.component.dto.response.ProductSuggestionCategoryResponse;
import my.com.mandrill.component.service.ProductSuggestionCategoryService;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("product-suggestions")
@RequiredArgsConstructor
public class ProductSuggestionController {

	private final ProductSuggestionCategoryService productSuggestionCategoryService;

	private final ProxyFeignClient proxyFeignClient;

	@GetMapping("products")
	public ResponseEntity<List<ProductSuggestionCategoryResponse>> findAllActiveProducts(
			@RequestParam(required = false) String type) {

		Set<String> exclusionTags = proxyFeignClient.getRetirementFeignClient().getProductExclusionTags();

		List<ProductSuggestionCategory> result = CollectionUtils.isEmpty(exclusionTags)
				? productSuggestionCategoryService.findAllWithProductSuggestion(type)
				: productSuggestionCategoryService.findAllWithProductSuggestionWithExclusions(type, exclusionTags);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toProductSuggestionCategoryResponseList(result));
	}

}