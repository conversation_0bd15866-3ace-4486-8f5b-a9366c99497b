package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Token;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExtendAccessTokenDTO;
import my.com.mandrill.component.dto.model.RevokeRequestDTO;
import my.com.mandrill.component.dto.request.AuthServiceRequest;
import my.com.mandrill.component.dto.request.TokenSearchRequest;
import my.com.mandrill.component.dto.response.TokenServiceResponse;
import my.com.mandrill.utilities.core.dto.model.UserActivityDTO;
import my.com.mandrill.utilities.general.constant.ClientPlatformType;
import my.com.mandrill.utilities.general.constant.TokenStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface TokenService {

	TokenServiceResponse createToken(AuthServiceRequest request, User user);

	void createUserActivity(UserActivityDTO userActivityDTO);

	Page<TokenServiceResponse> findAllPaginate(TokenSearchRequest request, Pageable pageable);

	Token revokeToken(RevokeRequestDTO requestDTO);

	void revokeAllToken(String reason);

	List<Token> deleteToken(User user);

	void logoutToken(String token);

	List<TokenServiceResponse> revokedByUser(User user);

	List<TokenServiceResponse> revokedByUser(User user, ClientPlatformType platform, TokenStatus revokedStatus);

	TokenServiceResponse extendToken(ExtendAccessTokenDTO request);

}
