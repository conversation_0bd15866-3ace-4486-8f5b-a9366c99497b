package my.com.mandrill.component.service.impl;

import com.rometools.utils.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Ekyc;
import my.com.mandrill.component.domain.VaultType;
import my.com.mandrill.component.dto.model.TelcoOperatorDTO;
import my.com.mandrill.component.dto.model.VaultDTO;
import my.com.mandrill.component.dto.request.VaultRequest;
import my.com.mandrill.component.dto.response.EkycImageResponse;
import my.com.mandrill.component.dto.response.EkycRegisterResponse;
import my.com.mandrill.component.exception.EkycErrorCodeEnum;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.EkycRepository;
import my.com.mandrill.component.repository.jpa.GlobalSystemConfigurationRepository;
import my.com.mandrill.component.repository.jpa.ProductGroupRepository;
import my.com.mandrill.component.repository.jpa.ProductPlatformRepository;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.client.InsuranceFeignClient;
import my.com.mandrill.utilities.general.constant.AttachmentTypeFileStorageEnum;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.TopUpPrepaidDiscountEnum;
import my.com.mandrill.utilities.general.constant.VaultTypeEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import org.apache.commons.lang.StringUtils;
import org.apache.tika.detect.DefaultDetector;
import org.apache.tika.io.TikaInputStream;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.mime.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

import static my.com.mandrill.component.service.impl.FormulaServiceImpl.CONSTANT_PERCENTAGE;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class ValidationServiceImpl implements ValidationService {

	public static final long MAX_SIZE_FOR_VAULT = 10485760; // 10MB

	public static final List<String> APPROVED_EXTENSION = List.of("application/pdf", "image/png", "image/jpeg",
			"image/jpg", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			"application/msword", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

	public static final List<String> APPROVED_EXTENSION_FOR_UTILITY = List.of("application/pdf", "image/png",
			"image/jpeg", "image/jpg");

	public static final String[] ALLOWED_CODE = { "IOS_MINIMUM_VERSION", "ANDROID_MINIMUM_VERSION",
			"HUAWEI_MINIMUM_VERSION", "INFOBIP_WHATSAPP_FLOW_ENABLED", "INFOBIP_LIVECHAT_ENABLED",
			"REFERRAL_WITHDRAWAL_ENABLED", "RECAPTCHA_ENABLED" };

	private final VaultTypeService vaultTypeService;

	private final UtilityTypeService utilityTypeService;

	private final IdentityTypeService identityTypeService;

	private final PropertyDocumentTypeService propertyDocumentTypeService;

	private final VehicleDocumentTypeService vehicleDocumentTypeService;

	private final FinanceTypeService financeTypeService;

	private final InsuranceFeignClient insuranceFeignClient;

	private final LegalDocumentTypeService legalDocumentTypeService;

	private final VaultService vaultService;

	private final ProductPlatformRepository productPlatformRepository;

	private final ProductGroupRepository productGroupRepository;

	private final GlobalSystemConfigurationRepository globalSystemConfigurationRepository;

	private final EkycRepository ekycRepository;

	@Override
	public VaultDTO validateVaultRequest(VaultRequest vaultRequest) {
		VaultDTO vaultDTO = MapStructConverter.MAPPER.toVaultDto(vaultRequest);

		validationAttachmentLimit(vaultDTO);
		validationVaultClassAndType(vaultDTO, vaultRequest);
		validationVaultReminder(vaultDTO);
		validationVaultDocumentType(vaultDTO, vaultRequest);
		validationExtensionTypeAndSize(vaultRequest);

		return vaultDTO;
	}

	private void validationAttachmentLimit(VaultDTO vaultDTO) {
		long requestAttachmentCount = vaultDTO.getAttachments().stream()
				.filter(attachmentDTO -> attachmentDTO.getId() == null).count();
		long dbAttachmentCount = vaultService.countByAttachmentGroupId(vaultDTO.getAttachmentGroupId());
		if (dbAttachmentCount + requestAttachmentCount > 20) {
			throw new BusinessException(ErrorCodeEnum.EXCEED_NUMBER_OF_ATTACHMENT);
		}
	}

	private VaultDTO validationVaultReminder(VaultDTO vaultDTO) {
		if (Boolean.TRUE.equals(vaultDTO.getIsReminder()) && Objects.isNull(vaultDTO.getReminder())) {
			throw new BusinessException(ErrorCodeEnum.INVALID_REMINDER);
		}
		return vaultDTO;
	}

	private VaultDTO validationVaultClassAndType(VaultDTO vaultDTO, VaultRequest vaultRequest) {
		VaultType vaultType = vaultTypeService.findById(vaultRequest.getVaultType().getId());
		if (vaultType.getCode().equals(VaultTypeEnum.MY_PROPERTY.getName())) {
			vaultDTO.setClassName(EntityName.PROPERTY.getCode());
			vaultDTO.setType(AttachmentTypeFileStorageEnum.PROPERTY_DOC.toString());
		}
		else if (vaultType.getCode().equals(VaultTypeEnum.MY_VEHICLE.getName())) {
			vaultDTO.setClassName(EntityName.VEHICLE.getCode());
			vaultDTO.setType(AttachmentTypeFileStorageEnum.VEHICLE_DOC.toString());
		}
		else if (vaultType.getCode().equals(VaultTypeEnum.MY_LEGAL_DOCS.getName())) {
			vaultDTO.setClassName(EntityName.LEGAL.getCode());
			vaultDTO.setType(AttachmentTypeFileStorageEnum.LEGAL_DOC.toString());
		}
		else if (vaultType.getCode().equals(VaultTypeEnum.MY_UTILITY.getName())) {
			vaultDTO.setClassName(EntityName.UTILITY.getCode());
			vaultDTO.setType(AttachmentTypeFileStorageEnum.UTILITY_BILL.toString());
		}
		else if (vaultType.getCode().equals(VaultTypeEnum.MY_IDENTITY.getName())) {
			vaultDTO.setClassName(EntityName.USER.getCode());
			vaultDTO.setType(AttachmentTypeFileStorageEnum.IDENTITY_DOC.toString());
		}
		else if (vaultType.getCode().equals(VaultTypeEnum.MY_FINANCE.getName())) {
			vaultDTO.setClassName(EntityName.BANK.getCode());
			vaultDTO.setType(AttachmentTypeFileStorageEnum.BANK_DOC.toString());
		}
		else if (vaultType.getCode().equals(VaultTypeEnum.MY_INSURANCE.getName())) {
			vaultDTO.setClassName(EntityName.INSURANCE.getCode());
			vaultDTO.setType(AttachmentTypeFileStorageEnum.INSURANCE_DOC.toString());
		}
		return vaultDTO;
	}

	private VaultDTO validationVaultDocumentType(VaultDTO vaultDTO, VaultRequest vaultRequest) {
		VaultType vaultType = vaultTypeService.findById(vaultRequest.getVaultType().getId());
		switch (vaultType.getDocumentType()) {
			case UTILITY_TYPE -> {
				if (Objects.nonNull(vaultRequest.getDocumentType())) {
					vaultDTO.setDocumentTypeId(
							utilityTypeService.findById(vaultRequest.getDocumentType().getId()).getId());
					vaultDTO.setDocumentTypeName(null);
				}
				else {
					throw new BusinessException(ErrorCodeEnum.INVALID_DOCUMENT_TYPE);
				}
			}
			case IDENTITY_TYPE -> {
				if (Objects.nonNull(vaultRequest.getDocumentType())) {
					vaultDTO.setDocumentTypeId(
							identityTypeService.findById(vaultRequest.getDocumentType().getId()).getId());
					vaultDTO.setDocumentTypeName(null);
				}
				else {
					throw new BusinessException(ErrorCodeEnum.INVALID_DOCUMENT_TYPE);
				}
			}
			case PROPERTY_TYPE -> {
				if (Objects.nonNull(vaultRequest.getDocumentType())) {
					vaultDTO.setDocumentTypeId(propertyDocumentTypeService
							.findByIdAndActiveTrue(vaultRequest.getDocumentType().getId()).getId());
					vaultDTO.setDocumentTypeName(null);
				}
				else {
					throw new BusinessException(ErrorCodeEnum.INVALID_DOCUMENT_TYPE);
				}
			}
			case VEHICLE_TYPE -> {
				if (Objects.nonNull(vaultRequest.getDocumentType())) {
					vaultDTO.setDocumentTypeId(vehicleDocumentTypeService
							.findByIdAndActiveTrue(vaultRequest.getDocumentType().getId()).getId());
					vaultDTO.setDocumentTypeName(null);
				}
				else {
					throw new BusinessException(ErrorCodeEnum.INVALID_DOCUMENT_TYPE);
				}
			}
			case FINANCE_TYPE -> {
				if (Objects.nonNull(vaultRequest.getDocumentType())) {
					vaultDTO.setDocumentTypeId(
							financeTypeService.findByIdAndActiveTrue(vaultRequest.getDocumentType().getId()).getId());
					vaultDTO.setDocumentTypeName(null);
				}
				else {
					throw new BusinessException(ErrorCodeEnum.INVALID_DOCUMENT_TYPE);
				}
			}
			case INSURANCE_TYPE -> {
				if (Objects.nonNull(vaultRequest.getDocumentType())) {
					vaultDTO.setDocumentTypeId(insuranceFeignClient
							.findTypeByIdAndActiveTrue(vaultRequest.getDocumentType().getId()).getId());
					vaultDTO.setDocumentTypeName(null);
				}
				else {
					throw new BusinessException(ErrorCodeEnum.INVALID_DOCUMENT_TYPE);
				}
			}
			case LEGAL_TYPE -> {
				if (Objects.nonNull(vaultRequest.getDocumentType())) {
					vaultDTO.setDocumentTypeId(legalDocumentTypeService
							.findByIdAndActiveTrue(vaultRequest.getDocumentType().getId()).getId());
					vaultDTO.setDocumentTypeName(null);
				}
				else {
					throw new BusinessException(ErrorCodeEnum.INVALID_DOCUMENT_TYPE);
				}
			}
			default -> throw ExceptionPredicate.notSupportedByEntityName(vaultType.getDocumentType().getName()).get();
		}
		return vaultDTO;
	}

	@Override
	public void validationExtensionTypeAndSize(VaultRequest vaultRequest) {
		vaultRequest.getAttachments().forEach(attachmentDTO -> {
			if (StringUtils.isNotBlank(attachmentDTO.getBlobFile())) {
				checkExtensionType(vaultRequest, getRealMimeType(attachmentDTO.getBlobFile()));
			}
		});
	}

	@Override
	public void validateGetAllProductTypes(String productPlatformCode, String productGroupCode) {
		if (!productPlatformRepository.existsByCodeIgnoreCase(productPlatformCode)) {
			throw new BusinessException(ErrorCodeEnum.INVALID_PRODUCT_PLATFORM_CODE);
		}
		if (!Strings.isBlank(productGroupCode)) {
			if (!productGroupRepository.existsByCodeIgnoreCase(productGroupCode)) {
				throw new BusinessException(ErrorCodeEnum.INVALID_PRODUCT_GROUP_CODE);
			}
		}
	}

	private MediaType getRealMimeType(String base64String) {
		byte[] decodedBytes = validateFileSize(base64String);
		try {
			return new DefaultDetector().detect(TikaInputStream.get(decodedBytes), new Metadata());
		}
		catch (IOException e) {
			throw new BusinessException(ErrorCodeEnum.CORRUPT_EXTENSION);
		}
	}

	public byte[] validateFileSize(String base64String) {
		byte[] decodedBytes = Base64.getDecoder().decode(base64String.getBytes());
		if (decodedBytes.length >= MAX_SIZE_FOR_VAULT) {
			throw new BusinessException(ErrorCodeEnum.INVALID_FILE_SIZE, MAX_SIZE_FOR_VAULT);
		}
		return decodedBytes;
	}

	private void checkExtensionType(VaultRequest vaultRequest, MediaType realMimeType) {
		VaultType vaultType = vaultTypeService.findById(vaultRequest.getVaultType().getId());
		if (vaultType.getCode().equals(VaultTypeEnum.MY_UTILITY.getName())) {
			if (!APPROVED_EXTENSION_FOR_UTILITY.contains(realMimeType.toString())) {
				throw new BusinessException(ErrorCodeEnum.INVALID_EXTENSION);
			}
		}
		else {
			if (!APPROVED_EXTENSION.contains(realMimeType.toString())) {
				throw new BusinessException(ErrorCodeEnum.INVALID_EXTENSION);
			}
		}

	}

	public void validateMobileAccess(String code) {
		if (Arrays.stream(ALLOWED_CODE).noneMatch(allowedCode -> allowedCode.equals(code))) {
			throw new BusinessException(ErrorCodeEnum.GLOBAL_SYSTEM_CONFIG_INVALID_CODE);
		}
	}

	@Override
	public void validateGlobalSysConfigCode(String code) {
		if (globalSystemConfigurationRepository.existsByCodeAllIgnoreCase(code)) {
			throw new BusinessException(ErrorCodeEnum.SYSTEM_CONFIG_EXISTS_BY_CODE);
		}
	}

	public Ekyc validateEkycApplicant(String userId) {
		return ekycRepository.findByUserId(userId);
	}

	@Override
	public Ekyc findByApplicantId(String applicantId) {
		return ekycRepository.findByApplicantId(applicantId)
				.orElseThrow(ExceptionPredicate.ekycNotFoundByApplicantId(applicantId));
	}

	@Override
	public void validateEkycResponse(EkycImageResponse response) {
		// TODO validate ErrorCode and translate to BusinessException
		if (response == null) {
			throw new BusinessException(ErrorCodeEnum.EKYC_EXCEPTION_RESPONSE_NULL);
		}

		if (response.getData() == null) {
			throw new BusinessException(ErrorCodeEnum.EKYC_EXCEPTION_RESPONSE_NULL);
		}
	}

	@Override
	public void validateEkycResponse(EkycRegisterResponse response) {
		if (response == null) {
			throw new BusinessException(ErrorCodeEnum.EKYC_EXCEPTION_RESPONSE_NULL);
		}

		if (response.getData() == null) {
			throw new BusinessException(ErrorCodeEnum.EKYC_EXCEPTION_RESPONSE_NULL);
		}
		if (EkycErrorCodeEnum.SUCCESS.getCode().equals(response.getErrorCode())) {
			return;
		}
		if (EkycErrorCodeEnum.ERROR_DB.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_ERROR_DB);
		}
		if (EkycErrorCodeEnum.ERROR_DATAWAREHOUSE.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_ERROR_DATAWAREHOUSE);
		}
		if (EkycErrorCodeEnum.MISSING_INPUT.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_MISSING_INPUT);
		}
		if (EkycErrorCodeEnum.GENERAL_EXCEPTION.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_GENERAL_EXCEPTION);
		}
		if (EkycErrorCodeEnum.DUPLICATE_KEY_INSERTION.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_DUPLICATE_KEY_INSERTION);
		}
		if (EkycErrorCodeEnum.FAILED_UPDATE.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_FAILED_UPDATE);
		}
		if (EkycErrorCodeEnum.INVALID_INPUT_FORMAT.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_INVALID_INPUT_FORMAT);
		}
		if (EkycErrorCodeEnum.FAILED_INSERTION.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_FAILED_INSERTION);
		}
		if (EkycErrorCodeEnum.RECORD_NOT_FOUND.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_RECORD_NOT_FOUND);
		}
		if (EkycErrorCodeEnum.INVALID_URL.getCode().equals(response.getErrorCode())) {
			throw new BusinessException(ErrorCodeEnum.EKYC_INVALID_URL);
		}
	}

	@Override
	public void validateTelcoRequest(TelcoOperatorDTO telcoOperatorDTO) {
		if (telcoOperatorDTO.getIsCampaignWithDiscount()) {
			if (Objects.isNull(telcoOperatorDTO.getDiscountType())) {
				telcoOperatorDTO.setDiscountType(TopUpPrepaidDiscountEnum.PERCENTAGE);
			}

			if (telcoOperatorDTO.getDiscountType() == null || telcoOperatorDTO.getDiscountAmount() == null) {
				throw new BusinessException(ErrorCodeEnum.DISCOUNT_DATA_IS_MANDATORY);
			}
			if (telcoOperatorDTO.getDiscountType().equals(TopUpPrepaidDiscountEnum.PERCENTAGE)
					&& (telcoOperatorDTO.getDiscountAmount().compareTo(CONSTANT_PERCENTAGE) > 0
							|| telcoOperatorDTO.getDiscountAmount().doubleValue() > 5)) {
				throw new BusinessException(ErrorCodeEnum.DISCOUNT_AMOUNT_IS_NOT_VALID);
			}
		}
	}

}
