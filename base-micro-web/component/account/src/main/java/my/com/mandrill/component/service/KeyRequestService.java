package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.utilities.general.constant.RequestKeyType;

import java.util.Optional;

public interface KeyRequestService {

	Optional<UserKeyRequest> findFirstByUsernameAndTypeAndStatusOrderByCreatedDateDesc(String usernameRaw,
			RequestKeyType requestKeyType, RequestKeyStatus requestKeyStatus);

	UserKeyRequest save(UserKeyRequest keyRequest);

	UserKeyRequest createRequestKey(String username, RequestKeyType requestKeyType, int expiryInHours);

	Optional<UserKeyRequest> findValidByKeyValueAndType(String keyValueRaw, RequestKeyType requestKeyType);

	Optional<UserKeyRequest> findValidByUsernameAndType(String usernameRaw, RequestKeyType requestKeyType);

}
