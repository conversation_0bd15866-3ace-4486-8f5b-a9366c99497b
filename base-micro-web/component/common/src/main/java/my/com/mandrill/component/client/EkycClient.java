package my.com.mandrill.component.client;

import my.com.mandrill.component.dto.model.EkycCreateDTO;
import my.com.mandrill.component.dto.request.EkycSubmitICRequest;
import my.com.mandrill.component.dto.request.EkycSubmitPhotoRequest;
import my.com.mandrill.utilities.general.dto.request.EkycCreateRequestDTO;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import org.springframework.web.service.annotation.PutExchange;

@HttpExchange(contentType = MediaType.APPLICATION_JSON_VALUE, accept = MediaType.APPLICATION_JSON_VALUE)
public interface EkycClient {

	@PutExchange("/EkycWS/rest/applicant")
	ResponseEntity<String> updateApplicant(@RequestBody EkycCreateRequestDTO request);

	@PostExchange("/EkycWS/rest/applicant")
	ResponseEntity<String> createApplicant(@RequestBody EkycCreateDTO request);

	@PostExchange("/EkycWS/kycic/submit")
	ResponseEntity<String> submitIC(@RequestBody EkycSubmitICRequest request);

	@PostExchange("/EkycWS/kycselfie/submit/photo")
	ResponseEntity<String> submitPhoto(@RequestBody EkycSubmitPhotoRequest request);

	@GetExchange("/EkycWS/kycresult/{applicantId}")
	ResponseEntity<String> getResult(@PathVariable String applicantId);

	@GetExchange("/EkycWS/rest/applicant/ori/idno/{applicantId}")
	ResponseEntity<String> getIdNo(@PathVariable String applicantId);

}
