package my.com.mandrill.component.exception;

import lombok.Getter;
import my.com.mandrill.utilities.general.exception.ExceptionEnum;

@Getter
public enum ErrorCodeEnum implements ExceptionEnum {

	UNKNOWN_EXCEPTION("ACC9999", "This is unexpected error. Contact <EMAIL>"),
	USERNAME_EXISTED("ACC0001", "Username exist."), EMAIL_EXISTED("ACC0002", "Email exist."),
	INVALID_KEY("ACC0003", "Incorrect OTP."), INVALID_PASSWORD("ACC0004", "Incorrect password."),
	PERMISSION_EXISTED("ACC0005", "Permission already exist."), AUTHORITY_EXISTED("ACC0006", "User Role exist."),
	INSTITUTION_ONE_VALIDATION("ACC0007", "User can only have 1 tier one institution."),
	MOBILE_EXISTED("ACC0008", "Account Already Exists",
			"This mobile number is already registered. If you recently deleted your account, please wait 30 days <NAME_EMAIL> to reactivate it. Otherwise, use a different number or log in."),
	NRIC_EXISTED("ACC0009", "NRIC exist."),

	PARENT_INSTITUTION_NOT_ACTIVATED("ACC0010",
			"Partner is inactive, please contact system administrator for <NAME_EMAIL>"),
	MAX_TIER_CONFIG_REACHED("ACC0011", "Partner exist."),
	LOGIN_ATTEMPT_EXCEED("ACC0012",
			"Too many failed attempts. Your account has been locked, please contact MoneyX administrator to unlock your <NAME_EMAIL> and reset your password"),
	USER_NOT_VERIFIED("ACC0013", "User not verified."), FULL_NAME_MANDATORY("ACC0014", "Fullname is required."),
	ADDRESS1_MANDATORY("ACC0015", "Address is required."),
	REQUEST_OTP_LIMIT("ACC0016", "You have exceeded limit for OTP attempts. Try again after 1 hour."),
	DELETE_DEPENDENCY_FAIL("ACC0017", "The operation can't be completed."),
	INVALID_REMINDER("ACC0018", "Invalid reminder."), SEGMENT_EXISTS("ACC0019", "Segment code and name exist."),
	INSTITUTION_EXISTS("ACC0020", "Institution exist."), INCOME_TYPE_EXISTS("ACC0021", "Income type exist."),
	EXPENSE_TYPE_EXISTS("ACC0022", "Expense type exist."), EMPLOYMENT_TYPE_EXISTS("ACC0023", "Employment type exist."),
	CURRENCY_EXISTS("ACC0024", "Currency exist."), INVALID_LOGIN_TYPE("ACC0025", "Incorrect Login. Try login again."),
	EMAIL_BLACKLIST("ACC0026", "Incorrect email entered, try another email."),
	PASSWORD_TRANSACTION_EXIST("ACC0027",
			"The password entered is same as per the last 3 password. Try another password."),
	SIGNATURE_NOT_VERIFIED("ACC0028", "This is unexpected error. Contact <EMAIL>"),
	SIGNATURE_CHALLENGE_EXPIRED("ACC0029", "This is unexpected error. Contact <EMAIL>"),
	DEVICE_REGISTRATION_NOT_FINISHED("ACC0030", "This is unexpected error. Contact <EMAIL>"),
	DATE_FROM_IS_MISSING("ACC0032", "From date required."), DATE_TO_IS_MISSING("ACC0033", "To date required."),
	YEAR_MONTH_IS_MISSING("ACC0034", "Year required."), INVALID_DATE_RANGE("ACC0035", "Incorrect date range."),
	ACCOUNT_ALREADY_DELETED_MORE_THAN_30_DAYS("ACC0036", "This account was deleted 30 days ago."),
	CANNOT_UPDATE_STATUS_USER_TO_ACTIVE("ACC0037",
			"This account was deleted 30 days ago and removed. User account can't be activated, user will need to register again."),
	CANNOT_UPDATE_DELETE_STATUS_USER("ACC0038", "User status cannot be updated to deleted! User account is active!"),
	BUSINESS_NATURE_EXISTED("ACC0039", "Business nature exist."),
	INSTITUTION_NOT_RELATED("ACC0040", "Incorrect Institution."), INCOME_EXISTS("ACC0041", "Income exist."),
	MONTH_FORMAT_INVALID("ACC0042",
			"Incorrect month format. Format should be  (Note: Include the correct format for user to enter)"),
	INCOME_TYPE_CODE_EXISTS("ACC0043", "Income type exist"),
	OTP_TRY_TIMES_EXCEEDING("ACC0044", "You have exceeded limit for OTP attempts. Please request a new OTP code."),
	AGE_RESTRICTION("ACC0045", "User must be 18 years old and above."),
	BIG_QUERY_JOB_NOT_EXISTS("ACC0046", "This is unexpected error. Contact <EMAIL>"),
	BIG_QUERY_JOB_EXECUTION_FAILED("ACC0047", "This is unexpected error. Contact <EMAIL>"),
	BIG_QUERY_EXECUTION_INTERRUPTED("ACC0048", "This is unexpected error. Contact <EMAIL>"),
	INVALID_HASH("ACC0049", "This is unexpected error. Contact <EMAIL>"),
	IDENTIFIER_EXISTED("ACC0050", "This is unexpected error. Contact <EMAIL>"),
	INVALID_WEEK_NUMBER("ACC0051", "Incorrect week entered."),
	WEEKLY_USER_TARGET_EXISTS("ACC0052", "Incorrect weekly target entered."),
	INVALID_MONTH("ACC0053", "Incorrect month."),
	MONTHLY_USER_TARGET_EXISTS("ACC0054", "Incorrect monthly target entered."),
	PIN_ATTEMPT_EXCEED("ACC0055",
			"Too many failed attempts. Your account has been locked, please contact MoneyX administrator to unlock your <NAME_EMAIL> and reset your password"),
	INVALID_PASSCODE_VERIFICATION_REQUEST_TYPE("ACC0056", "Incorrect Passcode"),
	EMAIL_DOES_NOT_EXISTS("ACC0057", "No email entered. Update email at personal information."),
	HOME_MENU_CODE_IS_EXIST("ACC0058", "Home menu exist"),
	HOME_MENU_GROUP_CODE_IS_EXIST("ACC0059", "Home menu group exist"),
	DEVICE_BINDING_ERROR("ACC0060",
			"Account has been locked, please contact system administrator for <NAME_EMAIL>"),
	MOBILE_EXISTED_PENDING_DELETION("ACC0061", "Account Deleted Recently",
			"The account deletion is in progress. To register again with the same mobile number, please wait until the account is completely deleted."),
	HOME_MENU_IN_USED("ACC0062", "Home Menu in used by Home Menu Map. Cannot delete Home Menu."),
	HOME_MENU_GROUP_IN_USED("ACC0063", "Home Menu Group in used by Home Menu Map. Cannot delete Home Menu Group."),
	PRODUCT_SEQUENCE_IN_USE("ACC0064",
			"Sequence number already has been used by other link product. Please use another sequence number or update existing link product sequence."),
	HOME_MENU_ID_IS_EMPTY("AC0065", "Home Menu Id cannot be empty"),
	REQUIRED_FIELD_MISSING("AC0066", "Required Fields are missing"),
	NOT_PRE_REGISTRATION("ACC0067",
			"This account is not a pre-registered account and cannot complete the registration process"),
	PHONE_NUMBER_NOT_ALLOWED_REQUEST_OTP("ACC0068", "The phone number from this country is currently not supported"),
	EXIST_IN_PROGRESS_WITHDRAWAL("ACC0069", "There is an existing in-progress withdrawal, deletion is prohibited"),
	PASSWORD_MUST_BE_FILLED("ACC0070", "Please enter your Username and/or Password."),
	USERNAME_AND_PASSWORD_MUST_BE_FILLED("ACC0071", "Please enter your Username and/or Password."),
	USERNAME_MUST_BE_FILLED("ACC0072", "Please enter your Username and/or Password.");

	/**
	 * ACC0031 code is unused if there is new error code, can use ACC0031 code. once used
	 * this ACC0031, please remove this comment.
	 */

	private final String code;

	private final String description;

	private final String title;

	ErrorCodeEnum(String code, String description) {
		this.code = code;
		this.description = description;
		this.title = null;
	}

	ErrorCodeEnum(String code, String title, String description) {
		this.code = code;
		this.title = title;
		this.description = description;
	}

}
