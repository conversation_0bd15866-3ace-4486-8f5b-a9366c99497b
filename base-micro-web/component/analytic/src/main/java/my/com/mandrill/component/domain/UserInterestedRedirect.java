package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import my.com.mandrill.crypto.converter.ConfidentialDataConverter;
import my.com.mandrill.utilities.converter.ProtectedDataConverterBigDecimal;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Table(name = "user_interested_redirect")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UserInterestedRedirect extends AuditSection implements Serializable {

	@NotBlank
	@Size(max = 36)
	@Column(name = "user_id", nullable = false, length = 36)
	private String userId;

	@Enumerated(EnumType.STRING)
	@Column(name = "entity_name", nullable = false)
	private EntityName entityName;

	@NotBlank
	@Column(name = "url", nullable = false, columnDefinition = "TEXT")
	private String url;

	@Column(name = "campaign_id", length = 36)
	private String campaignId;

	@Column(name = "terms_and_condition_version")
	private String termsAndConditionVersion;

	@Column(name = "full_name", length = 100)
	private String fullName;

	@Column(name = "provider_id")
	private String providerId;

	@Column(name = "product_id")
	private String productId;

	@Column(name = "product_name")
	private String productName;

	@Column(name = "product_type")
	private String productType;

	@Column(name = "email")
	@Convert(converter = ConfidentialDataConverter.class)
	private String email;

	@Column(name = "phone_number", length = 100)
	@Convert(converter = ConfidentialDataConverter.class)
	private String phoneNumber;

	@Column(name = "nric")
	@Convert(converter = ConfidentialDataConverter.class)
	private String nric;

	@Column(name = "ref_no")
	private String refNo;

	@Column(name = "user_type")
	private String userType;

	@Enumerated(EnumType.STRING)
	@Column(name = "rsm_relation")
	private RSMRelationType rsmRelation;

	@Enumerated(EnumType.STRING)
	@Column(name = "rsm_status")
	private RSMStatus rsmStatus;

	@Column(name = "rsm_eligible")
	private boolean rsmEligible;

	@Column(name = "rsm_commission_attached")
	private boolean rsmCommissionAttached;

	@Column(name = "application_ref_no", length = 36)
	private String applicationRefNo;

	@Column(name = "income_amount", length = 100)
	@Convert(converter = ProtectedDataConverterBigDecimal.class)
	private BigDecimal incomeAmount;

	@Enumerated(EnumType.STRING)
	@Column(name = "source")
	private UserInterestedSource source;

	@Column(name = "source_id", length = 36)
	private String sourceId;

	@Column(name = "remarks")
	private String remarks;

	@Column(name = "application_date")
	private Instant applicationDate;

}
