package my.com.mandrill.component.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.service.SchedulerDataConfig;

@Getter
@AllArgsConstructor
public enum SchedulerDataInit implements SchedulerDataConfig {

	RSM_CLOSING_BALANCE_REPORT_SCHEDULER(KafkaTopic.RSM_CLOSING_BALANCE_REPORT_SCHEDULER_TOPIC, KafkaTopicConfig.GROUP,
			KafkaTopic.RSM_CLOSING_BALANCE_REPORT_SCHEDULER_TOPIC, TWELVE_AM_KL_CRON),
	RSM_POINT_EXPIRY_CLEAN_UP(KafkaTopic.RSM_POINT_EXPIRY_CLEAN_UP_SCHEDULER_TOPIC, KafkaTopicConfig.GROUP,
			KafkaTopic.RSM_POINT_EXPIRY_CLEAN_UP_SCHEDULER_TOPIC, FIRST_DAY_EVERY_MONTH_12AM_KL_CRON),
	GET_RSM_HEADER_LIVE_ENDED_SCHEDULER(KafkaTopicConfig.GET_LIVE_ENDING_RSM_HEADERS, KafkaTopicConfig.GROUP,
			KafkaTopicConfig.GET_LIVE_ENDING_RSM_HEADERS, TWELVE_AM_KL_CRON),
	RSM_POINT_EXPIRY_REMINDER(KafkaTopic.RSM_POINT_EXPIRY_REMINDER_SCHEDULER_TOPIC, KafkaTopicConfig.GROUP,
			KafkaTopic.RSM_POINT_EXPIRY_REMINDER_SCHEDULER_TOPIC, FIRST_DAY_EVERY_MONTH_8AM_KL_CRON);

	private final String destination;

	private final String jobGroup;

	private final String jobName;

	private final String cronExpression;

}
