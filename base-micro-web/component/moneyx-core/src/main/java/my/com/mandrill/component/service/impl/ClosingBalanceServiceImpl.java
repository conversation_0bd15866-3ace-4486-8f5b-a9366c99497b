package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.RsmClosingBalance;
import my.com.mandrill.component.dto.projection.ClosingBalanceProjection;
import my.com.mandrill.component.dto.response.BizPointSummaryFilterResponse;
import my.com.mandrill.component.repository.RsmClosingBalanceRepository;
import my.com.mandrill.component.service.ClosingBalanceService;
import my.com.mandrill.utilities.core.audit.UlidIdGenerator;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ClosingBalanceServiceImpl implements ClosingBalanceService {

	private final RsmClosingBalanceRepository rsmClosingBalanceRepository;

	@Override
	public BigDecimal findLatestClosingBalanceBySource(SourceSystemEnum source) {
		return rsmClosingBalanceRepository.findTopBySourceOrderByClosingDateDesc(source)
				.map(RsmClosingBalance::getClosingBalance).orElse(BigDecimal.ZERO);
	}

	@Override
	public Page<ClosingBalanceProjection> aggregateByMonthAndYearAndSource(Integer month, Integer year,
			SourceSystemEnum source, Pageable pageable) {
		return rsmClosingBalanceRepository.findByYearAndMonthAndSource(year, month, source.name(), pageable);
	}

	@Override
	public List<ClosingBalanceProjection> aggregateByMonthAndYearReportExport(Integer month, Integer year,
			SourceSystemEnum source) {
		return rsmClosingBalanceRepository.findByYearAndMonthAndSource(year, month, source.name());
	}

	@Override
	public Optional<RsmClosingBalance> findCurrentDayClosingBalanceBySource(SourceSystemEnum source) {
		LocalDate localDate = LocalDate.now(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE));
		Instant start = localDate.atStartOfDay(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toInstant();
		Instant end = localDate.atTime(LocalTime.MAX).atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toInstant();
		return rsmClosingBalanceRepository.findBySourceAndClosingDateBetween(source, start, end);
	}

	@Override
	public Optional<RsmClosingBalance> findPreviousDayClosingBalanceBySource(SourceSystemEnum source) {
		LocalDate localDate = LocalDate.now(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).minusDays(1);
		Instant start = localDate.atStartOfDay(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toInstant();
		Instant end = localDate.atTime(LocalTime.MAX).atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toInstant();
		return rsmClosingBalanceRepository.findBySourceAndClosingDateBetween(source, start, end);
	}

	@Transactional
	@Override
	public void updateBalanceRealtime(String id, BigDecimal pointAwarded, BigDecimal pointSpent,
			BigDecimal closingBalance) {
		rsmClosingBalanceRepository.updateBalanceRealtime(id, pointAwarded, pointSpent, closingBalance);
	}

	@Override
	public BizPointSummaryFilterResponse getAvailableTimeFilterForBiz() {
		List<Integer> months = rsmClosingBalanceRepository.getAvailableMonthsBySource(SourceSystemEnum.MXBIZ.name());
		List<Integer> years = rsmClosingBalanceRepository.getAvailableYearsBySource(SourceSystemEnum.MXBIZ.name());

		return new BizPointSummaryFilterResponse(months, years);
	}

	@Transactional
	@Override
	public void updateTotalExpired(BigDecimal totalExpired, Instant closingDate, SourceSystemEnum source) {
		rsmClosingBalanceRepository.upsertMonthlyExpiredPoint(UlidIdGenerator.generateUlid(), closingDate, totalExpired,
				source.name());
	}

	@Transactional
	public void save(RsmClosingBalance closingBalance) {
		rsmClosingBalanceRepository.upsertDailyClosingBalance(UlidIdGenerator.generateUlid(),
				closingBalance.getClosingDate(), closingBalance.getPointAwarded(), closingBalance.getPointSpent(),
				closingBalance.getClosingBalance(), closingBalance.getSource().name());
	}

}
