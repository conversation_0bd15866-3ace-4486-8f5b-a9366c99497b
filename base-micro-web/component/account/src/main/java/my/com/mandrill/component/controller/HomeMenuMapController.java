package my.com.mandrill.component.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.HomeSectionEnum;
import my.com.mandrill.component.dto.response.HomeMenuMapClientResponse;
import my.com.mandrill.component.dto.response.HomeMenuProductResponse;
import my.com.mandrill.component.service.HomeMenuMapIntgService;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/home-menu-maps")
public class HomeMenuMapController {

	private final HomeMenuMapIntgService homeMenuMapIntgService;

	@GetMapping("/{section}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<HomeMenuMapClientResponse>> findBySection(
			@PathVariable("section") HomeSectionEnum section) {
		List<HomeMenuMapClientResponse> responses = homeMenuMapIntgService.findBySectionEnum(section);
		return ResponseEntity.ok(responses);
	}

	@GetMapping("products/{menuCode}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<HomeMenuProductResponse>> findBySection(@PathVariable("menuCode") String menuCode) {
		List<HomeMenuProductResponse> responses = homeMenuMapIntgService.findAllByHomeMenuCode(menuCode);
		return ResponseEntity.ok(responses);
	}

}
