package my.com.mandrill.utilities.general.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.util.List;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class RequestUtil {

	public static final String API_KEY = "api-key";

	public static final String INTERNAL_API_KEY = "x-internal-api-key";

	public static final String HASH = "hash";

	public static final String IDENTIFIER = "identifier";

	public static final String PRINCIPAL = "principal";

	public static final String TIMESTAMP = "timestamp";

	public static final String SIGNATURE_CHALLENGE = "signature-challenge";

	public static final String CREDENTIAL = "credential";

	public static final String X_FORWARDED_FOR = "x-forwarded-for";

	public static final String CLIENT_PLATFORM = "x-client-platform";

	private static final List<String> IP_HEADERS = List.of("x-real-ip", X_FORWARDED_FOR);

	private static final List<String> HOST_HEADERS = List.of(HttpHeaders.ORIGIN, "x-forwarded-host",
			"x-forwarded-server");

	public static final String USER_AGENT = "user-agent";

	public static String getIpAddress(HttpServletRequest request) {
		try {
			for (String header : IP_HEADERS) {
				if (StringUtils.isNotBlank(request.getHeader(header))) {
					if (X_FORWARDED_FOR.equals(header)) {
						return request.getHeader(header).split(",")[0];
					}
					return request.getHeader(header);
				}
			}
		}
		catch (Exception e) {
			log.error("Failed to get client ip: {}", e.getMessage());
			return request.getRemoteAddr();
		}
		return request.getRemoteAddr();
	}

	public static String getHost(HttpServletRequest request) {
		for (String header : HOST_HEADERS) {
			if (StringUtils.isNotBlank(request.getHeader(header))) {
				return request.getHeader(header);
			}
		}
		return request.getRemoteHost();
	}

	@Nullable
	public static Instant parseTimestampOrNull(HttpServletRequest request) {
		try {
			return Instant.parse(request.getHeader(TIMESTAMP));
		}
		catch (Exception e) {
			log.error("Failed to parse timestamp header: {}", e.getMessage());
			return null;
		}
	}

}
