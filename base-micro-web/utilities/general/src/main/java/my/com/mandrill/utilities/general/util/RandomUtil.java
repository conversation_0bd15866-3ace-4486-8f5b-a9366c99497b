package my.com.mandrill.utilities.general.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class RandomUtil {

	private static final Random RANDOM = new Random();

	private static final String ALL_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

	public static String generateAlphanumeric(int count) {
		return RandomStringUtils.randomAlphanumeric(count);
	}

	/**
	 * This method will generate a guaranteed alphanumeric character with one lower case,
	 * one upper case, and one numeric character with string length given by the count
	 * argument. Due to the guaranteed characters, the count parameter cannot be less than
	 * 3.
	 * @param count - length of the random generated string
	 * @return A string with guaranteed alphanumeric character
	 */
	public static String generateGuaranteedAlphanumeric(int count) {
		if (count < 3) {
			throw new IllegalArgumentException("Random string with guaranteed mix of upper, "
					+ "lower and numeric character cannot have length less than 3");
		}

		char upperCaseChar = (char) ('A' + RANDOM.nextInt(26));
		char lowerCaseChar = (char) ('a' + RANDOM.nextInt(26));
		char numericChar = (char) ('0' + RANDOM.nextInt(10));

		List<Character> result = new ArrayList<>();
		result.add(upperCaseChar);
		result.add(lowerCaseChar);
		result.add(numericChar);

		List<Character> remainingCharacters = RANDOM.ints(count - 3, 0, ALL_CHARS.length()).mapToObj(ALL_CHARS::charAt)
				.toList();

		result.addAll(remainingCharacters);

		Collections.shuffle(result, RANDOM);

		return result.stream().map(String::valueOf).collect(Collectors.joining());
	}

	public static String generateAlphanumeric(int min, int max) {
		return RandomStringUtils.randomAlphanumeric(min, max);
	}

	public static String generateNumber(int count) {
		return RandomStringUtils.randomNumeric(count);
	}

	public static String generateNumber(int min, int max) {
		return RandomStringUtils.randomNumeric(min, max);
	}

}
