package my.com.mandrill.component.service;

import jakarta.servlet.http.HttpServletRequest;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.dto.model.AuthenticateResultDTO;
import my.com.mandrill.component.dto.request.AuthServiceRequest;
import my.com.mandrill.component.dto.request.AuthenticateUserRequest;
import my.com.mandrill.utilities.feign.dto.AuthenticateDTO;

public interface AuthIntgService {

	AuthenticateResultDTO authenticateUser(AuthServiceRequest authServiceRequest);

	AuthenticateDTO authenticatePassword(AuthenticateUserRequest request);

	AuthenticateResultDTO createSignatureToken(AppUser user, HttpServletRequest request);

}
