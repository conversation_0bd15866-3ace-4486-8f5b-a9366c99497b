package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.*;
import my.com.mandrill.component.constant.UserInterestStatus;
import my.com.mandrill.crypto.converter.ConfidentialDataConverter;
import my.com.mandrill.utilities.converter.ProtectedDataConverterBigDecimal;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.LinkedHashSet;
import java.util.Set;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Builder
@Table(name = "user_interest_record", uniqueConstraints = { @UniqueConstraint(columnNames = { "ref_no" }) })
public class UserInterestRecord extends AuditSection implements Serializable {

	@Column(name = "user_id", length = 36)
	private String userId;

	@Column(name = "ref_no", length = 36)
	private String refNo;

	@Column(name = "full_name", length = 100)
	private String fullName;

	@Column(name = "email")
	@Convert(converter = ConfidentialDataConverter.class)
	private String email;

	@Column(name = "phone_number", length = 100)
	@Convert(converter = ConfidentialDataConverter.class)
	private String phoneNumber;

	@Column(name = "provider_id", nullable = false)
	private String providerId;

	@Column(name = "issuer_code", nullable = false)
	private String issuerCode;

	@Column(name = "issuer_type", nullable = false)
	private String issuerType;

	@Column(name = "product_type", nullable = false)
	private String productType;

	@Column(name = "product_name", nullable = false)
	private String productName;

	@Column(name = "product_id", length = 36, nullable = false)
	private String productId;

	@Column(name = "nric")
	@Convert(converter = ConfidentialDataConverter.class)
	private String nric;

	@Column(name = "reapply_date")
	private Instant reapplyDate;

	@Column(name = "is_redirect")
	private boolean isRedirect = false;

	@Column(name = "is_balance_transfer", nullable = false, columnDefinition = "BOOLEAN DEFAULT FALSE")
	private Boolean isBalanceTransfer = false;

	@Enumerated(EnumType.STRING)
	@Column(name = "source")
	private UserInterestedSource source;

	@Column(name = "source_id", length = 36)
	private String sourceId;

	@NotBlank
	@Column(name = "user_ref_no", length = 15)
	private String userRefNo;

	@Column(name = "status")
	@Enumerated(EnumType.STRING)
	private UserInterestStatus status = UserInterestStatus.OPEN;

	@Transient
	private Long vaultCount;

	@Column(name = "url", columnDefinition = "TEXT")
	private String url;

	@ToString.Exclude
	@Builder.Default
	@JsonManagedReference
	@OneToMany(mappedBy = "userInterestRecord")
	private Set<UserInterestRecordAddon> addons = new LinkedHashSet<>();

	@Column(name = "income_amount", length = 100)
	@Convert(converter = ProtectedDataConverterBigDecimal.class)
	private BigDecimal incomeAmount;

	@Enumerated(EnumType.STRING)
	@Column(name = "rsm_relation")
	private RSMRelationType rsmRelation;

	@Enumerated(EnumType.STRING)
	@Column(name = "rsm_status")
	private RSMStatus rsmStatus;

	@Column(name = "rsm_eligible")
	private boolean rsmEligible;

	@Column(name = "rsm_commission_attached")
	private boolean rsmCommissionAttached;

	@Column(name = "remarks")
	private String remarks;

	@Column(name = "application_date")
	private Instant applicationDate;

}
