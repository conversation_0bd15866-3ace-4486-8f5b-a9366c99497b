package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.constant.HomeMenuStatusEnum;
import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.domain.HomeMenuInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HomeMenuRepository extends JpaRepository<HomeMenu, String> {

	boolean existsByCodeIgnoreCase(String code);

	Page<HomeMenu> findByCodeContainsIgnoreCase(String code, Pageable pageable);

	List<HomeMenu> findAllByParentId(String id);

	@Query("SELECT m FROM HomeMenu m LEFT JOIN FETCH m.parent")
	List<HomeMenu> findAllWithParent();

	@EntityGraph(attributePaths = { "parent", "homeMenuProduct", "homeMenuProduct.subProducts" })
	@Query("SELECT h FROM HomeMenu h")
	List<HomeMenu> findAllWithParentAndProducts();

	List<HomeMenu> findAllByParentIdIsNull();

	Page<HomeMenu> findAllByCodeContainsIgnoreCaseAndParentIdIsNull(String code, Pageable pageable);

	Page<HomeMenu> findAllByCodeContainsIgnoreCaseAndParentIdIsNotNull(String code, Pageable pageable);

	@Query("""
			SELECT new my.com.mandrill.component.domain.HomeMenuInfo(h.id,h.name,h.code,h.description) FROM HomeMenu h
			WHERE h.state = :state
			AND LOWER(h.name) LIKE %:name%
			""")
	List<HomeMenuInfo> findByState(HomeMenuStatusEnum state, String name);

	@Query("SELECT hm FROM HomeMenu hm WHERE LOWER(hm.code) LIKE LOWER(CONCAT('%', :code, '%')) AND hm.homeMenuProduct IS NOT EMPTY")
	Page<HomeMenu> findByCodeContainsIgnoreCaseAndHomeMenuProductNotEmpty(String code, Pageable pageable);

}
