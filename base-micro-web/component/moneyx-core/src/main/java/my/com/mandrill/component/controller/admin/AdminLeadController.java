package my.com.mandrill.component.controller.admin;

import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.LeadDetailResponse;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.general.constant.ApplicationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("admin")
@RequiredArgsConstructor
public class AdminLeadController {

	private final ********************** leadIntegrationService;

	@PostMapping("v1/lead")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_READ)")
	public Page<UserInterestRecordRSMPaginationDTO> getLead(Pageable pageable,
			@Valid @RequestBody RSMLeadRequest request) {
		return leadIntegrationService.getLeads(pageable, request);
	}

	@GetMapping("v1/lead/{applicationType}/{applicationId}")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_READ)")
	public LeadDetailResponse getLeadDetail(@PathVariable ApplicationType applicationType,
			@PathVariable String applicationId) {
		return leadIntegrationService.getLeadDetail(applicationType, applicationId);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("v1/lead/attach-commission")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_CREATE)")
	public void attachCommission(@Valid @RequestBody AttachCommissionRequest request) {
		leadIntegrationService.attachCommission(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("v1/lead/disburse-commission")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_UPDATE)")
	public void disburseCommission(@Valid @RequestBody DisburseCommissionRequest request) {
		leadIntegrationService.disburseCommission(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("v1/lead/delete-commission")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_DELETE)")
	public void deleteCommission(@Valid @RequestBody DeleteCommissionRequest request) {
		leadIntegrationService.deleteCommission(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("v1/lead/bulk-delete-commission")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_DELETE)")
	public void bulkDeleteCommission(@Valid @RequestBody BulkDeleteCommissionRequest request) {
		leadIntegrationService.deleteCommission(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("v1/lead/update-status")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_CREATE)")
	public void updateStatus(@Valid @RequestBody UpdateRSMInfoRequest request) {
		leadIntegrationService.updateStatus(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("v1/lead/update-status/bulk")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_CREATE)")
	public void bulkUpdateStatus(@Valid @RequestBody BulkUpdateRsmInfoRequest request) {
		leadIntegrationService.bulkUpdateStatus(request);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PostMapping("v1/lead/manual")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_LEAD_MANUAL_CREATE)")
	public void createManualLead(@Valid @RequestBody AdminCreateManualLeadRequest adminCreateManualLeadRequest)
			throws JsonProcessingException {
		leadIntegrationService.createManualLead(adminCreateManualLeadRequest);
	}

}
