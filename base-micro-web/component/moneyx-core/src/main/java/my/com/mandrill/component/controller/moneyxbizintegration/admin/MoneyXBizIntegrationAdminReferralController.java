package my.com.mandrill.component.controller.moneyxbizintegration.admin;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.constant.RSMReferralCodeStatus;
import my.com.mandrill.component.dto.request.UpdateCustomerReferralRequest;
import my.com.mandrill.component.dto.response.ReferralCodeResponse;
import my.com.mandrill.component.service.ReferralIntegrationService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.Set;

@RestController
@RequiredArgsConstructor
@RequestMapping("moneyxbiz-integration/admin/referral")
public class MoneyXBizIntegrationAdminReferralController {

	private final ReferralIntegrationService referralIntegrationService;

	@PublicAuth
	@SecurityRequirements
	@PutMapping("/{id}/status")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_UPDATE_API)")
	public ResponseEntity<ReferralCodeResponse> updateReferralStatusForInstitutionId(@PathVariable String id,
			@Valid @RequestBody UpdateCustomerReferralRequest request) {
		return ResponseEntity.ok(referralIntegrationService.updateReferralStatusForInstitutionId(id, request));
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("/company-ids")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public ResponseEntity<Set<String>> getCompanyIdsWithReferralApplicationStatus(Sort sort,
			@RequestParam(required = false) RSMReferralCodeStatus status,
			@RequestParam(required = false) Instant dateFrom, @RequestParam(required = false) Instant dateTo) {
		return ResponseEntity.ok(referralIntegrationService.getBizCompanyIdsWithReferralApplicationStatus(sort, status,
				dateFrom, dateTo));
	}

}
