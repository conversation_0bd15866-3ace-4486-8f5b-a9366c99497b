package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.ApplicationType;
import my.com.mandrill.utilities.general.constant.RSMStatus;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateRSMInfoRequest {

	@NotBlank
	private String applicationId;

	@NotNull
	private ApplicationType applicationType;

	@NotNull
	private RSMStatus status;

	private String remarks;

}
