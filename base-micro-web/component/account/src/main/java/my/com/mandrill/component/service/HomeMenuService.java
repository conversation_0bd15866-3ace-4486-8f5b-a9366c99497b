package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.domain.HomeMenuInfo;
import my.com.mandrill.component.dto.model.HomeMenuDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface HomeMenuService {

	HomeMenu save(HomeMenu homeMenu);

	HomeMenu findById(String id);

	void delete(HomeMenu homeMenu);

	boolean existsByCode(String code);

	Page<HomeMenu> findAll(String code, Pageable pageable);

	List<HomeMenuInfo> findEnabledState(String name);

	List<HomeMenu> findWithoutParentId();

	Page<HomeMenu> findAllWithoutParentId(String code, Pageable pageable);

	Page<HomeMenu> findAllWithParentId(String code, Pageable pageable);

	Page<HomeMenu> findAllProducts(String code, Pageable pageable);

	List<HomeMenu> findAllWithParent();

}
