package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.*;
import my.com.mandrill.component.constant.AuthenticationProviderSource;
import my.com.mandrill.crypto.converter.ConfidentialDataConverter;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.converter.ConfidentialDataConverterLocalDate;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.feign.dto.CountryDTO;
import my.com.mandrill.utilities.feign.dto.StateDTO;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.util.CalculationUtil;
import org.hibernate.Hibernate;
import org.hibernate.annotations.Cascade;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "app_user", uniqueConstraints = { @UniqueConstraint(columnNames = { "username", "login_type" }),
		@UniqueConstraint(columnNames = { "ref_no" }), @UniqueConstraint(columnNames = { "email", "login_type" }) })
@NamedEntityGraph(name = "graph.User.load-lazy",
		attributeNodes = { @NamedAttributeNode(value = "incomes"), @NamedAttributeNode(value = "expenses") })
public class User extends AuditSection implements Serializable {

	@NotNull
	@Column(name = "ref_no", updatable = false, length = 15)
	private String refNo;

	@NotNull
	@Pattern(regexp = Constant.LOGIN_REGEX)
	@Size(min = 1, max = 100)
	@Column(length = 100, nullable = false, updatable = false)
	private String username;

	@Size(min = 60, max = 60)
	@Column(name = "password_hash", length = 60)
	private String password;

	@Column(name = "pin")
	private String pin;

	@Enumerated(EnumType.STRING)
	@Column(nullable = false, length = 50)
	private AuthenticationProviderSource provider;

	@Size(max = 200)
	@Column(name = "full_name", length = 200)
	private String fullName;

	@Size(min = 5, max = 100)
	@Column(length = 100)
	private String email;

	@NotNull
	@Column(name = "email_verified", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean emailVerified = false;

	@Column(name = "phone_country", length = 3)
	private String phoneCountry;

	@Column(name = "phone_number", length = 100)
	private String phoneNumber;

	@NotNull
	@Column(name = "phone_verified", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean phoneVerified = false;

	@Column(name = "lang_key", length = 6)
	private Language langKey = Language.ENGLISH;

	@NotNull
	@Column(columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean active = false;

	@Column(nullable = false, columnDefinition = "INT(2) DEFAULT 0")
	private Integer loginFailAttempt;

	@Column(name = "login_fail_device_lock_log")
	private String loginFailDeviceLockLog;

	@ToString.Exclude
	@ManyToMany(fetch = FetchType.EAGER, cascade = { CascadeType.REFRESH })
	@JoinTable(name = "app_user_authority",
			joinColumns = { @JoinColumn(name = "user_id", nullable = false, updatable = false) },
			inverseJoinColumns = { @JoinColumn(name = "authority_id", nullable = false, updatable = false) })
	@Cascade({ org.hibernate.annotations.CascadeType.DETACH, org.hibernate.annotations.CascadeType.LOCK,
			org.hibernate.annotations.CascadeType.REFRESH, org.hibernate.annotations.CascadeType.REPLICATE })
	private Set<Authority> authorities = new HashSet<>();

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = "app_user_institution", joinColumns = @JoinColumn(name = "user_id"),
			inverseJoinColumns = @JoinColumn(name = "institution_id"))
	@ToString.Exclude
	private Set<Institution> institutions = new HashSet<>();

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "login_type", length = 50, columnDefinition = "VARCHAR(50) DEFAULT 'ADMIN'")
	private LoginTypeEnum loginType;

	// personal info
	@Column(name = "nric", length = 200)
	@Convert(converter = ConfidentialDataConverter.class)
	private String nric;

	@NotNull
	@Column(name = "is_nric_editable", columnDefinition = "BOOLEAN DEFAULT TRUE", nullable = false)
	private Boolean isNricEditable = true;

	@Column(name = "address1")
	@Convert(converter = ConfidentialDataConverter.class)
	private String address1;

	@Column(name = "address2")
	@Convert(converter = ConfidentialDataConverter.class)
	private String address2;

	@Column(name = "address3")
	@Convert(converter = ConfidentialDataConverter.class)
	private String address3;

	@Column(name = "postcode", length = 100)
	private String postcode;

	@Column(name = "country_id", length = 36)
	private String countryId;

	@Column(name = "state_id", length = 36)
	private String stateId;

	@ManyToOne
	@JoinColumn(name = "nationality_id")
	private Nationality nationality;

	@NotNull
	@Column(name = "is_nationality_editable", columnDefinition = "BOOLEAN DEFAULT TRUE", nullable = false)
	private Boolean isNationalityEditable = true;

	@Column(columnDefinition = "INT(3)")
	private Integer age;

	@Column(name = "gender", length = 10)
	private String gender;

	@Column(name = "marital_status", length = 15)
	private String maritalStatus;

	@Column(name = "ethnicity", length = 20)
	private String ethnicity;

	@Column(name = "religion", length = 20)
	private String religion;

	@ManyToOne
	@JoinColumn(name = "currency_id")
	private Currency currency;

	@Digits(integer = 5, fraction = 2)
	@Column(name = "epf_contribution", columnDefinition = "DECIMAL(5,2)", precision = 5, scale = 2)
	private BigDecimal epfContribution;

	@Column(name = "socso")
	private Boolean socso;

	@Column(name = "eis")
	private Boolean eis;

	@ManyToOne
	@JoinColumn(name = "education_level_id")
	private EducationLevel educationLevel;

	@ManyToOne
	@JoinColumn(name = "employment_type_id")
	private EmploymentType employmentType;

	@ManyToOne
	@JoinColumn(name = "occupation_group_id")
	private OccupationGroup occupationGroup;

	@ManyToOne
	@JoinColumn(name = "business_nature_id")
	private BusinessNature businessNature;

	@Column(name = "self_employed_name", length = 100)
	private String selfEmployedName;

	@ToString.Exclude
	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = "app_user_interest", joinColumns = @JoinColumn(name = "user_id"),
			inverseJoinColumns = @JoinColumn(name = "interest_id"))
	private Set<Interest> interests = new HashSet<>();

	@ToString.Exclude
	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = "app_user_financial_goal", joinColumns = @JoinColumn(name = "user_id"),
			inverseJoinColumns = @JoinColumn(name = "financial_goal_id"))
	private Set<FinancialGoal> financialGoals = new HashSet<>();

	@ToString.Exclude
	@OneToMany(fetch = FetchType.EAGER, mappedBy = "user")
	private Set<Income> incomes = new LinkedHashSet<>();

	@ToString.Exclude
	@OneToMany(fetch = FetchType.EAGER, mappedBy = "user")
	private Set<Expense> expenses = new LinkedHashSet<>();

	@ToString.Exclude
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "user")
	private Set<Token> tokens = new LinkedHashSet<>();

	@ManyToOne
	@JoinColumn(name = "segment_id")
	private Segment segment;

	@Column(name = "secret_key")
	@Convert(converter = ConfidentialDataConverter.class)
	private String secretKey;

	// the datetime the user start to initiate the delete process
	@Column(name = "deleted_datetime")
	private Instant deletedDatetime;

	// once the scheduler pickup the record after 30 days we will flag this to be deleted
	// record
	@Column(columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private Boolean deleted = false;

	@Column(name = "term_condition_version", length = 20)
	private String termConditionVersion;

	@Column(name = "privacy_policy_version", length = 20)
	private String privacyPolicyVersion;

	@Column(name = "platform_agreement_version", length = 20)
	private String platformAgreementVersion;

	@Column(name = "referral_code_term_condition_version", length = 20)
	private String referralCodeTermConditionVersion;

	// transient
	@Transient
	private Institution currentInstitution;

	@Transient
	private String key;

	@Transient
	private String value;

	@Column(name = "passport", length = 100)
	@Convert(converter = ConfidentialDataConverter.class)
	private String passport;

	@Column(name = "army", length = 100)
	@Convert(converter = ConfidentialDataConverter.class)
	private String army;

	@Column(name = "dob")
	@Convert(converter = ConfidentialDataConverterLocalDate.class)
	private LocalDate dob;

	@Enumerated(EnumType.STRING)
	@Column(name = "payment_info_status")
	private PaymentAccountStatus paymentInfoStatus = PaymentAccountStatus.YET_TO_SUBMIT;

	@Column(name = "payment_info_approved_date")
	private Instant paymentInfoApprovedDate;

	@Enumerated(EnumType.STRING)
	@Column(name = "ekyc_verification_status")
	private EkycStatus ekycVerificationStatus;

	@Column(name = "referral_code")
	private String referralCode;

	@Column(name = "referral_code_used")
	private String referralCodeUsed;

	@Column(name = "rsm_relation_type")
	private String rsmRelationType;

	@Column(name = "blood_type", length = 3)
	private String bloodType;

	private transient CountryDTO country;

	// TODO: JIRA Requirements: https://mandrill.atlassian.net/browse/PRJA-206
	// @Transient
	// private UserJourneyEnum userJourney;
	private transient StateDTO state;

	public User(String id) {
		this.setId(id);
	}

	public User(String phoneCountry, String phoneNumber, LoginTypeEnum loginType) {
		setPhoneCountry(phoneCountry);
		setPhoneNumber(phoneNumber);
		setLoginType(loginType);
	}

	public void setDob(LocalDate dob) {
		this.dob = dob;
		if (dob != null)
			this.age = CalculationUtil.calculateAgeByDob(dob);
	}

	public String getEmail() {
		return this.email == null ? null : AesCryptoUtil.basicDecrypt(this.email);
	}

	public void setEmail(String email) {
		this.email = email == null ? null : AesCryptoUtil.basicEncrypt(email);
	}

	public String getEmailRaw() {
		return this.email;
	}

	public String getPhoneNumber() {
		return this.phoneNumber == null ? null : AesCryptoUtil.basicDecrypt(this.phoneNumber);
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber == null ? null : AesCryptoUtil.basicEncrypt(phoneNumber);
	}

	public String getPhoneNumberRaw() {
		return this.phoneNumber;
	}

	private void setIsNricEditable(Boolean isNricEditable) {
		// Disallow use of this method
	}

	public void setNric(String nric) {
		if (Boolean.TRUE.equals(this.isNricEditable)) {
			this.nric = nric;
		}
	}

	public void setNric(String nric, NricUpdateOrigin origin) {
		if (origin == null) {
			setNric(nric);
			return;
		}

		switch (origin) {
			case EKYC -> {
				this.isNricEditable = false;
				this.nric = nric;
			}
			case ACCOUNT_DELETION -> {
				this.isNricEditable = true;
				this.nric = nric;
			}
			default -> setNric(nric);
		}
	}

	private void setIsNationalityEditable(Boolean isNationalityEditable) {
		// Disallow use of this method
	}

	public void setNationality(Nationality nationality) {
		if (Boolean.TRUE.equals(this.isNationalityEditable)) {
			this.nationality = nationality;
		}
	}

	public void setNationality(Nationality nationality, NationalityUpdateOrigin origin) {
		if (origin == null) {
			setNationality(nationality);
			return;
		}

		switch (origin) {
			case EKYC -> {
				this.isNationalityEditable = false;
				this.nationality = nationality;
			}
			case ACCOUNT_DELETION -> {
				this.isNationalityEditable = true;
				this.nationality = nationality;
			}
			default -> setNationality(nationality);
		}
	}

	public PaymentAccountStatus getPaymentInfoStatus() {
		if (Objects.isNull(paymentInfoStatus)) {
			return PaymentAccountStatus.YET_TO_SUBMIT;
		}
		return paymentInfoStatus;
	}

	public void setPaymentInfoStatus(PaymentAccountStatus paymentInfoStatus) {
		if (Objects.isNull(paymentInfoStatus)) {
			this.paymentInfoStatus = PaymentAccountStatus.YET_TO_SUBMIT;
		}
		else {
			this.paymentInfoStatus = paymentInfoStatus;
		}
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		User user = (User) o;
		return getId() != null && Objects.equals(getId(), user.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}
