package my.com.mandrill.component.service.impl;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Authority;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.AuthorityRepository;
import my.com.mandrill.component.service.AuthorityService;
import my.com.mandrill.component.service.PermissionService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.service.GlobalValidationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Transactional(readOnly = true)
public class AuthorityServiceImpl implements AuthorityService {

	private static final String DEFAULT_ADMIN_NAME = "ROLE_ADMIN";

	private final AuthorityRepository authorityRepository;

	private final PermissionService permissionService;

	private final GlobalValidationService globalValidationService;

	public AuthorityServiceImpl(AuthorityRepository authorityRepository, PermissionService permissionService,
			GlobalValidationService globalValidationService) {
		this.authorityRepository = authorityRepository;
		this.permissionService = permissionService;
		this.globalValidationService = globalValidationService;
	}

	@Transactional
	@Override
	public Authority create(Authority authority) {
		authority.setPermissions(permissionService.getPermissions(authority.getPermissions()));
		return authorityRepository.save(authority);
	}

	@Transactional
	@Override
	public Authority createDefault(Institution institution) {
		Authority defaultAuthority = Authority.builder().name(DEFAULT_ADMIN_NAME).active(true).institution(institution)
				.permissions(new HashSet<>(permissionService.getPermissionByIsAdminFalse())).build();
		return authorityRepository.save(defaultAuthority);
	}

	@Override
	public void checkIfExists(String name, String institutionId) {
		Optional<Authority> authorityOptional = authorityRepository.findByNameAndInstitutionId(name, institutionId);
		if (authorityOptional.isPresent()) {
			throw new BusinessException(ErrorCodeEnum.AUTHORITY_EXISTED);
		}
	}

	@Override
	public Page<Authority> findAllByNameAndInstitutionId(Pageable page, String name, String institutionId) {
		return authorityRepository.findAllByNameAndInstitutionId(page,
				globalValidationService.validateNullToLowerCase(name), institutionId);
	}

	@Override
	public List<Authority> findAllByInstitutionIdsAndActiveTrue(List<String> institutionIds) {
		return authorityRepository.findAllByInstitutionIdInAndActiveTrue(institutionIds);
	}

	public Authority findByIdAndInstitutionId(String authorityId, String institutionId) {
		return authorityRepository.findByIdAndInstitutionId(authorityId, institutionId)
				.orElseThrow(ExceptionPredicate.authorityByIdNotFound(authorityId));
	}

	@Transactional
	public Authority updateAuthority(Authority authority, String institutionId) {
		Authority existingAuthority = findByIdAndInstitutionId(authority.getId(), institutionId);
		// non-updatable field
		authority.setInstitution(existingAuthority.getInstitution());

		authority.setPermissions(permissionService.getPermissions(authority.getPermissions()));

		return authorityRepository.save(authority);
	}

	@Transactional
	public void deleteByIdAndInstitutionId(String authorityId, String institutionId) {
		authorityRepository.deleteByIdAndInstitutionId(authorityId, institutionId);
	}

	@Override
	public Authority findByName(String name) {
		return authorityRepository.findByName(name).orElseThrow(ExceptionPredicate.authorityNotFoundByName(name));
	}

	@Override
	public Optional<Authority> findById(String id) {
		return authorityRepository.findById(id);
	}

}
