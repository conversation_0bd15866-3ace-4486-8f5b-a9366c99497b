package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Property;
import my.com.mandrill.component.dto.model.PropertyDTO;
import my.com.mandrill.component.dto.request.PropertyRequest;
import my.com.mandrill.component.dto.response.PropertyResponse;
import my.com.mandrill.component.service.PropertyIntegrationService;
import my.com.mandrill.component.service.PropertyService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "01-property")
@Slf4j
@RestController
@RequestMapping("/v2/properties")
@RequiredArgsConstructor
public class PropertyControllerV2 {

	private final PropertyService propertyService;

	private final PropertyIntegrationService propertyIntegrationService;

	private final ValidationService validationService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_CREATE)")
	public ResponseEntity<PropertyResponse> createPropertyUseRevamp(@Valid @RequestBody PropertyRequest request) {
		Property property = MapStructConverter.MAPPER.toProperty(request);
		property = validationService.validateProperty(property, request, true);
		property.setUserId(SecurityUtil.currentUserId());
		Property propertyResponse = propertyIntegrationService.create(property);
		propertyIntegrationService.sendDashboardActivity(propertyResponse.getCreatedDate());

		propertyIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.PROPERTIES, NetWorthSource.PROPERTY);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toPropertyResponse(property));
	}

	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<PropertyResponse> updatePropertyUseRevamp(@PathVariable String id,
			@Valid @RequestBody PropertyRequest request) {
		Property existingProperty = propertyService.findById(id, SecurityUtil.currentUserId());
		existingProperty = validationService.validateProperty(existingProperty, request, true);
		Property propertyResponse = propertyIntegrationService.create(existingProperty);

		propertyIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.PROPERTIES, NetWorthSource.PROPERTY);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toPropertyResponse(propertyResponse));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<PropertyResponse>> getPropertyForRevamp(Sort sort) {
		List<PropertyDTO> result = propertyIntegrationService.findByUserId(SecurityUtil.currentUserId(), sort);
		return ResponseEntity.ok(result.stream().map(MapStructConverter.MAPPER::toPropertyResponse).toList());
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<PropertyResponse> findByIdForRevamp(@PathVariable String id) {
		Property result = propertyService.findById(id, SecurityUtil.currentUserId());
		PropertyResponse propertyResponse = MapStructConverter.MAPPER.toPropertyResponse(result);
		if (StringUtils.isNotBlank(propertyResponse.getAttachmentGroupId())) {
			propertyIntegrationService.populateAttachment(propertyResponse);
		}
		propertyIntegrationService.populateState(propertyResponse);
		propertyIntegrationService.populateMonthlyInstallment(propertyResponse);
		return ResponseEntity.ok(propertyResponse);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@DeleteMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void deleteProperty(@PathVariable String id) {
		propertyIntegrationService.deleteV2(id, SecurityUtil.currentUserId());

		propertyIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.PROPERTIES, NetWorthSource.PROPERTY);
	}

}
