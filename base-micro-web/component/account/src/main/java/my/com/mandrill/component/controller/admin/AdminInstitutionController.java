package my.com.mandrill.component.controller.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.dto.model.InstitutionDTO;
import my.com.mandrill.component.dto.model.ObjectDTO;
import my.com.mandrill.component.dto.request.InstitutionRequest;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.core.annotation.HasUserInterestedAccess;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "07-institution")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/institution")
public class AdminInstitutionController {

	private final AccountSystemConfigurationService accountSystemConfigurationService;

	private final AuthorityService authorityService;

	private final InstitutionService institutionService;

	private final InstitutionIntegrationService institutionIntegrationService;

	private final ValidationService validationService;

	private final ObjectMapper objectMapper;

	private final ProxyFeignClient proxyFeignClient;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.INSTITUTION_CREATE)")
	public ResponseEntity<InstitutionDTO> createInstitution(@Valid @RequestBody InstitutionRequest institutionDTO)
			throws BusinessException {
		// Transformation
		Institution institution = objectMapper.convertValue(institutionDTO, Institution.class);

		// Validation
		validationService.validateInstitutionExist(institution.getName());
		// Implementation
		Institution result = institutionIntegrationService.createInstitution(institution);
		if (result.getTier().equals(1)) {
			accountSystemConfigurationService.createDefaultSystemConfiguration(result);
		}
		authorityService.createDefault(result);
		// Result
		return ResponseEntity
				.ok(objectMapper.convertValue(createOrUpdate(result, institutionDTO), InstitutionDTO.class));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.INSTITUTION_READ)")
	public ResponseEntity<Page<InstitutionDTO>> getAllInstitutions(
			@PageableDefault(size = Integer.MAX_VALUE) Pageable pageable, @RequestParam(required = false) String name,
			@RequestParam(required = false) String parentId) {
		Page<Institution> result = institutionService.getAllInstitutions(pageable, name, parentId);

		return ResponseEntity.ok().body(result.map(value -> objectMapper.convertValue(value, InstitutionDTO.class)));
	}

	@GetMapping("no-pagination")
	@PreAuthorize("hasAuthority(@authorityPermission.INSTITUTION_READ)")
	public ResponseEntity<List<ObjectDTO>> getAllInstitutionsNoPagination(Sort sort) {
		List<Institution> result = institutionService.getAllInstitutionsNoPaginationWithSorting(sort);
		return ResponseEntity.ok()
				.body(result.stream().map(value -> objectMapper.convertValue(value, ObjectDTO.class)).toList());
	}

	@PutMapping
	@PreAuthorize("hasAuthority(@authorityPermission.INSTITUTION_UPDATE)")
	public ResponseEntity<InstitutionDTO> updateInstitution(@Valid @RequestBody InstitutionRequest institutionDTO)
			throws BusinessException {
		// Transformation
		Institution institution = objectMapper.convertValue(institutionDTO, Institution.class);

		// Validation
		Institution existingInstitution = institutionService.getInstitutionById(institution.getId());
		if (!existingInstitution.getName().equals(institution.getName())) {
			validationService.validateInstitutionExist(institution.getName());
		}
		institution.setAttachmentGroupId(existingInstitution.getAttachmentGroupId());
		institution.setUrl(existingInstitution.getUrl());
		// Result
		Institution updatedInstitution = institutionService.updateInstitution(institution);
		return ResponseEntity.ok(
				objectMapper.convertValue(createOrUpdate(updatedInstitution, institutionDTO), InstitutionDTO.class));
	}

	private Institution createOrUpdate(Institution institution, InstitutionRequest institutionDTO) {
		if (institutionDTO.getAttachmentGroupDTO() != null) {
			return institutionIntegrationService.uploadLogo(institution.getId(),
					institutionDTO.getAttachmentGroupDTO());
		}
		return institution;
	}

	@GetMapping("/{institutionId}")
	@PreAuthorize("hasAuthority(@authorityPermission.INSTITUTION_READ)")
	public ResponseEntity<InstitutionDTO> getInstitutionById(@PathVariable String institutionId) {
		Institution result = institutionService.getInstitutionById(institutionId);
		return ResponseEntity.ok(objectMapper.convertValue(result, InstitutionDTO.class));
	}

	@GetMapping("active/{institutionId}")
	public ResponseEntity<InstitutionDTO> getActiveInstitutionById(@PathVariable String institutionId) {
		Institution result = institutionService.getInstitutionByIdAndActiveTrue(institutionId);
		return ResponseEntity.ok(objectMapper.convertValue(result, InstitutionDTO.class));
	}

	@GetMapping("parent/{parentInstitutionId}")
	public ResponseEntity<List<InstitutionDTO>> getInstitutionsByParentId(@PathVariable String parentInstitutionId) {
		List<Institution> result = institutionService.getInstitutionsByParentId(parentInstitutionId);

		return ResponseEntity.ok(result.stream()
				.map(institution -> objectMapper.convertValue(institution, InstitutionDTO.class)).toList());
	}

	@GetMapping("sort/{currentInstitutionId}")
	@HasUserInterestedAccess
	public List<ObjectDTO> getChildInstitutionsSorted(@PathVariable String currentInstitutionId,
			@RequestParam(required = false) List<String> productTypes,
			@RequestParam(required = false) List<String> productCategories,
			@RequestParam(required = false) Boolean rsmActive) {

		validationService.validateCurrentLoginAndInstitution(currentInstitutionId);
		return institutionIntegrationService.findSimpleSelectionInstitution(currentInstitutionId, productTypes,
				productCategories, rsmActive);
	}

}
