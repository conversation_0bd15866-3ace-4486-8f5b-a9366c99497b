package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.response.CurrentUserInstitutionResponse;
import my.com.mandrill.component.exception.AccountComponentException;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.InstitutionRepository;
import my.com.mandrill.component.service.AppUserService;
import my.com.mandrill.component.service.InstitutionService;
import my.com.mandrill.component.service.UserService;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.util.BooleanUtil;
import my.com.mandrill.utilities.general.util.CryptoUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class InstitutionServiceImpl implements InstitutionService {

	private final InstitutionRepository institutionRepository;

	private final BaseProperties baseProperties;

	private final AppUserService appUserService;

	private final UserService userService;

	@Transactional
	@Override
	public Institution createInstitution(Institution institution) throws BusinessException {
		Institution persistedInstitution = institutionRepository.saveAndFlush(institution);
		// Update PATH
		persistedInstitution
				.setPath((persistedInstitution.getPath() == null ? "" : persistedInstitution.getPath() + "/")
						+ persistedInstitution.getId());
		return institutionRepository.save(persistedInstitution);
	}

	@Override
	public Page<Institution> getAllInstitutions(Pageable pageable, String name, String parentId) {
		return institutionRepository.findAllByNameAndParentInstitutionId(pageable,
				Strings.isBlank(name) ? "" : name.toLowerCase(), parentId);
	}

	@Override
	public List<Institution> getAllInstitutionsNoPagination() {
		return institutionRepository.findAll();
	}

	@Override
	public List<Institution> getAllInstitutionsNoPaginationWithSorting(Sort sort) {
		return institutionRepository.findAll(sort);
	}

	@Transactional
	@Override
	public Institution getInstitutionByIdAndActiveTrue(String institutionId) {
		return institutionRepository.findByIdAndActiveTrue(institutionId).map(this::generateSecretKey)
				.orElseThrow(ExceptionPredicate.institutionNotFound(institutionId));
	}

	@Transactional
	@Override
	public Institution updateInstitution(Institution institution) {
		Institution existingInstitution = getInstitutionById(institution.getId());

		// non-updatable fields
		institution.setParentInstitution(existingInstitution.getParentInstitution());
		institution.setTier(existingInstitution.getTier());
		institution.setPath(existingInstitution.getPath());

		// Avoid activation if parent_id is not activated
		if (existingInstitution.getParentInstitution() != null && institution.getActive().equals(true)
				&& existingInstitution.getParentInstitution().getActive().equals(false)) {
			log.warn(String.format("Parent Institution=%s not activated",
					existingInstitution.getParentInstitution().getId()));
			throw new BusinessException(ErrorCodeEnum.PARENT_INSTITUTION_NOT_ACTIVATED);
		}

		Institution persistedInstitution = institutionRepository.saveAndFlush(institution);
		List<Institution> childInstitutions = institutionRepository
				.findAllChildInstitutionsByPathAndActiveTrue(persistedInstitution.getPath());
		if (persistedInstitution.getActive().equals(false) && !childInstitutions.isEmpty()) {
			log.info(String.format("Deactivating [%d] child Institutions for Institution: %s", childInstitutions.size(),
					persistedInstitution.getId()));
			deactivateChildInstitutions(childInstitutions);
		}
		return persistedInstitution;
	}

	@Override
	public Institution getInstitutionById(String id) {
		return institutionRepository.findById(id).orElseThrow(ExceptionPredicate.institutionNotFound(id));
	}

	private void deactivateChildInstitutions(List<Institution> institutions) {
		institutions.forEach(institution -> {
			institution.setActive(false);
			institutionRepository.save(institution);
		});
	}

	@Override
	public List<Institution> getInstitutionsByParentId(String parentId) {
		return institutionRepository.findByParentInstitutionId(parentId);
	}

	@Override
	public Institution getInstitutionTierOneWithAuthorities(User userParam) {
		AppUser appUser = appUserService.findAuthByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(
				userParam.getPhoneNumber(), userParam.getPhoneCountry(), userParam.getLoginType());
		return appUser.getInstitutions().stream().filter(i -> i.getTier() == 1 || i.getTier() == 0)
				.sorted(Comparator.comparing(Institution::getTier).reversed()).findFirst()
				.orElseThrow(NoSuchElementException::new);
	}

	@Override
	public Institution getInstitutionByAiMapping(String aiMapping) {
		return institutionRepository.findByAiMapping(aiMapping)
				.orElseThrow(ExceptionPredicate.institutionNotFoundByAiMapping(aiMapping));
	}

	@Override
	public List<Institution> getListInstitutionByAiMapping() {
		return institutionRepository.findAllByAiMappingIsNotNull();
	}

	@Override
	public List<Institution> findInstitutionById(List<String> ids) {
		return institutionRepository.findByIdIn(ids);
	}

	@Override
	public List<Institution> findAllByPathContainingAndActiveTrue(String path) {
		return institutionRepository.findAllByPathContainingAndActiveTrue(path);
	}

	@Override
	public boolean existsByName(String name) {
		return institutionRepository.existsByName(name);
	}

	@Override
	public Optional<Institution> findByIdAndActiveTrue(String currentInstitutionId) {
		return institutionRepository.findByIdAndActiveTrue(currentInstitutionId);
	}

	@Override
	@Transactional
	public Institution save(Institution institution) {
		return institutionRepository.save(institution);
	}

	@Override
	public List<CurrentUserInstitutionResponse> getCurrentUserInstitution() {
		String userRefNo = SecurityUtil.currentUserLogin();
		return institutionRepository.getCurrentUserInstitutions(userRefNo);
	}

	@Override
	public Set<String> getInstitutionAiMapping(String institutionId) {
		return institutionRepository.findAiMappingByPath("%" + institutionId + "%").stream().map(v -> {
			return Set.of(v.split("\\|"));
		}).flatMap(Collection::stream).collect(Collectors.toSet());
	}

	@Override
	@Transactional
	public String getInstitutionSecretKey(String aiMappings, String apiKey) {
		if (StringUtils.isNotBlank(baseProperties.getApiKey())) {
			if (!baseProperties.getApiKey().equals(apiKey)) {
				throw new AccessDeniedException("Not allowed to access this method");
			}
		}
		else {
			throw new AccountComponentException("api-key not configured");
		}
		Institution institution = getInstitutionByAiMapping(aiMappings);
		return generateSecretKey(institution).getSecretKey();
	}

	private Institution generateSecretKey(Institution institution) {
		if (institution.getSecretKey() == null) {
			String secretKey = CryptoUtil.generateSecretKey();
			institution.setSecretKey(secretKey);
			return institutionRepository.save(institution);
		}
		return institution;
	}

	@Override
	public List<Institution> getChildInstitutionsByPath(String path, Sort sort) {
		return institutionRepository.findAllChildInstitutionsByPathAndActiveTrue(path, sort);
	}

	@Override
	public List<Institution> getChildInstitutionsByPathAndProviderIds(String path, List<String> providerIds,
			Sort sort) {
		return institutionRepository.findAllChildInstitutionsByPathAndIdsAndActiveTrue(path, providerIds, sort);
	}

	@Override
	public List<Institution> findByIdIn(List<String> ids, Sort sort) {
		return institutionRepository.findByIdIn(ids, sort);
	}

	@Override
	public Institution getHighestPriorityInstitution(AppUser appUser) {
		return appUser.getInstitutions().stream().filter(i -> BooleanUtil.or(i.getTier() == 1, i.getTier() == 0))
				.sorted(Comparator.comparing(Institution::getTier).reversed()).findFirst()
				.orElseThrow(NoSuchElementException::new);
	}

}
