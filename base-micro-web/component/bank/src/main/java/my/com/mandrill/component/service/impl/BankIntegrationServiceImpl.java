package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Nullable;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.dto.model.BankDTO;
import my.com.mandrill.component.dto.model.BankDetailDTO;
import my.com.mandrill.component.dto.model.LoanDTO;
import my.com.mandrill.component.dto.request.BankDetailRequest;
import my.com.mandrill.component.dto.request.BankRequest;
import my.com.mandrill.component.dto.request.ReminderIntegrationRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.client.NotificationFeignClient;
import my.com.mandrill.utilities.feign.client.PropertyFeignClient;
import my.com.mandrill.utilities.feign.dto.*;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.model.DashboardActivityMessage;
import my.com.mandrill.utilities.general.dto.response.UserJourneyResponse;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.service.DashboardTriggerService;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.Year;
import java.util.*;

@Slf4j
@RequiredArgsConstructor
@Service
public class BankIntegrationServiceImpl implements BankIntegrationService {

	private final BankService bankService;

	private final NotificationFeignClient notificationFeignClient;

	private final ObjectMapper objectMapper;

	private final BankDetailService bankDetailService;

	private final CommonFeignClient commonFeignClient;

	private final DashboardTriggerService dashboardTriggerService;

	private final LoanService loanService;

	private final LoanIntegrationService loanIntegrationService;

	private final PropertyFeignClient propertyFeignClient;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	@Override
	public Bank process(BankRequest bankRequest, String userId, String id) {
		Bank bank = null;
		if (Objects.nonNull(id)) {
			bank = bankService.findById(id);
			bank.setCombineCreditCardLimit(bankRequest.getCombineCreditCardLimit());
			// Check whether reminders exist, and delete them
			bank.getBankDetails().forEach(bankDetail -> {
				if (Boolean.TRUE.equals(bankDetail.getIsReminder())) {
					notificationFeignClient.deleteByReminderTypeAndDataId(ReminderType.BANK, bankDetail.getId());
				}
			});
		}
		else {
			if (bankService.existsByBankListIdAndUserId(bankRequest.getBankList().getId(), userId)) {
				throw new BusinessException(ErrorCodeEnum.BANK_EXISTS);
			}
			bank = objectMapper.convertValue(bankRequest, Bank.class);
		}

		Bank processedBank = processBankDetails(bank, bankRequest);

		processedBank.setUserId(userId);
		Bank persisted = bankService.saveAndFlush(processedBank);

		persisted.getBankDetails().forEach(bankDetail -> bankDetail.setBank(persisted));

		return bankService.save(persisted);
	}

	private Bank processBankDetails(Bank bank, BankRequest bankRequest) {
		bankRequest.getBankDetails().forEach(this::validationForYearInBank);

		// bank details
		Set<BankDetail> bankDetails = new HashSet<>();
		bankRequest.getBankDetails().forEach(bankDetailRequest -> {
			BankDetail theBankDetail = objectMapper.convertValue(bankDetailRequest, BankDetail.class);
			ReminderIntegrationRequest reminderIntegrationRequest = bankDetailRequest.getReminderIntegrationRequest();
			if (Objects.nonNull(reminderIntegrationRequest) && Boolean.TRUE.equals(bankDetailRequest.getIsReminder())) {
				theBankDetail.setReminderStartDate(reminderIntegrationRequest.getStartDate());
				theBankDetail.setReminderDeliveryType(DeliveryType.PUSH);
				theBankDetail.setReminderFrequency(ReminderFrequency.ONCE);
			}
			bankDetails.add(theBankDetail);
		});

		bank.getBankDetails().clear();
		bank.getBankDetails().addAll(bankDetails);

		return bank;
	}

	public void validationForYearInBank(BankDetailRequest bankDetailRequest) {
		if (Objects.nonNull(bankDetailRequest.getBankCardExpirationYear())) {
			validationForYear(bankDetailRequest.getBankCardExpirationYear());
		}
		else if (Objects.nonNull(bankDetailRequest.getFixedDepositMaturityYear())) {
			validationForYear(bankDetailRequest.getFixedDepositMaturityYear());
		}
		else if (Objects.nonNull(bankDetailRequest.getCreditCardTypeExpiryYear())) {
			validationForYear(bankDetailRequest.getCreditCardTypeExpiryYear());
		}
	}

	public void validationForYear(String yearString) {
		int yearInt;
		yearInt = Integer.parseInt(yearString);

		Year currentYear = Year.now();
		Year maxYear = currentYear.plusYears(
				Integer.parseInt(SystemConfigurationEnum.DEFAULT_YEAR_RANGE_OF_EXPIRY_CARD_PICKLIST.getValue()));

		if (yearInt < currentYear.getValue() || yearInt > maxYear.getValue()) {
			throw new BusinessException(ErrorCodeEnum.INVALID_YEAR);
		}

	}

	@Override
	public void createOrUpdateReminder(BankDetail bankDetail) {
		ReminderIntegrationRequest reminderIntegrationRequest = new ReminderIntegrationRequest();
		reminderIntegrationRequest.setStartDate(bankDetail.getReminderStartDate());
		ReminderRequest reminderRequest = MapStructConverter.MAPPER.toReminderRequest(reminderIntegrationRequest);
		ObjectRequest data = new ObjectRequest();
		data.setId(bankDetail.getId());
		reminderRequest.setData(data);
		reminderRequest.setReminderType(ReminderType.BANK);
		reminderRequest.setDeliveryType(DeliveryType.PUSH);
		reminderRequest.setReminderFrequency(ReminderFrequency.ONCE);

		try {
			ReminderResponse response = notificationFeignClient
					.findByReminderTypeAndDataId(reminderRequest.getReminderType(), reminderRequest.getData().getId());
			notificationFeignClient.update(reminderRequest, response.getId());
		}
		catch (EntityNotFoundException e) {
			notificationFeignClient.integration(reminderRequest);
		}
	}

	@Override
	public void deleteCreditCard(String id, String userId) {
		List<BankDetail> deletedBankDetails = bankService.deleteBankDetailByCreditCard(id, userId);

		// delete reminders
		for (BankDetail detail : deletedBankDetails) {
			deleteBankReminder(detail.getId());
		}

		// if there's no other bank detail and loan, delete bank as well
		bankService.deleteByIdAndUserIdAndBankDetailsEmptyAndLoansEmpty(id, userId);

		commonFeignClient.deleteUserJourneyByEntityName(EntityName.BANK, id);
	}

	@Override
	public void deleteBankReminder(String id) {
		notificationFeignClient.deleteByReminderTypeAndDataId(ReminderType.BANK, id);
	}

	@Override
	public void completeUserJourney(String dataId, String userJourneyId) {
		if (StringUtils.isNotBlank(userJourneyId)) {
			UserJourneyResponse response = commonFeignClient.getUserJourneyById(userJourneyId);
			UserJourneyRequest userJourneyRequest = UserJourneyRequest.builder().id(userJourneyId)
					.entityName(EntityName.BANK).entityId(dataId).build();
			commonFeignClient.createUserJourney(response.getJourneyStep().getName(), userJourneyRequest);
		}
	}

	@Override
	public void delete(String id, String userId) {
		List<BankDetail> deletedBankDetails = bankService.deleteAllBankDetail(id, userId);

		deletedBankDetails.forEach(bankDetail -> {
			deleteSavingGoalAccount(bankDetail);
			deleteBankReminder(bankDetail.getId());
		});

		bankService.deleteByIdAndUserIdAndBankDetailsEmptyAndLoansEmpty(id, userId);

		commonFeignClient.deleteUserJourneyByEntityName(EntityName.BANK, id);
	}

	@Override
	public void sendDashboardActivity(final AccountType accountType, Instant createdDate) {
		DashboardActivityMessage dto = DashboardActivityMessage.builder().category(DashboardCategory.MODULE_RECORDS)
				.value(1L).createdDate(createdDate).build();

		if (AccountType.SAVINGS_ACCOUNT.equals(accountType) || AccountType.FIXED_DEPOSIT.equals(accountType)) {
			dto.setType(DashboardType.BANKS);
		}
		else if (AccountType.CREDIT_CARD.equals(accountType)) {
			dto.setType(DashboardType.CREDIT_CARD);
		}

		dashboardTriggerService.send(dto);
	}

	@Override
	public void linkVault(BankDetail bankDetail, VaultLinkDTO vaultLinkDTO) {
		bankDetail.setAttachmentGroupId(vaultLinkDTO.getAttachmentGroupId());
		bankDetailService.save(bankDetail);
	}

	/**
	 * @deprecated this method has been migrated to BankLoanCalculationService, reason: to
	 * decouple the dependency hence to avoid circular dependency issue
	 */
	@Deprecated(since = "PRJA-4017", forRemoval = true)
	@Override
	public List<DetailedNetWorthDTO> calculateDetailedNetWorthByUserId(String userId) {
		List<DetailedNetWorthDTO> netWorthResponse = new ArrayList<>();
		List<Bank> bankList = bankService.findByUserId(userId, Sort.unsorted());
		BigDecimal totalAssetBank = new BigDecimal(0);

		for (Bank bank : bankList) {
			totalAssetBank = totalAssetBank.add(calculateAsset(bank));
		}

		netWorthResponse.add(DetailedNetWorthDTO.builder().netWorthType(NetWorthType.CASH_SAVINGS).value(totalAssetBank)
				.valueType(DetailedNetWorthDTO.ValueType.ASSET).build());

		List<Loan> loanList = loanService.findByUserId(userId);

		Map<NetWorthType, BigDecimal> totalMonthlyInstallmentByLoanType = new HashMap<>();
		Map<NetWorthType, BigDecimal> totalOutstandingBalanceByLoanType = new HashMap<>();
		Map<NetWorthType, DetailedNetWorthDTO.ValueFlag> loanValueFlag = new HashMap<>();

		for (Loan loan : loanList) {
			NetWorthType netWorthType = getNetWorthType(loan);
			if (netWorthType != null) {
				totalMonthlyInstallmentByLoanType.merge(netWorthType, loan.getMonthlyInstallment(), BigDecimal::add);

				BigDecimal endingBalance = loanService.calculateLoanEndingBalance(loan);
				loanValueFlag.compute(netWorthType, (k, v) -> {
					if (Objects.isNull(v) && BigDecimal.ZERO.compareTo(endingBalance) >= 0) {
						return DetailedNetWorthDTO.ValueFlag.FULLY_PAID;
					}
					else if (BigDecimal.ZERO.compareTo(endingBalance) < 0) {
						return DetailedNetWorthDTO.ValueFlag.ON_GOING;
					}
					return v;
				});
				totalOutstandingBalanceByLoanType.merge(netWorthType, endingBalance, BigDecimal::add);
			}
		}
		totalMonthlyInstallmentByLoanType.forEach((netWorthType,
				value) -> netWorthResponse.add(DetailedNetWorthDTO.builder().netWorthType(netWorthType)
						.value(DetailedNetWorthDTO.ValueFlag.FULLY_PAID.equals(loanValueFlag.get(netWorthType))
								? BigDecimal.ZERO : value)
						.valueType(DetailedNetWorthDTO.ValueType.RECURRING_REPAYMENT)
						.valueFlag(loanValueFlag.get(netWorthType)).build()));
		totalOutstandingBalanceByLoanType.forEach((netWorthType,
				value) -> netWorthResponse.add(DetailedNetWorthDTO.builder().netWorthType(netWorthType).value(value)
						.valueType(DetailedNetWorthDTO.ValueType.LIABILITY).valueFlag(loanValueFlag.get(netWorthType))
						.build()));

		return netWorthResponse;
	}

	@Nullable
	private NetWorthType getNetWorthType(Loan loan) {
		NetWorthType netWorthType;

		switch (loan.getType()) {
			case AUTO_LOANS -> netWorthType = NetWorthType.VEHICLE_LOAN_MONTHLY_REPAYMENT;
			case EDUCATION_LOANS -> netWorthType = NetWorthType.EDUCATION_LOAN_MONTHLY_REPAYMENT;
			case HOME_LOANS -> netWorthType = NetWorthType.MORTGAGE_LOAN_MONTHLY_REPAYMENT;
			case OTHER_LOAN -> netWorthType = NetWorthType.OTHER_LOAN_MONTHLY_REPAYMENT;
			case PERSONAL_LOANS -> netWorthType = NetWorthType.PERSONAL_LOAN_MONTHLY_REPAYMENT;
			case ISLAMIC_FINANCING -> netWorthType = NetWorthType.ISLAMIC_FINANCING_LOAN_MONTHLY_REPAYMENT;
			default -> netWorthType = null;
		}
		return netWorthType;
	}

	private BigDecimal calculateAsset(Bank bank) {
		BigDecimal assets = new BigDecimal(0);
		for (BankDetail bankDetail : bank.getBankDetails()) {
			if (bankDetail.getSavingsAmount() != null) {
				BigDecimal savingAmount = bankDetail.getSavingsAmount();
				assets = assets.add(savingAmount);
			}
			if (bankDetail.getFixedDepositAmount() != null) {
				BigDecimal fixedDepositAmount = bankDetail.getFixedDepositAmount();
				assets = assets.add(fixedDepositAmount);
			}
		}
		return assets;
	}

	@Override
	public List<BankDTO> findUserBankDetails(String userId, AccountType accountType, String bankListId, Sort sort) {
		List<Bank> banks = null;

		if (Objects.isNull(accountType) && StringUtils.isNotBlank(bankListId)) {
			// if account type is null and banklist got value
			banks = bankService.findByUserIdAndBankListId(userId, bankListId, sort);
		}
		else if (Objects.nonNull(accountType) && StringUtils.isBlank(bankListId)) {
			// if account type got value and banklist is null
			banks = bankService.findByUserIdAndBankDetailsAccountType(userId, accountType, sort);
		}
		else if (Objects.nonNull(accountType) && StringUtils.isNotBlank(bankListId)) {
			// if account type got value and banklist got value
			banks = bankService.findByUserIdAndBankDetailsAccountTypeAndBankListId(userId, accountType, bankListId,
					sort);
		}
		else {
			// if account type and banklist is null
			banks = bankService.findByUserId(userId, sort);
		}

		List<BankDTO> bankDTOS = new ArrayList<>();
		for (Bank bank : banks) {
			// need to map the loans with purchase value to ease fe
			loanIntegrationService.mapEntityDetails(bank.getLoans().stream().toList());
			BankDTO bankDTOConverted = objectMapper.convertValue(bank, BankDTO.class);

			if (!CollectionUtils.isEmpty(bankDTOConverted.getBankDetails())) {
				Optional<BankDetailDTO> firstCreditCardAccountType = bankDTOConverted.getBankDetails().stream()
						.filter(v -> AccountType.CREDIT_CARD.equals(v.getAccountType())).findFirst();

				firstCreditCardAccountType.ifPresent(bankDetailDTO -> bankDTOConverted
						.setCombineCreditCardLimit(bankDetailDTO.getCreditCardLimit()));
			}
			for (LoanDTO loanDTO : bankDTOConverted.getLoans()) {
				String entityId = loanDTO.getEntityId();
				BigDecimal amount = BigDecimal.ZERO;
				if (loanDTO.getAmount() != null && loanDTO.getAmount().compareTo(BigDecimal.ZERO) > 0) {
					amount = loanDTO.getAmount();
				}
				else {
					if (entityId != null) {
						BigDecimal result = loanService.withDetachedCheck(loanDTO.getId(), () -> {
							PropertyDTO propertyDTO = propertyFeignClient.findById(entityId);
							if (propertyDTO != null && propertyDTO.getPurchaseValue() != null
									&& loanDTO.getPercentage() != null) {
								return propertyDTO.getPurchaseValue()
										.multiply(loanDTO.getPercentage().divide(BigDecimal.valueOf(100)));
							}
							return null;
						});
						if (Objects.nonNull(result)) {
							amount = result;
						}
					}

				}
				loanDTO.setAmount(amount);
			}
			bankDTOS.add(bankDTOConverted);
		}

		return bankDTOS;

	}

	@Override
	public Bank findByIdAndUserId(String id, String userId) {
		Bank bank = bankService.findByIdAndUserId(id, userId);
		loanIntegrationService.mapEntityDetails(bank.getLoans().stream().toList());
		return bank;
	}

	@Override
	public void deleteSavingGoalAccount(BankDetail bankDetail) {
		String userId = SecurityUtil.currentUserId();
		if (bankDetail.getAccountType() == AccountType.SAVINGS_ACCOUNT) {
			kafkaTemplate.send(KafkaTopic.SAVING_GOALS_DELETION, jsonUtil.convertToString(
					new SavingGoalDeletionDTO(userId, bankDetail.getId(), SavingGoalAccountType.BANK_SAVING_ACCOUNT)));
		}
	}

}
