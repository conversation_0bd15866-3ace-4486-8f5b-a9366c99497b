package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.UserInterestRecord;
import my.com.mandrill.component.dto.model.UserInterestedRedirectDTO;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface UserInterestRecordRepository
		extends JpaRepository<UserInterestRecord, String>, JpaSpecificationExecutor<UserInterestRecord> {

	List<UserInterestRecord> findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			List<String> institution, Instant startDate, Instant enDate);

	Page<UserInterestRecord> findAllByProviderIdInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			List<String> institution, Instant startDate, Instant enDate, Pageable pageable);

	List<UserInterestRecord> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			Instant startDate, Instant enDate);

	Page<UserInterestRecord> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectFalse(
			Instant startDate, Instant enDate, Pageable pageable);

	Optional<UserInterestRecord> findFirstByUserIdAndProductIdAndIsRedirectFalseOrderByReapplyDateDesc(String userId,
			String productId);

	@Query("""
			select new my.com.mandrill.component.dto.model.UserInterestedRedirectDTO(u.providerId, u.productType, u.productName, count(u))
			from UserInterestRecord u where u.issuerType = :issuerType and u.createdDate >= :startDate and u.createdDate <= :endDate
			and u.isRedirect = true
			group by u.providerId, u.productType, u.productName
			""")
	List<UserInterestedRedirectDTO> countAllByIssuerTypeAndBetweenDateAndIsRedirectTrue(String issuerType,
			Instant startDate, Instant endDate);

	@Query("""
			SELECT new my.com.mandrill.component.dto.model.UserInterestedRedirectDTO(
			 CAST( DATE(FUNCTION('timezone', 'Asia/Kuala_Lumpur', FUNCTION('timezone', 'UTC', uir.createdDate))) AS string),
			 uir.issuerType, uir.providerId, uir.productType, uir.productName, uir.fullName, uir.phoneNumber, uir.email, uir.userRefNo, COUNT(uir))
			FROM UserInterestRecord uir
			WHERE uir.createdDate >= :startDate and uir.createdDate <= :endDate and uir.isRedirect = true
			GROUP BY
				DATE(FUNCTION('timezone', 'Asia/Kuala_Lumpur', FUNCTION('timezone', 'UTC', uir.createdDate))),
				uir.issuerType, uir.providerId, uir.productType, uir.productName, uir.fullName, uir.phoneNumber, uir.email, uir.userRefNo
			""")
	List<UserInterestedRedirectDTO> countAllBetweenDateAndIsRedirectTrue(Instant startDate, Instant endDate);

	@Query("""
			SELECT COUNT(uir)
			FROM UserInterestRecord uir
			WHERE uir.createdDate >= :startDate and uir.createdDate <= :endDate and uir.isRedirect = :isRedirect
			""")
	Long countBetweenDateAndIsRedirect(Instant startDate, Instant endDate, Boolean isRedirect);

	long countByIssuerCodeAndProductTypeAndSourceAndCreatedDateBetween(String issuerCode, String productType,
			UserInterestedSource source, Instant createdDateStart, Instant createdDateEnd);

	Optional<UserInterestRecord> findByIdAndIssuerCodeIn(String id, Set<String> issuerCodes);

	List<UserInterestRecord> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectTrueOrderByCreatedDateAsc(
			Instant startDateTime, Instant endDateTime);

	Page<UserInterestRecord> findAllByCreatedDateGreaterThanEqualAndCreatedDateLessThanAndIsRedirectTrue(
			Instant startDateTime, Instant endDateTime, Pageable pageable);

	@Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END " + "FROM UserInterestRecord e "
			+ "WHERE e.id IN :ids AND (e.rsmStatus = :status OR e.rsmCommissionAttached = true)")
	boolean existsAllByIdInAndRsmStatusAndRsmCommissionAttachedIsTrue(Set<String> ids, RSMStatus status);

	@Modifying
	@Query("UPDATE UserInterestRecord uir SET uir.rsmStatus = ?1 WHERE uir.id IN ?2")
	void bulkUpdateStatusByIdIn(RSMStatus status, Set<String> ids);

}
