package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class KafkaTopicConfig {

	public static final String GROUP = "moneyx-core";

	public static final String SET_RSM_HEADER_STATUS = "set-rsm-header-status";

	public static final String GET_LIVE_ENDING_RSM_HEADERS = "get-live-ending-rsm-headers";

	@Bean
	public NewTopic updateUserReferee() {
		return TopicBuilder.name(KafkaTopic.USER_REFEREE_UPDATE_TOPIC)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

	@Bean
	public NewTopic closingBalanceReportSchedulerTopic() {
		return TopicBuilder.name(KafkaTopic.RSM_CLOSING_BALANCE_REPORT_SCHEDULER_TOPIC)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

	@Bean
	public NewTopic accountDeleteUpdateTopic() {
		return TopicBuilder.name(KafkaTopic.RSM_ACCOUNT_DELETION_UPDATE_TOPIC)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

	@Bean
	public NewTopic updateRsmHeaderStatus() {
		return TopicBuilder.name(KafkaTopicConfig.SET_RSM_HEADER_STATUS)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

	@Bean
	public NewTopic rsmPointExpiryCleanUpTopic() {
		return TopicBuilder.name(KafkaTopic.RSM_POINT_EXPIRY_CLEAN_UP_SCHEDULER_TOPIC)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

	@Bean
	public NewTopic rsmPointExpiryReminderTopic() {
		return TopicBuilder.name(KafkaTopic.RSM_POINT_EXPIRY_REMINDER_SCHEDULER_TOPIC)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

	@Bean
	public NewTopic getLiveEndingRsmHeaderTopic() {
		return TopicBuilder.name(KafkaTopicConfig.GET_LIVE_ENDING_RSM_HEADERS)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

}
