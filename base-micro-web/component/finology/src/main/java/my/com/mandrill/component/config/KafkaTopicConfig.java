package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class KafkaTopicConfig {

	public static final String GROUP = "finology";

	@Bean
	public NewTopic paymentCallbackTopic() {
		return TopicBuilder.name(KafkaTopic.FINOLOGY_PAYMENT_CALLBACK_TOPIC)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

	@Bean
	public NewTopic finologyUpdateVehicleRenewalDateAndMarketValueTopic() {
		return TopicBuilder.name(KafkaTopic.FINOLOGY_UPDATE_VEHICLE_RENEWAL_DATE_AND_MARKET_VALUE_TOPIC)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

	@Bean
	public NewTopic finologySysRenewalDateTopic() {
		return TopicBuilder.name(KafkaTopic.FINOLOGY_SYNC_RENEWAL_DATE_TOPIC)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

}
