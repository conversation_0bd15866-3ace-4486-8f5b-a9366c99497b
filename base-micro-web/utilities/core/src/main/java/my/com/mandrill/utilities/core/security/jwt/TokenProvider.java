package my.com.mandrill.utilities.core.security.jwt;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.core.config.InfobipProperties;
import my.com.mandrill.utilities.core.config.SecurityBaseProperties;
import my.com.mandrill.utilities.core.config.ZendeskProperties;
import my.com.mandrill.utilities.core.dto.model.JwtTokenRequest;
import my.com.mandrill.utilities.core.dto.model.UserActivityDTO;
import my.com.mandrill.utilities.core.dto.model.UserContext;
import my.com.mandrill.utilities.core.token.domain.UserToken;
import my.com.mandrill.utilities.core.token.service.UserTokenService;
import my.com.mandrill.utilities.feign.dto.GlobalSystemConfigurationDTO;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.exception.UnauthorizedException;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.HttpRequestUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.SecretKey;
import java.security.Key;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TokenProvider {

	private static final String AUTHORITIES_KEY = "auth";

	private static final String TOKEN_ID = "tokenId";

	private static final String IDENTITY_REFERENCE = "ref";

	private static final String IDENTIFY_USER_ID = "user_id";

	private static final String INVALID_TOKEN_ERROR_MESSAGE = "Invalid JWT token.";

	private static final String EXP = "exp";

	private final Key key;

	private final JwtParser jwtParser;

	private final long tokenValidityInMilliseconds;

	private final long tokenValidityInMillisecondsForRememberMe;

	private final SecurityBaseProperties securityBaseProperties;

	private final ZendeskProperties zendeskProperties;

	private final InfobipProperties infobipProperties;

	private final UserTokenService userTokenService;

	private final ProxyFeignClient proxyFeignClient;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	private final HttpRequestUtil httpRequestUtil;

	private final FeatureFlagOutbound featureFlagOutbound;

	private final RedisService redisService;

	public TokenProvider(SecurityBaseProperties securityBaseProperties, ZendeskProperties zendeskProperties,
			InfobipProperties infobipProperties, UserTokenService userTokenService, ProxyFeignClient proxyFeignClient,
			KafkaTemplate<String, String> kafkaTemplate, JSONUtil jsonUtil, HttpRequestUtil httpRequestUtil,
			FeatureFlagOutbound featureFlagOutbound, RedisService redisService) {
		this.securityBaseProperties = securityBaseProperties;
		this.zendeskProperties = zendeskProperties;
		this.infobipProperties = infobipProperties;
		this.userTokenService = userTokenService;
		this.proxyFeignClient = proxyFeignClient;
		this.kafkaTemplate = kafkaTemplate;
		this.jsonUtil = jsonUtil;
		this.httpRequestUtil = httpRequestUtil;
		byte[] keyBytes;
		keyBytes = Decoders.BASE64.decode(this.securityBaseProperties.getJwt().getBase64Secret());
		key = Keys.hmacShaKeyFor(keyBytes);
		jwtParser = Jwts.parserBuilder().setSigningKey(key).build();
		this.tokenValidityInMilliseconds = 1000 * this.securityBaseProperties.getJwt().getTokenValidityInSeconds();
		this.tokenValidityInMillisecondsForRememberMe = 1000
				* this.securityBaseProperties.getJwt().getTokenValidityInSecondsForRememberMe();
		this.featureFlagOutbound = featureFlagOutbound;
		this.redisService = redisService;
	}

	public String createToken(Authentication authentication, String refNo, String userId, boolean rememberMe) {
		String authorities = authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority).distinct()
				.collect(Collectors.joining(","));

		return createDefaultToken(authorities, refNo, authentication.getName(), userId, rememberMe);
	}

	public String createDefaultToken(String authorities, String refNo, String subject, String userId,
			boolean rememberMe) {
		long now = (new Date()).getTime();
		Date validity;
		if (rememberMe) {
			validity = new Date(now + this.tokenValidityInMillisecondsForRememberMe);
		}
		else {
			validity = new Date(now + this.tokenValidityInMilliseconds);
		}

		JwtBuilder jwtBuilder = Jwts.builder().setSubject(subject).claim(IDENTITY_REFERENCE, refNo)
				.claim(IDENTIFY_USER_ID, userId).claim(AUTHORITIES_KEY, authorities)
				.signWith(key, SignatureAlgorithm.HS512);

		if (securityBaseProperties.getJwt().isTokenExpiryEnabled()) {
			jwtBuilder.setExpiration(validity);
		}

		return jwtBuilder.compact();
	}

	public Authentication getAuthentication(String token, HttpServletRequest httpServletRequest) {
		Claims claims = jwtParser.parseClaimsJws(token).getBody();
		Object authKey = claims.get(AUTHORITIES_KEY);
		if (ObjectUtils.isEmpty(authKey)) {
			throw new UnauthorizedException();
		}
		Collection<? extends GrantedAuthority> authorities = Arrays
				.stream(claims.get(AUTHORITIES_KEY).toString().split(",")).filter(auth -> !auth.trim().isEmpty())
				.map(SimpleGrantedAuthority::new).toList();

		User principal = new User(claims.getSubject(), "", authorities);

		UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(principal, token,
				authorities);
		auth.setDetails(claims.get(IDENTIFY_USER_ID));
		return auth;
	}

	public Authentication getAuthenticationV2(String token, HttpServletRequest httpServletRequest) {
		Claims claims = jwtParser.parseClaimsJws(token).getBody();
		String tokenId = claims.get(TOKEN_ID, String.class);
		if (ObjectUtils.isEmpty(tokenId)) {
			throw new UnauthorizedException();
		}
		Optional<UserToken> savedToken = userTokenService.findByTokenId(tokenId);
		if (savedToken.isPresent()) {
			Collection<? extends GrantedAuthority> authorities = savedToken.get().getPermissions().stream()
					.filter(auth -> !auth.trim().isEmpty()).map(SimpleGrantedAuthority::new).toList();

			UserContext principal = new UserContext(claims.getSubject(), token, authorities, savedToken.get());

			UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(principal, token,
					authorities);
			auth.setDetails(claims.get(IDENTIFY_USER_ID));
			if (featureFlagOutbound.isFeatureEnabled(GlobalSystemConfigurationEnum.TOKEN_TRANSACTION_ENABLED)) {
				kafkaTemplate.send(KafkaTopic.USER_ACTIVITY_TRANSACTION, tokenId,
						jsonUtil.convertToString(createUserActivityPayload(savedToken.get(), httpServletRequest)));
			}
			return auth;
		}

		throw new UnauthorizedException();
	}

	private UserActivityDTO createUserActivityPayload(UserToken userToken, HttpServletRequest httpServletRequest) {
		return UserActivityDTO.builder().tokenId(userToken.getId())
				.httpDetailDTO(httpRequestUtil.parseResource(httpServletRequest)).build();
	}

	public String createDefaultTokenV2(JwtTokenRequest request) {
		long accessTokenInSeconds = Integer.parseInt(
				getGlobalSystemConfiguration(GlobalSystemConfigurationEnum.ACCESS_TOKEN_VALIDITY_IN_SECOND).getValue());

		Instant validityInstant = Instant.now().plusSeconds(accessTokenInSeconds);
		Date validity = Date.from(validityInstant);

		userTokenService.create(request, validity.getTime());

		JwtBuilder jwtBuilder = Jwts.builder().setSubject(request.getUserRefNo())
				.claim(IDENTITY_REFERENCE, request.getUserRefNo()).claim(TOKEN_ID, request.getAccessToken())
				.claim(EXP, validity).claim(IDENTIFY_USER_ID, request.getUserId())
				.signWith(key, SignatureAlgorithm.HS512);

		if (securityBaseProperties.getJwt().isTokenExpiryEnabled()) {
			jwtBuilder.setExpiration(validity);
		}

		return jwtBuilder.compact();
	}

	private GlobalSystemConfigurationDTO getGlobalSystemConfiguration(
			GlobalSystemConfigurationEnum systemConfiguration) {
		return redisService
				.getFromHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION, systemConfiguration.getCode(),
						GlobalSystemConfigurationDTO.class)
				.orElseGet(() -> proxyFeignClient.getCommonFeignClient()
						.getIntegrationGlobalSystemConfigurationByCode(systemConfiguration.getCode()));
	}

	public boolean validateToken(String authToken) {
		try {
			Jws<Claims> claimsJws = jwtParser.parseClaimsJws(authToken);
			if (securityBaseProperties.getJwt().isTokenExpiryEnabled() && claimsJws.getBody().getExpiration() == null) {
				throw new ExpiredJwtException(null, claimsJws.getBody(), null);
			}
			return true;
		}
		catch (ExpiredJwtException e) {
			log.trace(INVALID_TOKEN_ERROR_MESSAGE, e);
		}
		catch (UnsupportedJwtException e) {
			log.trace(INVALID_TOKEN_ERROR_MESSAGE, e);
		}
		catch (MalformedJwtException e) {
			log.trace(INVALID_TOKEN_ERROR_MESSAGE, e);
		}
		catch (SecurityException e) {
			log.trace(INVALID_TOKEN_ERROR_MESSAGE, e);
		}
		catch (IllegalArgumentException e) {
			log.error("Token validation error {}", e.getMessage());
		}

		return false;
	}

	// this method just for testing only
	public String createAccessToken(String userRefNo, String userId, String accessToken) {
		JwtBuilder jwtBuilder = Jwts.builder().setSubject(userRefNo).claim(IDENTITY_REFERENCE, userRefNo)
				.claim(TOKEN_ID, accessToken).claim(IDENTIFY_USER_ID, userId).signWith(key, SignatureAlgorithm.HS512);
		return jwtBuilder.compact();
	}

	public String createLiveChatToken(String id, String email, String name) {
		SecretKey secretKey = Keys.hmacShaKeyFor(this.zendeskProperties.getJwt().getSecret().getBytes());

		long now = (new Date()).getTime();
		Date validity = new Date(
				now + TimeUnit.HOURS.toMillis(this.zendeskProperties.getJwt().getTokenValidityInHours()));

		JwtBuilder jwtBuilder = Jwts.builder().setHeaderParam("typ", "JWT")
				.setHeaderParam("kid", this.zendeskProperties.getJwt().getKid()).claim("scope", "user")
				.claim("email", email).claim("external_id", id).claim("name", name)
				.signWith(secretKey, SignatureAlgorithm.HS256).setExpiration(validity);

		return jwtBuilder.compact();
	}

	public String generateInfobipInboxToken(String userId) {
		try {
			MACSigner personalizationTokenSigner = new MACSigner(
					HexFormat.of().parseHex(infobipProperties.getJwt().getSecret()));
			JWTClaimsSet claimsSet = new JWTClaimsSet.Builder().claim("typ", "Bearer")
					.jwtID(UUID.randomUUID().toString()).subject(userId)
					.issuer(infobipProperties.getJwt().getApplicationCode()).issueTime(new Date())
					.expirationTime(Date.from(
							Instant.now().plus(infobipProperties.getJwt().getTokenValidityInHours(), ChronoUnit.HOURS)))
					.claim("infobip-api-key", infobipProperties.getJwt().getApplicationCode()).build();
			JWSHeader jwsHeader = new JWSHeader.Builder(JWSAlgorithm.HS256).type(JOSEObjectType.JWT)
					.keyID(infobipProperties.getJwt().getKid()).build();
			SignedJWT personalizedToken = new SignedJWT(jwsHeader, claimsSet);
			personalizedToken.sign(personalizationTokenSigner);
			return personalizedToken.serialize();
		}
		catch (JOSEException e) {
			return null;
		}
	}

	public String getTokenFromRequest(HttpServletRequest request) {
		String jwt = resolveToken(request);
		if (ObjectUtils.isNotEmpty(jwt)) {
			Claims claims = jwtParser.parseClaimsJws(jwt).getBody();
			return claims.get(TOKEN_ID, String.class);
		}
		return null;
	}

	public String resolveToken(HttpServletRequest request) {
		String bearerToken = request.getHeader(HttpHeaders.AUTHORIZATION);
		if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
			return bearerToken.substring(7);
		}
		return null;
	}

}
