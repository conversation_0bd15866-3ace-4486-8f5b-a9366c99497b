package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Authority;
import my.com.mandrill.component.domain.Institution;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface AuthorityService {

	Authority create(Authority authority);

	Authority createDefault(Institution institution);

	void checkIfExists(String name, String institutionId);

	Page<Authority> findAllByNameAndInstitutionId(Pageable page, String name, String institutionId);

	List<Authority> findAllByInstitutionIdsAndActiveTrue(List<String> institutionIds);

	Authority findByIdAndInstitutionId(String authorityId, String institutionId);

	Authority updateAuthority(Authority authority, String institutionId);

	void deleteByIdAndInstitutionId(String authorityId, String institutionId);

	Authority findByName(String name);

	Optional<Authority> findById(String id);

}
