package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.DashboardActivity;
import my.com.mandrill.component.dto.model.DashboardBarChartProjection;
import my.com.mandrill.component.dto.model.DashboardProjection;
import my.com.mandrill.component.repository.jpa.DashboardActivityRepository;
import my.com.mandrill.component.service.DashboardActivityService;
import my.com.mandrill.utilities.general.constant.DashboardCategory;
import my.com.mandrill.utilities.general.constant.DashboardType;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DashboardActivityServiceImpl implements DashboardActivityService {

	private final DashboardActivityRepository dashboardActivityRepository;

	@Override
	@Transactional
	public DashboardActivity save(DashboardActivity dashboardActivity) {
		return dashboardActivityRepository.save(dashboardActivity);
	}

	@Override
	public List<DashboardBarChartProjection> findBarChartByCategoryAndDateBetween(DashboardCategory dashboardCategory,
			Instant dateStart, Instant dateEnd) {
		return dashboardActivityRepository.findBarChartByCategoryAndDateBetween(dashboardCategory, dateStart, dateEnd);
	}

	@Override
	public Optional<DashboardActivity> findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(
			DashboardCategory dashboardCategory, DashboardType dashboardType, Instant dateStart, Instant dateEnd) {
		return dashboardActivityRepository.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(
				dashboardCategory, dashboardType, dateStart, dateEnd);
	}

	@Override
	public long countByDateBetweenAndLoginType(Instant dateStart, Instant dateEnd, LoginTypeEnum loginTypeEnum) {
		return dashboardActivityRepository.countByDateBetweenAndLoginType(dateStart, dateEnd, loginTypeEnum);
	}

}
