package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.utilities.core.audit.AuditSectionTimeSeries;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "rsm_detail", uniqueConstraints = { @UniqueConstraint(columnNames = { "header_id", "focal_type" }) })
public class RsmDetail extends AuditSectionTimeSeries {

	@NotNull
	@JsonIgnore
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "header_id", nullable = false)
	private RsmHeader header;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "focal_type")
	private RSMFocalType focalType;

	@Column(name = "c2c_revenue_distribution")
	private BigDecimal c2cRevenueDistribution;

	@Column(name = "b2c_revenue_distribution")
	private BigDecimal b2cRevenueDistribution;

	@Column(name = "b2b2c_revenue_distribution")
	private BigDecimal b2b2cRevenueDistribution;

	@Column(name = "b2b_revenue_distribution")
	private BigDecimal b2bRevenueDistribution;

	@Column(name = "non_referral_revenue_distribution")
	private BigDecimal nonReferralRevenueDistribution;

	@Column(name = "external_b2b_c2c_non_referral_revenue_distribution")
	private String externalB2bC2cNonReferralRevenueDistribution;

	@Column(name = "external_b2b2c_revenue_distribution")
	private String externalB2b2cRevenueDistribution;

}
