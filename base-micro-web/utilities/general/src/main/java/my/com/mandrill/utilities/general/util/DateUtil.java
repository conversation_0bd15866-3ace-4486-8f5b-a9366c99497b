package my.com.mandrill.utilities.general.util;

import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.dto.DateDetailDTO;
import org.springframework.data.util.Pair;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateUtil {

	public static final String DEFAULT_DATE_FORMAT = "dd/MM/yyyy - HH:mm:ss z";

	public static final String DATE_FORMAT = "yyyy-MM-dd";

	public static final String DATE_FORMAT_VISIT = "dd-MM-yyyy HH:mm";

	public static final DateTimeFormatter defaultDateFormatter = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);

	public static final DateTimeFormatter BIG_QUERY_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

	public static final DateTimeFormatter DD_MMM_YY = DateTimeFormatter.ofPattern("dd MMM yy");

	public static final DateTimeFormatter DD_MMM_YYYY_DASH = DateTimeFormatter.ofPattern("dd-MMM-yyyy");

	public static final DateTimeFormatter HH_MM_SS_Z_SEMI_COLON = DateTimeFormatter.ofPattern("HH:mm:ss z");

	public static final DateTimeFormatter EEEE_DD_MMM_YY = DateTimeFormatter.ofPattern("EEEE dd MMM yy");

	public static final DateTimeFormatter EEEE = DateTimeFormatter.ofPattern("EEEE");

	public static final DateTimeFormatter MMMM_YY = DateTimeFormatter.ofPattern("MMMM yy");

	public static final DateTimeFormatter YYYY_MM_DD_DASH = DateTimeFormatter.ofPattern(DATE_FORMAT);

	public static final DateTimeFormatter MMMM_YYYY = DateTimeFormatter.ofPattern("MMMM yyyy");

	public static LocalDate instantToLocalDate(Instant instant) {
		return LocalDate.ofInstant(instant, TimeConstant.DEFAULT_ZONE_ID);
	}

	/**
	 * This is used in Jasper Report Please do not delete
	 * @param instant - time
	 * @return formatted string
	 */
	@SuppressWarnings("unused")
	public static String instantToDateDash(Instant instant) {
		return instant.atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).format(DD_MMM_YYYY_DASH);
	}

	/**
	 * This is used in Jasper Report Please do not delete
	 * @param instant - time
	 * @return formatted string
	 */
	@SuppressWarnings("unused")
	public static String instantToDateISO(Instant instant) {
		return instant.atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).format(YYYY_MM_DD_DASH);
	}

	/**
	 * This is used in Jasper Report Please do not delete
	 * @param instant - time
	 * @return formatted string
	 */
	@SuppressWarnings("unused")
	public static String instantToTimeSemiColon(Instant instant) {
		return instant.atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).format(HH_MM_SS_Z_SEMI_COLON);
	}

	public static boolean isAfterToday(LocalDate localDate) {
		LocalDate today = LocalDate.now(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE));
		return localDate.isAfter(today);
	}

	public static boolean isBeforeTomorrow(LocalDate localDate) {
		return !isAfterToday(localDate);
	}

	public static boolean isSameDay(Instant first, Instant second) {
		return LocalDate.ofInstant(first, ZoneOffset.UTC).equals(LocalDate.ofInstant(second, ZoneOffset.UTC));
	}

	public static LocalDate getReminderEndDate(@NotNull Short duration, @NotNull Year repaymentStartYear,
			@NotNull LocalDate startDate, @NotNull Month repaymentStartMonth) {

		int remainingYear = duration
				- (Year.now(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).getValue() - repaymentStartYear.getValue());

		return startDate.plusYears(remainingYear).withMonth(repaymentStartMonth.getValue());
	}

	public static boolean equalOrAfter(Instant first, Instant second) {
		return first.compareTo(second) >= 0;
	}

	public static Pair<LocalDate, LocalDate> findRangeFromStartDateMonthly(LocalDate bigBang) {
		LocalDate currentDate = LocalDate.now();

		long monthCounter = 1;
		while (bigBang.plusMonths(monthCounter).isBefore(currentDate)) {
			monthCounter++;
		}
		return Pair.of(bigBang.plusMonths(monthCounter - 1L), bigBang.plusMonths(monthCounter));
	}

	public static Pair<LocalDate, LocalDate> findRangeFromStartDateWeekly(LocalDate bigBang) {
		LocalDate currentDate = LocalDate.now();

		long weekCounter = 1;
		while (bigBang.plusWeeks(weekCounter).isBefore(currentDate)) {
			weekCounter++;
		}
		return Pair.of(bigBang.plusWeeks(weekCounter - 1L), bigBang.plusWeeks(weekCounter));
	}

	public static DateDetailDTO convertToDateDetail(LocalDate localDate) {
		return DateDetailDTO.builder().standard(localDate.toString()).day(localDate.format(DateUtil.EEEE))
				.formatted(localDate.format(DateUtil.DD_MMM_YY)).build();
	}

	public static DateDetailDTO convertToDateDetail(YearMonth yearMonth) {
		return DateDetailDTO.builder().standard(yearMonth.toString()).formatted(yearMonth.format(DateUtil.MMMM_YY))
				.build();
	}

	public static Year getOrDefault(Year actual, Year defaultYear) {
		return Objects.nonNull(actual) ? actual : defaultYear;
	}

	public static String formatInstantToMonthYear(Instant instant) {
		return instant.atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).format(MMMM_YYYY);
	}

}
