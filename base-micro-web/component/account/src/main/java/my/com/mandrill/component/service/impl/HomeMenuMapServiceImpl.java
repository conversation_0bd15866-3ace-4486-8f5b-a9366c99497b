package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.HomeSectionEnum;
import my.com.mandrill.component.domain.HomeMenuGroup;
import my.com.mandrill.component.domain.HomeMenuMap;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.HomeMenuMapRepository;
import my.com.mandrill.component.service.HomeMenuMapService;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class HomeMenuMapServiceImpl implements HomeMenuMapService {

	private final HomeMenuMapRepository homeMenuMapRepository;

	@Override
	@Transactional
	public HomeMenuMap create(HomeMenuMap homeMenuMap) {
		return homeMenuMapRepository.save(homeMenuMap);
	}

	@Override
	@Transactional
	public void saveAll(List<HomeMenuMap> homeMenuMap) {
		homeMenuMapRepository.saveAllAndFlush(homeMenuMap);
	}

	@Override
	public List<HomeMenuMap> findBySection(HomeSectionEnum homeSectionEnum) {
		return homeMenuMapRepository.findBySection(homeSectionEnum);
	}

	@Override
	public Page<HomeMenuMap> findAll(HomeSectionEnum sectionEnum, String menuCode, String groupCode,
			Pageable pageable) {
		return homeMenuMapRepository
				.findBySectionAndHomeMenuCodeContainsIgnoreCaseAndHomeMenuGroupCodeContainsIgnoreCase(sectionEnum,
						StringUtils.defaultIfBlank(menuCode, StringUtils.EMPTY),
						StringUtils.defaultIfBlank(groupCode, StringUtils.EMPTY), pageable);
	}

	@Override
	public HomeMenuMap findById(String id) {
		return homeMenuMapRepository.findById(id).orElseThrow(ExceptionPredicate.homeMenuMapNotFound(id));
	}

	@Override
	public Integer findLatestGroupSequence(HomeSectionEnum sectionEnum) {
		HomeMenuMap result = homeMenuMapRepository.findTopBySectionOrderByGroupSequenceDesc(sectionEnum);
		return result == null ? null : result.getGroupSequence();
	}

	@Override
	public Integer findGroupSequenceByHomeMenuGroup(HomeSectionEnum sectionEnum, HomeMenuGroup group) {
		HomeMenuMap result = homeMenuMapRepository.findTopBySectionAndHomeMenuGroupOrderByGroupSequenceDesc(sectionEnum,
				group);
		return result == null ? null : result.getGroupSequence();
	}

	@Override
	public Integer findLatestMenuSequence(HomeSectionEnum sectionEnum, HomeMenuGroup group) {
		HomeMenuMap result = homeMenuMapRepository.findTopBySectionAndHomeMenuGroupOrderByMenuSequenceDesc(sectionEnum,
				group);
		return result == null ? null : result.getMenuSequence();
	}

	@Override
	@Transactional
	public void delete(HomeMenuMap homeMenuMap) {
		homeMenuMapRepository.deleteById(homeMenuMap.getId());
	}

	@Override
	public List<HomeMenuMap> findAllBySectionAndHomeMenuGroupAndMenuSequenceGreaterThan(HomeSectionEnum homeSectionEnum,
			HomeMenuGroup group, int seq) {
		return homeMenuMapRepository.findAllBySectionAndHomeMenuGroupAndMenuSequenceGreaterThan(homeSectionEnum, group,
				seq);
	}

	@Override
	public List<HomeMenuMap> findAllBySectionAndGroupSequenceGreaterThan(HomeSectionEnum homeSectionEnum, int seq) {
		return homeMenuMapRepository.findAllBySectionAndGroupSequenceGreaterThan(homeSectionEnum, seq);
	}

	@Override
	public HomeMenuMap findHomeMenuBeforeMenuSequence(HomeSectionEnum sectionEnum, HomeMenuGroup group, int seq) {
		return homeMenuMapRepository.findTopBySectionAndHomeMenuGroupAndMenuSequenceLessThanOrderByMenuSequenceDesc(
				sectionEnum, group, seq);
	}

	@Override
	public HomeMenuMap findHomeMenuAfterMenuSequence(HomeSectionEnum sectionEnum, HomeMenuGroup group, int seq) {
		return homeMenuMapRepository.findTopBySectionAndHomeMenuGroupAndMenuSequenceGreaterThanOrderByMenuSequenceAsc(
				sectionEnum, group, seq);
	}

	@Override
	public HomeMenuMap findHomeMenuBeforeGroupSequence(HomeSectionEnum sectionEnum, int seq) {
		return homeMenuMapRepository.findTopBySectionAndGroupSequenceLessThanOrderByGroupSequenceDesc(sectionEnum, seq);
	}

	@Override
	public HomeMenuMap findHomeMenuAfterGroupSequence(HomeSectionEnum sectionEnum, int seq) {
		return homeMenuMapRepository.findTopBySectionAndGroupSequenceGreaterThanOrderByGroupSequenceAsc(sectionEnum,
				seq);
	}

	@Override
	public List<HomeMenuMap> findAllBySectionAndHomeMenuGroupAndGroupSequence(HomeSectionEnum homeSectionEnum,
			HomeMenuGroup group, int seq) {
		return homeMenuMapRepository.findAllBySectionAndHomeMenuGroupAndGroupSequence(homeSectionEnum, group, seq);
	}

	@Override
	public boolean existsByHomeMenuId(String id) {
		return homeMenuMapRepository.existsByHomeMenuId(id);
	}

	@Override
	public boolean existsByHomeMenuGroupId(String id) {
		return homeMenuMapRepository.existsByHomeMenuGroupId(id);
	}

}
