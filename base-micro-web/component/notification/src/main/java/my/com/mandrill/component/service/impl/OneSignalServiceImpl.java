package my.com.mandrill.component.service.impl;

import com.google.gson.JsonSyntaxException;
import com.onesignal.client.ApiException;
import com.onesignal.client.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.InfoProperties;
import my.com.mandrill.component.config.OneSignalProperties;
import my.com.mandrill.component.constant.ButtonConstant;
import my.com.mandrill.component.constant.NotificationSegment;
import my.com.mandrill.component.domain.UserPlayer;
import my.com.mandrill.component.dto.model.onesignal.NotificationWithMetaInternal;
import my.com.mandrill.component.service.OneSignalService;
import my.com.mandrill.utilities.general.constant.Language;
import my.com.mandrill.utilities.general.util.GZipFileUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class OneSignalServiceImpl implements OneSignalService {

	private final OneSignalProperties oneSignalProperties;

	private final InfoProperties infoProperties;

	private final OneSignalDefaultApi oneSignalApiClient;

	@Override
	public String getAppId() {
		return this.oneSignalProperties.getAppId();
	}

	@Override
	public NotificationSlice getNotifications(Integer limit, Integer offset, Integer kind) throws ApiException {
		return oneSignalApiClient.getNotifications(this.getAppId(), limit, offset, kind);
	}

	@Override
	public NotificationWithMeta getNotification(String notificationId) throws ApiException {
		return oneSignalApiClient.getNotification(getAppId(), notificationId);
	}

	@Override
	public NotificationWithMetaInternal getNotificationInternal(String notificationId) throws ApiException {
		return oneSignalApiClient.getNotificationInternal(getAppId(), notificationId);
	}

	@Override
	public CreateNotificationSuccessResponse createNotification(String name, String englishContent)
			throws ApiException {
		Notification notification = new Notification();
		notification.setAppId(this.getAppId());
		notification.setIncludedSegments(List.of(NotificationSegment.SUBSCRIBED_USERS.getCode()));
		notification.setName(name);
		notification.setLargeIcon(null);
		StringMap contents = new StringMap();
		contents.en(englishContent);
		notification.setContents(contents);
		return oneSignalApiClient.createNotification(notification);
	}

	@Override
	public CreateNotificationSuccessResponse createNotification(String name, String englishContent, String playerId)
			throws ApiException {
		Notification notification = new Notification();
		notification.setAppId(this.getAppId());
		notification.setIncludePlayerIds(List.of(playerId));
		notification.setName(name);
		notification.setLargeIcon(null);
		StringMap contents = new StringMap();
		contents.en(englishContent);
		notification.setContents(contents);
		return oneSignalApiClient.createNotification(notification);
	}

	@Override
	public CreateNotificationSuccessResponse createNotification(Notification notification) throws ApiException {
		try {
			notification.setAppId(this.getAppId());
			notification.setAndroidChannelId(oneSignalProperties.getAndroidChannelId());
			notification.setHuaweiChannelId(oneSignalProperties.getAndroidChannelId());
			return oneSignalApiClient.createNotification(notification);
		}
		catch (JsonSyntaxException e) {
			log.warn("Failed to create Notification: {}", e.getMessage());
			return null;
		}
	}

	@Override
	public PlayerSlice getPlayers(Integer limit, Integer offset) throws ApiException {
		return oneSignalApiClient.getPlayers(this.getAppId(), limit, offset);
	}

	@Override
	public UserPlayer createPlayer(UserPlayer user) throws ApiException {
		Player player = new Player(); // Player
		// init player
		player.setAppId(this.getAppId());
		player.setDeviceType(user.getDeviceType().getOrdinal());
		player.setIdentifier(user.getPlayerIdentifier()); // android identifier
		player.setExternalUserId(user.getUserId());
		player.setLanguage(user.getLangKey() != null ? user.getLangKey().getCode() : Language.ENGLISH.getCode());
		player.setGameVersion(infoProperties.getProject().getVersion()); // app version
		player.setDeviceModel(user.getDeviceModel());
		player.setDeviceOs(user.getDeviceOS());

		CreatePlayerSuccessResponse response = oneSignalApiClient.createPlayer(player);
		if (response != null && Boolean.TRUE.equals(response.getSuccess())) {
			log.info("Add One Signal User [{}]", response);
			user.setPlayerId(response.getId());
			user.setAppId(this.getAppId());
			return user;
		}
		else {
			// Failed will throw an error
			log.error("Failed to store one signal");
		}
		return null;
	}

	@Override
	public DeletePlayerSuccessResponse deletePlayer(String playerId) throws ApiException {
		return oneSignalApiClient.deletePlayer(this.getAppId(), playerId);
	}

	@Override
	public CancelNotificationSuccessResponse cancelNotification(String notificationId) throws ApiException {
		log.info("Cancel notification [{}]", notificationId);
		return oneSignalApiClient.cancelNotification(this.getAppId(), notificationId);
	}

	@Override
	public List<Button> createButtons(List<ButtonConstant> buttonConstants) {
		return buttonConstants.stream().map(buttonConstant -> {
			Button button = new Button();
			button.setId(buttonConstant.getId());
			button.setText(buttonConstant.getText());
			button.setIcon(buttonConstant.getIcon());
			return button;
		}).toList();
	}

	@Override
	public List<String> getUserIdBySegment(String segment) throws ApiException {
		ExportPlayersRequestBody payload = new ExportPlayersRequestBody();
		payload.setExtraFields(List.of("external_id"));
		payload.setSegmentName(segment);

		ExportPlayersSuccessResponse exportedPlayer = oneSignalApiClient.exportPlayers(this.getAppId(), payload);
		List<List<String>> results = GZipFileUtil.getCSVFromURL(exportedPlayer.getCsvFileUrl(), 3, 0);
		return results.stream().skip(1).map(v -> {
			if (v.size() > 16) {
				return v.get(16);
			}
			return null;
		}).filter(StringUtils::isNotBlank).toList();
	}

	@Cacheable(cacheNames = "COUNT_USER_SEGMENTATION_IN_ONE_SIGNAL_DATASET", key = "#segments",
			unless = "#result.size() != #segments.size()")
	public List<Long> getTotalUserInSegment(List<String> segments) {
		List<Long> countUser = new ArrayList<>();
		for (String segment : segments) {
			try {
				long total = Integer.toUnsignedLong(this.getUserIdBySegment(segment).size());
				if (total > 0) {
					countUser.add(total);
				}
			}
			catch (Exception e) {
				log.error("error when get user-id-segments: {}", e.getMessage());
			}
		}
		return countUser;
	}

}
