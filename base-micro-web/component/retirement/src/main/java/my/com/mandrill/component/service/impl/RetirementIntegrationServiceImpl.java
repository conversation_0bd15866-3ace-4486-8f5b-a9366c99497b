package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.RetirementAssessmentKey;
import my.com.mandrill.component.domain.RetirementCalculation;
import my.com.mandrill.component.dto.request.EpfAttachmentRequest;
import my.com.mandrill.component.dto.request.RetirementCalculationRequest;
import my.com.mandrill.component.dto.response.RetirementCalculationResponse;
import my.com.mandrill.component.repository.jpa.AssessmentProductExclusionRulesRepository;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.AttachmentDTO;
import my.com.mandrill.utilities.feign.dto.AttachmentGroupDTO;
import my.com.mandrill.utilities.feign.dto.AttachmentGroupResponse;
import my.com.mandrill.utilities.general.constant.AttachmentTypeEnum;
import my.com.mandrill.utilities.general.constant.AttachmentTypeFileStorageEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
public class RetirementIntegrationServiceImpl implements RetirementIntegrationService {

	public static final BigDecimal CONSTANT_PERCENTAGE = new BigDecimal(100);

	public static final BigDecimal TOTAL_MONTHS_IN_A_YEAR = new BigDecimal(12);

	public static final BigDecimal CONSTANT_INFLATION_RATE = new BigDecimal("0.03");

	public static final int INFINITE_YEARS = 999;

	private final AssessmentProductExclusionRulesRepository assessmentProductExclusionRulesRepository;

	private final RetirementCalculationService retirementCalculationService;

	private final RetirementCoverageCopyWritingService retirementCoverageCopyWritingService;

	private final CommonFeignClient commonFeignClient;

	private final UserRetirementAssessmentAnswerService userRetirementAssessmentAnswerService;

	private final AssessmentProductExclusionRulesService assessmentProductExclusionRulesService;

	@Override
	public RetirementCalculationResponse processRetirementCalculation(RetirementCalculationRequest request,
			String userId) {

		// do calculations
		BigDecimal roiRate = request.getAnnualRoiPercentage().divide(CONSTANT_PERCENTAGE, MathContext.DECIMAL128);
		BigDecimal annualWithdrawal = request.getMonthlyWithdrawal().multiply(TOTAL_MONTHS_IN_A_YEAR);
		BigDecimal annualSavings = request.getMonthlySavings().multiply(TOTAL_MONTHS_IN_A_YEAR);

		BigDecimal projectedRetirementSavings = calculateProjectedRetirementSavings(request.getCurrentTotalSavings(),
				annualSavings, roiRate, request.getRetirementAge() - request.getCurrentAge());

		BigDecimal requiredCorpus = calculateRequiredCorpus(annualWithdrawal, roiRate,
				request.getLifeExpectancy() - request.getRetirementAge());

		BigDecimal retirementCoveragePercentage = calculateRetirementCoveragePercentage(projectedRetirementSavings,
				requiredCorpus);

		BigDecimal projectedBalanceAmount = calculateProjectedBalanceAmount(projectedRetirementSavings, requiredCorpus);

		Integer projectedSavingsLastFor = calculateProjectedSavingsLastFor(projectedRetirementSavings, annualWithdrawal,
				roiRate, request.getRetirementAge());

		// map retirement calculation
		RetirementCalculation retirementCalculation = MapStructConverter.MAPPER.toRetirementCalculation(request);
		retirementCalculation.setUserId(userId);
		retirementCalculation.setProjectedRetirementSavings(projectedRetirementSavings);
		retirementCalculation.setRetirementCoveragePercentage(retirementCoveragePercentage);
		retirementCalculation.setProjectedBalanceAmount(projectedBalanceAmount);
		retirementCalculation.setProjectedSavingsLastFor(projectedSavingsLastFor);
		retirementCalculation.setRequiredCorpus(requiredCorpus);

		AttachmentGroupResponse attachmentGroupResponse = uploadAttachment(request.getEpfAttachmentRequest());
		retirementCalculation.setAttachmentGroupId(
				attachmentGroupResponse != null ? attachmentGroupResponse.getAttachmentGroupId() : null);

		RetirementCalculationResponse response = MapStructConverter.MAPPER
				.toRetirementCalculationResponse(retirementCalculationService.save(retirementCalculation));
		if (response != null) {
			response.setRetirementCoverageCopyWriting(retirementCoverageCopyWritingService
					.findCopyWritingByCoveragePercentage(response.getRetirementCoveragePercentage()));
			response.setProjectedMonthlyWithdrawal(
					calculateProjectedMonthlyWithdrawal(response.getProjectedRetirementSavings(),
							response.getLifeExpectancy() - response.getRetirementAge()));
		}

		return response;

	}

	@Override
	public RetirementCalculationResponse getLatestRetirementCalculationResult(String userId) {
		RetirementCalculationResponse response = MapStructConverter.MAPPER.toRetirementCalculationResponse(
				retirementCalculationService.findLatestRetirementCalculationByUserId(userId));
		if (response != null) {
			response.setRetirementCoverageCopyWriting(retirementCoverageCopyWritingService
					.findCopyWritingByCoveragePercentage(response.getRetirementCoveragePercentage()));
			response.setProjectedMonthlyWithdrawal(
					calculateProjectedMonthlyWithdrawal(response.getProjectedRetirementSavings(),
							response.getLifeExpectancy() - response.getRetirementAge()));
		}
		return response;
	}

	@Override
	public Set<String> getProductExclusionTags(String userId) {

		Set<String> exclusionTags = new HashSet<>();

		// find user's most recent answer for ASSET_ARRANGEMENT question
		// and check if it's in the exclusion list
		userRetirementAssessmentAnswerService
				.findFirstByUserIdAndKeyOrderByCreatedDateDesc(userId, RetirementAssessmentKey.ASSET_ARRANGEMENT)
				.ifPresent(latestAnswer -> {
					if (assessmentProductExclusionRulesService.existsByAssessmentKeyAndAnswerAndActiveTrue(
							latestAnswer.getKey(), latestAnswer.getValue())) {
						exclusionTags.add(latestAnswer.getKey().name());
					}
				});

		return exclusionTags;
	}

	private BigDecimal calculateRetirementCoveragePercentage(BigDecimal projectedRetirementSavings,
			BigDecimal requiredCorpus) {

		return projectedRetirementSavings.multiply(CONSTANT_PERCENTAGE).divide(requiredCorpus, 2, RoundingMode.HALF_UP);
	}

	private BigDecimal calculateProjectedBalanceAmount(BigDecimal projectedRetirementSavings,
			BigDecimal requiredCorpus) {
		return projectedRetirementSavings.subtract(requiredCorpus).setScale(2, RoundingMode.HALF_UP);
	}

	private BigDecimal calculateRequiredCorpus(BigDecimal annualWithdrawal, BigDecimal roiRate,
			Integer yearsInRetirement) {

		if (CONSTANT_INFLATION_RATE.compareTo(roiRate) == 0) {
			// use below formula when inflation rate same as roi rate
			log.info("inflation rate same as roi rate");
			return annualWithdrawal.multiply(BigDecimal.valueOf(yearsInRetirement)).setScale(2, RoundingMode.HALF_UP);
		}

		// Calculate (1+g)/(1+r)
		BigDecimal ratio = CONSTANT_INFLATION_RATE.add(BigDecimal.ONE).divide(roiRate.add(BigDecimal.ONE),
				MathContext.DECIMAL128);

		// Calculate ((1+g)/(1+r))^n
		BigDecimal ratioPowerN = ratio.pow(yearsInRetirement);

		// Calculate 1 - ((1+g)/(1+r))^n
		BigDecimal numerator = BigDecimal.ONE.subtract(ratioPowerN);

		// Calculate r - g
		BigDecimal denominator = roiRate.subtract(CONSTANT_INFLATION_RATE);

		// Final calculation: PMT * [1 - ((1+g)/(1+r))^n] / (r-g)
		return annualWithdrawal.multiply(numerator.divide(denominator, MathContext.DECIMAL128)).setScale(2,
				RoundingMode.HALF_UP);
	}

	private BigDecimal calculateProjectedRetirementSavings(BigDecimal currentTotalSavings, BigDecimal annualSavings,
			BigDecimal roiRate, Integer years) {

		// Calculate (1 + r)^t
		BigDecimal basePower = BigDecimal.ONE.add(roiRate).pow(years);

		// P(1 + r)^t
		BigDecimal firstTerm = currentTotalSavings.multiply(basePower);

		// ((1 + r)^t - 1) / r
		BigDecimal secondTerm = basePower.subtract(BigDecimal.ONE).divide(roiRate, MathContext.DECIMAL128);

		// C * [((1 + r)^t - 1) / r]
		BigDecimal thirdTerm = annualSavings.multiply(secondTerm);

		// Final calculation: P(1 + r)^t + C * [((1 + r)^t - 1) / r]
		return firstTerm.add(thirdTerm).setScale(2, RoundingMode.HALF_UP);

	}

	private Integer calculateProjectedSavingsLastFor(BigDecimal projectedSavings, BigDecimal annualWithdrawal,
			BigDecimal roiRate, Integer retirementAge) {

		if (CONSTANT_INFLATION_RATE.compareTo(roiRate) == 0) {
			// use below formula when inflation rate same as roi rate
			log.info("inflation rate same as roi rate");
			return projectedSavings.divide(annualWithdrawal, MathContext.DECIMAL128).intValue() + retirementAge;
		}

		double sustainabilityRatio = BigDecimal.ONE.subtract(projectedSavings
				.multiply(roiRate.subtract(CONSTANT_INFLATION_RATE)).divide(annualWithdrawal, MathContext.DECIMAL128))
				.doubleValue();

		if (sustainabilityRatio <= 0) {
			// this means the savings can last for forever
			// mobile will cater to display 99+ years old
			return INFINITE_YEARS;
		}

		BigDecimal top = BigDecimal.valueOf(Math.log(sustainabilityRatio));

		BigDecimal bottom = BigDecimal.valueOf(Math.log((BigDecimal.ONE.add(CONSTANT_INFLATION_RATE))
				.divide(BigDecimal.ONE.add(roiRate), MathContext.DECIMAL128).doubleValue()));

		return top.divide(bottom, MathContext.DECIMAL128).intValue() + retirementAge;
	}

	private AttachmentGroupResponse uploadAttachment(EpfAttachmentRequest epfAttachmentRequest) {
		if (epfAttachmentRequest == null) {
			return null;
		}
		AttachmentGroupDTO attachmentGroupDTO = new AttachmentGroupDTO();
		attachmentGroupDTO.setClassName(RetirementCalculation.class.getSimpleName());
		attachmentGroupDTO.setType(AttachmentTypeFileStorageEnum.RETIREMENT_EPF_DOC.name());
		AttachmentDTO attachmentDTO = MapStructConverter.MAPPER.toAttachmentDTO(epfAttachmentRequest);
		attachmentDTO.setType(AttachmentTypeEnum.Image.name());
		attachmentGroupDTO.setAttachments(List.of(attachmentDTO));
		return commonFeignClient.uploadAttachment(attachmentGroupDTO);
	}

	private BigDecimal calculateProjectedMonthlyWithdrawal(BigDecimal projectedRetirementSavings,
			Integer yearsInRetirement) {

		if (projectedRetirementSavings == null || projectedRetirementSavings.compareTo(BigDecimal.ZERO) <= 0) {
			return BigDecimal.ZERO;
		}

		return projectedRetirementSavings
				.divide(BigDecimal.valueOf(yearsInRetirement).multiply(TOTAL_MONTHS_IN_A_YEAR), MathContext.DECIMAL128)
				.setScale(2, RoundingMode.HALF_UP);

	}

}
