package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeleteAccountMessage;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.model.TermConditionAndPrivacyPolicyDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.utilities.general.constant.DeliveryType;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.dto.request.EkycCreateRequestDTO;
import my.com.mandrill.utilities.general.dto.request.UserPublicWebRequest;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;

public interface AccountService {

	void checkIfUserExists(User user);

	void checkIfEmailExists(String email, LoginTypeEnum loginType);

	void checkIfAdminUserExists(User user);

	boolean checkIfPhoneNumberExists(String fullPhoneNumber);

	User registerUser(User request);

	User getCurrentUser();

	String getUserRefNo(String accessType, LoginTypeEnum loginType, String username);

	UserKeyRequest completePasswordResetEmail(UpdatePasswordResetRequest request);

	UserKeyRequest requestUpdateMobileOTP(String phoneNumber, DeliveryType deliveryType);

	UserKeyRequest completeUpdateMobile(UpdateMobileRequest request);

	void verifyMobileOTP(String username, String key, String value, RequestKeyType requestKeyType,
			UsernameType usernameType);

	UserKeyRequest requestEmailKey(String email, LoginTypeEnum loginType, RequestKeyType requestKeyType);

	void changePassword(User user, ChangePasswordRequest request);

	void createPin(User user, CreatePinRequest request);

	void completeEmailVerification(EmailVerificationRequest request);

	User updateTermConditionAndPrivacyPolicyVersion(User user, TermConditionAndPrivacyPolicyDTO request);

	TermConditionAndPrivacyPolicyDTO validateTermConditionAndPrivacyPolicy(User user);

	void sendEmailPasswordUpdated(User optionalUser);

	void sendSmsVerification(UserKeyRequest keyRequest, String receiver);

	void sendWhatsAppVerification(UserKeyRequest keyRequest, String receiver);

	void sendEmailOneTimePassword(String otp, String receiver);

	String getSecretKey(String refNo, String apiKey);

	ExtendedNetWorthCalculationResponse calculateExtendedNetWorth(String userId, Boolean isRevamp);

	void deleteProcess(AppUser user);

	CurrentUserIdResponse getCurrentUserIdAndRefNo();

	AccountResponse getCurrentUserIdAndRefNoV2();

	CurrentUserIdResponse getCurrentUserIdAndRefNo(String id);

	KeyResponse requestOTPByPhoneNumber(OTPRequest request);

	KeyResponse requestOTPByEmail(OTPRequest request);

	void validateOtpVerification(UserKeyRequest existOtp, String otpNeedToValidate);

	void validateUsernameType(UserKeyRequest keyRequest, UsernameType usernameType);

	String getUserEmail(String phoneCountry, String phoneNumber, LoginTypeEnum loginType);

	PublicUserDetailRequest validateUsernameType(PublicUserDetailRequest publicUserDetailRequest,
			UsernameType usernameType);

	void saveDeleteAccountMessage(DeleteAccountMessage deleteAccountMessage);

	IncomeAndEpfResponse findLatestIncomeAndEpf(String incomeTypeId);

	UserPublicWebResponse checkAndRegisterNewAccount(UserPublicWebRequest request);

	PublicUserDetailResponse getValidatedUserDetailAndKycStatus(PublicUserDetailRequest publicUserDetailRequest);

	CurrentUserDataForAIResponse getCurrentUserDataForAI(String userId);

	void updateApplicant(EkycCreateRequestDTO ekycCreateRequestDTO);

	KeyResponse checkAndDeliverKeyRequest(OTPRequest request, User user, Runnable preCheck);

	User buildUserFromCombinedPhoneNumber(String username);

}
