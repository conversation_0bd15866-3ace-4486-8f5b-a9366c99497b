package my.com.mandrill.component.service;

import jakarta.persistence.EntityNotFoundException;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.repository.jpa.InstitutionRepository;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.component.service.impl.**********************;
import my.com.mandrill.component.service.impl.UserServiceImpl;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.SystemConfigurationDTO;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.*;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class InstitutionServiceTest {

	@InjectMocks
	@Spy
	********************** institutionService;

	@Mock
	CommonFeignClient commonFeignClient;

	@Mock
	InstitutionRepository institutionRepository;

	@Mock
	UserRepository userRepository;

	@Mock
	UserServiceImpl userService;

	Institution institutionMock;

	List<Institution> institutionListMock;

	Page<Institution> institutionPageMock;

	SystemConfigurationDTO systemConfigurationDTOMock;

	User userMock;

	@BeforeEach
	void Setup() {
		institutionMock = new Institution();
		institutionMock.setId("id");

		institutionListMock = new ArrayList<>();
		institutionListMock.add(institutionMock);

		institutionPageMock = new PageImpl<>(institutionListMock);

		systemConfigurationDTOMock = new SystemConfigurationDTO();
		systemConfigurationDTOMock.setValue("1");

		userMock = new User();
	}

	@Test
	void createInstitution_ParentIsNull_Positive() {
		Mockito.when(institutionRepository.saveAndFlush(any())).thenReturn(institutionMock);
		Mockito.when(institutionRepository.save(any())).thenReturn(institutionMock);

		Institution data = institutionService.createInstitution(institutionMock);
		assertThat(data.getId()).isEqualTo("id");
		verify(institutionRepository, times(1)).saveAndFlush(any());
		verify(institutionRepository, times(1)).save(any());
	}

	@Test
	void getAllInstitutions_Positive() {
		Mockito.when(institutionRepository.findAllByNameAndParentInstitutionId(any(), any(), any()))
				.thenReturn(institutionPageMock);

		Page<Institution> data = institutionService.getAllInstitutions(Pageable.ofSize(5), "name", "id");
		assertThat(data.getContent().size()).isEqualTo(1);
	}

	@Test
	void getAllInstitutionsNoPagination_Success() {
		Mockito.when(institutionRepository.findAll()).thenReturn(institutionListMock);

		List<Institution> data = institutionService.getAllInstitutionsNoPagination();

		assertThat(data.size()).isEqualTo(1);
		verify(institutionRepository, times(1)).findAll();
	}

	@Test
	void getInstitutionByIdAndActiveTrue_ThrowNotFound_Negative() {
		Mockito.when(institutionRepository.findByIdAndActiveTrue(any())).thenReturn(Optional.empty());

		Assertions.assertThrows(EntityNotFoundException.class,
				() -> institutionService.getInstitutionByIdAndActiveTrue(""));
	}

	@Test
	void getInstitutionByIdAndActiveTrue_DoesntThrowNotFound_Positive() {
		Mockito.when(institutionRepository.findByIdAndActiveTrue(any())).thenReturn(Optional.of(institutionMock));

		Assertions.assertDoesNotThrow(() -> institutionService.getInstitutionByIdAndActiveTrue(""));
	}

	@Test
	void updateInstitution_ThrowNotFound_Negative() {
		Mockito.when(institutionRepository.findById(any())).thenReturn(Optional.empty());

		Assertions.assertThrows(EntityNotFoundException.class,
				() -> institutionService.updateInstitution(institutionMock));

	}

	@Test
	void updateInstitution_ThrowParentIsNotActive_Negative() {
		Institution parent = new Institution();
		parent.setId("parentId");
		parent.setActive(false);
		institutionMock.setParentInstitution(parent);
		institutionMock.setActive(true);
		Mockito.when(institutionRepository.findById(any())).thenReturn(Optional.of(institutionMock));

		Assertions.assertThrows(BusinessException.class, () -> institutionService.updateInstitution(institutionMock));

	}

	@Test
	void updateInstitution_DoesntThrowParentIsNotActiveAndDeActiveChildInstitutionsInvoked_Positive() {
		Institution parent = new Institution();
		parent.setId("parentId");
		parent.setActive(true);
		institutionMock.setParentInstitution(parent);
		institutionMock.setActive(true);

		Institution institutionAfterSaveAndFlush = institutionMock;
		institutionAfterSaveAndFlush.setActive(false);

		Mockito.when(institutionRepository.findById(any())).thenReturn(Optional.of(institutionMock));
		Mockito.when(institutionRepository.saveAndFlush(any())).thenReturn(institutionAfterSaveAndFlush);
		Mockito.when(institutionRepository.findAllChildInstitutionsByPathAndActiveTrue(any()))
				.thenReturn(institutionListMock);
		Mockito.when(institutionRepository.save(any())).thenReturn(institutionMock);

		Assertions.assertDoesNotThrow(() -> institutionService.updateInstitution(institutionMock));
		verify(institutionRepository, times(institutionListMock.size())).save(any());

	}

	@Test
	void getInstitutionById_ThrowNotFound_Negative() {
		Mockito.when(institutionRepository.findById(any())).thenReturn(Optional.empty());

		Assertions.assertThrows(EntityNotFoundException.class, () -> institutionService.getInstitutionById(""));
	}

	@Test
	void getInstitutionById_DoesntThrowNotFound_Positive() {
		Mockito.when(institutionRepository.findById(any())).thenReturn(Optional.of(institutionMock));

		Assertions.assertDoesNotThrow(() -> institutionService.getInstitutionById(""));
		Assertions.assertEquals(institutionService.getInstitutionById("id"), institutionMock);
	}

	@Test
	void getInstitutionsByParentId_Positive() {
		Mockito.when(institutionRepository.findByParentInstitutionId(any())).thenReturn(institutionListMock);

		Assertions.assertEquals(institutionService.getInstitutionsByParentId("id"), institutionListMock);
	}

	@Test
	void getInstitutionsByUser_Positive() {
		Mockito.when(institutionRepository.findByUsersUsernameAndActiveTrueOrderByTierAscNameAsc(any()))
				.thenReturn(institutionListMock);

		Assertions.assertEquals(institutionService.getInstitutionsByUser("id"), institutionListMock);
	}

	@Test
	void getInstitutionTierOneWithAuthorities_ThrowEntityNotFound_Negative() {
		Mockito.when(userService.findOneWithAuthoritiesByUsernameAndLoginType(any(), any()))
				.thenReturn(Optional.empty());

		Assertions.assertThrows(EntityNotFoundException.class,
				() -> institutionService.getInstitutionTierOneWithAuthorities(userMock));
	}

	@Test
	void getInstitutionTierOneWithAuthorities_ThrowNoSuchElement_Negative() {
		Mockito.when(userService.findOneWithAuthoritiesByUsernameAndLoginType(any(), any()))
				.thenReturn(Optional.of(userMock));

		Assertions.assertThrows(NoSuchElementException.class,
				() -> institutionService.getInstitutionTierOneWithAuthorities(userMock));
	}

	@Test
	void getInstitutionTierOneWithAuthorities_DoesntThrowError_Positive() {
		institutionMock.setTier(1);
		Set<Institution> institutionSet = new HashSet<>();
		institutionSet.add(institutionMock);
		userMock.setInstitutions(institutionSet);
		Mockito.when(userService.findOneWithAuthoritiesByUsernameAndLoginType(any(), any()))
				.thenReturn(Optional.of(userMock));

		Institution data = Assertions
				.assertDoesNotThrow(() -> institutionService.getInstitutionTierOneWithAuthorities(userMock));
		assertThat(data).isEqualTo(institutionMock);
	}

	@Test
	void getInstitutionByAiMapping_ThrowNotFound_Negative() {
		Mockito.when(institutionRepository.findByAiMapping(any())).thenReturn(Optional.empty());

		Assertions.assertThrows(EntityNotFoundException.class, () -> institutionService.getInstitutionByAiMapping(""));
	}

	@Test
	void getInstitutionByAiMapping_DoesntThrowNotFound_Positive() {
		Mockito.when(institutionRepository.findByAiMapping(any())).thenReturn(Optional.of(institutionMock));

		Institution data = Assertions.assertDoesNotThrow(() -> institutionService.getInstitutionByAiMapping(""));
		assertThat(data).isEqualTo(institutionMock);
	}

	@Test
	void getListInstitutionByAiMapping_Positive() {
		Mockito.when(institutionRepository.findAllByAiMappingIsNotNull()).thenReturn(institutionListMock);

		assertThat(institutionService.getListInstitutionByAiMapping().size()).isEqualTo(1);
	}

}
