package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.ConversionStatusEnum;
import my.com.mandrill.component.constant.DashboardDateType;
import my.com.mandrill.component.domain.DashboardActivity;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.service.BigQueryService;
import my.com.mandrill.component.service.DashboardActivityService;
import my.com.mandrill.component.service.DashboardService;
import my.com.mandrill.utilities.feign.dto.AdvertisementDTO;
import my.com.mandrill.utilities.feign.dto.DocumentTypeCodeAndCountDTO;
import my.com.mandrill.utilities.feign.dto.GroupAttachmentTypeAndCountDTO;
import my.com.mandrill.utilities.feign.dto.UserInterestedRedirectEntityAndCountDTO;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.util.DateUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

	private static final BigDecimal MULTIPLIER_PERCENTAGE_100 = new BigDecimal("100");

	private final DashboardActivityService dashboardActivityService;

	private final ProxyFeignClient proxyFeignClient;

	private final BigQueryService bigQueryService;

	private static final String ALLIANCE_CAMPAIGN_ISSUER_CODE = "ALLI";

	private static final String ALLIANCE_CAMPAIGN_PRODUCT_TYPE = "credit-card";

	private static final String KNOW_YOUR_CARD_CODE = "GET_TO_KNOW_CREDIT_CARD";

	@Override
	public DashboardResponse getModuleRecordByDaily(final LocalDate dateFrom) {
		// TODO timezone should be retrieve from FE, and defaulted at the controller to
		// Malaysia
		final Instant dateStart = dateFrom.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
		final Instant dateEnd = dateFrom.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant();
		List<DashboardBarChartProjection> dashboardProjections = dashboardActivityService
				.findBarChartByCategoryAndDateBetween(DashboardCategory.MODULE_RECORDS, dateStart, dateEnd);

		return toBarChartResponse(dashboardProjections, dateFrom.toString());
	}

	@Override
	public DashboardResponse getModuleRecordByWeekly(final LocalDate dateFrom, final LocalDate dateTo) {
		// TODO timezone should be retrieve from FE, and defaulted at the controller to
		// Malaysia
		final Instant dateStart = dateFrom.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
		final Instant dateEnd = dateTo.atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant();
		List<DashboardBarChartProjection> dashboardProjections = dashboardActivityService
				.findBarChartByCategoryAndDateBetween(DashboardCategory.MODULE_RECORDS, dateStart, dateEnd);

		String date = "%s - %s".formatted(dateFrom, dateTo);
		return toBarChartResponse(dashboardProjections, date);
	}

	@Override
	public DashboardResponse getModuleRecordByMonthly(final YearMonth yearMonth) {
		// TODO timezone should be retrieve from FE, and defaulted at the controller to
		// Malaysia
		final Instant dateStart = yearMonth.atDay(1).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
		final Instant dateEnd = yearMonth.atEndOfMonth().atTime(LocalTime.MAX).atZone(ZoneId.systemDefault())
				.toInstant();
		List<DashboardBarChartProjection> dashboardProjections = dashboardActivityService
				.findBarChartByCategoryAndDateBetween(DashboardCategory.MODULE_RECORDS, dateStart, dateEnd);

		return toBarChartResponse(dashboardProjections, yearMonth.toString());
	}

	@Override
	public DashboardResponse getAppDownloadDaily(LocalDate dateFrom, ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateFrom.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		DashboardActivity install = dashboardActivityService
				.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(DashboardCategory.APP_DOWNLOAD,
						DashboardType.INSTALL, dateStart, dateEnd)
				.orElse(null);

		DashboardActivity uninstall = dashboardActivityService
				.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(DashboardCategory.APP_DOWNLOAD,
						DashboardType.UNINSTALL, dateStart, dateEnd)
				.orElse(null);

		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setInstallCount(install == null ? 0L : install.getValue());
		response.setUninstallCount(uninstall == null ? 0L : uninstall.getValue());
		return response;
	}

	@Override
	public DashboardResponse getAppDownloadRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setDateTo(DateUtil.convertToDateDetail(dateTo));
		response.setInstallCount(0L);
		response.setUninstallCount(0L);
		dateFrom.datesUntil(dateTo.plusDays(1L)).forEach(localDate -> {
			DashboardResponse dailyResult = getAppDownloadDaily(localDate, zoneId);
			response.setInstallCount(response.getInstallCount() + dailyResult.getInstallCount());
			response.setUninstallCount(response.getUninstallCount() + dailyResult.getUninstallCount());

		});
		return response;
	}

	@Override
	public DashboardResponse getAppDownloadMonthly(YearMonth yearMonth, ZoneId zoneId) {
		DashboardResponse response = getAppDownloadRange(yearMonth.atDay(1), yearMonth.atEndOfMonth(), zoneId);
		response.setYearMonth(DateUtil.convertToDateDetail(yearMonth));
		return response;
	}

	@Override
	public DashboardResponse getAppTimeSpentDaily(LocalDate dateFrom, ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateFrom.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		DashboardActivity screenTime = dashboardActivityService
				.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(DashboardCategory.APP_TIME_SPENT,
						DashboardType.SCREEN_TIME, dateStart, dateEnd)
				.orElse(null);
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setScreenTimeCount(screenTime == null ? 0L : screenTime.getValue());
		return response;
	}

	@Override
	public List<DashboardResponse> getAppTimeSpentDailyBefore(LocalDate dateFrom, ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			dashboardResponses.add(getAppTimeSpentDaily(dateFrom.minusDays(slice), zoneId));
		}
		return dashboardResponses;
	}

	@Override
	public DashboardResponse getAppTimeSpentRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setDateTo(DateUtil.convertToDateDetail(dateTo));
		response.setScreenTimeCount(0L);
		dateFrom.datesUntil(dateTo.plusDays(1L)).forEach(localDate -> {
			DashboardResponse dailyResult = getAppTimeSpentDaily(localDate, zoneId);
			response.setScreenTimeCount(response.getScreenTimeCount() + dailyResult.getScreenTimeCount());
		});
		return response;
	}

	@Override
	public List<DashboardResponse> getAppTimeSpentWeeklyBefore(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId,
			Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			LocalDate currentDateFrom = dateFrom.minusWeeks(slice);
			LocalDate currentDateTo = dateTo.minusWeeks(slice);
			DashboardResponse response = getAppTimeSpentRange(currentDateFrom, currentDateTo, zoneId);
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public List<DashboardResponse> getAppTimeSpentMonthlyBefore(YearMonth yearMonth, ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			YearMonth currentYearMonth = yearMonth.minusMonths(slice);
			LocalDate dateFrom = currentYearMonth.atDay(1);
			LocalDate dateTo = currentYearMonth.atEndOfMonth();
			DashboardResponse response = getAppTimeSpentRange(dateFrom, dateTo, zoneId);
			response.setYearMonth(DateUtil.convertToDateDetail(currentYearMonth));
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public DashboardResponse getAppUserActivityDaily(LocalDate dateFrom, ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateFrom.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		DashboardActivity dashboardActivity = dashboardActivityService
				.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(
						DashboardCategory.APP_USER_ACTIVITY, DashboardType.TOTAL_OPEN, dateStart, dateEnd)
				.orElse(null);
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setTotalOpenCount(dashboardActivity == null ? 0L : dashboardActivity.getValue());
		return response;
	}

	@Override
	public List<DashboardResponse> getAppUserActivityDailyBefore(LocalDate dateFrom, ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			dashboardResponses.add(getAppUserActivityDaily(dateFrom.minusDays(slice), zoneId));
		}
		return dashboardResponses;
	}

	@Override
	public DashboardResponse getAppUserActivityRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setDateTo(DateUtil.convertToDateDetail(dateTo));
		response.setTotalOpenCount(0L);
		dateFrom.datesUntil(dateTo.plusDays(1L)).forEach(localDate -> {
			DashboardResponse dailyResult = getAppUserActivityDaily(localDate, zoneId);
			response.setTotalOpenCount(response.getTotalOpenCount() + dailyResult.getTotalOpenCount());
		});
		return response;
	}

	@Override
	public List<DashboardResponse> getAppUserActivityWeeklyBefore(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId,
			Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			LocalDate currentDateFrom = dateFrom.minusWeeks(slice);
			LocalDate currentDateTo = dateTo.minusWeeks(slice);
			DashboardResponse response = getAppUserActivityRange(currentDateFrom, currentDateTo, zoneId);
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public List<DashboardResponse> getAppUserActivityMonthlyBefore(YearMonth yearMonth, ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			YearMonth currentYearMonth = yearMonth.minusMonths(slice);
			LocalDate dateFrom = currentYearMonth.atDay(1);
			LocalDate dateTo = currentYearMonth.atEndOfMonth();
			DashboardResponse response = getAppUserActivityRange(dateFrom, dateTo, zoneId);
			response.setYearMonth(DateUtil.convertToDateDetail(currentYearMonth));
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public DashboardResponse getAppActiveUserDaily(LocalDate dateFrom, ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateFrom.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		DashboardActivity dashboardActivity = dashboardActivityService
				.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(
						DashboardCategory.APP_ACTIVE_USER, DashboardType.USER_COUNT, dateStart, dateEnd)
				.orElse(null);
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setActiveUserCount(dashboardActivity == null ? 0L : dashboardActivity.getValue());
		return response;
	}

	@Override
	public List<DashboardResponse> getAppActiveUserDailyBefore(LocalDate dateFrom, ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			dashboardResponses.add(getAppActiveUserDaily(dateFrom.minusDays(slice), zoneId));
		}
		return dashboardResponses;
	}

	@Override
	public DashboardResponse getAppActiveUserRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setDateTo(DateUtil.convertToDateDetail(dateTo));
		response.setActiveUserCount(0L);
		dateFrom.datesUntil(dateTo.plusDays(1L)).forEach(localDate -> {
			DashboardResponse dailyResult = getAppActiveUserDaily(localDate, zoneId);
			response.setActiveUserCount(response.getActiveUserCount() + dailyResult.getActiveUserCount());
		});
		return response;
	}

	@Override
	public List<DashboardResponse> getAppActiveUserWeeklyBefore(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId,
			Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			LocalDate currentDateFrom = dateFrom.minusWeeks(slice);
			LocalDate currentDateTo = dateTo.minusWeeks(slice);
			DashboardResponse response = getAppActiveUserRange(currentDateFrom, currentDateTo, zoneId);
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public List<DashboardResponse> getAppActiveUserMonthlyBefore(YearMonth yearMonth, ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			YearMonth currentYearMonth = yearMonth.minusMonths(slice);
			LocalDate dateFrom = currentYearMonth.atDay(1);
			LocalDate dateTo = currentYearMonth.atEndOfMonth();
			DashboardResponse response = getAppActiveUserRange(dateFrom, dateTo, zoneId);
			response.setYearMonth(DateUtil.convertToDateDetail(currentYearMonth));
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public DashboardResponse getModuleTimeSpentDaily(LocalDate dateFrom, ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateFrom.atTime(LocalTime.MAX).atZone(zoneId).toInstant();

		DashboardResponse response = new DashboardResponse();
		DashboardType.MODULE_TIME_SPENT_TYPES.forEach(dashboardType -> {
			DashboardActivity dashboardActivity = dashboardActivityService
					.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(
							DashboardCategory.MODULE_TIME_SPENT, dashboardType, dateStart, dateEnd)
					.orElse(null);
			setValueByDashboardType(dashboardType, response,
					dashboardActivity == null ? 0L : dashboardActivity.getValue());
		});
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		return response;
	}

	@Override
	public List<DashboardResponse> getModuleTimeSpentDailyBefore(LocalDate dateFrom, ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			dashboardResponses.add(getModuleTimeSpentDaily(dateFrom.minusDays(slice), zoneId));
		}
		return dashboardResponses;
	}

	@Override
	public DashboardResponse getModuleTimeSpentRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setDateTo(DateUtil.convertToDateDetail(dateTo));
		dateFrom.datesUntil(dateTo.plusDays(1L)).forEach(localDate -> {
			DashboardResponse dailyResult = getModuleTimeSpentDaily(localDate, zoneId);
			updateValue(response, dailyResult);
		});
		return response;
	}

	@Override
	public List<DashboardResponse> getModuleTimeSpentWeeklyBefore(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId,
			Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			LocalDate currentDateFrom = dateFrom.minusWeeks(slice);
			LocalDate currentDateTo = dateTo.minusWeeks(slice);
			DashboardResponse response = getModuleTimeSpentRange(currentDateFrom, currentDateTo, zoneId);
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public List<DashboardResponse> getModuleTimeSpentMonthlyBefore(YearMonth yearMonth, ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		for (int slice = 0; slice < step; slice++) {
			YearMonth currentYearMonth = yearMonth.minusMonths(slice);
			LocalDate dateFrom = currentYearMonth.atDay(1);
			LocalDate dateTo = currentYearMonth.atEndOfMonth();
			DashboardResponse response = getModuleTimeSpentRange(dateFrom, dateTo, zoneId);
			response.setYearMonth(DateUtil.convertToDateDetail(currentYearMonth));
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	private DashboardResponse toBarChartResponse(List<DashboardBarChartProjection> dashboardProjections, String date) {
		DashboardResponse response = new DashboardResponse();
		response.setDate(date);
		dashboardProjections.forEach(dp -> setValueDashboardResponse(response, dp));
		return response;
	}

	private void setValueDashboardResponse(final DashboardResponse response, final DashboardBarChartProjection data) {
		long value = data.getValue() == null ? 0 : data.getValue();
		this.setValueByDashboardType(data.getType(), response, value);
	}

	private void setValueByDashboardType(DashboardType type, final DashboardResponse response, final long value) {
		// TODO switch case should have the default handler
		switch (type) {
			case INSTALL -> response.setInstallCount(value);
			case UNINSTALL -> response.setUninstallCount(value);
			case SCREEN_TIME -> response.setScreenTimeCount(value);
			case BANKS -> response.setBanksCount(value);
			case LOANS -> response.setLoansCount(value);
			case PROPERTIES -> response.setPropertiesCount(value);
			case VEHICLES -> response.setVehiclesCount(value);
			case UTILITIES -> response.setUtilitiesCount(value);
			case INSURANCES -> response.setInsuranceCount(value);
			case CREDIT_CARD -> response.setCreditCardCount(value);
			case USER_COUNT -> response.setActiveUserCount(value);
			case TOTAL_OPEN -> response.setTotalOpenCount(value);
			case IOS -> response.setTotalIosCount(value);
			case ANDROID -> response.setTotalAndroidCount(value);
		}
	}

	private void updateValue(final DashboardResponse response, final DashboardResponse newResponse) {
		response.setInstallCount(response.getInstallCount() + newResponse.getInstallCount());
		response.setUninstallCount(response.getUninstallCount() + newResponse.getUninstallCount());
		response.setScreenTimeCount(response.getScreenTimeCount() + newResponse.getScreenTimeCount());
		response.setBanksCount(response.getBanksCount() + newResponse.getBanksCount());
		response.setLoansCount(response.getLoansCount() + newResponse.getLoansCount());
		response.setPropertiesCount(response.getPropertiesCount() + newResponse.getPropertiesCount());
		response.setVehiclesCount(response.getVehiclesCount() + newResponse.getVehiclesCount());
		response.setUtilitiesCount(response.getUtilitiesCount() + newResponse.getUtilitiesCount());
		response.setInsuranceCount(response.getInsuranceCount() + newResponse.getInsuranceCount());
		response.setCreditCardCount(response.getCreditCardCount() + newResponse.getCreditCardCount());
		response.setActiveUserCount(response.getActiveUserCount() + newResponse.getActiveUserCount());
		response.setTotalOpenCount(response.getTotalOpenCount() + newResponse.getTotalOpenCount());
		response.setTotalIosCount(response.getTotalIosCount() + newResponse.getTotalIosCount());
		response.setTotalAndroidCount(response.getTotalAndroidCount() + newResponse.getTotalAndroidCount());
	}

	@Override
	public List<DashboardResponse> getModuleTimeSpentWeeklyBeforeLiveFeed(ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		LocalDate dateFrom = findNearestMonday(LocalDate.now());
		LocalDate dateTo = dateFrom.plusDays(6L);
		for (int slice = 0; slice < step; slice++) {
			LocalDate currentDateFrom = dateFrom.minusWeeks(slice);
			LocalDate currentDateTo = dateTo.minusWeeks(slice);
			DashboardResponse response = getModuleTimeSpentRange(currentDateFrom, currentDateTo, zoneId);
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public List<DashboardResponse> getAppActiveUserWeeklyBeforeLiveFeed(ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		LocalDate dateFrom = findNearestMonday(LocalDate.now());
		LocalDate dateTo = dateFrom.plusDays(6L);
		for (int slice = 0; slice < step; slice++) {
			LocalDate currentDateFrom = dateFrom.minusWeeks(slice);
			LocalDate currentDateTo = dateTo.minusWeeks(slice);
			DashboardResponse response = getAppActiveUserRange(currentDateFrom, currentDateTo, zoneId);
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public List<DashboardResponse> getAppDownloadRangeLiveFeed(ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		LocalDate dateFrom = findNearestMonday(LocalDate.now());
		LocalDate dateTo = dateFrom.plusDays(6L);
		for (int slice = 0; slice < step; slice++) {
			DashboardResponse response = new DashboardResponse();
			LocalDate currentDateFrom = dateFrom.minusWeeks(slice);
			LocalDate currentDateTo = dateTo.minusWeeks(slice);
			response.setDateFrom(DateUtil.convertToDateDetail(currentDateFrom));
			response.setDateTo(DateUtil.convertToDateDetail(currentDateTo));
			response.setInstallCount(0L);
			response.setUninstallCount(0L);
			currentDateFrom.datesUntil(currentDateTo.plusDays(1L)).forEach(localDate -> {
				DashboardResponse dailyResult = getAppDownloadDaily(localDate, zoneId);
				response.setInstallCount(response.getInstallCount() + dailyResult.getInstallCount());
				response.setUninstallCount(response.getUninstallCount() + dailyResult.getUninstallCount());
			});
			dashboardResponses.add(response);
		}
		return dashboardResponses;
	}

	@Override
	public List<DashboardResponse> getAppDownloadRangeLiveFeedDaily(ZoneId zoneId, Integer step) {
		List<DashboardResponse> dashboardResponses = new ArrayList<>();
		ZonedDateTime dateFrom = Instant.now().atZone(zoneId);
		for (int slice = 0; slice < step; slice++) {
			ZonedDateTime currentDateFrom = dateFrom.minusDays(slice);
			DashboardResponse dailyResult = getAppDownloadDaily(currentDateFrom.toLocalDate(), zoneId);
			dashboardResponses.add(dailyResult);
		}
		return dashboardResponses;
	}

	@Override
	public DashboardResponse getOsDownloadDaily(LocalDate dateFrom, ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateFrom.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		DashboardActivity android = dashboardActivityService
				.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(DashboardCategory.OS_DOWNLOAD,
						DashboardType.ANDROID, dateStart, dateEnd)
				.orElse(null);

		DashboardActivity ios = dashboardActivityService
				.findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(DashboardCategory.OS_DOWNLOAD,
						DashboardType.IOS, dateStart, dateEnd)
				.orElse(null);

		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setTotalAndroidCount(android == null ? 0L : android.getValue());
		response.setTotalIosCount(ios == null ? 0L : ios.getValue());
		return response;
	}

	@Override
	public DashboardResponse getOsDownloadRange(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		DashboardResponse response = new DashboardResponse();
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setDateTo(DateUtil.convertToDateDetail(dateTo));
		response.setTotalAndroidCount(0L);
		response.setTotalIosCount(0L);
		dateFrom.datesUntil(dateTo.plusDays(1L)).forEach(localDate -> {
			DashboardResponse dailyResult = getOsDownloadDaily(localDate, zoneId);
			response.setTotalAndroidCount(response.getTotalAndroidCount() + dailyResult.getTotalAndroidCount());
			response.setTotalIosCount(response.getTotalIosCount() + dailyResult.getTotalIosCount());
		});
		return response;
	}

	@Override
	public long getTodaysNewUsers(LocalDate dateFrom, ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateFrom.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		return dashboardActivityService.countByDateBetweenAndLoginType(dateStart, dateEnd, LoginTypeEnum.USER);
	}

	@Override
	public long getTotalUser(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateTo.atTime(LocalTime.MAX).atZone(zoneId).toInstant();
		return dashboardActivityService.countByDateBetweenAndLoginType(dateStart, dateEnd, LoginTypeEnum.USER);
	}

	@Override
	public List<DashboardActiveUserTotalUserResponseData> getAppActiveUserTotalUserDailyBigQuery(
			DashboardDateType dateType, LocalDate dateFrom, ZoneId zoneId) {

		List<DashboardActiveUserTotalUserResponseData> responseList = new ArrayList<>();
		List<ActiveUserByDateDTO> activeUser = bigQueryService.countUniqueActiveUserByDateType(dateType, dateFrom,
				dateFrom);
		for (ActiveUserByDateDTO result : activeUser) {
			DashboardActiveUserTotalUserResponseData responseData = new DashboardActiveUserTotalUserResponseData();
			responseData.setDateFrom(DateUtil.convertToDateDetail(result.getDateFrom()));
			responseData.setActiveUsersCount(result.getActiveUser());
			responseData.setUsersCount(getTotalUser(result.getDateFrom(), result.getDateFrom(), zoneId));
			responseList.add(responseData);
		}
		return responseList;
	}

	@Override
	public List<DashboardActiveUserTotalUserResponseData> getAppActiveUserTotalUserWeeklyBigQuery(
			DashboardDateType dateType, LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {

		List<DashboardActiveUserTotalUserResponseData> responseList = new ArrayList<>();
		List<ActiveUserByDateDTO> activeUser = bigQueryService.countUniqueActiveUserByDateType(dateType, dateFrom,
				dateTo);
		for (ActiveUserByDateDTO result : activeUser) {
			DashboardActiveUserTotalUserResponseData responseData = new DashboardActiveUserTotalUserResponseData();
			responseData.setDateFrom(DateUtil.convertToDateDetail(result.getDateFrom()));
			responseData.setDateTo(DateUtil.convertToDateDetail(result.getDateTo()));
			responseData.setActiveUsersCount(result.getActiveUser());
			responseData.setUsersCount(getTotalUser(result.getDateFrom(), result.getDateTo(), zoneId));
			responseList.add(responseData);
		}
		return responseList;
	}

	@Override
	public List<DashboardActiveUserTotalUserResponseData> getAppActiveUserTotalUserMonthlyBigQuery(
			DashboardDateType dateType, YearMonth yearMonth, ZoneId zoneId) {
		LocalDate dateFrom = yearMonth.atDay(1);
		LocalDate dateTo = yearMonth.atEndOfMonth();
		List<DashboardActiveUserTotalUserResponseData> responseList = new ArrayList<>();
		List<ActiveUserByDateDTO> activeUser = bigQueryService.countUniqueActiveUserByDateType(dateType, dateFrom,
				dateTo);
		for (ActiveUserByDateDTO result : activeUser) {
			DashboardActiveUserTotalUserResponseData responseData = new DashboardActiveUserTotalUserResponseData();
			responseData.setYearMonth(DateUtil.convertToDateDetail(YearMonth.from(result.getDateFrom())));
			responseData.setActiveUsersCount(result.getActiveUser());
			responseData.setUsersCount(getTotalUser(result.getDateFrom(), result.getDateTo(), zoneId));
			responseList.add(responseData);
		}
		return responseList;
	}

	private void setValueByDashboardType(DashboardType type,
			final DashboardRecordOrTimeSpentPerModuleResponseData responseData, final long value) {
		switch (type) {
			case BANKS -> responseData.setBanksCount(value);
			case LOANS -> responseData.setLoansCount(value);
			case PROPERTIES -> responseData.setPropertiesCount(value);
			case VEHICLES -> responseData.setVehiclesCount(value);
			case UTILITIES -> responseData.setUtilitiesCount(value);
			case INSURANCES -> responseData.setInsuranceCount(value);
			case CREDIT_CARD -> responseData.setCreditCardCount(value);
			case REMINDERS -> responseData.setRemindersCount(value);
			case FINANALYSIS -> responseData.setFinAnalysisCount(value);
			case CAMPAIGN_KNOW_YOUR_CARD -> responseData.setCampaignCardCount(value);
			case CAMPAIGN_KNOW_YOUR_LOAN_LIMIT -> responseData.setCampaignLoanLimitCount(value);
			default -> log.info(type + " is not found in DashboardType");
		}
	}

	@Override
	public DashboardRecordOrTimeSpentPerModuleResponseData getModuleRecord(LocalDate dateFrom, LocalDate dateTo,
			ZoneId zoneId) {
		final Instant dateStart = dateFrom.atStartOfDay(zoneId).toInstant();
		final Instant dateEnd = dateTo.atTime(LocalTime.MAX).atZone(zoneId).toInstant();

		DashboardRecordOrTimeSpentPerModuleResponseData responseData = new DashboardRecordOrTimeSpentPerModuleResponseData();

		List<DashboardBarChartProjection> moduleRecords = dashboardActivityService
				.findBarChartByCategoryAndDateBetween(DashboardCategory.MODULE_RECORDS, dateStart, dateEnd);

		for (DashboardBarChartProjection moduleRecord : moduleRecords) {
			setValueByDashboardType(moduleRecord.getType(), responseData, moduleRecord.getValue());
		}

		long finAnalysisCompletedCount = proxyFeignClient.getFinancialAnalysisFeignClient()
				.countNewUserAverage(dateStart, dateEnd);
		long campaignLoanLimitCount = proxyFeignClient.getBankFeignClient().countLoanLimitByCreatedDate(dateStart,
				dateEnd);

		long campaignCardCount = 0L;
		AdvertisementDTO advertisementDTO = proxyFeignClient.getPromotionFeignClient()
				.getAdvertisementByCode(KNOW_YOUR_CARD_CODE);
		if (advertisementDTO != null) {
			campaignCardCount = proxyFeignClient.getBankFeignClient().countCardAddedViaCampaign(dateStart, dateEnd,
					advertisementDTO.getId());
		}

		responseData.setFinAnalysisCount(finAnalysisCompletedCount);
		responseData.setCampaignCardCount(campaignCardCount);
		responseData.setCampaignLoanLimitCount(campaignLoanLimitCount);
		return responseData;
	}

	@Override
	public DashboardRecordOrTimeSpentPerModuleResponseData getModuleRecordDaily(LocalDate dateFrom, ZoneId zoneId) {
		DashboardRecordOrTimeSpentPerModuleResponseData response = getModuleRecord(dateFrom, dateFrom, zoneId);
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		return response;
	}

	@Override
	public DashboardRecordOrTimeSpentPerModuleResponseData getModuleRecordWeekly(LocalDate dateFrom, LocalDate dateTo,
			ZoneId zoneId) {
		DashboardRecordOrTimeSpentPerModuleResponseData response = getModuleRecord(dateFrom, dateTo, zoneId);
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setDateTo(DateUtil.convertToDateDetail(dateTo));
		return response;
	}

	@Override
	public DashboardRecordOrTimeSpentPerModuleResponseData getModuleRecordMonthly(YearMonth yearMonth, ZoneId zoneId) {
		final LocalDate dateFrom = yearMonth.atDay(1);
		final LocalDate dateTo = yearMonth.atEndOfMonth();
		DashboardRecordOrTimeSpentPerModuleResponseData response = getModuleRecord(dateFrom, dateTo, zoneId);
		response.setYearMonth(DateUtil.convertToDateDetail(yearMonth));
		return response;
	}

	private LocalDate findNearestMonday(LocalDate date) {
		return date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
	}

	@Override
	public List<DashboardDownloadsResponseData> getAppDownloadDailyByBigQuery(LocalDate dateFrom,
			DashboardDateType dateType, ZoneId zoneId) {
		List<DashboardDownloadsResponseData> response = new ArrayList<>();
		List<AppDownloadByDateDTO> appDownloadList = bigQueryService.countAppDownloadByDateType(dateType, dateFrom,
				dateFrom);

		for (AppDownloadByDateDTO appDownload : appDownloadList) {
			DashboardDownloadsResponseData responseData = new DashboardDownloadsResponseData();
			responseData.setDateFrom(DateUtil.convertToDateDetail(appDownload.getDateFrom()));
			responseData.setInstallCount(appDownload.getInstall());
			responseData.setUninstallCount(appDownload.getUninstall());
			responseData.setUsersCount(getTotalUser(appDownload.getDateFrom(), appDownload.getDateFrom(), zoneId));
			response.add(responseData);
		}

		return response;
	}

	@Override
	public List<DashboardDownloadsResponseData> getAppDownloadWeeklyByBigQuery(LocalDate dateFrom, LocalDate dateTo,
			DashboardDateType dateType, ZoneId zoneId) {
		List<DashboardDownloadsResponseData> response = new ArrayList<>();
		List<AppDownloadByDateDTO> appDownloadList = bigQueryService.countAppDownloadByDateType(dateType, dateFrom,
				dateTo);

		for (AppDownloadByDateDTO appDownload : appDownloadList) {
			DashboardDownloadsResponseData responseData = new DashboardDownloadsResponseData();
			responseData.setDateFrom(DateUtil.convertToDateDetail(appDownload.getDateFrom()));
			responseData.setDateTo(DateUtil.convertToDateDetail(appDownload.getDateTo()));
			responseData.setInstallCount(appDownload.getInstall());
			responseData.setUninstallCount(appDownload.getUninstall());
			responseData.setUsersCount(getTotalUser(appDownload.getDateFrom(), appDownload.getDateTo(), zoneId));
			response.add(responseData);
		}

		return response;
	}

	@Override
	public List<DashboardDownloadsResponseData> getAppDownloadMonthlyByBigQuery(YearMonth yearMonth,
			DashboardDateType dateType, ZoneId zoneId) {
		LocalDate dateFrom = yearMonth.atDay(1);
		LocalDate dateTo = yearMonth.atEndOfMonth();
		List<DashboardDownloadsResponseData> response = new ArrayList<>();
		List<AppDownloadByDateDTO> appDownloadList = bigQueryService.countAppDownloadByDateType(dateType, dateFrom,
				dateTo);

		for (AppDownloadByDateDTO appDownload : appDownloadList) {
			DashboardDownloadsResponseData responseData = new DashboardDownloadsResponseData();
			responseData.setYearMonth(DateUtil.convertToDateDetail(YearMonth.from(appDownload.getDateFrom())));
			responseData.setInstallCount(appDownload.getInstall());
			responseData.setUninstallCount(appDownload.getUninstall());
			responseData.setUsersCount(getTotalUser(appDownload.getDateFrom(), appDownload.getDateTo(), zoneId));
			response.add(responseData);
		}

		return response;
	}

	@Override
	public long getAverageTimeSpent(LocalDate dateFrom, LocalDate dateTo) {
		BigDecimal averageTimeSpent = bigQueryService.countAverageTimeSpentBetweenDate(dateFrom, dateTo);
		return averageTimeSpent.setScale(0, RoundingMode.HALF_UP).longValue();
	}

	@Override
	public long getAverageSessionDurationDailyBigQuery(LocalDate dateFrom, Integer step) {
		final LocalDate dateFromForTotal = dateFrom.minusDays(step - 1L);
		return getAverageTimeSpent(dateFromForTotal, dateFrom);
	}

	@Override
	public long getAverageSessionDurationWeeklyBigQuery(LocalDate dateFrom, LocalDate dateTo, Integer step) {
		final LocalDate dateFromForTotal = dateFrom.minusWeeks(step - 1L);
		return getAverageTimeSpent(dateFromForTotal, dateTo);
	}

	@Override
	public long getAverageSessionDurationMonthlyBigQuery(YearMonth yearMonth, Integer step) {
		final YearMonth yearMonthFrom = yearMonth.minusMonths(step - 1L);
		final LocalDate dateFromForTotal = yearMonthFrom.atDay(1);
		final LocalDate dateToForTotal = yearMonth.atEndOfMonth();
		return getAverageTimeSpent(dateFromForTotal, dateToForTotal);
	}

	@Override
	public DashboardUserEngagementResponse getUserEngagementModuleTimeSpent(LocalDate dateFrom, LocalDate dateTo) {
		Map<DashboardType, BigDecimal> timeSpentPerActiveUser = bigQueryService
				.countAverageModuleTimeSpentBetweenDate(dateFrom, dateTo, DashboardType.MODULE_USER_ENGAGEMENT);

		long homeCount = timeSpentPerActiveUser.get(DashboardType.HOME).setScale(0, RoundingMode.HALF_UP).longValue();
		long vaultCount = timeSpentPerActiveUser.get(DashboardType.VAULT).setScale(0, RoundingMode.HALF_UP).longValue();
		long searchCount = timeSpentPerActiveUser.get(DashboardType.SEARCH).setScale(0, RoundingMode.HALF_UP)
				.longValue();

		DashboardUserEngagementResponse response = new DashboardUserEngagementResponse();
		response.setAverageHomeCount(homeCount);
		response.setAverageVaultCount(vaultCount);
		response.setAverageSearchCount(searchCount);
		return response;
	}

	@Override
	public DashboardUserEngagementResponse getUserEngagementModuleDaily(LocalDate dateFrom, Integer step) {
		LocalDate dateFromForTotal = dateFrom.minusDays(step - 1L);
		return getUserEngagementModuleTimeSpent(dateFromForTotal, dateFrom);
	}

	@Override
	public DashboardUserEngagementResponse getUserEngagementModuleWeekly(LocalDate dateFrom, LocalDate dateTo,
			Integer step) {
		LocalDate dateFromForTotal = dateFrom.minusWeeks(step - 1L);
		return getUserEngagementModuleTimeSpent(dateFromForTotal, dateTo);
	}

	@Override
	public DashboardUserEngagementResponse getUserEngagementModuleMonthly(YearMonth yearMonth, Integer step) {
		final YearMonth yearMonthFrom = yearMonth.minusMonths(step - 1L);
		final LocalDate dateFromForTotal = yearMonthFrom.atDay(1);
		final LocalDate dateToForTotal = yearMonth.atEndOfMonth();
		return getUserEngagementModuleTimeSpent(dateFromForTotal, dateToForTotal);
	}

	@Override
	public List<DashboardUserEngagementResponseData> getUserEngagementDailyBigQuery(DashboardDateType dateType,
			LocalDate dateFrom) {
		List<DashboardUserEngagementResponseData> responseList = new ArrayList<>();
		List<BigDecimalResultByDateDTO> userEngagement = bigQueryService.countAverageTimeSpentByDateType(dateType,
				dateFrom, dateFrom);

		for (BigDecimalResultByDateDTO result : userEngagement) {
			DashboardUserEngagementResponseData responseData = new DashboardUserEngagementResponseData();
			responseData.setDateFrom(DateUtil.convertToDateDetail(result.getDateFrom()));
			responseData.setAverageTimeSpent(result.getResult().setScale(0, RoundingMode.HALF_UP).longValue());
			responseList.add(responseData);
		}
		return responseList;
	}

	@Override
	public List<DashboardUserEngagementResponseData> getUserEngagementWeeklyBigQuery(DashboardDateType dateType,
			LocalDate dateFrom, LocalDate dateTo) {
		List<DashboardUserEngagementResponseData> responseList = new ArrayList<>();

		List<BigDecimalResultByDateDTO> userEngagement = bigQueryService.countAverageTimeSpentByDateType(dateType,
				dateFrom, dateTo);

		for (BigDecimalResultByDateDTO result : userEngagement) {
			DashboardUserEngagementResponseData responseData = new DashboardUserEngagementResponseData();
			responseData.setDateFrom(DateUtil.convertToDateDetail(result.getDateFrom()));
			responseData.setDateTo(DateUtil.convertToDateDetail(result.getDateTo()));
			responseData.setAverageTimeSpent(result.getResult().setScale(0, RoundingMode.HALF_UP).longValue());
			responseList.add(responseData);
		}
		return responseList;
	}

	@Override
	public List<DashboardUserEngagementResponseData> getUserEngagementMonthlyBigQuery(DashboardDateType dateType,
			YearMonth yearMonth) {
		List<DashboardUserEngagementResponseData> responseList = new ArrayList<>();

		LocalDate dateFrom = yearMonth.atDay(1);
		LocalDate dateTo = yearMonth.atEndOfMonth();
		List<BigDecimalResultByDateDTO> userEngagement = bigQueryService.countAverageTimeSpentByDateType(dateType,
				dateFrom, dateTo);

		for (BigDecimalResultByDateDTO result : userEngagement) {
			DashboardUserEngagementResponseData responseData = new DashboardUserEngagementResponseData();
			responseData.setYearMonth(DateUtil.convertToDateDetail(YearMonth.from(result.getDateFrom())));
			responseData.setAverageTimeSpent(result.getResult().setScale(0, RoundingMode.HALF_UP).longValue());
			responseList.add(responseData);
		}
		return responseList;
	}

	@Override
	public DashboardRecordOrTimeSpentPerModuleResponseData getModuleTimeSpentBigQuery(LocalDate dateFrom,
			LocalDate dateTo) {
		DashboardRecordOrTimeSpentPerModuleResponseData responseData = new DashboardRecordOrTimeSpentPerModuleResponseData();
		Map<DashboardType, BigDecimal> moduleTimeSpent = bigQueryService
				.countAverageModuleTimeSpentBetweenDate(dateFrom, dateTo, DashboardType.MODULE_TIME_SPENT_TYPES);
		for (DashboardType type : DashboardType.values()) {
			BigDecimal defaultResult = moduleTimeSpent.getOrDefault(type, BigDecimal.ZERO);
			switch (type) {
				case BANKS -> responseData.setBanksCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case LOANS -> responseData.setLoansCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case PROPERTIES ->
					responseData.setPropertiesCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case VEHICLES ->
					responseData.setVehiclesCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case UTILITIES ->
					responseData.setUtilitiesCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case INSURANCES ->
					responseData.setInsuranceCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case CREDIT_CARD ->
					responseData.setCreditCardCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case REMINDERS ->
					responseData.setRemindersCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case FINANALYSIS ->
					responseData.setFinAnalysisCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case VOUCHER ->
					responseData.setVoucherCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case CAMPAIGN_KNOW_YOUR_CARD ->
					responseData.setCampaignCardCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case CAMPAIGN_KNOW_YOUR_LOAN_LIMIT ->
					responseData.setCampaignLoanLimitCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case CAMPAIGN_ALLIANCE_BANK_CREDIT_CARD -> responseData.setCampaignAllianceBankCreditCardCount(
						defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case EWILL -> responseData.setEWillCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case CAMPAIGN_EWILL ->
					responseData.setCampaignEWillCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case M_AND_A -> responseData.setMAndACount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case SOLAROO ->
					responseData.setSolarooCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case STASHAWAY ->
					responseData.setStashAwayCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case CAMPAIGN_STASHAWAY ->
					responseData.setCampaignStashAwayCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case MILIEU_SOLAR ->
					responseData.setMilieuSolarCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case CREDIT_BUREAU ->
					responseData.setCreditBureauCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case EASIWILL ->
					responseData.setEasiWillCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				case CTOS -> responseData.setCtosCount(defaultResult.setScale(0, RoundingMode.HALF_UP).longValue());
				default -> log.info(type + " is not found in DashboardType");
			}
		}
		return responseData;
	}

	@Override
	public DashboardRecordOrTimeSpentPerModuleResponseData getModuleTimeSpentDailyBigQuery(LocalDate dateFrom) {
		DashboardRecordOrTimeSpentPerModuleResponseData response = getModuleTimeSpentBigQuery(dateFrom, dateFrom);
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		return response;
	}

	@Override
	public DashboardRecordOrTimeSpentPerModuleResponseData getModuleTimeSpentWeeklyBigQuery(LocalDate dateFrom,
			LocalDate dateTo) {
		DashboardRecordOrTimeSpentPerModuleResponseData response = getModuleTimeSpentBigQuery(dateFrom, dateTo);
		response.setDateFrom(DateUtil.convertToDateDetail(dateFrom));
		response.setDateTo(DateUtil.convertToDateDetail(dateTo));
		return response;
	}

	@Override
	public DashboardRecordOrTimeSpentPerModuleResponseData getModuleTimeSpentMonthlyBigQuery(YearMonth yearMonth) {
		final LocalDate dateFrom = yearMonth.atDay(1);
		final LocalDate dateTo = yearMonth.atEndOfMonth();
		DashboardRecordOrTimeSpentPerModuleResponseData response = getModuleTimeSpentBigQuery(dateFrom, dateTo);
		response.setYearMonth(DateUtil.convertToDateDetail(yearMonth));
		return response;
	}

	@Override
	public DashboardStaticDataResponse getStaticData(ZoneId zoneId) {
		AppDownload appDownload = bigQueryService.countAppDownloadBetweenDate(DashboardDateType.APP_STARTING_DATE,
				LocalDate.now(zoneId));

		DashboardStaticDataResponse response = new DashboardStaticDataResponse();
		response.setAccumulativeDownloads(appDownload.getInstall());
		response.setBounceRate(getBounceRate(appDownload.getInstall(), appDownload.getUninstall()));
		response.setAccumulativeActiveUsers(
				bigQueryService.countUniqueActiveUser(DashboardDateType.APP_STARTING_DATE, LocalDate.now(zoneId)));
		response.setAccumulativeUsers(getTotalUser(DashboardDateType.APP_STARTING_DATE, LocalDate.now(), zoneId));
		response.setTodaysNewUsers(getTodaysNewUsers(LocalDate.now(), zoneId));
		return response;
	}

	@Override
	public BigDecimal getBounceRate(long downloads, long uninstall) {
		if (uninstall != 0L) {
			return BigDecimal.valueOf(downloads).divide(BigDecimal.valueOf(uninstall), 3, RoundingMode.HALF_UP)
					.multiply(MULTIPLIER_PERCENTAGE_100);
		}
		else {
			return BigDecimal.ZERO;
		}
	}

	@Override
	public List<VoucherCategoryAndCountResponse> getAllVoucherCategoryAndCountDaily(LocalDate dateFrom) {
		return getAllVoucherCategoryAndCount(dateFrom, dateFrom);
	}

	@Override
	public List<VoucherCategoryAndCountResponse> getAllVoucherCategoryAndCountWeekly(LocalDate dateFrom,
			LocalDate dateTo) {
		return getAllVoucherCategoryAndCount(dateFrom, dateTo);
	}

	@Override
	public List<VoucherCategoryAndCountResponse> getAllVoucherCategoryAndCountMonthly(YearMonth yearMonth) {
		final LocalDate dateFrom = yearMonth.atDay(1);
		final LocalDate dateTo = yearMonth.atEndOfMonth();
		return getAllVoucherCategoryAndCount(dateFrom, dateTo);
	}

	@Override
	public List<VoucherCategoryAndCountResponse> getAllVoucherCategoryAndCount(LocalDate dateFrom, LocalDate dateTo) {
		List<VoucherCategoryAndCountResponse> responseList = new ArrayList<>();

		for (VoucherCategoryEnum category : VoucherCategoryEnum.values()) {
			long count = proxyFeignClient.getPromotionFeignClient().countVoucherDetail(dateFrom, dateTo, category,
					VoucherStatusEnum.REDEEMED);
			VoucherCategoryAndCountResponse response = new VoucherCategoryAndCountResponse(category, count);
			responseList.add(response);
		}

		return responseList;

	}

	@Override
	public ReferralCountResponse countInvitedAndSucceedReferee(LocalDate dateFrom, LocalDate dateTo, String timeZone) {
		ReferralCountResponse response = new ReferralCountResponse();
		response.setTotalConversions(
				proxyFeignClient.getPromotionFeignClient().countTotalConversions(dateFrom, dateTo, timeZone));
		response.setSucceedReferee(proxyFeignClient.getPromotionFeignClient().countByConversionStatus(dateFrom, dateTo,
				timeZone, ConversionStatusEnum.SUCCESS));
		return response;
	}

	@Override
	public ReferralCountResponse countInvitedAndSucceedRefereeDaily(LocalDate dateFrom, String timeZone) {
		return countInvitedAndSucceedReferee(dateFrom, dateFrom, timeZone);
	}

	@Override
	public ReferralCountResponse countInvitedAndSucceedRefereeWeekly(LocalDate dateFrom, LocalDate dateTo,
			String timeZone) {
		return countInvitedAndSucceedReferee(dateFrom, dateTo, timeZone);
	}

	@Override
	public ReferralCountResponse countInvitedAndSucceedRefereeMonthly(YearMonth yearMonth, String timeZone) {
		final LocalDate dateFrom = yearMonth.atDay(1);
		final LocalDate dateTo = yearMonth.atEndOfMonth();
		return countInvitedAndSucceedReferee(dateFrom, dateTo, timeZone);
	}

	@Override
	public LeadGeneratedPerModuleResponse countLeadGeneratedPerModuleDaily(LocalDate dateFrom, String timeZone) {
		return countLeadGeneratedPerModule(dateFrom, dateFrom, timeZone);
	}

	@Override
	public LeadGeneratedPerModuleResponse countLeadGeneratedPerModuleWeekly(LocalDate dateFrom, LocalDate dateTo,
			String timeZone) {
		return countLeadGeneratedPerModule(dateFrom, dateTo, timeZone);
	}

	@Override
	public LeadGeneratedPerModuleResponse countLeadGeneratedPerModuleMonthly(YearMonth yearMonth, String timeZone) {
		final LocalDate dateFrom = yearMonth.atDay(1);
		final LocalDate dateTo = yearMonth.atEndOfMonth();
		return countLeadGeneratedPerModule(dateFrom, dateTo, timeZone);
	}

	@Override
	public LeadGeneratedPerModuleResponse countLeadGeneratedPerModule(LocalDate dateFrom, LocalDate dateTo,
			String timeZone) {
		LeadGeneratedPerModuleResponse response = new LeadGeneratedPerModuleResponse();

		List<UserInterestedRedirectEntityAndCountDTO> moduleResult = proxyFeignClient.getAnalyticFeignClient()
				.getEntityNameAndCount(dateFrom, dateTo, timeZone);
		List<UserInterestedRedirectEntityAndCountDTO> campaignResult = proxyFeignClient.getAnalyticFeignClient()
				.getCampaignEntityNameAndCount(dateFrom, dateTo, timeZone);
		long allianceCampaignCount = proxyFeignClient.getBankFeignClient().countAllianceBankCampaign(dateFrom, dateTo,
				timeZone, ALLIANCE_CAMPAIGN_ISSUER_CODE, ALLIANCE_CAMPAIGN_PRODUCT_TYPE,
				UserInterestedSource.ADVERTISEMENT);

		moduleResult.forEach(dto -> {
			switch (dto.getEntityName()) {
				case EWILL -> response.setEWill(dto.getCount());
				case M_AND_A_INTEREST -> response.setMAndACount(dto.getCount());
				case SOLAROO -> response.setSolaroo(dto.getCount());
				case STASHAWAY -> response.setStashAway(dto.getCount());
				case CREDIT_BUREAU -> response.setCreditBureau(dto.getCount());
				case MILIEU_SOLAR -> response.setMilieuSolar(dto.getCount());
				case EASIWILL -> response.setEasiWill(dto.getCount());
				case CTOS -> response.setCtos(dto.getCount());
				default -> log.info(dto + " is not found in User Interest Redirect");
			}
		});

		campaignResult.forEach(dto -> {
			switch (dto.getEntityName()) {
				case EWILL -> response.setEWillCampaign(dto.getCount());
				case STASHAWAY -> response.setStashAwayCampaign(dto.getCount());
				default -> log.info(dto + " campaign is not found in User Interest Redirect");
			}
		});

		response.setAllianceBankCreditCardCampaign(allianceCampaignCount);
		return response;
	}

	private DocumentUploadedCountInVaultSubModuleResponse countDocumentUploaded(LocalDate dateFrom, LocalDate dateTo,
			String timeZone) {

		DocumentUploadedCountInVaultSubModuleResponse response = new DocumentUploadedCountInVaultSubModuleResponse();
		List<GroupAttachmentTypeAndCountDTO> resultList = proxyFeignClient.getCommonFeignClient()
				.getGroupAttachmentTypeAndCount(dateFrom, dateTo, timeZone);

		resultList.forEach(result -> {
			long count = result.getCount();
			AttachmentTypeFileStorageEnum attachmentType = AttachmentTypeFileStorageEnum
					.fromString(result.getGroupAttachmentType());
			switch (attachmentType) {
				case PROPERTY_DOC -> response.setMyProperty(count);
				case VEHICLE_DOC -> response.setMyVehicle(count);
				case LEGAL_DOC -> response.setMyLegalDocs(count);
				case UTILITY_BILL -> response.setMyUtility(count);
				case IDENTITY_DOC -> response.setMyIdentity(count);
				case BANK_DOC -> response.setMyFinance(count);
				case INSURANCE_DOC -> response.setMyInsurance(count);
				default -> {
					break;
				}
			}
		});

		return response;

	}

	@Override
	public DocumentUploadedCountInVaultSubModuleResponse countDocumentUploadedDaily(LocalDate dateFrom,
			String timeZone) {
		return countDocumentUploaded(dateFrom, dateFrom, timeZone);
	}

	@Override
	public DocumentUploadedCountInVaultSubModuleResponse countDocumentUploadedWeekly(LocalDate dateFrom,
			LocalDate dateTo, String timeZone) {
		return countDocumentUploaded(dateFrom, dateTo, timeZone);
	}

	@Override
	public DocumentUploadedCountInVaultSubModuleResponse countDocumentUploadedMonthly(YearMonth yearMonth,
			String timeZone) {
		final LocalDate dateFrom = yearMonth.atDay(1);
		final LocalDate dateTo = yearMonth.atEndOfMonth();
		return countDocumentUploaded(dateFrom, dateTo, timeZone);
	}

	private List<DocumentTypeCodeAndCountDTO> countDocumentTypeUploaded(LocalDate dateFrom, LocalDate dateTo,
			String timeZone, VaultTypeEnum vaultType) {
		return proxyFeignClient.getCommonFeignClient().getGroupAttachmentDocumentTypeAndCount(dateFrom, dateTo,
				timeZone, vaultType);
	}

	@Override
	public List<DocumentTypeCodeAndCountDTO> countDocumentTypeUploadedDaily(LocalDate dateFrom, String timeZone,
			VaultTypeEnum vaultType) {
		return countDocumentTypeUploaded(dateFrom, dateFrom, timeZone, vaultType);
	}

	@Override
	public List<DocumentTypeCodeAndCountDTO> countDocumentTypeUploadedWeekly(LocalDate dateFrom, LocalDate dateTo,
			String timeZone, VaultTypeEnum vaultType) {
		return countDocumentTypeUploaded(dateFrom, dateTo, timeZone, vaultType);
	}

	@Override
	public List<DocumentTypeCodeAndCountDTO> countDocumentTypeUploadedMonthly(YearMonth yearMonth, String timeZone,
			VaultTypeEnum vaultType) {
		final LocalDate dateFrom = yearMonth.atDay(1);
		final LocalDate dateTo = yearMonth.atEndOfMonth();
		return countDocumentTypeUploaded(dateFrom, dateTo, timeZone, vaultType);
	}

}
