package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import my.com.mandrill.utilities.general.constant.AccessTypeEnum;
import my.com.mandrill.utilities.general.constant.ClientPlatformType;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import my.com.mandrill.utilities.general.dto.HttpDetailDTO;

@Getter
@Builder
@EqualsAndHashCode
public class AuthServiceRequest {

	@NotBlank
	private String username;

	@NotBlank
	@ToString.Exclude
	private String password;

	private boolean rememberMe;

	private AccessTypeEnum accessType;

	private LoginTypeEnum loginType;

	private PasscodeType passcodeType;

	private ClientPlatformType clientPlatform;

	private String deviceId;

	private HttpDetailDTO httpDetailDTO;

}
