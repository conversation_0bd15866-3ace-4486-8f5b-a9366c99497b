package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LeadGeneratedPerModuleResponse implements Serializable {

	private long mAndACount = 0L;

	private long solaroo = 0L;

	private long stashAway = 0L;

	private long eWill = 0L;

	private long allianceBankCreditCardCampaign = 0L;

	private long stashAwayCampaign = 0L;

	private long eWillCampaign = 0L;

	private long milieuSolar = 0L;

	private long creditBureau = 0L;

	private long easiWill = 0L;

	private long ctos = 0L;

}
