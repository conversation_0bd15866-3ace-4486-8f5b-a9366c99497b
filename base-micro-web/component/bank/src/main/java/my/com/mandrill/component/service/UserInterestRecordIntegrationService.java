package my.com.mandrill.component.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import my.com.mandrill.component.domain.UserInterestRecord;
import my.com.mandrill.component.dto.model.UserInterestRecordDetailDTO;
import my.com.mandrill.component.dto.model.UserInterestedRedirectDTO;
import my.com.mandrill.component.dto.request.CreateUserInterestRecord;
import my.com.mandrill.component.dto.request.CreateUserInterestRedirectRecord;
import my.com.mandrill.component.dto.request.PublicUserInterestRedirectRequest;
import my.com.mandrill.component.dto.request.ReportServiceRequest;
import my.com.mandrill.component.dto.response.UserInterestRecordPageResponse;
import my.com.mandrill.component.dto.response.UserInterestRecordResponse;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.CurrentUserInstitutionsDTO;
import my.com.mandrill.utilities.feign.dto.UserDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.request.CreateManualLeadRequest;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.feign.dto.response.UserInterestVaultResponse;
import my.com.mandrill.utilities.general.constant.UserInterestedSource;
import my.com.mandrill.utilities.general.dto.response.UserPublicWebResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Set;

public interface UserInterestRecordIntegrationService {

	UserInterestRecord insertRedirect(CreateUserInterestRedirectRecord createUserInterest, CurrentUserIdDTO userDTO)
			throws JsonProcessingException;

	UserInterestRecord createManualLeadRedirect(CreateManualLeadRequest createManualLeadRequest);

	UserInterestRecord insert(CreateUserInterestRecord createUserInterest, UserDTO userDTO);

	UserInterestRecord createManualLead(CreateManualLeadRequest createManualLeadRequest);

	List<UserInterestRecordResponse> export(List<CurrentUserInstitutionsDTO> institutionsDTO,
			ReportServiceRequest request);

	Page<UserInterestRecordPageResponse> export(List<CurrentUserInstitutionsDTO> institutionsDTO, LocalDate startDate,
			LocalDate endDate, List<String> providerId, Pageable pageable);

	List<UserInterestedRedirectDTO> export(LocalDate startDate, LocalDate endDate);

	Long count(LocalDate startDate, LocalDate endDate);

	Page<UserInterestedRedirectDTO> export(LocalDate startDate, LocalDate endDate, Pageable pageable);

	Long countRedirect(LocalDate startDate, LocalDate endDate);

	long countAllianceBankCampaign(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId, String issuerCode,
			String productType, UserInterestedSource source);

	UserInterestRecord findById(String currentInstitutionId, String id);

	UserInterestRecordDetailDTO findDetail(String currentInstitutionId, String id);

	UserInterestVaultResponse findVaultAndIssuerByRecordId(String recordId);

	UserInterestRecord insertRedirectPublic(PublicUserInterestRedirectRequest createUserInterest,
			UserPublicWebResponse userPublicWebResponse) throws JsonProcessingException;

	UserInterestRecordRSMViewDTO findById(String id);

	Page<UserInterestRecordRSMPaginationDTO> rsmLead(Pageable pageable, RSMLeadRequest request);

	List<UserInterestRecordRSMPaginationDTO> rsmLeadReport(RSMLeadRequest request);

	void updateRsmInfo(LeadRSMUpdateRequest request);

	Set<String> findAllApplicationIdsExcludingUserIdsIn(Set<String> applicationIds, Set<String> userIds);

}
