package my.com.mandrill.component.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.dto.model.HomeMenuDTO;
import my.com.mandrill.component.dto.request.HomeMenuRequest;
import my.com.mandrill.component.dto.request.HomeMenuV2Request;
import my.com.mandrill.component.dto.response.HomeMenuInfoResponse;
import my.com.mandrill.component.service.HomeMenuIntgService;
import my.com.mandrill.component.service.HomeMenuProductService;
import my.com.mandrill.component.service.HomeMenuService;
import my.com.mandrill.component.service.ValidationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/home-menus")
@RequiredArgsConstructor
public class AdminHomeMenuController {

	private final HomeMenuIntgService homeMenuIntgService;

	private final ValidationService validationService;

	private final HomeMenuProductService homeMenuProductService;

	private final HomeMenuService homeMenuService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_CREATE)")
	public ResponseEntity<HomeMenuDTO> create(@Valid @RequestBody HomeMenuRequest homeMenuRequest) {
		HomeMenuDTO homeMenu = homeMenuIntgService.save(homeMenuRequest);
		return ResponseEntity.ok(homeMenu);
	}

	@GetMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<HomeMenuDTO> findById(@PathVariable String id) {
		HomeMenuDTO response = homeMenuIntgService.findById(id);
		return ResponseEntity.ok(response);
	}

	@DeleteMapping("/{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_DELETE)")
	public void delete(@PathVariable String id) {
		validationService.validateDeleteHomeMenu(id);
		homeMenuIntgService.deleteById(id);
	}

	@PutMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_UPDATE)")
	public ResponseEntity<HomeMenuDTO> update(@PathVariable String id,
			@Valid @RequestBody HomeMenuRequest homeMenuRequest) {
		HomeMenuDTO homeMenu = homeMenuIntgService.update(id, homeMenuRequest);
		return ResponseEntity.ok(homeMenu);
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<Page<HomeMenuDTO>> findAll(@RequestParam(required = false) String code, Pageable pageable) {
		Page<HomeMenu> response = homeMenuIntgService.findAll(code, pageable);
		return ResponseEntity.ok(response.map(homeMenuIntgService::toDTO));
	}

	@GetMapping("/state/enabled")
	public ResponseEntity<List<HomeMenuInfoResponse>> findStateEnabled(@RequestParam(required = false) String name) {
		return ResponseEntity.ok(homeMenuIntgService.findEnabledState(name));
	}

	@GetMapping("/without-parent-id")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<List<HomeMenuDTO>> findWithoutParentId() {
		return ResponseEntity.ok(homeMenuIntgService.findWithoutParentId());
	}

	@GetMapping("/pagination/without-parent-id")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<Page<HomeMenuDTO>> findPaginationWithoutParentId(@RequestParam(required = false) String code,
			Pageable pageable) {
		Page<HomeMenu> response = homeMenuIntgService.findAllWithoutParentId(code, pageable);
		return ResponseEntity.ok(response.map(homeMenuIntgService::toDTO));
	}

	@GetMapping("/pagination/with-parent-id")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<Page<HomeMenuDTO>> findPaginationWithParentId(@RequestParam(required = false) String code,
			Pageable pageable) {
		Page<HomeMenu> response = homeMenuIntgService.findAllWithParentId(code, pageable);
		return ResponseEntity.ok(response.map(homeMenuIntgService::toDTO));
	}

	@PostMapping("/products")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_CREATE)")
	public ResponseEntity<HomeMenuDTO> addProducts(@Valid @RequestBody HomeMenuV2Request homeMenuRequest) {
		HomeMenu result = homeMenuIntgService.saveHomeMenu(homeMenuRequest);
		return ResponseEntity.ok(homeMenuIntgService.toDTO(result));
	}

	@PutMapping("/products/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_UPDATE)")
	public ResponseEntity<HomeMenuDTO> updateProducts(@Valid @RequestBody HomeMenuV2Request homeMenuRequest,
			@PathVariable String id) {
		homeMenuRequest.setId(id);
		HomeMenu result = homeMenuIntgService.updateHomeMenu(homeMenuRequest);
		return ResponseEntity.ok(homeMenuIntgService.toDTO(result));
	}

	@GetMapping("/products")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_READ)")
	public ResponseEntity<Page<HomeMenuDTO>> findAllProducts(@RequestParam(required = false) String code,
			Pageable pageable) {
		Page<HomeMenu> response = homeMenuIntgService.findAllProducts(code, pageable);
		return ResponseEntity.ok(response.map(homeMenuIntgService::toDTO));
	}

	@DeleteMapping("/products/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.HOME_MENU_DELETE)")
	public ResponseEntity<Void> deleteProducts(@PathVariable String id) {
		HomeMenu homeMenu = homeMenuService.findById(id);
		homeMenuProductService.deleteByHomeMenu(homeMenu);
		return ResponseEntity.ok().build();
	}

}
