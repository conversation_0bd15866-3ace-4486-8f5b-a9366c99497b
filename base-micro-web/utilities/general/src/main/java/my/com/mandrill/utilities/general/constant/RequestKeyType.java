package my.com.mandrill.utilities.general.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RequestKeyType {

	PASSWORD_RESET(false), PIN_RESET(false), PHONE_VERIFICATION(false), REG<PERSON><PERSON>ATION(false), PRE_REGISTRATION(false),
	USER_UPDATE_MOBILE(false), VERIFICATION_CHANGE_MOBILE(true), VERIFICATION_CHANGE_EMAIL(false),
	VERIFICATION_CHANGE_PIN(false), VERIFICATION_ENABLE_BIOMETRIC(false), VERIFICATION_DELETE_ACCOUNT(false),
	VERIFICATION_VIEW_VAULT(false), VERIFIED_USER_INTEREST_REDIRECT_SUBMIT(false),
	VERIFICATION_SUBMIT_USER_INTERESTED(false), CREATE_PASSWORD(false), VERIFICATION_EMAIL(false),
	VERIFICATION_GUEST_APPLY_PRODUCT(true);

	final boolean isMultiUse;

}
