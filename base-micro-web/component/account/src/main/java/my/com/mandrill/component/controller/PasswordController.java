package my.com.mandrill.component.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.request.PasswordValidateRequest;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.service.AppUserService;
import my.com.mandrill.component.service.KeyRequestService;
import my.com.mandrill.component.service.PasswordIntegrationService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("password")
@RequiredArgsConstructor
public class PasswordController {

	private final ValidationService validationService;

	private final PasswordIntegrationService passwordIntegrationService;

	private final KeyRequestService keyRequestService;

	private final AppUserService appUserService;

	private final FeatureFlagOutbound featureFlagOutbound;

	@PostMapping("/validate")
	public ResponseEntity<KeyResponse> validatePin(@Valid @RequestBody PasswordValidateRequest request) {
		log.info("validating key request validating password");
		validationService.validatePasscodeVerificationRequestKey(request.requestKeyType());
		AppUser appUser = appUserService.findByRefNo(SecurityUtil.currentUserLogin());

		log.info("validate and generate key user request");
		int expiryInHours = featureFlagOutbound.getIntegerValue(GlobalSystemConfigurationEnum.KEY_REQUEST_VALID_HOURS);
		UserKeyRequest keyRequest = passwordIntegrationService.validate(request.password(), appUser, () -> {
			log.info("post process execute...");
			return keyRequestService.createRequestKey(appUser.getRefNo(), request.requestKeyType(), expiryInHours);
		});

		log.info("user key request succeed");
		KeyResponse keyResponse = new KeyResponse();
		keyResponse.setKey(keyRequest.getKeyValue());
		return ResponseEntity.ok(keyResponse);
	}

}
