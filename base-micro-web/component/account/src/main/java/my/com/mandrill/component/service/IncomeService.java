package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.EmploymentType;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.IncomeType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.component.dto.model.UserIncome;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import org.springframework.data.domain.Sort;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface IncomeService {

	Income findByIdAndUser(String id, User user);

	Optional<Income> findOptionalByIdAndUser(String id, User user);

	List<Income> findByUserAndIncomeTypeAndEmploymentType(User user, IncomeType incomeType,
			EmploymentType employmentType, Sort sort);

	void existsByUserAndIncomeTypeAndEmploymentType(User user, IncomeType incomeType, EmploymentType employmentType);

	Income findUserIncomeSalary(String userId, EmploymentType employmentType);

	Optional<Income> findOptionalUserIncomeSalary(String userId, EmploymentType employmentType);

	Income create(Income income, User user);

	Income update(String id, Income income, User user);

	Income save(Income income);

	void delete(Income income);

	Optional<Income> findByUserAndEmploymentTypeAndIncomeType(@NonNull User user,
			@NonNull EmploymentType employmentType, @NonNull IncomeType incomeType);

	boolean existsByIncomeTypeId(String incomeTypeId);

	List<UserIncome> findByUserDeletedFalseAndLoginTypeAndCreatedDateGreaterThanEqual(LoginTypeEnum loginTypeEnum,
			Instant time);

	Income findLastByUserAndIncomeType(String userId, String incomeTypeId);

	List<IncomeDTO> getUserCurrentIncome(String refNo);

	BigDecimal findIncome(String userId);

	void createOrUpdateIncome(String userId, BigDecimal incomeAmount);

}
