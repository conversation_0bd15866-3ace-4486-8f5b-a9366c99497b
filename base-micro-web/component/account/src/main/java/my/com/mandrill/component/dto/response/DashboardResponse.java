package my.com.mandrill.component.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.dto.DateDetailDTO;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashboardResponse implements Serializable {

	private String date;

	private DateDetailDTO dateFrom;

	private DateDetailDTO dateTo;

	private DateDetailDTO yearMonth;

	private long installCount = 0L;

	private long uninstallCount = 0L;

	private long screenTimeCount = 0L;

	private long banksCount = 0L;

	private long loansCount = 0L;

	private long propertiesCount = 0L;

	private long vehiclesCount = 0L;

	private long utilitiesCount = 0L;

	private long insuranceCount = 0L;

	private long creditCardCount = 0L;

	private long activeUserCount = 0L;

	private long totalOpenCount = 0L;

	private long totalIosCount = 0L;

	private long totalAndroidCount = 0L;

}
