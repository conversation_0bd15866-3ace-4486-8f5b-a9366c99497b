package my.com.mandrill.component.service.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.DashboardDateType;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.repository.jpa.DashboardActivityRepository;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.DateUtil;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class DownloadsStatisticsReportServiceImpl implements DownloadsStatisticsReportService {

	public static final int MONTHS_IN_A_YEAR = 12;

	public static final Month[] QUARTER_START_MONTHS = { Month.JANUARY, Month.APRIL, Month.JULY, Month.OCTOBER };

	private static final BigDecimal MULTIPLIER_PERCENTAGE_100 = new BigDecimal(100);

	private static final YearMonth START_MONTH_OF_MONTHLY_USER_TARGET = YearMonth.of(2024, 2);

	private final BigQueryService bigQueryService;

	private final DashboardService dashboardService;

	private final AccountSystemConfigurationService accountSystemConfigurationService;

	private final DashboardActivityRepository dashboardActivityRepository;

	private final MonthlyUserTargetService monthlyUserTargetService;

	@Override
	public DownloadsStatisticsReportResponse getResultBetweenDate(LocalDate dateFrom, LocalDate dateTo, ZoneId zoneId) {
		DownloadsStatisticsReportResponse response = new DownloadsStatisticsReportResponse();
		AppDownload appDownload = bigQueryService.countAppDownloadBetweenDate(dateFrom, dateTo);
		long registeredUser = dashboardService.getTotalUser(dateFrom, dateTo, zoneId);

		response.setTotalInstallCount(appDownload.getInstall());
		response.setTotalUninstallCount(appDownload.getUninstall());
		response.setTotalRegisteredUser(registeredUser);
		response.setUninstallPercentage(
				divideAndMultiplyByPercentage(appDownload.getUninstall(), appDownload.getInstall()));
		response.setTotalActiveUser(bigQueryService.countUniqueActiveUser(dateFrom, dateTo));

		Long targetedRegistered = accountSystemConfigurationService.getTargetedRegistered();
		response.setTargetedRegistered(targetedRegistered);

		if (Objects.nonNull(targetedRegistered)) {
			response.setRegisteredPercentage(divideAndMultiplyByPercentage(registeredUser, targetedRegistered));
		}
		else {
			response.setRegisteredPercentage(null);
		}

		return response;
	}

	@Override
	public DownloadsStatisticsReportResponse getResultBetweenDate(Year year, int week, ZoneId zoneId,
			Long registeredUserTarget) {

		DateUtil.DateRange dateRange = DateUtil.DateRange.from(week, year);
		LocalDate dateFrom = dateRange.from();
		LocalDate dateTo = dateRange.to();

		DownloadsStatisticsReportResponse response = new DownloadsStatisticsReportResponse();
		AppDownload appDownload = bigQueryService.countAppDownloadBetweenDate(dateFrom, dateTo);
		long registeredUser = dashboardService.getTotalUser(dateFrom, dateTo, zoneId);

		response.setTotalInstallCount(appDownload.getInstall());
		response.setTotalUninstallCount(appDownload.getUninstall());
		response.setTotalRegisteredUser(registeredUser);
		response.setUninstallPercentage(
				divideAndMultiplyByPercentage(appDownload.getUninstall(), appDownload.getInstall()));
		response.setTotalActiveUser(bigQueryService.countUniqueActiveUser(dateFrom, dateTo));

		if (registeredUserTarget != null) {
			response.setTargetedRegistered(registeredUserTarget);
		}
		else {
			response.setTargetedRegistered(null);
		}

		long previousWeekRegisteredUser = dashboardService.getTotalUser(dateFrom.minusDays(7), dateTo.minusDays(7),
				zoneId);

		response.setRegisteredPercentage(divideAndMultiplyByPercentage((registeredUser - previousWeekRegisteredUser),
				previousWeekRegisteredUser));

		return response;
	}

	@Override
	public BigDecimal divideAndMultiplyByPercentage(Long dividend, Long divisor) {
		if (divisor != null && dividend != null && divisor != 0L) {
			return BigDecimal.valueOf(dividend).divide(BigDecimal.valueOf(divisor), 4, RoundingMode.HALF_UP)
					.multiply(MULTIPLIER_PERCENTAGE_100);
		}
		else {
			return null;
		}
	}

	@Override
	public DownloadsStatisticsReportResponse getResultBetweenDateTime(Instant dateTimeFrom, Instant dateTimeTo,
			ZoneId zoneId) {

		ZonedDateTime zonedDateTimeFrom = dateTimeFrom.atZone(zoneId);
		ZonedDateTime zonedDateTimeTo = dateTimeTo.atZone(zoneId);

		LocalDate zonedDateFrom = zonedDateTimeFrom.toLocalDate();
		LocalDate zonedDateTo = zonedDateTimeTo.toLocalDate();

		long timeFromInMicroseconds = TimeUnit.SECONDS.toMicros(dateTimeFrom.getEpochSecond())
				+ TimeUnit.NANOSECONDS.toMicros(dateTimeFrom.getNano());
		long timeToInMicroseconds = TimeUnit.SECONDS.toMicros(dateTimeTo.getEpochSecond())
				+ TimeUnit.NANOSECONDS.toMicros(dateTimeFrom.getNano());

		AppDownload appDownload = bigQueryService.countAppDownloadBetweenTimestamp(zonedDateFrom, zonedDateTo,
				timeFromInMicroseconds, timeToInMicroseconds);

		long registeredUser = dashboardActivityRepository.countByDateBetweenAndLoginType(dateTimeFrom, dateTimeTo,
				LoginTypeEnum.USER);

		DownloadsStatisticsReportResponse response = new DownloadsStatisticsReportResponse();
		response.setTotalInstallCount(appDownload.getInstall());
		response.setTotalUninstallCount(appDownload.getUninstall());
		response.setTotalRegisteredUser(registeredUser);
		response.setRegisteredPercentage(divideAndMultiplyByPercentage(registeredUser, appDownload.getInstall()));
		response.setUninstallPercentage(
				divideAndMultiplyByPercentage(appDownload.getUninstall(), appDownload.getInstall()));
		response.setTotalActiveUser(bigQueryService.countUniqueActiveUserBetweenTimestamp(zonedDateFrom, zonedDateTo,
				timeFromInMicroseconds, timeToInMicroseconds));

		return response;

	}

	@Override
	public List<DownloadsStatisticsReportDailyResponse> getDailyResult(Year year, int week, ZoneId zoneId) {

		DateUtil.DateRange dateRange = DateUtil.DateRange.from(week, year);
		LocalDate dateTo = dateRange.to();

		List<AppDownloadByDateDTO> appDownloadList = bigQueryService.countAppDownloadByDateType(DashboardDateType.DAILY,
				dateTo, dateTo);
		List<ActiveUserByDateDTO> activeUserList = bigQueryService
				.countUniqueActiveUserByDateType(DashboardDateType.DAILY, dateTo, dateTo);

		Map<LocalDate, DownloadsStatisticsReportDailyResponse> dateAndResponseMap = new HashMap<>();

		for (AppDownloadByDateDTO appDownload : appDownloadList) {
			LocalDate date = appDownload.getDateFrom();
			DownloadsStatisticsReportDailyResponse dailyResponse = dateAndResponseMap.computeIfAbsent(date, d -> {
				DownloadsStatisticsReportDailyResponse response = new DownloadsStatisticsReportDailyResponse();
				response.setDate(my.com.mandrill.utilities.general.util.DateUtil.convertToDateDetail(d));
				return response;
			});

			dailyResponse.setInstallCount(appDownload.getInstall());
			dailyResponse.setUninstallCount(appDownload.getUninstall());
			dailyResponse.setRegisteredUser(dashboardService.getTotalUser(date, date, zoneId));
		}

		for (ActiveUserByDateDTO activeUser : activeUserList) {
			LocalDate date = activeUser.getDateFrom();
			DownloadsStatisticsReportDailyResponse dailyResponse = dateAndResponseMap.computeIfAbsent(date, d -> {
				DownloadsStatisticsReportDailyResponse response = new DownloadsStatisticsReportDailyResponse();
				response.setDate(my.com.mandrill.utilities.general.util.DateUtil.convertToDateDetail(d));
				return response;
			});

			dailyResponse.setActiveUser(activeUser.getActiveUser());

		}

		List<DownloadsStatisticsReportDailyResponse> responseList = new ArrayList<>(dateAndResponseMap.values());

		responseList.sort((response1, response2) -> response2.getDate().getStandard()
				.compareTo(response1.getDate().getStandard()));

		return responseList;
	}

	@Override
	public OsDownloadsLastUpdatedResponse getOsDownloadsLastUpdated(ZoneId zoneId) {
		OsDownload todaysOsDownloads = bigQueryService.countOSDownloadBetweenDate(LocalDate.now(zoneId),
				LocalDate.now(zoneId));
		OsDownload totalOsDownloads = bigQueryService.countOSDownloadBetweenDate(DashboardDateType.APP_STARTING_DATE,
				LocalDate.now(zoneId));
		OsDownloadsLastUpdatedResponse response = new OsDownloadsLastUpdatedResponse();

		response.setTodaysAndroidDownloads(todaysOsDownloads.getAndroid());
		response.setTodaysIosDownloads(todaysOsDownloads.getIos());
		response.setTotalAndroidDownloads(totalOsDownloads.getAndroid());
		response.setTotalIosDownloads(totalOsDownloads.getIos());
		return response;

	}

	@Override
	public List<DateRangeAllDTO> getDateRangeOfWeek(@NonNull Year year) {
		int totalWeekNumber = DateUtil.getNumberOfWeeksInYear(year);
		return getDateRangeOfWeek(year, totalWeekNumber);
	}

	@Override
	public List<DateRangeAllDTO> getDateRangeOfWeekFull(@NonNull Year year) {
		int totalWeekNumber = DateUtil.getNumberOfWeeksInYearFull(year);
		return getDateRangeOfWeek(year, totalWeekNumber);
	}

	private List<DateRangeAllDTO> getDateRangeOfWeek(@NonNull Year year, int totalWeekNumber) {
		if (totalWeekNumber == 0) {
			return new ArrayList<>();
		}

		List<DateRangeAllDTO> resultList = new ArrayList<>();

		int startWeek = (year.equals(Year.of(DashboardDateType.APP_STARTING_DATE.getYear())))
				? DateUtil.getWeekNumberOfDate(DashboardDateType.APP_STARTING_DATE)
				: DateUtil.DASHBOARD_FIRST_WEEK_VALUE;

		for (int week = startWeek; week <= totalWeekNumber; week++) {
			DateRangeAllDTO result = new DateRangeAllDTO();
			DateUtil.DateRange dateRange = DateUtil.DateRange.from(week, year);

			result.setWeekNumber(week);
			result.setDateFrom(dateRange.from());
			result.setDateTo(dateRange.to());
			resultList.add(result);
		}

		return resultList;

	}

	@Override
	public List<DateRangeAllDTO> getMonthlyDateRange(Year year) {
		List<DateRangeAllDTO> dateRange = new ArrayList<>();

		int startMonth = (year.equals(Year.of(START_MONTH_OF_MONTHLY_USER_TARGET.getYear())))
				? START_MONTH_OF_MONTHLY_USER_TARGET.getMonthValue() : DateUtil.DASHBOARD_FIRST_MONTH_VALUE;

		for (int month = startMonth; month <= MONTHS_IN_A_YEAR; month++) {
			YearMonth yearMonth = YearMonth.of(year.getValue(), month);
			LocalDate firstDayOfMonth = yearMonth.atDay(1);
			LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();
			DateRangeAllDTO dateRangeAllDTO = new DateRangeAllDTO();
			dateRangeAllDTO.setDateFrom(firstDayOfMonth);
			dateRangeAllDTO.setDateTo(lastDayOfMonth);
			dateRange.add(dateRangeAllDTO);
		}

		return dateRange;

	}

	@Override
	public List<MonthlyUserTargetAndActualResponse> getRegisteredUserByDateRange(List<DateRangeAllDTO> dateRangeDTOList,
			ZoneId zoneId) {
		List<MonthlyUserTargetAndActualResponse> responseList = new ArrayList<>();

		dateRangeDTOList.forEach(dateRangeDTO -> {
			MonthlyUserTargetAndActualResponse response = new MonthlyUserTargetAndActualResponse();
			int month = dateRangeDTO.getDateFrom().getMonthValue();
			Year year = Year.of(dateRangeDTO.getDateFrom().getYear());

			response.setMonth(month);
			response.setYear(year);
			response.setRegistered(
					dashboardService.getTotalUser(dateRangeDTO.getDateFrom(), dateRangeDTO.getDateTo(), zoneId));
			response.setTarget(monthlyUserTargetService.findTargetByYearAndMonth(year, month));
			responseList.add(response);
		});

		return responseList;
	}

	@Override
	public List<DateRangeAllDTO> getQuarterlyDateRange(Year year) {
		List<DateRangeAllDTO> quarterDateRange = new ArrayList<>();

		int startQuarter = (year.equals(Year.of(DashboardDateType.APP_STARTING_DATE.getYear())))
				? DashboardDateType.APP_STARTING_DATE.get(IsoFields.QUARTER_OF_YEAR)
				: DateUtil.DASHBOARD_FIRST_QUARTER_VALUE;

		for (int quarter = startQuarter; quarter < QUARTER_START_MONTHS.length + 1; quarter++) {

			Month startMonth = QUARTER_START_MONTHS[quarter - 1];
			LocalDate quarterFirstDay = LocalDate.of(year.getValue(), startMonth, 1);
			LocalDate quarterLastDay = quarterFirstDay.plusMonths(2).with(TemporalAdjusters.lastDayOfMonth());

			DateRangeAllDTO dateRange = new DateRangeAllDTO();
			dateRange.setQuarter(quarter);
			dateRange.setDateFrom(quarterFirstDay);
			dateRange.setDateTo(quarterLastDay);
			quarterDateRange.add(dateRange);
		}

		return quarterDateRange;

	}

	@Override
	public List<DownloadsAndRegisteredUserResponse> getDownloadsAndRegisteredUserByDateRange(
			List<DateRangeAllDTO> dateRangeDTOList, Year year, ZoneId zoneId) {
		List<DownloadsAndRegisteredUserResponse> responseList = new ArrayList<>();

		dateRangeDTOList.forEach(dateRangeDTO -> {
			DownloadsAndRegisteredUserResponse response = new DownloadsAndRegisteredUserResponse();

			LocalDate dateFrom = dateRangeDTO.getDateFrom();
			LocalDate dateTo = dateRangeDTO.getDateTo();
			long registeredUser = dashboardService.getTotalUser(dateFrom, dateTo, zoneId);

			response.setQuarter(dateRangeDTO.getQuarter());
			response.setYear(year);
			response.setDownloads(bigQueryService.countAppDownloadBetweenDate(dateFrom, dateTo).getInstall());
			response.setRegistered(registeredUser);
			response.setRegisteredPercentage(divideAndMultiplyByPercentage(registeredUser,
					accountSystemConfigurationService.getTargetedRegistered()));

			responseList.add(response);
		});

		return responseList;
	}

}
