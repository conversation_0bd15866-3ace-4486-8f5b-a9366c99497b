package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.DashboardActivity;
import my.com.mandrill.component.dto.model.DashboardBarChartProjection;
import my.com.mandrill.component.dto.model.DashboardProjection;
import my.com.mandrill.utilities.general.constant.DashboardCategory;
import my.com.mandrill.utilities.general.constant.DashboardType;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface DashboardActivityRepository extends JpaRepository<DashboardActivity, String> {

	Optional<DashboardActivity> findFirstByCategoryAndTypeAndCreatedDateBetweenOrderByCreatedDateDesc(
			@NonNull DashboardCategory category, @NonNull DashboardType type, @NonNull Instant createdDateStart,
			@NonNull Instant createdDateEnd);

	@Query("""
			select date(d.createdDate) as date, d.type as type, sum(d.value) as value from DashboardActivity d
			where d.category = :category and d.createdDate between :createdDateStart and :createdDateEnd
			group by date, type
			order by date asc
			""")
	List<DashboardProjection> findByCategoryAndDateBetween(DashboardCategory category, Instant createdDateStart,
			Instant createdDateEnd);

	@Query("""
			select d.type as type, sum(d.value) as value from DashboardActivity d
			where d.category = :category and d.createdDate between :createdDateStart and :createdDateEnd
			group by type
			""")
	List<DashboardBarChartProjection> findBarChartByCategoryAndDateBetween(DashboardCategory category,
			Instant createdDateStart, Instant createdDateEnd);

	@Query("""
			select count(distinct u.id) as user_count
			from User u
			where u.createdDate between :createdDateStart and :createdDateEnd
			and u.loginType = :loginType
			and u.phoneNumber is not null
			""")
	long countByDateBetweenAndLoginType(Instant createdDateStart, Instant createdDateEnd, LoginTypeEnum loginType);

}
