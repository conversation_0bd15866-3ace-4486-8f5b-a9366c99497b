package my.com.mandrill.utilities.general.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RsmClosingBalanceResponse implements Serializable {

	private Instant closingDate;

	private BigDecimal pointAwarded;

	private BigDecimal pointSpent;

	private BigDecimal closingBalance;

	private BigDecimal totalExpiredPoint;

}
