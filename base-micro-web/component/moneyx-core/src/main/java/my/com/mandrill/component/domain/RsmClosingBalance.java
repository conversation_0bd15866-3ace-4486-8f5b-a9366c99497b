package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.core.audit.AuditSectionTimeSeries;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "rsm_closing_balance")
public class RsmClosingBalance extends AuditSectionTimeSeries {

	@Column(name = "closing_date", unique = true, nullable = false)
	private Instant closingDate;

	@Column(name = "point_awarded", nullable = false)
	private BigDecimal pointAwarded;

	@Column(name = "point_spent", nullable = false)
	private BigDecimal pointSpent;

	@Column(name = "closing_balance", nullable = false)
	private BigDecimal closingBalance;

	@Column(name = "total_expired_point", nullable = false)
	private BigDecimal totalExpiredPoint;

	@Enumerated(EnumType.STRING)
	@NotNull
	@Column(name = "source")
	private SourceSystemEnum source;

}
