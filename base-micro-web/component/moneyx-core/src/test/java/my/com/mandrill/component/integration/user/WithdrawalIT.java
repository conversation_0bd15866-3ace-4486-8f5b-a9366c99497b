package my.com.mandrill.component.integration.user;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.PointWithdrawal;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.PointTransactionRepository;
import my.com.mandrill.component.repository.PointWithdrawalRepository;
import my.com.mandrill.component.repository.RsmDetailRepository;
import my.com.mandrill.component.repository.RsmHeaderRepository;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@Slf4j
public class WithdrawalIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private PointWithdrawalRepository pointWithdrawalRepository;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@Autowired
	private RsmHeaderRepository rsmHeaderRepository;

	@Autowired
	private RsmDetailRepository rsmDetailRepository;

	private final List<String> rsmHeaderIds = List.of("01JJ9WHXE27EX28F3XF0W3X4T7");

	private final List<String> rsmDetailIds = Arrays.asList("01JJ9WKBYXCH4Y07SAJ5DDDMKN", "01JJ9WKHY68Z0B6P9A6SGW30B5");

	private final List<String> pointTransactionsIds = List.of("01JJ9X2EF8Z6SQBGWCY4EQQJM4");

	@AfterEach
	public void tearDown() {
		pointWithdrawalRepository.deleteAll();
		pointTransactionRepository.deleteAll();
		rsmDetailRepository.deleteAll();
		rsmHeaderRepository.deleteAll();
	}

	@SneakyThrows
	@Test
	public void multipleTransactionId_onlyProcessedOneRequest_success() {
		int threadCount = 3;

		ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
		CountDownLatch latch = new CountDownLatch(threadCount);

		for (int i = 0; i < threadCount; i++) {
			executorService.execute(() -> {
				try {
					mockMvc.perform(post("/payment/point/v1/withdrawal")
							.header("Authorization", "Bearer " + getUserSecretToken()).content(getRequest())
							.contentType(MediaType.APPLICATION_JSON))
							.andExpect(MockMvcResultMatchers.status().isNoContent());
				}
				catch (Exception e) {
					throw new RuntimeException(e);
				}
				finally {
					latch.countDown();
				}
			});
		}

		latch.await();
		executorService.shutdown();

		List<PointWithdrawal> pointWithdrawal = pointWithdrawalRepository.findAll();
		Assertions.assertEquals(1, pointWithdrawal.size(), "Only one PointWithdrawal should be processed");
		Assertions.assertEquals(pointWithdrawal.get(0).getRequestedAmount().setScale(2, RoundingMode.UNNECESSARY),
				BigDecimal.valueOf(5000).setScale(2, RoundingMode.UNNECESSARY));
		Assertions.assertEquals(pointWithdrawal.get(0).getAdminFee().setScale(2, RoundingMode.UNNECESSARY),
				BigDecimal.valueOf(500).setScale(2, RoundingMode.UNNECESSARY));
		Assertions.assertEquals(pointWithdrawal.get(0).getReceivableAmount().setScale(2, RoundingMode.UNNECESSARY),
				BigDecimal.valueOf(4500).setScale(2, RoundingMode.UNNECESSARY));

		pointTransactionRepository.deleteAllById(pointTransactionsIds);
		rsmDetailRepository.deleteAllById(rsmDetailIds);
		rsmHeaderRepository.deleteAllById(rsmHeaderIds);
		pointWithdrawalRepository.deleteAll();

	}

	@SneakyThrows
	@Test
	public void withdrawal_balanceNotEnough_error() {
		String uuid = UUID.randomUUID().toString();
		mockMvc.perform(post("/payment/point/v1/withdrawal").header("Transaction-Id", uuid)
				.header("Authorization", "Bearer " + getUserSecretToken()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(jsonPath("$.errorCode").value(ErrorCodeGlobalEnum.POINT_BALANCE_NOT_ENOUGH.getCode()))
				.andExpect(jsonPath("$.message").value(ErrorCodeGlobalEnum.POINT_BALANCE_NOT_ENOUGH.getDescription()));

		pointTransactionRepository.deleteAllById(pointTransactionsIds);
		rsmDetailRepository.deleteAllById(rsmDetailIds);
		rsmHeaderRepository.deleteAllById(rsmHeaderIds);
		pointWithdrawalRepository.deleteAll();
	}

	@SneakyThrows
	@Test
	public void withdrawal_balanceNotEnoughWithPending_error() {
		String uuid = UUID.randomUUID().toString();
		mockMvc.perform(post("/payment/point/v1/withdrawal").header("Transaction-Id", uuid)
				.header("Authorization", "Bearer " + getUserSecretToken()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(jsonPath("$.errorCode").value(ErrorCodeGlobalEnum.POINT_BALANCE_NOT_ENOUGH.getCode()))
				.andExpect(jsonPath("$.message").value(ErrorCodeGlobalEnum.POINT_BALANCE_NOT_ENOUGH.getDescription()));
		pointTransactionRepository.deleteAllById(pointTransactionsIds);
		rsmDetailRepository.deleteAllById(rsmDetailIds);
		rsmHeaderRepository.deleteAllById(rsmHeaderIds);
		pointWithdrawalRepository.deleteAll();
	}

	@SneakyThrows
	@Test
	public void withdrawal_ekycNotSuccess_error() {
		String uuid = UUID.randomUUID().toString();
		mockMvc.perform(post("/payment/point/v1/withdrawal").header("Transaction-Id", uuid)
				.header("Authorization", "Bearer " + getUserSecretToken()).content(getRequest())
				.contentType(MediaType.APPLICATION_JSON)).andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(jsonPath("$.errorCode").value(ErrorCodeGlobalEnum.EKYC_NOT_SUCCESS_YET.getCode()))
				.andExpect(jsonPath("$.message").value(ErrorCodeGlobalEnum.EKYC_NOT_SUCCESS_YET.getDescription()));
	}

	@SneakyThrows
	@Test
	public void withdrawal_paymentAccountNotApproved_error() {
		String uuid = UUID.randomUUID().toString();
		mockMvc.perform(post("/payment/point/v1/withdrawal")
				.header("Transaction-Id", uuid).header("Authorization", "Bearer " + getUserSecretToken())
				.content(getRequest()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isBadRequest())
				.andExpect(
						jsonPath("$.errorCode").value(ErrorCodeGlobalEnum.PAYMENT_ACCOUNT_NOT_APPROVED_YET.getCode()))
				.andExpect(jsonPath("$.message")
						.value(ErrorCodeGlobalEnum.PAYMENT_ACCOUNT_NOT_APPROVED_YET.getDescription()));
	}

	@SneakyThrows
	@Test
	public void withdrawal_unauthorized_error() {
		mockMvc.perform(post("/payment/point/v1/withdrawal").contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isUnauthorized());
	}

	@SneakyThrows
	@Test
	public void withdrawal_invalidContentType_error() {
		mockMvc.perform(post("/payment/point/v1/withdrawal").header("Authorization", "Bearer " + getUserSecretToken())
				.contentType(MediaType.TEXT_PLAIN)).andExpect(MockMvcResultMatchers.status().isUnsupportedMediaType());
	}

}
