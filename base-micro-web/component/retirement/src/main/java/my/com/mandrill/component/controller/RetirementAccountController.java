package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.RetirementAccount;
import my.com.mandrill.component.dto.request.RetirementAccountCreateRequest;
import my.com.mandrill.component.dto.request.RetirementAccountUpdateRequest;
import my.com.mandrill.component.dto.response.RetirementAccountResponse;
import my.com.mandrill.component.service.RetirementAccountIntegrationService;
import my.com.mandrill.component.service.RetirementAccountService;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("retirement/v1/accounts")
@RequiredArgsConstructor
public class RetirementAccountController {

	private final AccountFeignClient accountFeignClient;

	private final RetirementAccountIntegrationService retirementAccountIntegrationService;

	private final RetirementAccountService retirementAccountService;

	@PostMapping
	public ResponseEntity<RetirementAccountResponse> create(
			@Valid @RequestBody RetirementAccountCreateRequest request) {

		String userId = SecurityUtil.currentUserId();

		RetirementAccount retirementAccount = retirementAccountIntegrationService.processRetirementAccount(userId,
				request);

		retirementAccountIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.ASSETS,
				NetWorthType.RETIREMENT, NetWorthSource.RETIREMENT);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toRetirementAccountResponse(retirementAccount));

	}

	@PutMapping("{id}")
	public ResponseEntity<RetirementAccountResponse> update(@PathVariable String id,
			@Valid @RequestBody RetirementAccountUpdateRequest request) {

		String userId = SecurityUtil.currentUserId();

		RetirementAccount retirementAccount = retirementAccountIntegrationService.updateRetirementAccount(request, id,
				userId);

		retirementAccountIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.ASSETS,
				NetWorthType.RETIREMENT, NetWorthSource.RETIREMENT);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toRetirementAccountResponse(retirementAccount));
	}

	@GetMapping("{id}")
	public ResponseEntity<RetirementAccountResponse> findById(@PathVariable String id) {

		String userId = SecurityUtil.currentUserId();

		RetirementAccount retirementAccount = retirementAccountService.findByIdAndUserId(id, userId);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toRetirementAccountResponse(retirementAccount));
	}

	@GetMapping
	public ResponseEntity<List<RetirementAccountResponse>> findAllByUserId() {

		String userId = SecurityUtil.currentUserId();

		List<RetirementAccount> retirementAccounts = retirementAccountService.findAllByUserId(userId);

		return ResponseEntity
				.ok(retirementAccounts.stream().map(MapStructConverter.MAPPER::toRetirementAccountResponse).toList());
	}

	@Hidden
	@GetMapping("integration/net-worth")
	public NetWorthDTO calculateNetWorth() {
		String userId = SecurityUtil.currentUserId();
		return retirementAccountIntegrationService.calculateNetWorth(userId);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@DeleteMapping("{id}")
	public void delete(@PathVariable String id) {
		String userId = SecurityUtil.currentUserId();
		retirementAccountIntegrationService.deleteRetirementAccount(id, userId);

		retirementAccountIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.ASSETS,
				NetWorthType.RETIREMENT, NetWorthSource.RETIREMENT);
	}

}
