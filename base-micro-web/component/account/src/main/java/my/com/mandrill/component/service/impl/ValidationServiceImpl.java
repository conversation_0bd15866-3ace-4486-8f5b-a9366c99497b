package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.DashboardDateType;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.request.EpfContributionRequest;
import my.com.mandrill.component.dto.request.HomeMenuProductRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.DateUtil;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.general.constant.Constant;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.service.GlobalValidationService;
import my.com.mandrill.utilities.general.util.CalculationUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import my.com.mandrill.utilities.general.util.StringNullCheckerUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
public class ValidationServiceImpl implements ValidationService {

	public static final DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("dd-MMM-yyyy");

	final static int NUMBER_OF_MONTHS_IN_YEAR = 12;

	private final AccountService accountService;

	private final UserService userService;

	private final InstitutionService institutionService;

	private final BusinessNatureService businessNatureService;

	private final CommonFeignClient commonFeignClient;

	private final CurrencyService currencyService;

	private final EducationLevelService educationLevelService;

	private final EmploymentTypeService employmentTypeService;

	private final NationalityService nationalityService;

	private final OccupationGroupService occupationGroupService;

	private final InterestService interestService;

	private final FinancialGoalService financialGoalService;

	private final SegmentService segmentService;

	private final AuthorityService authorityService;

	private final IncomeService incomeService;

	private final IncomeTypeService incomeTypeService;

	private final EmailBlacklistService emailBlacklistService;

	private final DeviceBindingService deviceBindingService;

	private final HomeMenuMapService homeMenuMapService;

	private final FeatureFlagOutbound featureFlagOutbound;

	@Override
	public void validateCurrentLoginAndInstitution(String curIns) {
		String username = SecurityUtil.currentUserLogin();
		boolean flag = userService.existsByUsernameAndInstitutionsIdAndLoginType(username, curIns, LoginTypeEnum.ADMIN);
		if (!flag) {
			throw ExceptionPredicate.currentUserAndInstitutionNotMatch(username, curIns).get();
		}

	}

	@Override
	public void validateDeleteAuthority(String authorityId) {
		if (userService.existsByAuthoritiesId(authorityId)) {
			throw new BusinessException(ErrorCodeEnum.DELETE_DEPENDENCY_FAIL);
		}
	}

	@Override
	public void validateDeleteSegment(String segmentId) {
		if (userService.existsBySegmentId(segmentId)) {
			throw new BusinessException(ErrorCodeEnum.DELETE_DEPENDENCY_FAIL);
		}
	}

	@Override
	public void validateSegmentExist(String code, String name) {
		if (segmentService.existsByCodeIgnoreCaseAndNameIgnoreCase(code, name)) {
			throw new BusinessException(ErrorCodeEnum.SEGMENT_EXISTS);
		}
	}

	@Override
	public void validateInstitutionExist(String name) {
		if (institutionService.existsByName(name)) {
			throw new BusinessException(ErrorCodeEnum.INSTITUTION_EXISTS);
		}
	}

	@Override
	public void validateIncomeTypeExist(String code) {
		if (incomeTypeService.existsByCodeIgnoreCase(code)) {
			throw new BusinessException(ErrorCodeEnum.INCOME_TYPE_CODE_EXISTS);
		}
	}

	@Override
	public void validateDeleteIncomeType(String incomeTypeId) {
		if (incomeService.existsByIncomeTypeId(incomeTypeId)) {
			throw new BusinessException(ErrorCodeEnum.DELETE_DEPENDENCY_FAIL);
		}
	}

	@Override
	public Institution validateInstitution(String currentInstitutionId) {
		return institutionService.findByIdAndActiveTrue(currentInstitutionId)
				.orElseThrow(ExceptionPredicate.institutionNotFound(currentInstitutionId));
	}

	@Override
	public void validateCreateAdminUser(User user) {
		// business validation
		if (user.getEmploymentType() != null && !StringUtils.isBlank(user.getEmploymentType().getId())) {
			user.setEmploymentType(employmentTypeService.findById(user.getEmploymentType().getId()));
		}
		if (user.getCountry() != null && !StringUtils.isBlank(user.getCountry().getId())) {
			user.setCountryId(commonFeignClient.getCountry(user.getCountry().getId()).getId());
		}

		Set<Institution> institutionList = new HashSet<>();
		user.getAuthorities().forEach(authority -> {
			Authority auth = authorityService.findById(authority.getId())
					.orElseThrow(ExceptionPredicate.authorityByIdNotFound(authority.getId()));
			Institution ins = institutionService.findByIdAndActiveTrue(auth.getInstitution().getId())
					.orElseThrow(ExceptionPredicate.institutionNotFound(auth.getInstitution().getId()));
			if (!ins.getPath().contains(user.getCurrentInstitution().getId())) {
				throw new BusinessException(ErrorCodeEnum.INSTITUTION_NOT_RELATED);
			}
			institutionList.add(ins);
		});
		if (!institutionList.isEmpty()) {
			user.setInstitutions(institutionList);
		}
	}

	@Override
	public void validateUpdateAdminUser(User existingUser, User user) {
		// business validation

		// mapping
		if (user.getActive() != null) {
			existingUser.setActive(user.getActive());
		}
		if (user.getPhoneCountry() != null) {
			existingUser.setPhoneCountry(user.getPhoneCountry());
		}
		if (user.getPhoneNumber() != null) {
			existingUser.setPhoneNumber(user.getPhoneNumber());
		}
		Set<Institution> institutionList = new HashSet<>();
		user.getAuthorities().forEach(authority -> {
			Authority auth = authorityService.findById(authority.getId())
					.orElseThrow(ExceptionPredicate.authorityByIdNotFound(authority.getId()));
			Institution ins = institutionService.findByIdAndActiveTrue(auth.getInstitution().getId())
					.orElseThrow(ExceptionPredicate.institutionNotFound(auth.getInstitution().getId()));
			if (!ins.getPath().contains(existingUser.getCurrentInstitution().getId())) {
				throw new BusinessException(ErrorCodeEnum.INSTITUTION_NOT_RELATED);
			}
			institutionList.add(ins);
		});
		if (!institutionList.isEmpty()) {
			existingUser.setInstitutions(institutionList);
		}
		setUserInfo(existingUser, user);
	}

	@Override
	public void validateAuthority(User existingUser, User user, String institutionId) {
		if (user.getAuthorities() != null) {
			List<Authority> othersAuthInstitution = existingUser.getAuthorities().stream()
					.filter(auth -> !auth.getInstitution().getId().equals(institutionId)).toList();
			Set<Authority> auth = user.getAuthorities();
			if (!Constant.DEFAULT_INSTITUTION_ID.equals(institutionId)) {
				// to keep other authorities institution when update user
				auth.addAll(othersAuthInstitution);
			}
			existingUser.setAuthorities(auth);
		}
	}

	@Override
	public void validateUpdateUser(User existingUser, User user) {
		// business validation
		if (user.getEmail() != null && !user.getEmail().equalsIgnoreCase(existingUser.getEmail())) {
			this.validateEmailBlacklist(user.getEmail());
			accountService.checkIfEmailExists(user.getEmail(), user.getLoginType());
		}

		// mapping
		setUserInfo(existingUser, user);

	}

	@Override
	public void validateEmailBlacklist(final String email) {
		if (StringUtils.isBlank(email)) {
			return;
		}

		String[] emailPattern = email.split("@");
		if (this.emailBlacklistService.findByEmailIgnoreCase(emailPattern[1]).isPresent()) {
			throw new BusinessException(ErrorCodeEnum.EMAIL_BLACKLIST);
		}
	}

	@Override
	public void validateDateType(@NonNull DashboardDateType dateType, @Nullable LocalDate dateFrom,
			@Nullable LocalDate dateTo, @Nullable YearMonth yearMonth) {
		switch (dateType) {
			case DAILY -> {
				if (dateFrom == null) {
					throw new BusinessException(ErrorCodeEnum.DATE_FROM_IS_MISSING);
				}
			}
			case WEEKLY -> {
				if (dateFrom == null) {
					throw new BusinessException(ErrorCodeEnum.DATE_FROM_IS_MISSING);
				}
				else if (dateTo == null) {
					throw new BusinessException(ErrorCodeEnum.DATE_TO_IS_MISSING);
				}
				else if (!dateTo.isAfter(dateFrom)) {
					throw new BusinessException(ErrorCodeEnum.INVALID_DATE_RANGE);
				}
			}
			case MONTHLY -> {
				if (yearMonth == null) {
					throw new BusinessException(ErrorCodeEnum.YEAR_MONTH_IS_MISSING);
				}
			}
		}
	}

	private void setUserInfo(User existingUser, User user) {
		// business validation
		validateUserBusinessNature(existingUser, user);
		validateUserCountry(existingUser, user);
		validateUserCurrency(existingUser, user);
		validateUserEducationLevel(existingUser, user);
		validateUserEmploymentType(existingUser, user);
		validateUserNationality(existingUser, user);
		validateUserOccupationGroup(existingUser, user);
		validateUserState(existingUser, user);
		validateUserInterests(existingUser, user);
		validateUserFinancialGoals(existingUser, user);

		validateUserSegment(existingUser, user);

		// mapping
		validateUserFullName(existingUser, user);
		validateUserAddress1(existingUser, user);

		if (user.getEmail() != null) {
			existingUser.setEmail(user.getEmail());
			existingUser.setEmailVerified(
					existingUser.getEmailVerified() && user.getEmail().equals(existingUser.getEmail()));
		}
		if (user.getAddress2() != null) {
			existingUser.setAddress2(user.getAddress2());
		}
		if (user.getAddress3() != null) {
			existingUser.setAddress3(user.getAddress3());
		}
		if (user.getPostcode() != null) {
			existingUser.setPostcode(user.getPostcode());
		}
		if (user.getGender() != null) {
			existingUser.setGender(user.getGender());
		}
		if (user.getNric() != null) {
			existingUser.setNric(user.getNric());
		}
		if (user.getMaritalStatus() != null) {
			existingUser.setMaritalStatus(user.getMaritalStatus());
		}
		if (user.getEthnicity() != null) {
			existingUser.setEthnicity(user.getEthnicity());
		}
		if (user.getReligion() != null) {
			existingUser.setReligion(user.getReligion());
		}
		if (user.getEpfContribution() != null) {
			existingUser.setEpfContribution(user.getEpfContribution());
		}
		if (user.getSocso() != null) {
			existingUser.setSocso(user.getSocso());
		}
		if (user.getEis() != null) {
			existingUser.setEis(user.getEis());
		}
		if (user.getSelfEmployedName() != null) {
			existingUser.setSelfEmployedName(user.getSelfEmployedName());
		}
		if (user.getAge() != null) {
			validateAge(user.getAge());
			existingUser.setAge(user.getAge());
		}
		if (user.getArmy() != null) {
			existingUser.setArmy(user.getArmy());
		}
		if (user.getPassport() != null) {
			existingUser.setPassport(user.getPassport());
		}
		// Set bloodType if present in request, otherwise keep existing
		if (user.getBloodType() != null) {
			existingUser.setBloodType(user.getBloodType());
		}
		// PRJA-959 requirements:
		// https://mandrill.atlassian.net/browse/PRJA-959?focusedCommentId=17680
		// DOB field can be set back to null on request is an empty String so no need to
		// check for null
		if (Objects.nonNull(user.getDob())) {
			validateAge(user.getDob());
		}
		existingUser.setDob(user.getDob());
	}

	/*
	 * @see <a href="https://mandrill.atlassian.net/browse/PRJA-1893">PRJA-1895 ticket</a>
	 */
	@Override
	public void validateAge(Integer userAge) {
		if (Objects.nonNull(userAge)
				&& (userAge < GlobalValidationService.MINIMUM_AGE || userAge > GlobalValidationService.MAXIMUM_AGE)) {
			throw new BusinessException(ErrorCodeEnum.AGE_RESTRICTION);
		}
	}

	/*
	 * @see <a href="https://mandrill.atlassian.net/browse/PRJA-1893">PRJA-1895 ticket</a>
	 */
	private void validateAge(LocalDate dob) {
		int age = CalculationUtil.calculateAgeByDob(dob);
		if (age < GlobalValidationService.MINIMUM_AGE || age > GlobalValidationService.MAXIMUM_AGE) {
			throw new BusinessException(ErrorCodeEnum.AGE_RESTRICTION);
		}
	}

	private void validateUserFullName(User existingUser, User user) {
		if (user.getFullName() != null) {
			if (!StringUtils.isBlank(user.getFullName())) {
				existingUser.setFullName(user.getFullName());
			}
			else {
				throw new BusinessException(ErrorCodeEnum.FULL_NAME_MANDATORY);
			}
		}
	}

	private void validateUserAddress1(User existingUser, User user) {
		if (user.getAddress1() != null) {
			if (!StringUtils.isBlank(user.getAddress1())) {
				existingUser.setAddress1(user.getAddress1());
			}
			else {
				throw new BusinessException(ErrorCodeEnum.ADDRESS1_MANDATORY);
			}
		}
	}

	private void validateUserSegment(User existingUser, User user) {
		if (user.getSegment() != null) {
			if (StringUtils.isBlank(user.getSegment().getId())) {
				existingUser.setSegment(null);
			}
			else {
				existingUser.setSegment(segmentService.findById(user.getSegment().getId()));

				// TODO: JIRA Requirements: https://mandrill.atlassian.net/browse/PRJA-206
				// existingUser.setUserJourney(UserJourneyEnum.SEGMENT);
			}
		}
	}

	private void validateUserCountry(User existingUser, User user) {
		if (user.getCountry() != null) {
			if (StringUtils.isBlank(user.getCountry().getId())) {
				existingUser.setCountryId(null);
			}
			else {
				existingUser.setCountryId(commonFeignClient.getCountry(user.getCountry().getId()).getId());
			}
		}
	}

	private void validateUserBusinessNature(User existingUser, User user) {
		if (user.getBusinessNature() != null) {
			if (StringUtils.isBlank(user.getBusinessNature().getId())) {
				existingUser.setBusinessNature(null);
			}
			else {
				existingUser.setBusinessNature(businessNatureService.findById(user.getBusinessNature().getId()));
			}
		}
	}

	private void validateUserCurrency(User existingUser, User user) {
		if (user.getCurrency() != null) {
			if (StringUtils.isBlank(user.getCurrency().getId())) {
				existingUser.setCurrency(null);
			}
			else {
				existingUser.setCurrency(currencyService.findById(user.getCurrency().getId()));
			}
		}
	}

	private void validateUserEducationLevel(User existingUser, User user) {
		if (user.getEducationLevel() != null) {
			if (StringUtils.isBlank(user.getEducationLevel().getId())) {
				existingUser.setEducationLevel(null);
			}
			else {
				existingUser.setEducationLevel(educationLevelService.findById(user.getEducationLevel().getId())
						.orElseThrow(ExceptionPredicate.educationLevelNotFound(user.getEducationLevel().getId())));
			}
		}
	}

	private void validateUserEmploymentType(User existingUser, User user) {
		if (user.getEmploymentType() != null) {
			if (StringUtils.isBlank(user.getEmploymentType().getId())) {
				existingUser.setEmploymentType(null);
			}
			else {
				existingUser.setEmploymentType(employmentTypeService.findById(user.getEmploymentType().getId()));
			}
		}
	}

	private void validateUserNationality(User existingUser, User user) {
		if (user.getNationality() != null) {
			if (StringUtils.isBlank(user.getNationality().getId())) {
				existingUser.setNationality(null);
			}
			else {
				Nationality nationality = nationalityService.findById(user.getNationality().getId())
						.orElseThrow(ExceptionPredicate.nationalityNotFound(user.getNationality().getId()));
				existingUser.setNationality(nationality);
			}
		}
	}

	private void validateUserOccupationGroup(User existingUser, User user) {
		if (user.getOccupationGroup() != null) {
			if (StringUtils.isBlank(user.getOccupationGroup().getId())) {
				existingUser.setOccupationGroup(null);
			}
			else {
				existingUser.setOccupationGroup(occupationGroupService.findById(user.getOccupationGroup().getId())
						.orElseThrow(ExceptionPredicate.occupationGroupNotFound(user.getOccupationGroup().getId())));
			}
		}
	}

	private void validateUserState(User existingUser, User user) {
		if (user.getState() != null) {
			if (StringUtils.isBlank(user.getState().getId())) {
				existingUser.setStateId(null);
			}
			else {
				existingUser.setStateId(commonFeignClient.getState(user.getState().getId()).getId());
			}
		}
	}

	private void validateUserInterests(User existingUser, User user) {
		if (user.getInterests() != null) {
			List<Interest> interestLists = new ArrayList<>();
			for (Interest interest : user.getInterests()) {
				interestLists.add(interestService.findById(interest.getId())
						.orElseThrow(ExceptionPredicate.interestNotFound(interest.getId())));
			}
			existingUser.getInterests().addAll(interestLists);
		}
	}

	private void validateUserFinancialGoals(User existingUser, User user) {
		if (user.getFinancialGoals() != null) {
			List<FinancialGoal> financialGoals = new ArrayList<>();
			for (FinancialGoal financialGoal : user.getFinancialGoals()) {
				financialGoals.add(financialGoalService.findById(financialGoal.getId())
						.orElseThrow(ExceptionPredicate.financialGoalNotFound(financialGoal.getId())));
			}
			existingUser.getFinancialGoals().addAll(financialGoals);
		}
	}

	@Override
	public List<EpfContribution> validateEpfContribution(List<EpfContributionRequest> requests, User user) {
		return requests.stream().map(epfContributionRequest -> {
			EpfContribution epfContribution = new EpfContribution();
			try {
				epfContribution.setMonth(Month.from(monthFormatter.parse(
						"01-%s-%s".formatted(epfContributionRequest.getMonth(), epfContributionRequest.getYear()))));
				epfContribution.setYear(epfContributionRequest.getYear());
				epfContribution.setContribution(epfContributionRequest.getContribution());
				epfContribution.setUser(user);
			}
			catch (DateTimeParseException e) {
				throw new BusinessException(ErrorCodeEnum.MONTH_FORMAT_INVALID);
			}
			return epfContribution;
		}).toList();
	}

	@Override
	public void validateRegister(User user) {
		validateAge(user.getAge());
		validateEmailBlacklist(user.getEmail());
	}

	@Override
	public void validateDate(@NonNull LocalDate dateFrom, @NonNull LocalDate dateTo) {
		if (!dateTo.isAfter(dateFrom)) {
			throw new BusinessException(ErrorCodeEnum.INVALID_DATE_RANGE);
		}
	}

	@Override
	public void validateWeekNumber(@NonNull Year year, int weekNumber) {
		int totalWeeks = DateUtil.getNumberOfWeeksInYearFull(year);
		if (weekNumber <= 0 || totalWeeks < weekNumber) {
			throw new BusinessException(ErrorCodeEnum.INVALID_WEEK_NUMBER);
		}
	}

	@Override
	public void validateMonth(int month) {
		if (month <= 0 || NUMBER_OF_MONTHS_IN_YEAR < month) {
			throw new BusinessException(ErrorCodeEnum.INVALID_MONTH);
		}
	}

	@Override
	public void validatePasscodeVerificationRequestKey(RequestKeyType keyType) {
		Set<RequestKeyType> allowKeys = Set.of(RequestKeyType.VERIFICATION_CHANGE_MOBILE,
				RequestKeyType.VERIFICATION_CHANGE_EMAIL, RequestKeyType.VERIFICATION_CHANGE_PIN,
				RequestKeyType.PASSWORD_RESET, RequestKeyType.VERIFICATION_ENABLE_BIOMETRIC,
				RequestKeyType.VERIFICATION_DELETE_ACCOUNT, RequestKeyType.VERIFICATION_VIEW_VAULT);
		if (!allowKeys.contains(keyType)) {
			throw new BusinessException(ErrorCodeEnum.INVALID_PASSCODE_VERIFICATION_REQUEST_TYPE, allowKeys.toString());
		}
	}

	@Override
	public void validateSignatureVerificationRequestKey(RequestKeyType keyType) {
		Set<RequestKeyType> allowKeys = Set.of(RequestKeyType.VERIFICATION_CHANGE_MOBILE,
				RequestKeyType.VERIFICATION_CHANGE_EMAIL, RequestKeyType.VERIFICATION_DELETE_ACCOUNT,
				RequestKeyType.VERIFICATION_VIEW_VAULT);
		if (!allowKeys.contains(keyType)) {
			throw new BusinessException(ErrorCodeEnum.INVALID_PASSCODE_VERIFICATION_REQUEST_TYPE, allowKeys.toString());
		}
	}

	@Override
	public void validateLoginDeviceBinding(String userId, String deviceId) {
		if (Strings.isBlank(deviceId)) {
			throw new BusinessException(ErrorCodeEnum.DEVICE_BINDING_ERROR);
		}
		Instant bindingDuration = getDeviceBindingDuration();

		if (deviceBindingService.existsByUserIdAndDeviceIdAndLogoutDatetimeAfter(userId, deviceId, bindingDuration)) {
			return;
		}
		validateUserCountDeviceBinding(userId, bindingDuration, deviceId);
		validateDeviceCountDeviceBinding(deviceId, bindingDuration, userId);
	}

	@Override
	public boolean getDeviceBindingValidationFlag() {
		return featureFlagOutbound.isFeatureEnabled(GlobalSystemConfigurationEnum.ENABLED_DEVICE_BINDING);
	}

	@Override
	public void validateRegisterDeviceBinding(String deviceId) {

		if (Strings.isBlank(deviceId)) {
			throw new BusinessException(ErrorCodeEnum.DEVICE_BINDING_ERROR);
		}
		Instant bindingDuration = getDeviceBindingDuration();

		validateDeviceCountDeviceBinding(deviceId, bindingDuration, "NEW_USER");
	}

	private void validateUserCountDeviceBinding(String userId, Instant bindingDuration, String deviceId) {
		int minDevicePerUser = Integer
				.parseInt(getGlobalSysConfigByCode(GlobalSystemConfigurationEnum.MAXIMUM_DEVICE_PER_USER));
		int totalLoginDevices = deviceBindingService.countByUserIdAndLogoutDatetimeAfter(userId, bindingDuration);
		if (totalLoginDevices >= minDevicePerUser) {
			log.info("validateUserCountDeviceBinding: userId = {}, deviceId = {}", userId, deviceId);
			throw new BusinessException(ErrorCodeEnum.DEVICE_BINDING_ERROR);
		}
	}

	private Instant getDeviceBindingDuration() {
		int deviceBindingDurationConfig = Integer
				.parseInt(getGlobalSysConfigByCode(GlobalSystemConfigurationEnum.DEVICE_BINDING_DAY_DURATION));
		return Instant.now().minus(deviceBindingDurationConfig, ChronoUnit.DAYS);
	}

	private void validateDeviceCountDeviceBinding(String deviceId, Instant bindingDuration, String userId) {
		int minUserPerDevice = Integer
				.parseInt(getGlobalSysConfigByCode(GlobalSystemConfigurationEnum.MAXIMUM_USER_PER_DEVICE));
		int totalLoginUser = deviceBindingService.countByDeviceIdAndLogoutDatetimeAfter(deviceId, bindingDuration);
		if (totalLoginUser >= minUserPerDevice) {
			log.info("validateDeviceCountDeviceBinding: deviceId = {}, userId = {}", deviceId, userId);
			updateLoginFailDeviceLockLog(userId, deviceId, bindingDuration);
			throw new BusinessException(ErrorCodeEnum.DEVICE_BINDING_ERROR);
		}
	}

	private void updateLoginFailDeviceLockLog(String userId, String deviceId, Instant bindingDuration) {
		CompletableFuture.supplyAsync(() -> {
			log.info("update login fail device block to persistence");
			userService.setLoginFailDeviceLockLog(userId, deviceId, bindingDuration);
			return null;
		}).thenAccept(v -> log.info("success save login fail device block persisted..."));
	}

	private String getGlobalSysConfigByCode(GlobalSystemConfigurationEnum configurationEnum) {
		try {
			return commonFeignClient.getIntegrationGlobalSystemConfigurationByCode(configurationEnum.getCode())
					.getValue();
		}
		catch (Exception e) {
			return configurationEnum.getValue();
		}
	}

	@Override
	public void validateDeleteHomeMenu(String id) {
		if (homeMenuMapService.existsByHomeMenuId(id)) {
			throw new BusinessException(ErrorCodeEnum.HOME_MENU_IN_USED);
		}
	}

	@Override
	public void validateDeleteHomeMenuGroup(String id) {
		if (homeMenuMapService.existsByHomeMenuGroupId(id)) {
			throw new BusinessException(ErrorCodeEnum.HOME_MENU_GROUP_IN_USED);
		}
	}

	@Override
	public boolean hasDeviceBinding(String userId) {
		Instant bindingDuration = getDeviceBindingDuration();
		return deviceBindingService.countByUserIdAndLogoutDatetimeAfter(userId, bindingDuration) > 0;
	}

	@Override
	public void validateHomeMenuProductRequest(String homeMenuId,
			Set<HomeMenuProductRequest> homeMenuProductRequestSet) {
		Set<Integer> seenSequences = new HashSet<>();
		if (homeMenuProductRequestSet.isEmpty())
			return;

		if (homeMenuProductRequestSet.size() <= 1)
			return;

		for (HomeMenuProductRequest product : homeMenuProductRequestSet) {
			if (StringNullCheckerUtil.isNullOrBlank(product.getTitleEn())
					|| StringNullCheckerUtil.isNullOrBlank(product.getSubTitleEn())
					|| StringNullCheckerUtil.isNullOrBlank(product.getTitleMy())
					|| StringNullCheckerUtil.isNullOrBlank(product.getSubTitleMy())
					|| StringNullCheckerUtil.isNullOrBlank(product.getTitleCn())
					|| StringNullCheckerUtil.isNullOrBlank(product.getSubTitleCn()) || product.getSequence() == null) {
				throw new BusinessException(ErrorCodeEnum.REQUIRED_FIELD_MISSING);
			}

			if (!seenSequences.add(product.getSequence())) {
				throw new BusinessException(ErrorCodeEnum.PRODUCT_SEQUENCE_IN_USE);
			}
		}
	}

}
