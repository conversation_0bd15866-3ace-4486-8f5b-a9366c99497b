package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.EpfContribution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.EpfContributionRepository;
import my.com.mandrill.component.service.**********************;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Month;
import java.time.Year;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class **********************Impl implements ********************** {

	private final EpfContributionRepository epfContributionRepository;

	@Override
	public Optional<EpfContribution> findByUserAndMonthAndYear(User user, Month month, Year year) {
		return epfContributionRepository.findByUserAndMonthAndYear(user, month, year);
	}

	@Override
	public EpfContribution findLatest(User user) {
		return epfContributionRepository.findLatest(user).orElseThrow(ExceptionPredicate.epfContributionNotFound());
	}

	@Override
	@Transactional
	public EpfContribution save(EpfContribution epfContribution) {
		return epfContributionRepository.save(epfContribution);
	}

	@Override
	@Transactional
	public void process(List<EpfContribution> epfContributions) {
		for (EpfContribution epfContribution : epfContributions) {
			Optional<EpfContribution> epfContributionOptional = findByUserAndMonthAndYear(epfContribution.getUser(),
					epfContribution.getMonth(), epfContribution.getYear());

			if (epfContributionOptional.isPresent()) {
				EpfContribution newEpfContribution = epfContributionOptional.get();
				newEpfContribution.setContribution(epfContribution.getContribution());
				save(newEpfContribution);
			}
			else {
				save(epfContribution);
			}

		}
	}

	@Override
	public BigDecimal findLatestOrReturnDefault(String userId) {
		EpfContribution data = epfContributionRepository.findTopByUserIdOrderByCreatedDateDesc(userId);
		if (data == null) {
			return null;
		}
		return data.getContribution();
	}

}
