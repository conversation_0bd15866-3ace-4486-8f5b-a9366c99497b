package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import my.com.mandrill.component.constant.PointTransactionStatus;
import my.com.mandrill.component.constant.PointTransactionType;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.utilities.core.audit.AuditSectionTimeSeries;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;

import java.math.BigDecimal;
import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "point_transaction")
public class PointTransaction extends AuditSectionTimeSeries {

	@Column(name = "ref_no")
	private String refNo;

	@Column(name = "user_id")
	private String userId;

	@Enumerated(EnumType.STRING)
	@Column(name = "status")
	private PointTransactionStatus status;

	@Column(name = "point_amount")
	private BigDecimal pointAmount;

	@Enumerated(EnumType.STRING)
	@NotNull
	@Column(name = "source")
	private SourceSystemEnum source;

	@Column(name = "approved_date")
	private Instant approvedDate;

	@Enumerated(EnumType.STRING)
	@Column(name = "type")
	private PointTransactionType type;

	@Column(name = "name")
	private String name;

	@Enumerated(EnumType.STRING)
	@Column(name = "rsm_detail_focal_type")
	private RSMFocalType rsmDetailFocalType;

	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "chart_of_account_id")
	private ChartOfAccount chartOfAccount;

	@Column(name = "company_id")
	private String companyId;

	@Column(name = "expired_due")
	private Instant expiredDue;

}
