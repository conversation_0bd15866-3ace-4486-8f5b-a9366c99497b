package my.com.mandrill.component.repository;

import my.com.mandrill.component.domain.RsmClosingBalance;
import my.com.mandrill.component.dto.projection.ClosingBalanceProjection;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface RsmClosingBalanceRepository extends JpaRepository<RsmClosingBalance, String> {

	Optional<RsmClosingBalance> findTopBySourceOrderByClosingDateDesc(@NonNull SourceSystemEnum source);

	/**
	 * aggregate per month as first requirement, dont delete yet in case product want to
	 * changed again
	 */
	@Query(value = """
			    SELECT
			        SUM(closing_balance) as closingBalance,
			        SUM(point_awarded) as pointAwarded,
			        SUM(point_spent) as pointSpent,
			        DATE_TRUNC('MONTH', closing_date) as closingDate
			    FROM rsm_closing_balance
			    WHERE (:year IS NULL OR EXTRACT(YEAR FROM closing_date) = :year)
			      AND (:month IS NULL OR EXTRACT(MONTH FROM closing_date) = :month)
			    GROUP BY DATE_TRUNC('MONTH', closing_date)
			    ORDER BY closingDate DESC LIMIT :limit OFFSET :offset
			""", nativeQuery = true)
	List<ClosingBalanceProjection> aggregateByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month,
			long limit, long offset);

	@Query(value = """
			SELECT
			    closing_balance as closingBalance,
			    point_awarded as pointAwarded,
			    point_spent as pointSpent,
			    closing_date as closingDate,
			    total_expired_point as totalExpiredPoint
			FROM rsm_closing_balance
			WHERE (:year IS NULL OR EXTRACT(YEAR FROM (closing_date AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kuala_Lumpur')) = :year)
			  AND (:month IS NULL OR EXTRACT(MONTH FROM (closing_date AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kuala_Lumpur')) = :month)
			  AND (source = :source)
			""",
			countQuery = """
					SELECT
					    COUNT(*)
					FROM rsm_closing_balance
					WHERE (:year IS NULL OR EXTRACT(YEAR FROM (closing_date AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kuala_Lumpur')) = :year)
					  AND (:month IS NULL OR EXTRACT(MONTH FROM (closing_date AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kuala_Lumpur')) = :month)
					  AND (source = :source)
					""",
			nativeQuery = true)
	Page<ClosingBalanceProjection> findByYearAndMonthAndSource(@Param("year") Integer year,
			@Param("month") Integer month, @Param("source") String source, Pageable pageable);

	@Query(value = """
			    SELECT
			        closing_balance as closingBalance,
			        point_awarded as pointAwarded,
			        point_spent as pointSpent,
			        closing_date as closingDate,
			    	total_expired_point as totalExpiredPoint
			    FROM rsm_closing_balance
			    WHERE (:year IS NULL OR EXTRACT(YEAR FROM (closing_date AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kuala_Lumpur')) = :year)
			      AND (:month IS NULL OR EXTRACT(MONTH FROM (closing_date AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kuala_Lumpur')) = :month)
			      AND (source = :source)
			    ORDER BY closingDate DESC
			""",
			nativeQuery = true)
	List<ClosingBalanceProjection> findByYearAndMonthAndSource(@Param("year") Integer year,
			@Param("month") Integer month, @Param("source") String source);

	@Query(value = """
			    SELECT
			        SUM(closing_balance) as closingBalance,
			        SUM(point_awarded) as pointAwarded,
			        SUM(point_spent) as pointSpent,
			        DATE_TRUNC('MONTH', closing_date) as closingDate
			    FROM rsm_closing_balance
			    WHERE (:year IS NULL OR EXTRACT(YEAR FROM closing_date) = :year)
			      AND (:month IS NULL OR EXTRACT(MONTH FROM closing_date) = :month)
			    GROUP BY DATE_TRUNC('MONTH', closing_date)
			    ORDER BY closingDate DESC
			""", nativeQuery = true)
	List<ClosingBalanceProjection> aggregateByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

	@Query(value = """
			    SELECT COUNT(DISTINCT DATE_TRUNC('MONTH', closing_date))
			    FROM rsm_closing_balance
			    WHERE (:year IS NULL OR EXTRACT(YEAR FROM closing_date) = :year)
			      AND (:month IS NULL OR EXTRACT(MONTH FROM closing_date) = :month)
			""", nativeQuery = true)
	long countAggregateByYearAndMonth(@Param("year") Integer year, @Param("month") Integer month);

	Optional<RsmClosingBalance> findBySourceAndClosingDateBetween(SourceSystemEnum source, Instant start, Instant end);

	@Modifying
	@Transactional
	@Query(value = """
			    UPDATE rsm_closing_balance
			    SET point_awarded = CASE WHEN :pointAwarded IS NOT NULL THEN point_awarded + :pointAwarded ELSE point_awarded END,
			    	point_spent = CASE WHEN :pointSpent IS NOT NULL THEN point_spent + :pointSpent ELSE point_spent END,
			    	closing_balance = :closingBalance
			    WHERE id = :id
			""",
			nativeQuery = true)
	void updateBalanceRealtime(@NonNull String id, BigDecimal pointAwarded, BigDecimal pointSpent,
			BigDecimal closingBalance);

	@Query(value = """
			SELECT DISTINCT DATE_PART('month', closing_date) AS month
			FROM rsm_closing_balance rcb
			WHERE source = :source
			ORDER by month;
			""", nativeQuery = true)
	List<Integer> getAvailableMonthsBySource(@NonNull String source);

	@Query(value = """
			SELECT DISTINCT DATE_PART('year', closing_date) AS year
			FROM rsm_closing_balance rcb
			WHERE source = :source
			ORDER by year;
			""", nativeQuery = true)
	List<Integer> getAvailableYearsBySource(@NonNull String source);

	@Modifying
	@Query(value = """
			    INSERT INTO rsm_closing_balance (
			        id, closing_date, point_awarded, point_spent, closing_balance, source, total_expired_point
			    ) VALUES (
			        :id, :closingDate, :pointAwarded, :pointSpent, :closingBalance, :source, 0
			    )
			    ON CONFLICT (closing_date, source) DO UPDATE
			    SET
			        point_awarded = EXCLUDED.point_awarded,
			        point_spent = EXCLUDED.point_spent,
			        closing_balance = EXCLUDED.closing_balance,
			        source = EXCLUDED.source
			""", nativeQuery = true)
	void upsertDailyClosingBalance(@Param("id") String id, @Param("closingDate") Instant closingDate,
			@Param("pointAwarded") BigDecimal pointAwarded, @Param("pointSpent") BigDecimal pointSpent,
			@Param("closingBalance") BigDecimal closingBalance, @Param("source") String source);

	@Modifying
	@Query(value = """
			    INSERT INTO rsm_closing_balance (
			        id, closing_date, total_expired_point, point_awarded, point_spent, closing_balance, source
			    ) VALUES (
			        :id, :closingDate, :totalExpiredPoint, 0, 0, 0, :source
			    )
			    ON CONFLICT (closing_date, source) DO UPDATE
			    SET
			        total_expired_point = EXCLUDED.total_expired_point
			""", nativeQuery = true)
	void upsertMonthlyExpiredPoint(@Param("id") String id, @Param("closingDate") Instant closingDate,
			@Param("totalExpiredPoint") BigDecimal totalExpiredPoint, @Param("source") String source);

}
