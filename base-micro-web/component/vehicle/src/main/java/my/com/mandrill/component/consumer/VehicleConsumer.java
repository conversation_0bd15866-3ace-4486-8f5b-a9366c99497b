package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Vehicle;
import my.com.mandrill.component.service.VehicleService;
import my.com.mandrill.utilities.feign.dto.VehichleUpdateRequest;
import my.com.mandrill.utilities.feign.dto.request.UpdateVehicleInsuranceRenewalRequest;
import my.com.mandrill.utilities.feign.dto.request.UpdateVehicleRoadTaxRequest;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.util.ExceptionUtil;
import org.apache.kafka.common.errors.SerializationException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.DltHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.annotation.RetryableTopic;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.retrytopic.TopicSuffixingStrategy;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.support.serializer.DeserializationException;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.retry.annotation.Backoff;
import org.springframework.stereotype.Service;

import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;

import static my.com.mandrill.component.config.KafkaTopicConfig.GROUP;

@Slf4j
@Service
@RequiredArgsConstructor
public class VehicleConsumer {

	private final ObjectMapper objectMapper;

	private final VehicleService vehicleService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	@Value("${spring.application.name}")
	String serviceName;

	@RetryableTopic(topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
			backoff = @Backoff(delay = 1000, multiplier = 2.0),
			exclude = { SerializationException.class, DeserializationException.class, EntityNotFoundException.class })
	@KafkaListener(topics = KafkaTopic.FINOLOGY_UPDATE_VEHICLE_ROAD_TAX_TOPIC, groupId = GROUP,
			id = KafkaTopic.FINOLOGY_UPDATE_VEHICLE_ROAD_TAX_TOPIC)
	public void updateVehicleRoadTax(String message) throws JsonProcessingException, NoSuchAlgorithmException {
		log.info("start consume topic = finology-update-vehicle-road-tax-callback with message = {}", message);
		UpdateVehicleRoadTaxRequest data = objectMapper.readValue(message, UpdateVehicleRoadTaxRequest.class);
		Vehicle vehicle = vehicleService.updateVehicleRoadTaxFinology(data);

		publishInsuranceRenewal(data.getRoadTaxRenewalDate(), vehicle.getId(), data.getUserId());
	}

	@RetryableTopic(topicSuffixingStrategy = TopicSuffixingStrategy.SUFFIX_WITH_INDEX_VALUE,
			backoff = @Backoff(delay = 1000, multiplier = 2.0),
			exclude = { SerializationException.class, DeserializationException.class, EntityNotFoundException.class })
	@KafkaListener(topics = KafkaTopic.FINOLOGY_UPDATE_VEHICLE_RENEWAL_DATE_AND_MARKET_VALUE_TOPIC, groupId = GROUP,
			id = KafkaTopic.FINOLOGY_UPDATE_VEHICLE_RENEWAL_DATE_AND_MARKET_VALUE_TOPIC)
	public void updateVehicle(String message) throws JsonProcessingException {
		log.info("start consume topic = finology-update-vehicle-renewal-date-and-market-value with message = {}",
				message);
		VehichleUpdateRequest data = objectMapper.readValue(message, VehichleUpdateRequest.class);
		vehicleService.updateVehicleRoadTaxAndMarketValue(data);

		publishInsuranceRenewal(data.getRoadTaxRenewalDate(), data.getVehicleId(), data.getUserId());
	}

	@DltHandler
	public void handleError(@Header(KafkaHeaders.RECEIVED_TOPIC) String topic, String message) {
		log.error("Error consuming message from topic: {}, message: {}", topic, message);
		ExceptionUtil.sendDltStackTrace(kafkaTemplate, message, topic, serviceName);
	}

	private void publishInsuranceRenewal(LocalDate roadTaxRenewalDate, String vehicleId, String userId)
			throws JsonProcessingException {
		UpdateVehicleInsuranceRenewalRequest updateVehicleInsuranceRenewalRequest = new UpdateVehicleInsuranceRenewalRequest();
		updateVehicleInsuranceRenewalRequest.setRenewalDate(roadTaxRenewalDate);
		updateVehicleInsuranceRenewalRequest.setVehicleId(vehicleId);
		updateVehicleInsuranceRenewalRequest.setUserId(userId);
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_VEHICLE_INSURANCE_RENEWAL_DATE_TOPIC,
				objectMapper.writeValueAsString(updateVehicleInsuranceRenewalRequest));
	}

}
