package my.com.mandrill.utilities.general.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.constant.ClientPlatformType;
import my.com.mandrill.utilities.general.dto.HttpDetailDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.StringJoiner;

import static my.com.mandrill.utilities.general.util.RequestUtil.CLIENT_PLATFORM;

@Slf4j
@Component
public class HttpRequestUtil {

	@Value("${spring.application.name}")
	private String serviceName;

	public HttpDetailDTO parseResource(HttpServletRequest httpServletRequest) {
		return HttpDetailDTO.builder().ipAddress(getIpAddress(httpServletRequest)).service(serviceName)
				.method(httpServletRequest.getMethod()).uri(buildUriWithParams(httpServletRequest))
				.userAgent(httpServletRequest.getHeader("user-agent"))
				.clientPlatform(getClientPlatform(httpServletRequest)).build();
	}

	public static String buildUriWithParams(HttpServletRequest request) {
		final String requestURL = request.getRequestURL().toString();
		StringJoiner queryStringJoiner = new StringJoiner("&");
		// Build the query string
		Enumeration<String> parameterNames = request.getParameterNames();
		while (parameterNames.hasMoreElements()) {
			String paramName = parameterNames.nextElement();
			String paramValue = request.getParameter(paramName);
			queryStringJoiner.add(encode(paramName) + "=" + encode(paramValue));
		}
		try {
			final String queries = queryStringJoiner.toString();
			return StringUtils.isNotBlank(queries) ? requestURL + "?" + queries : requestURL;
		}
		catch (Exception e) {
			log.error("Error building URL", e);
			return requestURL;
		}
	}

	private static String encode(String value) {
		return URLEncoder.encode(value, StandardCharsets.UTF_8);
	}

	private static String getIpAddress(HttpServletRequest request) {
		String ipAddress = request.getHeader("X-Forwarded-For");
		if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
		}
		return ipAddress;
	}

	public static String getClientIp(HttpServletRequest request) {
		String ipAddress = getIpAddress(request);
		String[] ipAddressPart = ipAddress.split(",");
		if (ipAddressPart.length > 1) {
			return ipAddressPart[0].trim();
		}
		return ipAddress;
	}

	private ClientPlatformType getClientPlatform(HttpServletRequest request) {
		String platform = request.getHeader(CLIENT_PLATFORM);
		if (StringUtils.isNotBlank(platform)) {
			try {
				return ClientPlatformType.valueOf(platform.toUpperCase());
			}
			catch (IllegalArgumentException e) {
				log.warn("Unknown client platform: {}", platform);
			}
		}
		return null;
	}

}
