package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.RSMReferralCodeStatus;
import my.com.mandrill.component.dto.model.PointEarningData;
import my.com.mandrill.component.dto.model.RefereeEarningData;
import my.com.mandrill.component.dto.request.CreateReferralCodeRequest;
import my.com.mandrill.component.dto.request.UpdateCustomerReferralRequest;
import my.com.mandrill.component.dto.request.UpdateReferralCodeRequest;
import my.com.mandrill.component.dto.response.BalanceResponse;
import my.com.mandrill.component.dto.response.BizReferralSummaryResponse;
import my.com.mandrill.component.dto.response.ReferralCodeResponse;
import my.com.mandrill.component.dto.response.ReferralSummaryResponse;
import my.com.mandrill.utilities.feign.dto.request.FindRelationTypeRequest;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import my.com.mandrill.utilities.general.dto.response.UserRefereeUpdateRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.lang.NonNull;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ReferralIntegrationService {

	ReferralSummaryResponse getSummary(String userId);

	BizReferralSummaryResponse getBizSummary(@NonNull String institutionId, @NonNull SourceSystemEnum source,
			Boolean shouldCheckReferralCode);

	Page<PointEarningData> getEarningHistory(@NonNull String userId, SourceSystemEnum source,
			@NonNull Pageable pageable);

	Page<PointEarningData> getBizEarningHistory(@NonNull String institutionId, SourceSystemEnum source,
			@NonNull Pageable pageable);

	Page<RefereeEarningData> getBizTopReferees(@NonNull String institutionId, @NonNull SourceSystemEnum source,
			@NonNull Pageable pageable);

	void updateRefereeFriend(UserRefereeUpdateRequest request);

	ReferralCodeResponse getReferralCode(String referralCode, SourceSystemEnum source);

	List<RefereeEarningData> getFriendEarningData(String userId);

	ReferralCodeResponse createReferralCode(CreateReferralCodeRequest createReferralCodeRequest);

	BigDecimal getTotalAvailablePoint(Collection<String> userIds);

	BigDecimal getTotalAvailablePoint(String userId);

	BalanceResponse getTotalAvailablePointV2(Set<String> userIds);

	Map<String, ReferralCodeResponse> getReferralCodeForUserIn(@NonNull Set<String> userIds);

	Map<String, ReferralCodeResponse> getReferralCodeForCompanyId(@NonNull String institutionId,
			@NonNull SourceSystemEnum source);

	Map<String, ReferralCodeResponse> getReferralCodeForCompanyIdSet(@NonNull Set<String> companyIds,
			@NonNull SourceSystemEnum source);

	ReferralCodeResponse updateReferralStatusForInstitutionId(@NonNull String id,
			@NonNull UpdateCustomerReferralRequest request);

	ReferralCodeResponse updateReferralCodeByCompanyId(@NonNull String companyId,
			@NonNull UpdateReferralCodeRequest request);

	RSMRelationType findRelationType(FindRelationTypeRequest request);

	Set<String> getBizCompanyIdsWithReferralApplicationStatus(@NonNull Sort sort, RSMReferralCodeStatus status,
			Instant dateFrom, Instant dateTo);

	void updateReferralToExpiredByUserIds(List<String> userIds);

	ReferralCodeResponse getReferralCodeOfReferrerByReferredId(String userId);

	String getReferralCodeByUserId(String userId);

	boolean existReferralCode(String referralCode);

}
