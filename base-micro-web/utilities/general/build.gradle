buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath("io.spring.javaformat:spring-javaformat-gradle-plugin:0.0.35")
    }
}

plugins {
    id 'org.springframework.boot' version "${spring_boot_version}"
    id 'io.spring.dependency-management' version "${spring_dependency_version}"
    id 'java'
}

apply plugin: 'io.spring.javaformat'

group "${project_group}"
version "${project_version}"

sourceCompatibility = "${java_compatibility_version}"
targetCompatibility = "${java_compatibility_version}"

dependencies {
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-data-jpa"
    implementation "org.springframework.boot:spring-boot-starter-security"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation "org.springframework.boot:spring-boot-starter-webflux"
    implementation "org.springframework.boot:spring-boot-starter-actuator"
    implementation "org.springframework.kafka:spring-kafka"
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    implementation "org.springframework.boot:spring-boot-starter-data-elasticsearch"
    implementation "org.springframework.cloud:spring-cloud-starter-openfeign:${spring_cloud_version}"
    implementation "org.apache.commons:commons-csv:${apache_commons_csv_version}"

    implementation "joda-time:joda-time:${joda}"
    compileOnly "org.projectlombok:lombok:${lombok_version}"
    annotationProcessor "org.projectlombok:lombok:${lombok_version}"

    implementation "org.apache.commons:commons-lang3:${apache_commons_lang3_version}"
    implementation "com.google.guava:guava:${google_guava}"
    implementation "com.googlecode.libphonenumber:libphonenumber:${google_libphonenumber}"

    implementation "org.mapstruct:mapstruct:${mapstruct_version}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstruct_version}"

    implementation "org.apache.pdfbox:pdfbox:${pdfbox_version}"

    implementation "io.micrometer:micrometer-core:1.15.0"
    implementation "io.micrometer:micrometer-tracing:1.5.0"
    implementation "io.micrometer:micrometer-tracing-bridge-brave:1.5.0"
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

}