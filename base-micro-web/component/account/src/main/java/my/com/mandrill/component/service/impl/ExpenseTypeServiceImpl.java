package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.ExpenseType;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.ExpenseTypeRepository;
import my.com.mandrill.component.service.ExpenseTypeService;
import my.com.mandrill.utilities.general.constant.ExpenseTypeEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.util.BooleanUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ExpenseTypeServiceImpl implements ExpenseTypeService {

	private final ExpenseTypeRepository expenseTypeRepository;

	@Override
	public ExpenseType findById(String id) {
		return expenseTypeRepository.findById(id).orElseThrow(ExceptionPredicate.expenseTypeNotFound(id));
	}

	@Override
	@Transactional
	public ExpenseType create(ExpenseType expenseType) {
		this.existsByNameAndType(expenseType.getName(), expenseType.getType());
		return expenseTypeRepository.save(expenseType);
	}

	@Override
	public Page<ExpenseType> findAllByName(Pageable page, String name) {
		return expenseTypeRepository.findAllByName(page, name);
	}

	@Override
	@Transactional
	public ExpenseType update(ExpenseType expenseType) {
		ExpenseType existingExpenseType = findById(expenseType.getId());

		if (BooleanUtil.or(!expenseType.getName().equalsIgnoreCase(existingExpenseType.getName()),
				!expenseType.getType().equals(existingExpenseType.getType()))) {
			this.existsByNameAndType(expenseType.getName(), expenseType.getType());
		}

		// Transformation
		existingExpenseType.setCode(expenseType.getCode());
		existingExpenseType.setName(expenseType.getName());
		existingExpenseType.setDescription(expenseType.getDescription());
		existingExpenseType.setType(expenseType.getType());
		existingExpenseType.setIsUtility(expenseType.getIsUtility());

		return expenseTypeRepository.save(existingExpenseType);
	}

	@Override
	@Transactional
	public void deleteById(String expenseTypeId) {
		ExpenseType expenseType = findById(expenseTypeId);

		expenseTypeRepository.delete(expenseType);
	}

	private void existsByNameAndType(String name, ExpenseTypeEnum type) {
		if (expenseTypeRepository.existsByNameIgnoreCaseAndType(name.toLowerCase(), type)) {
			throw new BusinessException(ErrorCodeEnum.EXPENSE_TYPE_EXISTS);
		}
	}

}
