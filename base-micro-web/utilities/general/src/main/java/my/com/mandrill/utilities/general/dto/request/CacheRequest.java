package my.com.mandrill.utilities.general.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CacheRequest implements Serializable {

	private Set<String> keys;

	private Set<HashKeyRequest> hashKeys;

	private Set<String> patterns;

}
