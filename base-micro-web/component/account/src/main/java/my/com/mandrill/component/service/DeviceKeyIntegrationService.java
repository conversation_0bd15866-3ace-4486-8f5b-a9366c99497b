package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.User;

public interface DeviceKeyIntegrationService {

	SignatureChallenge registerStart(<PERSON><PERSON><PERSON><PERSON> request, AppUser user);

	SignatureChallenge registerFinish(SignatureChallenge request, AppUser user);

	SignatureChallenge assertionStart(Device<PERSON>ey request, AppUser user);

	SignatureChallenge assertionFinish(SignatureChallenge request, AppUser user);

	SignatureChallenge createSignatureChallenge(DeviceKey deviceKey);

	void expireSignatureChallenge(String id);

}
