package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.RSMApplicationCancelReason;
import my.com.mandrill.component.constant.RSMReferralCodeStatus;
import my.com.mandrill.component.domain.ReferralCode;
import my.com.mandrill.component.dto.request.CreateReferralCodeRequest;
import my.com.mandrill.component.dto.request.UpdateReferralCodeRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.ReferralCodeRepository;
import my.com.mandrill.component.service.ReferralCodeService;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserKafkaRequest;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.RandomUtil;
import org.springframework.data.domain.Sort;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class ReferralCodeServiceImpl implements ReferralCodeService {

	private final ReferralCodeRepository referralCodeRepository;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	@Override
	@Transactional
	public ReferralCode create(CreateReferralCodeRequest request, int codeSize) {
		Optional<ReferralCode> referralCode = referralCodeRepository.findByUserId(request.getUserId());
		if (referralCode.isPresent()) {
			throw new BusinessException(ErrorCodeEnum.REFERRAL_CODE_EXISTS);
		}
		ReferralCode referral = MapStructConverter.MAPPER.toReferralCode(request);
		referral.setCode(RandomUtil.generateGuaranteedAlphanumeric(codeSize));
		referral.setStatus(RSMReferralCodeStatus.ACTIVE);

		return referralCodeRepository.save(referral);
	}

	@Override
	@Transactional
	public ReferralCode findOrCreate(String userId, SourceSystemEnum source, int codeSize) {
		Optional<ReferralCode> ref = referralCodeRepository.findByUserIdAndSource(userId, source);
		if (ref.isPresent()) {
			return ref.get();
		}

		ReferralCode referral = new ReferralCode();
		referral.setUserId(userId);
		referral.setCode(RandomUtil.generateGuaranteedAlphanumeric(codeSize));
		referral.setSource(source);
		referral.setStatus(RSMReferralCodeStatus.ACTIVE);
		referral = referralCodeRepository.save(referral);
		kafkaTemplate.send(KafkaTopic.UPDATE_USER_TOPIC, jsonUtil.convertToString(
				UpdateUserKafkaRequest.builder().id(referral.getUserId()).referralCode(referral.getCode()).build()));
		return referral;
	}

	public ReferralCode findByUserId(String userId) {
		Optional<ReferralCode> referralCode = referralCodeRepository.findByUserIdAndStatus(userId,
				RSMReferralCodeStatus.ACTIVE);
		return referralCode.orElse(null);
	}

	@Override
	public List<ReferralCode> findAllByUserIdIn(Set<String> userIds) {
		return referralCodeRepository.findLatestByUserIdIn(userIds);
	}

	@Override
	public List<ReferralCode> findAllByCompanyIdAndSource(String institutionId, SourceSystemEnum source) {
		return referralCodeRepository.findByCompanyIdAndSource(institutionId, source);
	}

	@Override
	public List<ReferralCode> findAllByCompanyIdSetAndSource(Set<String> companyIds, SourceSystemEnum source) {
		return referralCodeRepository.findByCompanyIdSetAndSource(companyIds, source);
	}

	@Override
	public Boolean existsReferralCode(String code, SourceSystemEnum source) {
		return referralCodeRepository.existsByCodeAndSourceAndStatus(code, source, RSMReferralCodeStatus.ACTIVE);
	}

	@Override
	public Boolean existsReferralCode(String code) {
		return referralCodeRepository.existsByCodeAndStatus(code, RSMReferralCodeStatus.ACTIVE);
	}

	@Override
	public ReferralCode findByReferralCodeAndSource(String referralCode, SourceSystemEnum source) {
		return referralCodeRepository.findByCodeAndSource(referralCode, source)
				.orElseThrow(() -> new BusinessException(ErrorCodeEnum.REFERRAL_CODE_NOT_EXISTS));
	}

	@Override
	@Transactional
	public ReferralCode save(ReferralCode request) {
		List<RSMReferralCodeStatus> statuses = List.of(RSMReferralCodeStatus.PENDING, RSMReferralCodeStatus.ACTIVE);
		List<ReferralCode> referralCodes = referralCodeRepository.findByUserIdAndStatusIn(request.getUserId(),
				statuses);
		if (!referralCodes.isEmpty()) {
			throw new BusinessException(ErrorCodeEnum.REFERRAL_CODE_EXISTS);
		}
		return referralCodeRepository.save(request);
	}

	@Override
	public ReferralCode findByReferralCodeAndSource(String referralCode) {
		return referralCodeRepository.findByCode(referralCode)
				.orElseThrow(() -> new BusinessException(ErrorCodeEnum.REFERRAL_CODE_NOT_EXISTS));
	}

	@Transactional
	@Override
	public ReferralCode updateReferralStatusForInstitutionId(@NonNull String id, @NonNull RSMReferralCodeStatus status,
			RSMApplicationCancelReason cancellationReason) {
		ReferralCode referralCode = referralCodeRepository.findLatestByCompanyId(id)
				.orElseThrow(() -> new BusinessException(ErrorCodeEnum.REFERRAL_CODE_NOT_EXISTS));
		referralCode.setStatus(status);
		referralCode.setApplicationCancelReason(cancellationReason);
		referralCodeRepository.save(referralCode);
		return referralCode;
	}

	@Transactional
	@Override
	public ReferralCode updateReferralCodeByCompanyId(@NonNull String companyId,
			@NonNull UpdateReferralCodeRequest request) {
		ReferralCode referralCode = referralCodeRepository.findLatestByCompanyId(companyId)
				.orElseThrow(() -> new BusinessException(ErrorCodeEnum.REFERRAL_CODE_NOT_EXISTS));
		referralCode.setStatus(request.getStatus());
		referralCode.setApplicantName(request.getApplicantName());
		referralCode.setApplicantEmail(request.getApplicantEmail());
		return referralCodeRepository.save(referralCode);
	}

	@Override
	public Set<String> findAllCompanyIdsWhereStatusIn(@NonNull Sort sort, @NonNull SourceSystemEnum source,
			RSMReferralCodeStatus status, Instant dateFrom, Instant dateTo) {
		if (dateFrom == null || dateTo == null) {
			// Make sure both are null or both aren't
			return referralCodeRepository.findAllCompanyIdsByStatus(sort, source, status, null, null);
		}
		return referralCodeRepository.findAllCompanyIdsByStatus(sort, source, status, dateFrom, dateTo);
	}

	@Override
	public void updateExpiredByUserIds(List<String> userIds) {
		referralCodeRepository.updateExpiredByUserIds(userIds, Instant.now(), RSMReferralCodeStatus.DELETED);
	}

	@Override
	public Optional<ReferralCode> getReferralCodeByUserId(String userId) {
		return referralCodeRepository.findByUserId(userId);
	}

}
