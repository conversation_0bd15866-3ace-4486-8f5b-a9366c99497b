package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.RSMFrequencyEnum;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;

import java.time.Instant;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RSMHeaderSearchRequest {

	private String search;

	private Boolean isActive;

	private String productCategoryId;

	private String productTypeId;

	private String providerId;

	private String productId;

	private RSMFrequencyEnum frequency;

	private LocalDate startDate;

	private LocalDate endDate;

	private Boolean isInternal;

	@JsonIgnore
	private SourceSystemEnum source;

}
