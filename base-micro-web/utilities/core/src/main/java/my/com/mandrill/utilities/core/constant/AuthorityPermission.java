package my.com.mandrill.utilities.core.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class AuthorityPermission {

	public static final String INSTITUTION_CREATE = "INSTITUTION_CREATE";

	public static final String INSTITUTION_READ = "INSTITUTION_READ";

	public static final String INSTITUTION_UPDATE = "INSTITUTION_UPDATE";

	public static final String INSTITUTION_DELETE = "INSTITUTION_DELETE";

	public static final String USER_CREATE = "USER_CREATE";

	public static final String USER_READ = "USER_READ";

	public static final String USER_UPDATE = "USER_UPDATE";

	public static final String USER_DELETE = "USER_DELETE";

	public static final String PERMISSION_CREATE = "PERMISSION_CREATE";

	public static final String PERMISSION_READ = "PERMISSION_READ";

	public static final String PERMISSION_UPDATE = "PERMISSION_UPDATE";

	public static final String PERMISSION_DELETE = "PERMISSION_DELETE";

	public static final String SYSTEM_CONFIGURATION_CREATE = "SYSTEM_CONFIGURATION_CREATE";

	public static final String SYSTEM_CONFIGURATION_READ = "SYSTEM_CONFIGURATION_READ";

	public static final String SYSTEM_CONFIGURATION_UPDATE = "SYSTEM_CONFIGURATION_UPDATE";

	public static final String SYSTEM_CONFIGURATION_DELETE = "SYSTEM_CONFIGURATION_DELETE";

	public static final String SCHEDULER_CREATE = "SCHEDULER_CREATE";

	public static final String SCHEDULER_READ = "SCHEDULER_READ";

	public static final String SCHEDULER_UPDATE = "SCHEDULER_UPDATE";

	public static final String SCHEDULER_DELETE = "SCHEDULER_DELETE";

	public static final String SCHEDULER_PAUSE = "SCHEDULER_PAUSE";

	public static final String SCHEDULER_RESUME = "SCHEDULER_RESUME";

	public static final String SEGMENT_CREATE = "SEGMENT_CREATE";

	public static final String SEGMENT_UPDATE = "SEGMENT_UPDATE";

	public static final String SEGMENT_READ = "SEGMENT_READ";

	public static final String SEGMENT_DELETE = "SEGMENT_DELETE";

	public static final String UTILITY_TYPE_CREATE = "UTILITY_TYPE_CREATE";

	public static final String UTILITY_TYPE_READ = "UTILITY_TYPE_READ";

	public static final String UTILITY_TYPE_UPDATE = "UTILITY_TYPE_UPDATE";

	public static final String INCOME_TYPE_CREATE = "INCOME_TYPE_CREATE";

	public static final String INCOME_TYPE_UPDATE = "INCOME_TYPE_UPDATE";

	public static final String INCOME_TYPE_READ = "INCOME_TYPE_READ";

	public static final String INCOME_TYPE_DELETE = "INCOME_TYPE_DELETE";

	public static final String EXPENSE_TYPE_CREATE = "EXPENSE_TYPE_CREATE";

	public static final String EXPENSE_TYPE_UPDATE = "EXPENSE_TYPE_UPDATE";

	public static final String EXPENSE_TYPE_READ = "EXPENSE_TYPE_READ";

	public static final String EXPENSE_TYPE_DELETE = "EXPENSE_TYPE_DELETE";

	public static final String EMPLOYMENT_TYPE_CREATE = "EMPLOYMENT_TYPE_CREATE";

	public static final String EMPLOYMENT_TYPE_UPDATE = "EMPLOYMENT_TYPE_UPDATE";

	public static final String EMPLOYMENT_TYPE_READ = "EMPLOYMENT_TYPE_READ";

	public static final String EMPLOYMENT_TYPE_DELETE = "EMPLOYMENT_TYPE_DELETE";

	public static final String COUNTRY_CREATE = "COUNTRY_CREATE";

	public static final String COUNTRY_UPDATE = "COUNTRY_UPDATE";

	public static final String COUNTRY_READ = "COUNTRY_READ";

	public static final String COUNTRY_DELETE = "COUNTRY_DELETE";

	public static final String CURRENCY_CREATE = "CURRENCY_CREATE";

	public static final String CURRENCY_UPDATE = "CURRENCY_UPDATE";

	public static final String CURRENCY_READ = "CURRENCY_READ";

	public static final String CURRENCY_DELETE = "CURRENCY_DELETE";

	public static final String UPLOAD_VEHICLE = "UPLOAD_VEHICLE";

	public static final String BANK_LIST_CREATE = "BANK_LIST_CREATE";

	public static final String BANK_LIST_UPDATE = "BANK_LIST_UPDATE";

	public static final String BANK_LIST_READ = "BANK_LIST_READ";

	public static final String BANK_LIST_DELETE = "BANK_LIST_DELETE";

	public static final String PROVIDER_CREATE = "PROVIDER_CREATE";

	public static final String PROVIDER_READ = "PROVIDER_READ";

	public static final String PROVIDER_UPDATE = "PROVIDER_UPDATE";

	public static final String PROVIDER_DELETE = "PROVIDER_DELETE";

	public static final String PROPERTY_TYPE_CREATE = "PROPERTY_TYPE_CREATE";

	public static final String PROPERTY_TYPE_READ = "PROPERTY_TYPE_READ";

	public static final String PROPERTY_TYPE_UPDATE = "PROPERTY_TYPE_UPDATE";

	public static final String PROPERTY_TYPE_DELETE = "PROPERTY_TYPE_DELETE";

	public static final String ADVERTISEMENT_CREATE = "ADVERTISEMENT_CREATE";

	public static final String ADVERTISEMENT_READ = "ADVERTISEMENT_READ";

	public static final String ADVERTISEMENT_UPDATE = "ADVERTISEMENT_UPDATE";

	public static final String REPORT_READ = "REPORT_READ";

	public static final String REPORT_READ_USER_INTERESTED = "REPORT_READ_USER_INTERESTED";

	public static final String DASHBOARD_ACTIVITY_READ = "DASHBOARD_ACTIVITY_READ";

	public static final String MOBILE_USER_READ = "MOBILE_USER_READ";

	public static final String MOBILE_USER_UPDATE = "MOBILE_USER_UPDATE";

	public static final String PLATFORM_CREATE = "PLATFORM_CREATE";

	public static final String PLATFORM_READ = "PLATFORM_READ";

	public static final String PLATFORM_UPDATE = "PLATFORM_UPDATE";

	public static final String PLATFORM_DELETE = "PLATFORM_DELETE";

	public static final String DASHBOARD_ACTIVITY_LIVE_FEED = "DASHBOARD_ACTIVITY_LIVE_FEED";

	public static final String DASHBOARD_PULL_BIG_QUERY_DATA = "DASHBOARD_PULL_BIG_QUERY_DATA";

	public static final String GLOBAL_SYSTEM_CONFIGURATION_READ = "GLOBAL_SYSTEM_CONFIGURATION_READ";

	public static final String GLOBAL_SYSTEM_CONFIGURATION_UPDATE = "GLOBAL_SYSTEM_CONFIGURATION_UPDATE";

	public static final String GLOBAL_SYSTEM_CONFIGURATION_CREATE = "GLOBAL_SYSTEM_CONFIGURATION_CREATE";

	public static final String BUSINESS_NATURE_CREATE = "BUSINESS_NATURE_CREATE";

	public static final String BUSINESS_NATURE_UPDATE = "BUSINESS_NATURE_UPDATE";

	public static final String BUSINESS_NATURE_READ = "BUSINESS_NATURE_READ";

	public static final String BUSINESS_NATURE_DELETE = "BUSINESS_NATURE_DELETE";

	public static final String BANNER_CREATE = "BANNER_CREATE";

	public static final String BANNER_READ = "BANNER_READ";

	public static final String BANNER_UPDATE = "BANNER_UPDATE";

	public static final String BANNER_DELETE = "BANNER_DELETE";

	public static final String VOUCHER_CREATE = "VOUCHER_CREATE";

	public static final String VOUCHER_READ = "VOUCHER_READ";

	public static final String VOUCHER_UPDATE = "VOUCHER_UPDATE";

	public static final String VOUCHER_DELETE = "VOUCHER_DELETE";

	public static final String VOUCHER_REPORT = "VOUCHER_REPORT";

	public static final String LOAN_PLUS_TRANSACTION_READ = "LOAN_PLUS_TRANSACTION_READ";

	public static final String PAYMENT_MERCHANT_UPDATE = "PAYMENT_MERCHANT_UPDATE";

	public static final String MIGRATE_REMINDER = "MIGRATE_REMINDER";

	public static final String FINOLOGY_TRANSACTION_READ = "FINOLOGY_TRANSACTION_READ";

	public static final String REPORT_READ_DOWNLOADS_STATISTICS = "REPORT_READ_DOWNLOADS_STATISTICS";

	public static final String EDGE_PROP_TRANSACTION_READ = "EDGE_PROP_TRANSACTION_READ";

	public static final String REPORT_READ_M_AND_A_INTEREST = "REPORT_READ_M_AND_A_INTEREST";

	public static final String PUBLIC_AUTHENTICATION_UPDATE = "PUBLIC_AUTHENTICATION_UPDATE";

	public static final String PUBLIC_AUTHENTICATION_READ = "PUBLIC_AUTHENTICATION_READ";

	public static final String WEEKLY_USER_TARGET_CREATE = "WEEKLY_USER_TARGET_CREATE";

	public static final String WEEKLY_USER_TARGET_READ = "WEEKLY_USER_TARGET_READ";

	public static final String WEEKLY_USER_TARGET_UPDATE = "WEEKLY_USER_TARGET_UPDATE";

	public static final String WEEKLY_USER_TARGET_DELETE = "WEEKLY_USER_TARGET_DELETE";

	public static final String MONTHLY_USER_TARGET_CREATE = "MONTHLY_USER_TARGET_CREATE";

	public static final String MONTHLY_USER_TARGET_READ = "MONTHLY_USER_TARGET_READ";

	public static final String MONTHLY_USER_TARGET_UPDATE = "MONTHLY_USER_TARGET_UPDATE";

	public static final String MONTHLY_USER_TARGET_DELETE = "MONTHLY_USER_TARGET_DELETE";

	public static final String REPORT_READ_USER_LIST = "REPORT_READ_USER_LIST";

	public static final String PUSH_NOTIFICATION_READ = "PUSH_NOTIFICATION_READ";

	public static final String PUSH_NOTIFICATION_UPDATE = "PUSH_NOTIFICATION_UPDATE";

	public static final String PUSH_NOTIFICATION_CREATE = "PUSH_NOTIFICATION_CREATE";

	public static final String PUSH_NOTIFICATION_CANCEL = "PUSH_NOTIFICATION_CANCEL";

	public static final String REPORT_READ_LOAN_PLUS = "REPORT_READ_LOAN_PLUS";

	public static final String REPORT_PROPERTY_VOUCHER = "REPORT_PROPERTY_VOUCHER";

	public static final String SURVEY_CREATE = "SURVEY_CREATE";

	public static final String SURVEY_READ = "SURVEY_READ";

	public static final String SURVEY_DELETE = "SURVEY_DELETE";

	public static final String SURVEY_UPDATE = "SURVEY_UPDATE";

	public static final String REPORT_STASH_AWAY_READ = "REPORT_STASH_AWAY_READ";

	public static final String REPORT_E_WILL_READ = "REPORT_E_WILL_READ";

	public static final String REPORT_MILIEU_SOLAR_READ = "REPORT_MILIEU_SOLAR_READ";

	public static final String REPORT_CREDIT_BUREAU_READ = "REPORT_CREDIT_BUREAU_READ";

	public static final String REPORT_EASIWILL_READ = "REPORT_EASIWILL_READ";

	public static final String REPORT_CTOS_READ = "REPORT_CTOS_READ";

	public static final String REPORT_SOLAROO_READ = "REPORT_SOLAROO_READ";

	public static final String REPORT_PRS_READ = "REPORT_PRS_READ";

	public static final String REPORT_JOMHIBAH_READ = "REPORT_JOMHIBAH_READ";

	public static final String REPORT_SINEGY_DAX_READ = "REPORT_SINEGY_DAX_READ";

	public static final String REPORT_CGS_INT_MY_READ = "REPORT_CGS_INT_MY_READ";

	public static final String REPORT_DWS_READ = "REPORT_DWS_READ";

	public static final String REPORT_FSM_ONE_READ = "REPORT_FSM_ONE_READ";

	public static final String REPORT_APEX_READ = "REPORT_APEX_READ";

	public static final String REPORT_SURVEY_READ = "REPORT_SURVEY_READ";

	public static final String HOME_MENU_CREATE = "HOME_MENU_CREATE";

	public static final String HOME_MENU_READ = "HOME_MENU_READ";

	public static final String HOME_MENU_UPDATE = "HOME_MENU_UPDATE";

	public static final String HOME_MENU_DELETE = "HOME_MENU_DELETE";

	public static final String REPORT_READ_VEHICLE_APPLICATION = "REPORT_READ_VEHICLE_APPLICATION";

	public static final String USER_INTEREST_LIST_READ = "USER_INTEREST_LIST_READ";

	public static final String USER_INTEREST_LIST_UPDATE = "USER_INTEREST_LIST_UPDATE";

	public static final String TELCO_OPERATOR_CREATE = "TELCO_OPERATOR_CREATE";

	public static final String TELCO_OPERATOR_READ = "TELCO_OPERATOR_READ";

	public static final String TELCO_OPERATOR_DELETE = "TELCO_OPERATOR_DELETE";

	public static final String TELCO_OPERATOR_UPDATE = "TELCO_OPERATOR_UPDATE";

	public static final String DENOM_AMOUNT_CREATE = "DENOM_AMOUNT_CREATE";

	public static final String DENOM_AMOUNT_READ = "DENOM_AMOUNT_READ";

	public static final String DENOM_AMOUNT_UPDATE = "DENOM_AMOUNT_UPDATE";

	public static final String DENOM_AMOUNT_DELETE = "DENOM_AMOUNT_DELETE";

	public static final String PRODUCT_CONFIGURATION_CREATE = "PRODUCT_CONFIGURATION_CREATE";

	public static final String PRODUCT_CONFIGURATION_READ = "PRODUCT_CONFIGURATION_READ";

	public static final String PRODUCT_CONFIGURATION_UPDATE = "PRODUCT_CONFIGURATION_UPDATE";

	public static final String PRODUCT_CONFIGURATION_DELETE = "PRODUCT_CONFIGURATION_DELETE";

	public static final String PRODUCT_CONFIGURATION_TEMPLATE_CREATE = "PRODUCT_CONFIGURATION_TEMPLATE_CREATE";

	public static final String PRODUCT_CONFIGURATION_TEMPLATE_READ = "PRODUCT_CONFIGURATION_TEMPLATE_READ";

	public static final String PRODUCT_CONFIGURATION_TEMPLATE_UPDATE = "PRODUCT_CONFIGURATION_TEMPLATE_UPDATE";

	public static final String PRODUCT_CONFIGURATION_TEMPLATE_DELETE = "PRODUCT_CONFIGURATION_TEMPLATE_DELETE";

	public static final String REPORT_READ_PURCHASE_VEHICLE_STATISTICS = "REPORT_READ_PURCHASE_VEHICLE_STATISTICS";

	public static final String REPORT_READ_RSM_CLOSING_BALANCE = "REPORT_READ_RSM_CLOSING_BALANCE";

	public static final String ATX_TRANSACTION_READ = "ATX_TRANSACTION_READ";

	public static final String BLOG_CREATE = "BLOG_CREATE";

	public static final String BLOG_READ = "BLOG_READ";

	public static final String BLOG_UPDATE = "BLOG_UPDATE";

	public static final String BLOG_DELETE = "BLOG_DELETE";

	public static final String BLOG_CATEGORY_CREATE = "BLOG_CATEGORY_CREATE";

	public static final String BLOG_CATEGORY_READ = "BLOG_CATEGORY_READ";

	public static final String BLOG_CATEGORY_UPDATE = "BLOG_CATEGORY_UPDATE";

	public static final String BLOG_CATEGORY_DELETE = "BLOG_CATEGORY_DELETE";

	public static final String DEVELOPER_CREATE = "DEVELOPER_CREATE";

	public static final String DEVELOPER_READ = "DEVELOPER_READ";

	public static final String DEVELOPER_UPDATE = "DEVELOPER_UPDATE";

	public static final String DEVELOPER_DELETE = "DEVELOPER_DELETE";

	public static final String PROPERTY_PROJECT_CREATE = "PROPERTY_PROJECT_CREATE";

	public static final String PROPERTY_PROJECT_READ = "PROPERTY_PROJECT_READ";

	public static final String PROPERTY_PROJECT_UPDATE = "PROPERTY_PROJECT_UPDATE";

	public static final String PROPERTY_PROJECT_DELETE = "PROPERTY_PROJECT_DELETE";

	public static final String PROPERTY_PROJECT_UNIT_CREATE = "PROPERTY_PROJECT_UNIT_CREATE";

	public static final String PROPERTY_PROJECT_UNIT_READ = "PROPERTY_PROJECT_UNIT_READ";

	public static final String PROPERTY_PROJECT_UNIT_DELETE = "PROPERTY_PROJECT_UNIT_DELETE";

	public static final String PROPERTY_PROJECT_UNIT_UPDATE = "PROPERTY_PROJECT_UNIT_UPDATE";

	public static final String REPORT_READ_PURCHASE_PROPERTY = "REPORT_READ_PURCHASE_PROPERTY";

	public static final String UPLOAD_PURCHASE_PROPERTY = "UPLOAD_PURCHASE_PROPERTY";

	public static final String PROPERTY_PROJECT_UNIT_UPLOAD = "PROPERTY_PROJECT_UNIT_UPLOAD";

	public static final String TOKEN_READ = "TOKEN_READ";

	public static final String TOKEN_UPDATE = "TOKEN_UPDATE";

	public static final String TOKEN_DELETE = "TOKEN_DELETE";

	public static final String TOKEN_CREATE = "TOKEN_CREATE";

	public static final String INVESTMENT_PROVIDER_CREATE = "INVESTMENT_PROVIDER_CREATE";

	public static final String INVESTMENT_PROVIDER_READ = "INVESTMENT_PROVIDER_READ";

	public static final String INVESTMENT_PROVIDER_UPDATE = "INVESTMENT_PROVIDER_UPDATE";

	public static final String INVESTMENT_PROVIDER_DELETE = "INVESTMENT_PROVIDER_DELETE";

	public static final String RETIREMENT_PROVIDER_READ = "RETIREMENT_PROVIDER_READ";

	public static final String RETIREMENT_PROVIDER_CREATE = "RETIREMENT_PROVIDER_CREATE";

	public static final String RETIREMENT_PROVIDER_UPDATE = "RETIREMENT_PROVIDER_UPDATE";

	public static final String RETIREMENT_PROVIDER_DELETE = "RETIREMENT_PROVIDER_DELETE";

	public static final String PAYMENT_ACCOUNT_UPDATE = "PAYMENT_ACCOUNT_UPDATE";

	public static final String RSM_HEADER_CREATE = "RSM_HEADER_CREATE";

	public static final String RSM_HEADER_READ = "RSM_HEADER_READ";

	public static final String RSM_HEADER_UPDATE = "RSM_HEADER_UPDATE";

	public static final String RSM_HEADER_DELETE = "RSM_HEADER_DELETE";

	public static final String REPORT_READ_POINTS_WITHDRAWAL = "REPORT_READ_POINTS_WITHDRAWAL";

	public static final String RSM_LEAD_CREATE = "RSM_LEAD_CREATE";

	public static final String RSM_LEAD_READ = "RSM_LEAD_READ";

	public static final String RSM_LEAD_UPDATE = "RSM_LEAD_UPDATE";

	public static final String RSM_LEAD_DELETE = "RSM_LEAD_DELETE";

	public static final String REPORT_READ_POINTS_DISBURSEMENT = "REPORT_READ_POINTS_DISBURSEMENT";

	public static final String POINT_SUMMARY_REPORT = "POINT_SUMMARY_REPORT";

	public static final String POINTS_WITHDRAWAL_MANAGEMENT_READ = "POINTS_WITHDRAWAL_MANAGEMENT_READ";

	public static final String POINTS_WITHDRAWAL_MANAGEMENT_UPDATE = "POINTS_WITHDRAWAL_MANAGEMENT_UPDATE";

	public static final String RSM_CLOSING_BALANCE_UPDATE = "RSM_CLOSING_BALANCE_UPDATE";

	public static final String RSM_CLOSING_BALANCE_READ = "RSM_CLOSING_BALANCE_READ";

	public static final String MESSAGE_TEMPLATE_HEADER_READ = "MESSAGE_TEMPLATE_HEADER_READ";

	public static final String MESSAGE_TEMPLATE_HEADER_UPDATE = "MESSAGE_TEMPLATE_HEADER_UPDATE";

	public static final String MESSAGE_TEMPLATE_HEADER_CREATE = "MESSAGE_TEMPLATE_HEADER_CREATE";

	public static final String REPORT_READ_NPS = "REPORT_READ_NPS";

	public static final String REPORT_UP_BY_CGS_MY_READ = "REPORT_UP_BY_CGS_MY_READ";

	public static final String REPORT_APEX_SECURITIES_READ = "REPORT_APEX_SECURITIES_READ";

	public static final String DOWNLOAD_TRANSACTION_READ = "DOWNLOAD_TRANSACTION_READ";

	public static final String VIEW_MOBILE_USER_OVERVIEW = "VIEW_MOBILE_USER_OVERVIEW";

	public static final String VIEW_MOBILE_USER_PERSONAL_INFO = "VIEW_MOBILE_USER_PERSONAL_INFO";

	public static final String VIEW_MOBILE_USER_PAYMENT_EKYC_INFO = "VIEW_MOBILE_USER_PAYMENT_EKYC_INFO";

	public static final String RSM_LEAD_MANUAL_CREATE = "RSM_LEAD_MANUAL_CREATE";

	public static final String MX_BIZ_READ_API = "MX_BIZ_READ_API";

	public static final String MX_BIZ_CREATE_API = "MX_BIZ_CREATE_API";

	public static final String MX_BIZ_UPDATE_API = "MX_BIZ_UPDATE_API";

	public static final String MX_BIZ_DELETE_API = "MX_BIZ_DELETE_API";

}
