package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.request.ChangePinRequest;

import java.util.function.Supplier;

public interface PinIntegrationService {

	UserKeyRequest validate(String pin, AppUser user, Supplier<UserKeyRequest> supplier);

	void changePin(User user, ChangePinRequest request);

}
