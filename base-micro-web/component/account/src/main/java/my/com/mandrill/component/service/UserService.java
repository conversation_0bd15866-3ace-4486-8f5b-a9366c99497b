package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.CheckAccountProjection;
import my.com.mandrill.component.dto.model.UserDataForAiProjection;
import my.com.mandrill.component.dto.request.MobileUserPaginationRequest;
import my.com.mandrill.component.dto.request.UpdatePasswordResetEmailRequest;
import my.com.mandrill.component.dto.request.UpdateStatusInternalMobileRequest;
import my.com.mandrill.component.dto.response.AccountResponse;
import my.com.mandrill.component.dto.response.CurrentUserIdResponse;
import my.com.mandrill.component.dto.response.InternalUserMobileResponse;
import my.com.mandrill.component.dto.response.UserDetailResponse;
import my.com.mandrill.component.dto.response.UserListResponse;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserKafkaRequest;
import my.com.mandrill.utilities.feign.dto.response.UserFullNameResponse;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.dto.request.PaymentInfoUpdateKafkaRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;

public interface UserService {

	User findByIdAndInstitutionsId(String id, String institutionId);

	User findByUsernameInputIgnoreCaseAndLoginType(String username, LoginTypeEnum loginType, Boolean active);

	User findByEmailAndLoginTypeAndActiveTrue(String email, LoginTypeEnum loginType);

	User findByPhoneCountryAndPhoneNumberAndLoginTypeAndActiveTrue(String phoneCountry, String phoneNumber,
			LoginTypeEnum loginType);

	User getCurrentUser();

	User save(User user);

	int saveUserUpdateRequest(UpdateUserKafkaRequest request, User user);

	User adminSave(User user);

	Page<User> findByInstitutionsIdAndLoginTypeAndFullName(String institutionId, LoginTypeEnum loginType,
			String fullName, Pageable pageable);

	User findByIdAndInstitutionsIdAndLoginType(String username, String institutionId, LoginTypeEnum loginType);

	void delete(User user);

	User findByRefNo(String refNo);

	void completePasswordResetEmail(UpdatePasswordResetEmailRequest request);

	void generateSecretKey();

	Page<UserDetailResponse> findInternalMobileUser(Pageable pageable, MobileUserPaginationRequest request);

	void updateStatusInternalMobile(String id, UpdateStatusInternalMobileRequest request);

	User findInternalMobileUserById(String id);

	boolean existsByUsernameAndInstitutionsIdAndLoginType(String username, String curIns, LoginTypeEnum loginTypeEnum);

	boolean existsByAuthoritiesId(String authorityId);

	boolean existsBySegmentId(String segmentId);

	Optional<User> findByEmailIgnoreCaseAndLoginType(String emailRaw, LoginTypeEnum loginTypeEnum);

	Optional<User> findByEmail(String email);

	Optional<User> findByEmailIgnoreCaseAndLoginTypeAndActiveTrue(String emailRaw, LoginTypeEnum loginType);

	String findSecretKeyByRefNo(String refNo);

	CurrentUserIdResponse findRefNoAndIdByRefNo(String refNo);

	AccountResponse findActiveById(String id);

	Optional<User> findOptionalByPhoneCountryAndPhoneNumber(String phoneCountry, String phoneNumber);

	List<User> findAllByAbleToLogicalDelete();

	Optional<User> findById(String id);

	boolean existsByEmailAndLoginType(String encryptedEmail, LoginTypeEnum loginTypeEnum);

	void saveAll(List<User> users);

	List<User> findByDobNotNull();

	boolean existsByEmploymentTypeId(String employmentTypeId);

	long countByDeletedAndLoginType(Instant startDateInstant, Instant endDateInstant, LoginTypeEnum loginTypeEnum);

	Page<UserListResponse> findRefNoByCreatedDate(Pageable pageable, LocalDate dateFrom, LocalDate dateTo,
			ZoneId zoneId);

	Page<String> getUserActiveAndUndeleted(Pageable pageable);

	List<String> findAllUserIdsByCreatedDate(Instant dateStart, Instant dateEnd);

	int setLoginFailAttempt(Integer failAttempt, String userId);

	List<String> findAllUserIdsByCreatedDateLessThan(Instant createdDate);

	List<UserFullNameResponse> findAllUserFullNames();

	List<InternalUserMobileResponse> findAllByUserIds(List<String> userIds);

	List<UserFullNameResponse> findUserFullNamesByUserIds(List<String> userIds, boolean includeDeleted);

	List<UserFullNameResponse> findUserFullNamesByUserRefNos(List<String> userRefNos);

	void setLoginFailDeviceLockLog(String userId, String deviceId, Instant logoutDatetime);

	void clearLoginFailDeviceLockLog(String userId);

	void updatePaymentStatus(PaymentInfoUpdateKafkaRequest request);

	Optional<CheckAccountProjection> checkAccountByUsername(String username, UsernameType usernameType);

	void updateReferralTnc(String referralCodeTnc);

	UserDataForAiProjection findCurrentUserDataForAiById(String userId);

	Optional<User> findByPhoneCountryAndPhoneNumber(String phoneCountry, String phoneNumber);

	boolean existsByPhoneCountryAndPhoneNumber(String phoneCountry, String phoneNumber);

	List<String> findUserIdsByPermission(String permission);

}
