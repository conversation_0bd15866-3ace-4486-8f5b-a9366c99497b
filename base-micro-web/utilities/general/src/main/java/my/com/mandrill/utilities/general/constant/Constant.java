package my.com.mandrill.utilities.general.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.text.DecimalFormat;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class Constant {

	public static final String LOGIN_REGEX = "^[_.+@A-Za-z0-9-]*$";

	public static final String FULL_NAME_REGEX = "^[a-zA-Z@/'’‘ ]+$";

	public static final String SYSTEM_ACCOUNT = "system";

	public static final String ANONYMOUS_USER = "anonymous";

	public static final String DEFAULT_INSTITUTION_ID = "97a0a02a-02ef-4616-96db-a1abf22a8b40";

	public static final DecimalFormat moneyDecimalFormat = new DecimalFormat("#.00");

	public static final String LABEL_DELIMITER = " - ";

	public static final String LABEL_SPACE_DELIMITER = " ";

	public static final String LOAN_PLUS_DATE_FORMAT = "dd MMM yyyy HH:mm:ss";

	public static final String SELF_EMPLOYED_EMPLOYMENT_TYPE_ID = "b604bbf8-ab7c-11ed-9f68-0242ac120002";

	public static final String FULL_TIME_EMPLOYMENT_TYPE_ID = "ea035370-ada2-11ed-a899-0242ac120002";

	public static final String PART_TIME_EMPLOYMENT_TYPE_ID = "ed1cb896-ada2-11ed-a899-0242ac120002";

	public static final String BUSINESS_OWNER_EMPLOYMENT_TYPE_ID = "bba2854b-ab7c-11ed-9f68-0242ac120002";

	public static final String SALARY_INCOME_TYPE_ID = "241fd082-51b3-444f-8e51-0feebe6e01bb";

	public static final String PROMOTION_ADVERTISEMENT_KYLL_ID = "fd624c8f-3585-40a7-93af-c21ba330b159";

	public static final String LOCAL_MALAYSIAN_PHONE_COUNTRY = "+60";

	public static final String PATH_SEPARATOR = "/";

}
