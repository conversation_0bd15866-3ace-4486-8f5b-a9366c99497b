package my.com.mandrill.component.service;

import my.com.mandrill.component.constants.EkycAction;
import my.com.mandrill.component.domain.Ekyc;
import my.com.mandrill.component.dto.request.EkycCallbackRequest;
import my.com.mandrill.component.dto.request.EkycCallbackResetRequest;
import my.com.mandrill.component.dto.request.EkycSubmitICMobileRequest;
import my.com.mandrill.component.dto.request.EkycSubmitPhotoMobileRequest;
import my.com.mandrill.component.dto.response.EkycImageResponse;
import my.com.mandrill.component.dto.response.EkycRegisterResponse;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.response.EkycDetailResponse;
import my.com.mandrill.utilities.general.dto.request.EkycCreateRequestDTO;

public interface EkycIntegrationService {

	Ekyc createApplicant(CurrentUserIdDTO user) throws Exception;

	Ekyc saveWithTransaction(Ekyc ekyc, EkycRegisterResponse ekycResponse, EkycAction action) throws Exception;

	Ekyc saveWithTransaction(Ekyc ekyc, EkycImageResponse ekycResponse, EkycAction action) throws Exception;

	void uploadIc(Ekyc ekyc, EkycSubmitICMobileRequest body, String userId) throws Exception;

	void uploadPhoto(Ekyc ekyc, EkycSubmitPhotoMobileRequest body, String userId) throws Exception;

	void submitIC(EkycSubmitICMobileRequest submitICRequest, Ekyc ekyc) throws Exception;

	void submitPhoto(EkycSubmitPhotoMobileRequest ekycSubmitPhotoRequest, Ekyc ekyc) throws Exception;

	Ekyc updateApplicant(CurrentUserIdDTO user, Ekyc ekyc) throws Exception;

	void updateStatus(EkycCallbackRequest request, Ekyc ekyc) throws Exception;

	void resetAttempt(EkycCallbackResetRequest request, Ekyc ekyc) throws Exception;

	void updateNric(Ekyc ekyc) throws Exception;

	Long countCompleted(String userId);

	Ekyc saveEkycTransaction(Ekyc ekyc, String payload, EkycAction action) throws Exception;

	Boolean getKycOverallStatus(String userId);

	EkycDetailResponse findByUserId(String id);

	void updateApplicant(EkycCreateRequestDTO request);

}
