package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.InvestmentAccount;
import my.com.mandrill.component.dto.request.InvestmentAccountRequest;
import my.com.mandrill.component.dto.response.InvestmentAccountResponse;
import my.com.mandrill.component.dto.response.ProviderResponse;
import my.com.mandrill.component.service.InvestmentAccountIntegrationService;
import my.com.mandrill.component.service.InvestmentAccountService;
import my.com.mandrill.component.service.ProviderService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.SavingGoalDeletionDTO;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.SavingGoalAccountType;
import my.com.mandrill.utilities.general.service.KafkaSender;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class InvestmentAccountIntegrationServiceImpl implements InvestmentAccountIntegrationService {

	private final InvestmentAccountService investmentAccountService;

	private final ValidationService validationService;

	private final ProviderService providerService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	private final KafkaSender kafkaSender;

	@Override
	public InvestmentAccount create(InvestmentAccountRequest investmentAccountRequest) {
		validationService.validateInvestmentAccount(investmentAccountRequest, null);
		InvestmentAccount investmentAccount = MapStructConverter.MAPPER.toInvestmentAccount(investmentAccountRequest);
		String userId = SecurityUtil.currentUserId();

		investmentAccount.setUserId(userId);
		return investmentAccountService.save(investmentAccount);
	}

	@Override
	public InvestmentAccount update(String id, InvestmentAccountRequest investmentAccountRequest) {
		validationService.validateInvestmentAccount(investmentAccountRequest, id);
		InvestmentAccount existingInvestmentAccount = investmentAccountService.findByIdAndUserId(id,
				SecurityUtil.currentUserId());
		existingInvestmentAccount.setAmount(investmentAccountRequest.getAmount());
		existingInvestmentAccount.setProviderId(investmentAccountRequest.getProviderId());

		return investmentAccountService.save(existingInvestmentAccount);
	}

	@Override
	public List<InvestmentAccountResponse> getInvestmentList() {
		String userId = SecurityUtil.currentUserId();
		List<InvestmentAccount> investmentAccounts = investmentAccountService.findByUserId(userId);
		if (investmentAccounts == null || investmentAccounts.isEmpty())
			return Collections.emptyList();
		List<InvestmentAccountResponse> responses = new ArrayList<>();
		for (InvestmentAccount investmentAccount : investmentAccounts) {
			InvestmentAccountResponse response = MapStructConverter.MAPPER
					.toInvestmentAccountResponse(investmentAccount);

			ProviderResponse provider = MapStructConverter.MAPPER
					.toProviderResponse(providerService.findById(investmentAccount.getProviderId()));
			response.setProvider(provider);

			responses.add(response);
		}
		return responses;
	}

	@Override
	public NetWorthDTO calculateNetWorth(String userId) {
		List<InvestmentAccount> investmentAccounts = investmentAccountService.findByUserId(userId);
		return NetWorthDTO.builder().assets(
				investmentAccounts.stream().map(InvestmentAccount::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
				.liabilities(BigDecimal.ZERO).build();
	}

	@Override
	public Long count() {
		String userId = SecurityUtil.currentUserId();
		return investmentAccountService.countByUserId(userId);
	}

	@Override
	public void deleteInvestmentAccount(String id) {
		String userId = SecurityUtil.currentUserId();
		InvestmentAccount investmentAccount = investmentAccountService.findByIdAndUserId(id, userId);
		investmentAccountService.delete(investmentAccount);
		kafkaTemplate.send(KafkaTopic.SAVING_GOALS_DELETION, jsonUtil.convertToString(
				new SavingGoalDeletionDTO(userId, investmentAccount.getId(), SavingGoalAccountType.INVESTMENT)));
	}

	@Override
	public void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source) {

		BigDecimal totalInvestmentAmount = calculateNetWorth(userId).getAssets();

		UpdateUserNetWorthTransactionRequest request = UpdateUserNetWorthTransactionRequest.builder().userId(userId)
				.mainType(mainType).subType(subType).amount(totalInvestmentAmount).source(source).build();

		kafkaSender.safeSend(KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC, userId, request);
	}

}
