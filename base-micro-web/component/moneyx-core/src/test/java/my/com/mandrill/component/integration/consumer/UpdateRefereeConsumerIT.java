package my.com.mandrill.component.integration.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.domain.ReferralCode;
import my.com.mandrill.component.domain.Referre;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.ReferralCodeRepository;
import my.com.mandrill.component.repository.ReferreRepository;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import my.com.mandrill.utilities.general.dto.response.UserRefereeUpdateRequest;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.KafkaTemplate;

import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

public class UpdateRefereeConsumerIT extends BaseIntegrationTest {

	private final String VALID_REFERRAL_CODE_1 = "cihuy";

	@Autowired
	private ReferralCodeRepository referralCodeRepository;

	@Autowired
	private ReferreRepository referreRepository;

	@Autowired
	private KafkaTemplate<String, String> kafkaTemplate;

	@Autowired
	private ConsumerFactory<String, String> testConsumerFactory;

	private org.apache.kafka.clients.consumer.Consumer<String, String> sendPNConsumer;

	@Autowired
	private JSONUtil jsonUtil;

	@BeforeAll
	public void setup() {
		sendPNConsumer = testConsumerFactory.createConsumer();
		sendPNConsumer.subscribe(Collections.singletonList(KafkaTopic.SEND_NOTIFICATION));
	}

	@AfterAll
	public void tearDown() {
		if (sendPNConsumer != null) {
			sendPNConsumer.unsubscribe();
			sendPNConsumer.close();
		}

	}

	@Test
	public void firstRelation_success() {
		ReferralCode referralCodes = referralCodeRepository.findAll().stream()
				.filter(referralCode -> VALID_REFERRAL_CODE_1.equals(referralCode.getCode())).findFirst().orElse(null);
		Assertions.assertNotNull(referralCodes);
		String newUserId = "9bf7fe8f-7deb-4342-8cc2-1a1f9282f074";
		Instant now = Instant.now();
		kafkaTemplate.send(KafkaTopic.USER_REFEREE_UPDATE_TOPIC,
				jsonUtil.convertToString(UserRefereeUpdateRequest.builder().source(SourceSystemEnum.MXAPP)
						.referrerCode(referralCodes.getCode()).referredId(newUserId).signUpDate(now).build()));

		Referre expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expectation);

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Referre result = referreRepository.findAll().stream()
					.filter(referre -> referre.getReferredId().equals(newUserId)).findFirst().orElse(null);
			Assertions.assertNotNull(result);
			Assertions.assertNotNull(result);
			Assertions.assertEquals(expectation.getReferredId(), result.getReferredId());
			Assertions.assertEquals(expectation.getReferrerId(), result.getReferrerId());
			Assertions.assertEquals(expectation.getFriendTree(), result.getFriendTree());
			Assertions.assertNotNull(result.getConfirmationDate());

			ConsumerRecords<String, String> records = sendPNConsumer.poll(Duration.ofSeconds(5));
			Assertions.assertTrue(records.count() > 0);
			for (ConsumerRecord<String, String> record : records) {
				if (record.value().contains(KafkaTopic.SEND_NOTIFICATION)
						&& record.value().contains(result.getReferrerId())) {
					Assertions.assertEquals(KafkaTopic.SEND_NOTIFICATION, record.topic());
					Assertions.assertTrue(record.value().contains(KafkaTopic.SEND_NOTIFICATION));
					Assertions.assertTrue(record.value().contains(result.getReferrerId()));
				}
			}
			referralCodeRepository.deleteAll();
			referreRepository.deleteAll();
		});

	}

	@Test
	public void secondRelation_success() {
		ReferralCode referralCodes = referralCodeRepository.findAll().stream()
				.filter(referralCode -> "whisp".equals(referralCode.getCode())).findFirst().orElse(null);
		Assertions.assertNotNull(referralCodes);
		String newUserId = "9bf7fe8f-7deb-4342-8cc2-1a1f9282f074";
		Instant now = Instant.now();
		kafkaTemplate.send(KafkaTopic.USER_REFEREE_UPDATE_TOPIC,
				jsonUtil.convertToString(UserRefereeUpdateRequest.builder().source(SourceSystemEnum.MXAPP)
						.referrerCode(referralCodes.getCode()).referredId(newUserId).signUpDate(now).build()));

		Referre expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expectation);

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Referre result = referreRepository.findAll().stream()
					.filter(referre -> referre.getReferredId().equals(newUserId)).findFirst().orElse(null);
			Assertions.assertNotNull(result);
			Assertions.assertNotNull(result);
			Assertions.assertEquals(expectation.getReferredId(), result.getReferredId());
			Assertions.assertEquals(expectation.getReferrerId(), result.getReferrerId());
			Assertions.assertEquals(expectation.getFriendTree(), result.getFriendTree());
			Assertions.assertNotNull(result.getConfirmationDate());

			ConsumerRecords<String, String> records = sendPNConsumer.poll(Duration.ofSeconds(5));
			Assertions.assertTrue(records.count() > 0);
			for (ConsumerRecord<String, String> record : records) {
				if (record.value().contains(KafkaTopic.SEND_NOTIFICATION)
						&& record.value().contains(result.getReferrerId())) {
					Assertions.assertEquals(KafkaTopic.SEND_NOTIFICATION, record.topic());
					Assertions.assertTrue(record.value().contains(KafkaTopic.SEND_NOTIFICATION));
					Assertions.assertTrue(record.value().contains(result.getReferrerId()));
				}
			}
			referralCodeRepository.deleteAll();
			referreRepository.deleteAll();
		});

	}

	@Test
	public void secondRelation_mxapp_success() {
		ReferralCode referralCodes = referralCodeRepository.findAll().stream()
				.filter(referralCode -> VALID_REFERRAL_CODE_1.equals(referralCode.getCode())).findFirst().orElse(null);
		Assertions.assertNotNull(referralCodes);
		String newUserId = "9bf7fe8f-7deb-4342-8cc2-1a1f9282f074";
		Instant now = Instant.now();
		kafkaTemplate.send(KafkaTopic.USER_REFEREE_UPDATE_TOPIC,
				jsonUtil.convertToString(UserRefereeUpdateRequest.builder().source(SourceSystemEnum.MXAPP)
						.referrerCode(referralCodes.getCode()).referredId(newUserId).signUpDate(now).build()));

		Referre expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(expectation);

		Awaitility.await().atMost(10, TimeUnit.SECONDS).pollInterval(200, TimeUnit.MILLISECONDS).untilAsserted(() -> {
			Referre result = referreRepository.findAll().stream()
					.filter(referre -> referre.getReferredId().equals(newUserId)).findFirst().orElse(null);
			Assertions.assertNotNull(result);
			Assertions.assertNotNull(result);
			Assertions.assertEquals(expectation.getReferredId(), result.getReferredId());
			Assertions.assertEquals(expectation.getReferrerId(), result.getReferrerId());
			Assertions.assertEquals(expectation.getFriendTree(), result.getFriendTree());
			Assertions.assertNotNull(result.getConfirmationDate());

			ConsumerRecords<String, String> records = sendPNConsumer.poll(Duration.ofSeconds(5));
			Assertions.assertTrue(records.count() > 0);
			for (ConsumerRecord<String, String> record : records) {
				if (record.value().contains(KafkaTopic.SEND_NOTIFICATION)
						&& record.value().contains(result.getReferrerId())) {
					Assertions.assertEquals(KafkaTopic.SEND_NOTIFICATION, record.topic());
					Assertions.assertTrue(record.value().contains(KafkaTopic.SEND_NOTIFICATION));
					Assertions.assertTrue(record.value().contains(result.getReferrerId()));
				}
			}
			referralCodeRepository.deleteAll();
			referreRepository.deleteAll();
		});

	}

}
