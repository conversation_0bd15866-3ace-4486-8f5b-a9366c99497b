package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.model.RsmHeaderProductDTO;
import my.com.mandrill.component.dto.request.BizCreateCommissionRequest;
import my.com.mandrill.component.dto.request.RsmHeaderRequest;

public interface ValidationService {

	void validateRsmHeader(RsmHeaderRequest request);

	void validateBizCommission(BizCreateCommissionRequest request);

	RsmHeaderProductDTO validateProduct(RsmHeaderProductDTO request);

	void validateDuplicateRsmHeader(RsmHeaderRequest request);

	void validateCompulsoryFields(RsmHeaderRequest request);

	void validateIsScheduledOrDraftStatus(RsmHeader header);

}
