package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.dto.response.CurrentUserInstitutionResponse;
import my.com.mandrill.component.service.InstitutionService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.dto.InstitutionDTO;
import my.com.mandrill.utilities.general.util.RequestUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@RequiredArgsConstructor
@RequestMapping("/institution")
public class InstitutionController {

	private final InstitutionService institutionService;

	private final ObjectMapper objectMapper;

	@Hidden
	@GetMapping("/integration/ai-mapping/{code}")
	public ResponseEntity<InstitutionDTO> getInstitutionByAiMapping(@PathVariable String code) {
		Institution institution = institutionService.getInstitutionByAiMapping(code);
		return ResponseEntity.ok(objectMapper.convertValue(institution, InstitutionDTO.class));
	}

	@ServiceToServiceAccess
	@GetMapping("/private/ai-mapping/{code}")
	public ResponseEntity<InstitutionDTO> getInstitutionByAiMappingPrivate(@PathVariable String code) {
		Institution institution = institutionService.getInstitutionByAiMapping(code);
		return ResponseEntity.ok(objectMapper.convertValue(institution, InstitutionDTO.class));
	}

	@ServiceToServiceAccess
	@PostMapping("/private/find-by-id")
	public ResponseEntity<List<InstitutionDTO>> findInstitutionById(@RequestBody List<String> ids) {
		return ResponseEntity.ok(institutionService.findInstitutionById(ids).stream()
				.map(MapStructConverter.MAPPER::toInstitutionFeignDTO).toList());
	}

	@Hidden
	@GetMapping("/integration/ai-mapping")
	public ResponseEntity<List<InstitutionDTO>> getAllInstitutionByAiMapping() {
		List<Institution> institutions = institutionService.getListInstitutionByAiMapping();
		return ResponseEntity.ok(institutions.stream()
				.map(institution -> objectMapper.convertValue(institution, InstitutionDTO.class)).toList());
	}

	@Hidden
	@GetMapping("/integration/active/{institutionId}")
	public ResponseEntity<my.com.mandrill.component.dto.model.InstitutionDTO> getActiveInstitutionById(
			@PathVariable String institutionId) {
		Institution result = institutionService.getInstitutionByIdAndActiveTrue(institutionId);
		return ResponseEntity
				.ok(objectMapper.convertValue(result, my.com.mandrill.component.dto.model.InstitutionDTO.class));
	}

	// being use for report
	@Hidden
	@GetMapping("/integration")
	public ResponseEntity<List<InstitutionDTO>> getAllInstitution() {
		List<Institution> result = institutionService.getAllInstitutionsNoPagination();
		return ResponseEntity
				.ok(result.stream().map(el -> objectMapper.convertValue(el, InstitutionDTO.class)).toList());
	}

	@GetMapping("/integrations/current")
	public ResponseEntity<List<CurrentUserInstitutionResponse>> getCurrentUserInstitutions() {
		List<CurrentUserInstitutionResponse> data = institutionService.getCurrentUserInstitution();
		return ResponseEntity.ok(data);
	}

	@GetMapping("/secret-key")
	public String getSecretKey(@RequestHeader(RequestUtil.API_KEY) String apiKey,
			@RequestParam String institutionCode) {
		return institutionService.getInstitutionSecretKey(institutionCode, apiKey);
	}

	@ServiceToServiceAccess
	@GetMapping("/private/secret-key")
	public String getSecretKeyForPublic(@RequestHeader(RequestUtil.API_KEY) String apiKey,
			@RequestParam String institutionCode) {
		return institutionService.getInstitutionSecretKey(institutionCode, apiKey);
	}

	@Hidden
	@GetMapping("/integrations/{institutionId}/ai-mapping")
	public ResponseEntity<Set<String>> getInstitutionAiMapping(@PathVariable String institutionId) {
		return ResponseEntity.ok(institutionService.getInstitutionAiMapping(institutionId));
	}

}
