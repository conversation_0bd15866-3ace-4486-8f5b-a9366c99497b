package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.EpfContribution;
import my.com.mandrill.component.domain.User;

import java.math.BigDecimal;
import java.time.Month;
import java.time.Year;
import java.util.List;
import java.util.Optional;

public interface EpfContributionService {

	Optional<EpfContribution> findByUserAndMonthAndYear(User user, Month month, Year year);

	EpfContribution findLatest(User user);

	EpfContribution save(EpfContribution epfContribution);

	void process(List<EpfContribution> epfContributions);

	BigDecimal findLatestOrReturnDefault(String userId);

}
