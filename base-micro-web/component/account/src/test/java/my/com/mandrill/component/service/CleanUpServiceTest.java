package my.com.mandrill.component.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import my.com.mandrill.component.domain.Expense;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.service.impl.CleanUpServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class CleanUpServiceTest {

	@InjectMocks
	CleanUpServiceImpl cleanUpService;

	@Mock
	KafkaTemplate kafkaTemplate;

	@Mock
	ObjectMapper objectMapper;

	User userMock;

	Expense expenseMock;

	List<Expense> expenseListMock;

	Income incomeMock;

	List<Income> incomeListMock;

	@BeforeEach
	void setup() {
		userMock = new User();

		expenseMock = new Expense();
		expenseMock.setId("id");

		expenseListMock = new ArrayList<>();
		expenseListMock.add(expenseMock);

		incomeMock = new Income();
		incomeListMock = new ArrayList<>();
		incomeListMock.add(incomeMock);
	}

	@Test
	void cleanUpExpenseReminder_Positive() throws JsonProcessingException {
		CompletableFuture<SendResult<String, String>> future = new CompletableFuture<>();

		Mockito.when(kafkaTemplate.send(any(), any(), any())).thenReturn(future);

		cleanUpService.cleanUpExpenseReminder(userMock, expenseListMock);

		verify(kafkaTemplate, times(1)).send(any(), any(), any());
		verify(objectMapper, times(1)).writeValueAsString(any());
	}

}
