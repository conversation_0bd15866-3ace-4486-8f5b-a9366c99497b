package my.com.mandrill.component.config;

import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.model.PropertyStagingDTO;
import my.com.mandrill.component.dto.model.PropertySubTypeDTO;
import my.com.mandrill.component.dto.model.PropertyTypeDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.edgeprop.domain.EdgePropTransaction;
import my.com.mandrill.edgeprop.dto.model.EdgePropTransactionDTO;
import my.com.mandrill.utilities.feign.dto.*;
import my.com.mandrill.utilities.feign.dto.PropertyDTO;
import my.com.mandrill.utilities.feign.dto.model.StandardObjectDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.general.constant.PropertyBuildingType;
import my.com.mandrill.utilities.general.service.LazyLoadingAwareMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MapStructConverter extends LazyLoadingAwareMapper {

	MapStructConverter MAPPER = Mappers.getMapper(MapStructConverter.class);

	ReminderRequest toReminderRequest(ReminderIntegrationRequest reminder);

	PropertyDTO toPropertyDTO(Property property);

	@Mapping(target = "utility.id", source = "utilityId")
	PropertyStagingDTO toPropertyStagingDTO(PropertyStaging propertyStaging);

	List<PropertyTypeDTO> toPropertyTypeDTOList(List<PropertyType> propertyTypes);

	PropertyTypeDTO toPropertyTypeDTO(PropertyType propertyType);

	List<PropertySubTypeDTO> toPropertySubTypeDTOList(List<PropertySubType> propertySubTypes);

	PropertyResponse toPropertyResponse(my.com.mandrill.component.dto.model.PropertyDTO propertyDTO);

	PropertyResponse toPropertyResponse(Property property);

	@Mapping(target = "duration", source = "loan.duration")
	@Mapping(target = "provider.id", source = "loanProviderId")
	@Mapping(target = "amount", source = "loan.amount")
	@Mapping(target = "percentage", source = "loan.percentage")
	@Mapping(target = "repaymentStartYear", source = "loan.repaymentStartYear")
	@Mapping(target = "repaymentStartMonth", source = "loan.repaymentStartMonth")
	@Mapping(target = "monthlyInstallment", source = "loan.monthlyInstallment")
	@Mapping(target = "interestRate", source = "loan.interestRate")
	LoanRequest toLoanRequest(Property property);

	@Mapping(target = "duration", source = "loanDuration")
	@Mapping(target = "provider.id", source = "loanProviderId")
	@Mapping(target = "amount", source = "loanAmount")
	@Mapping(target = "percentage", source = "loanPercentage")
	@Mapping(target = "repaymentStartYear", source = "repaymentStartYear")
	@Mapping(target = "repaymentStartMonth", source = "repaymentStartMonth")
	@Mapping(target = "monthlyInstallment", source = "monthlyInstallment")
	@Mapping(target = "interestRate", source = "interestRate")
	LoanRequest toLoanRequest(PropertyRequest propertyRequest);

	LoanDTO toFeignLoanDTO(LoanRequest loan);

	PropertySubTypeDTO toPropertySubTypeDTO(PropertySubType propertySubType);

	@Mapping(target = "loan.duration", source = "loanDuration")
	@Mapping(target = "loan.amount", source = "loanAmount")
	@Mapping(target = "loan.percentage", source = "loanPercentage")
	@Mapping(target = "loan.repaymentStartYear", source = "repaymentStartYear")
	@Mapping(target = "loan.repaymentStartMonth", source = "repaymentStartMonth")
	@Mapping(target = "loan.monthlyInstallment", source = "monthlyInstallment")
	@Mapping(target = "loan.interestRate", source = "interestRate")
	Property toProperty(PropertyRequest propertyRequest);

	EdgePropTransactionDTO toEdgePropTransactionDTO(EdgePropTransaction edgePropTransaction);

	PropertyParameterResponse toPropertyParameterResponse(PropertyParameter property);

	PropertyParameter toPropertyParameter(PropertyParameterRequest request);

	ProjectUnit toProjectUnit(ProjectUnitRequest request);

	Developer toDeveloper(DeveloperRequest developerRequest);

	@Mapping(target = "countryId", source = "country.id")
	@Mapping(target = "stateId", source = "state.id")
	@Mapping(target = "districtId", source = "district.id")
	@Mapping(target = "cityId", source = "city.id")
	@Mapping(target = "postcodeId", source = "postcode.id")
	@Mapping(target = "showroomCountryId", source = "showroomCountry.id")
	@Mapping(target = "showroomStateId", source = "showroomState.id")
	@Mapping(target = "showroomDistrictId", source = "showroomDistrict.id")
	@Mapping(target = "showroomCityId", source = "showroomCity.id")
	@Mapping(target = "showroomPostcodeId", source = "showroomPostcode.id")
	Project toProject(ProjectRequest data);

	ProjectBlock toBlock(ProjectBlockDTO data);

	ProjectBlockDTO toBlockDto(ProjectBlock data);

	ProjectLot toLot(ProjectLotDTO data);

	ProjectLotDTO toLotDto(ProjectLot data);

	ProjectDuplex toDuplex(ProjectDuplexDTO data);

	ProjectDuplexDTO toDuplexDto(ProjectDuplex data);

	ProjectShoplot toShoplot(ProjectShoplotDTO data);

	ProjectShoplotDTO toShoplotDto(ProjectShoplot data);

	DeveloperDTO toDeveloperDTO(Developer developer);

	ProjectPaginationViewDTO toProjectPaginationViewDTO(Project data);

	@Mapping(target = "id", source = "type.id")
	@Mapping(target = "code", source = "type.code")
	@Mapping(target = "name", source = "type.name")
	@Mapping(target = "isLanded", source = "type.isLanded")
	PropertyParameterViewDTO toPropertyParameterViewDTO(ProjectPropertyType data);

	@Mapping(target = "id", source = "propertyParameter.id")
	@Mapping(target = "code", source = "propertyParameter.code")
	@Mapping(target = "name", source = "propertyParameter.name")
	PropertyParameterViewDTO toPropertyParameterViewDTO(PropertyFacilitiesParameter data);

	@Mapping(target = "id", source = "propertyParameter.id")
	@Mapping(target = "code", source = "propertyParameter.code")
	@Mapping(target = "name", source = "propertyParameter.name")
	PropertyParameterViewDTO toPropertyParameterViewDTO(PropertyFinishingParameter data);

	ProjectDetailViewDTO toProjectDetailViewDTO(Project data);

	PropertyParameterViewDTO toPropertyParameterViewDTO(StandardObjectDTO data);

	ProjectBlockDTO toProjectBlockDTO(ProjectBlock data);

	ProjectLotDTO toProjectLotDTO(ProjectLot data);

	ProjectDuplexDTO toProjectDuplexDTO(ProjectDuplex data);

	ProjectShoplotDTO toProjectShoplotDTO(ProjectShoplot data);

	ProjectImage toProjectImage(ProjectImageRequest data);

	default PurchasePropertySearchResponse toPurchasePropertySearchResponse(Project project, ProjectLike projectLike,
			ProjectUnit cheapestUnit, List<PostcodeDTO> postcodes, List<CityDto> cities) {
		Map<String, PostcodeDTO> postcodeMap = postcodes.stream()
				.collect(Collectors.toMap(PostcodeDTO::getId, postcodeDTO -> postcodeDTO));
		Map<String, CityDto> cityDtoMap = cities.stream().collect(Collectors.toMap(CityDto::getId, cityDto -> cityDto));
		PurchasePropertySearchResponse purchasePropertySearchResponse = new PurchasePropertySearchResponse();
		purchasePropertySearchResponse.setProjectId(project.getId());
		purchasePropertySearchResponse.setName(project.getName());
		purchasePropertySearchResponse.setAddress(project.getAddress());
		purchasePropertySearchResponse.setCityName(cityDtoMap.get(project.getCityId()).getName());

		PostcodeDTO postcodeDTO = postcodeMap.get(project.getPostcodeId());
		purchasePropertySearchResponse.setStateName(postcodeDTO.getState().getName());
		purchasePropertySearchResponse.setCountryName(postcodeDTO.getState().getCountry().getName());
		purchasePropertySearchResponse.setPostcode(postcodeDTO.getPostcode());
		purchasePropertySearchResponse.setLike(projectLike != null);
		purchasePropertySearchResponse.setLikeAt(projectLike != null ? projectLike.getCreatedDate() : null);

		purchasePropertySearchResponse.setImages(project.getImages().stream().map(projectImage -> {
			PropertyImagesDTO propertyImagesDTO = new PropertyImagesDTO();
			propertyImagesDTO.setUrl(projectImage.getImageUrl());
			propertyImagesDTO.setRank(String.valueOf(projectImage.getRank()));
			return propertyImagesDTO;
		}).toList());

		PropertyDeveloperDTO propertyDeveloperDTO = new PropertyDeveloperDTO();
		propertyDeveloperDTO.setName(project.getDeveloper().getName());
		propertyDeveloperDTO.setOverview(project.getDeveloper().getOverview());
		propertyDeveloperDTO.setImage(project.getDeveloper().getImageUrl());
		purchasePropertySearchResponse.setDeveloper(propertyDeveloperDTO);

		PropertyDetailDTO propertyDetailDTO = new PropertyDetailDTO();
		propertyDetailDTO.setPrice(cheapestUnit.getPrice());
		propertyDetailDTO.setBedroom(cheapestUnit.getBedroom());
		propertyDetailDTO.setBathroom(cheapestUnit.getBathroom());
		propertyDetailDTO.setSize(cheapestUnit.getSize());
		purchasePropertySearchResponse.setPropertyDetail(propertyDetailDTO);

		return purchasePropertySearchResponse;
	}

	@Mapping(target = "id", source = "code")
	PropertyParameterViewDTO toPropertyParameterViewDTO(PropertyBuildingType data);

	ProjectUnitResponse toProjectUnitResponse(ProjectUnit data);

	@Mapping(target = "images", ignore = true)
	PropertyUnitDetailResponse toProjectUnitDetailResponse(ProjectUnit data);

	@Mapping(target = "tenure", ignore = true)
	@Mapping(target = "category", ignore = true)
	@Mapping(target = "facilities", ignore = true)
	@Mapping(target = "propertyTypes", ignore = true)
	@Mapping(target = "images", ignore = true)
	PurchasePropertyDetailResponse toPurchasePropertyDetailResponse(Project data);

	ProjectImageDTO toProjectImageDTO(ProjectImage data);

	@Mapping(target = "showroomAddress", source = "project.showroomAddress")
	@Mapping(target = "developerName", source = "project.developer.name")
	@Mapping(target = "projectName", source = "project.name")
	@Mapping(target = "projectAddress", source = "project.address")
	@Mapping(target = "unitName", source = "projectUnit.name")
	@Mapping(target = "cashbackValue", source = "projectUnit.cashbackValue")
	PropertyInterestPageDTO toPropertyInterestPageDTO(PropertyInterest data);

	PropertyParameterViewDTO toPropertyParameterViewDTO(PropertyParameter data);

	@Mapping(target = "incomeAmount", source = "income")
	@Mapping(target = "phoneNumber", source = "mobileNumber")
	@Mapping(target = "refNo", source = "applicationId")
	@Mapping(target = "fullName", source = "name")
	@Mapping(target = "showroomAddress", source = "project.showroomAddress")
	@Mapping(target = "providerId", source = "project.developer.id")
	@Mapping(target = "providerName", source = "project.developer.name")
	@Mapping(target = "productName", source = "project.name")
	@Mapping(target = "projectName", source = "project.name")
	@Mapping(target = "projectAddress", source = "project.address")
	@Mapping(target = "unitName", source = "projectUnit.name")
	@Mapping(target = "cashbackAmount", source = "projectUnit.cashbackValue")
	@Mapping(target = "source", source = "source.name")
	UserInterestRecordRSMPaginationDTO toUserInterestRecordRSMPaginationDTO(PropertyInterest propertyInterest);

	@Mapping(target = "phoneNumber", source = "mobileNumber")
	@Mapping(target = "refNo", source = "applicationId")
	@Mapping(target = "fullName", source = "name")
	@Mapping(target = "showroomAddress", source = "project.showroomAddress")
	@Mapping(target = "providerId", source = "project.developer.id")
	@Mapping(target = "providerName", source = "project.developer.name")
	@Mapping(target = "productId", source = "project.id")
	@Mapping(target = "productName", source = "project.name")
	@Mapping(target = "projectName", source = "project.name")
	@Mapping(target = "projectAddress", source = "project.address")
	@Mapping(target = "unitName", source = "projectUnit.name")
	@Mapping(target = "cashbackAmount", source = "projectUnit.cashbackValue")
	UserInterestRecordRSMViewDTO toUserInterestRecordRSMViewDTO(PropertyInterest propertyInterest);

	StandardObjectDTO toStandardObjectDTO(DeveloperSelectViewProjection data);

	StandardObjectDTO toStandardObjectDTO(ProjectSelectViewProjection data);

}