package my.com.mandrill.component.service;

import jakarta.servlet.http.HttpServletRequest;
import my.com.mandrill.component.domain.Transaction;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.Status;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface TransactionService {

	Transaction save(Transaction transaction);

	Transaction saveAndFlush(Transaction transaction);

	Transaction findById(String id, String userId);

	Transaction findByIdEntityId(String entityId);

	Transaction findById(String id);

	List<Transaction> findAll(String userId, List<EntityName> entityNames, Sort sort);

	RedirectView response(Map<String, String> response, RedirectAttributes redirectAttributes,
			HttpServletRequest request);

	List<Transaction> findByEntityName(String userId, EntityName entityName, String entityId, Sort sort);

	Optional<Transaction> findByRefNo(String refNo);

	Page<Transaction> findAllTransaction(EntityName entityName, LocalDate startDate, LocalDate endDate,
			Pageable pageable);

	Page<Transaction> findAllTransaction(EntityName entityName, List<Status> statuses, LocalDate startDate,
			LocalDate endDate, Pageable pageable);

	List<Transaction> findAllTransactionForReport(EntityName entityName, LocalDate startDate, LocalDate endDate);

	List<Transaction> findAllTransactionForReport(EntityName entityName, LocalDate startDate, LocalDate endDate,
			List<Status> statuses);

	Long countTransaction(EntityName entityName, LocalDate startDate, LocalDate endDate);

	List<Transaction> findAllPrepaidTopUpHistory(String transactionId, String userId);

	Page<Transaction> findByUserIdAndEntityName(String userId, EntityName entityName, List<Status> statuses,
			Pageable pageable);

	List<Transaction> findResolveAble(EntityName entityName, Instant pointer);

}
