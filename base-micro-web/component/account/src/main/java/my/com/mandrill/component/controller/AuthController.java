package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.AuthenticateResultDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.SignInResponse;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.feign.dto.AuthenticateDTO;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.request.HashRequest;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.HttpRequestUtil;
import my.com.mandrill.utilities.general.util.PhoneNumberUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static my.com.mandrill.utilities.general.util.RequestUtil.CLIENT_PLATFORM;

@Tag(name = "01-authentication")
@Slf4j
@RestController
@RequestMapping("authenticate")
@RequiredArgsConstructor
public class AuthController {

	private final AuthService authService;

	private final DeviceKeyIntegrationService deviceKeyIntegrationService;

	private final TokenProvider tokenProvider;

	private final UserService userService;

	private final AppUserService appUserService;

	private final UserIntegrationService userIntegrationService;

	private final AuthIntgService authIntgService;

	private final HttpRequestUtil httpRequestUtil;

	private final RedisService redisService;

	@SecurityRequirements
	@PostMapping
	public ResponseEntity<SignInResponse> authenticateUser(@Valid @RequestBody SignInRequest signInRequest,
			@RequestParam AccessTypeEnum accessType, @RequestParam LoginTypeEnum loginType,
			@RequestParam(required = false, defaultValue = "PASSWORD") PasscodeType passcodeType,
			@RequestHeader(required = false, name = CLIENT_PLATFORM) ClientPlatformType clientPlatform,
			@RequestParam(required = false) String deviceId, HttpServletRequest httpServletRequest) {
		log.debug("REST request to sign in : request: {}", signInRequest);
		AuthServiceRequest authServiceRequest = AuthServiceRequest.builder().username(signInRequest.getUsername())
				.password(signInRequest.getPassword()).rememberMe(signInRequest.isRememberMe()).accessType(accessType)
				.loginType(loginType).passcodeType(passcodeType).deviceId(deviceId).clientPlatform(clientPlatform)
				.httpDetailDTO(httpRequestUtil.parseResource(httpServletRequest)).build();

		String key = String.format(CacheKey.LOGIN_CONCURRENT_CACHE_KEY, authServiceRequest.getUsername(),
				authServiceRequest.getLoginType());
		AuthenticateResultDTO authResult = redisService.executeSingleProcess(key, authServiceRequest.getUsername(),
				() -> authIntgService.authenticateUser(authServiceRequest), ErrorCodeGlobalEnum.RUNTIME_EXCEPTION,
				TimeUnit.MINUTES);

		return ResponseEntity.ok(
				new SignInResponse(authResult.getAccessToken(), authResult.getRefreshToken(), authResult.getUserId()));
	}

	@SecurityRequirements
	@PostMapping("/signature-challenge")
	public ResponseEntity<SignInResponse> authenticateUser(
			@Valid @RequestBody SignInSignatureChallengeRequest signInRequest, HttpServletRequest httpServletRequest) {
		log.debug("REST request to signature-challenge : request: {}", signInRequest);

		PhoneNumberUtil.ExtractedPhoneNumber phoneNumber = PhoneNumberUtil
				.getPhoneNumberWithNoCountryCode(signInRequest.getUsername());
		String countryCode = phoneNumber.getPhoneCountry();
		String phone = phoneNumber.getPhoneNumber();

		AppUser appUser = appUserService.findAuthByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(phone,
				countryCode, LoginTypeEnum.USER);
		SignatureChallenge signatureChallenge = MapStructConverter.MAPPER.toSignatureChallenge(signInRequest);

		try {
			userIntegrationService.failLoginCheck(appUser, true);
			deviceKeyIntegrationService.assertionFinish(signatureChallenge, appUser);
		}
		catch (RuntimeException e) {
			log.error("/signature-challenge error: {}", e.getMessage());
			userIntegrationService.failLoginCheck(appUser, false);
			throw e;
		}

		AuthenticateResultDTO result = authIntgService.createSignatureToken(appUser, httpServletRequest);
		return ResponseEntity
				.ok(new SignInResponse(result.getAccessToken(), result.getRefreshToken(), result.getUserId()));
	}

	@Hidden
	@PostMapping("password")
	public ResponseEntity<AuthenticateDTO> authenticateUserWithPassword(
			@Valid @RequestBody AuthenticateUserRequest request) throws BadCredentialsException {
		log.debug("REST request to sign in authentication: accessType: {}, loginType:{}, username:{}, passcode: {}",
				request.accessType(), request.loginType(), request.passcodeType(), request.username());
		return ResponseEntity.ok(authIntgService.authenticatePassword(request));
	}

	@Hidden
	@PostMapping("check-password")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public void checkPassword(@Valid @RequestBody CheckPasswordRequest request) throws AccessDeniedException {
		User user = userService.findByRefNo(SecurityUtil.currentUserLogin());
		authService.checkPassword(user, request.password());
	}

	@PostMapping("/hash")
	@SecurityRequirements
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public void authenticateHash(@Valid @RequestBody HashRequest requestBody, HttpServletRequest request) {
		authService.authenticateHash(requestBody, request);
	}

	@PostMapping("/livechat-token")
	public ResponseEntity<SignInResponse> authenticateUser() {
		User user = userService.getCurrentUser();
		String accessToken = tokenProvider.createLiveChatToken(user.getId(), Optional.ofNullable(user.getEmail())
				.orElse(Strings.concat(user.getPhoneCountry(), user.getPhoneNumber())), user.getFullName());
		return ResponseEntity.ok(new SignInResponse(accessToken, null, user.getId()));
	}

	@PostMapping("/infobip-inbox-token")
	public ResponseEntity<SignInResponse> generateInfobipInboxToken() {
		String userId = SecurityUtil.currentUserId();
		String accessToken = tokenProvider.generateInfobipInboxToken(userId);
		return ResponseEntity.ok(new SignInResponse(accessToken, null, userId));
	}

}
