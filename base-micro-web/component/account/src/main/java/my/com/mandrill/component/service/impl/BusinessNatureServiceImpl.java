package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.BusinessNature;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.BusinessNatureRepository;
import my.com.mandrill.component.service.BusinessNatureService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessNatureServiceImpl implements BusinessNatureService {

	private final BusinessNatureRepository businessNatureRepository;

	@Override
	public BusinessNature createValidation(BusinessNature businessNature) {
		this.existsByName(businessNature.getName());
		return businessNature;
	}

	@Override
	public BusinessNature findById(String id) {
		return this.businessNatureRepository.findById(id)
				.orElseThrow(ExceptionPredicate.businessNatureNotFoundById(id));
	}

	@Override
	public Page<BusinessNature> findAll(Pageable pageable, String name) {
		return this.businessNatureRepository.findAllByNameContainsIgnoreCase(pageable,
				StringUtils.defaultIfBlank(name, StringUtils.EMPTY));
	}

	@Override
	public BusinessNature updateValidation(BusinessNature businessNature) {
		BusinessNature existingBusinessNature = this.findById(businessNature.getId());

		if (!existingBusinessNature.getName().equalsIgnoreCase(businessNature.getName())) {
			this.existsByName(businessNature.getName());
		}

		existingBusinessNature.setName(businessNature.getName());
		existingBusinessNature.setDescription(businessNature.getDescription());
		existingBusinessNature.setActive(businessNature.isActive());

		return existingBusinessNature;
	}

	@Override
	@Transactional
	public void deleteById(String id) {
		BusinessNature businessNature = this.businessNatureRepository.findById(id)
				.orElseThrow(ExceptionPredicate.businessNatureNotFoundById(id));
		businessNature.setActive(false);
		this.businessNatureRepository.save(businessNature);
	}

	private void existsByName(String name) {
		if (this.businessNatureRepository.existsByNameAllIgnoreCase(name)) {
			throw new BusinessException(ErrorCodeEnum.BUSINESS_NATURE_EXISTED);
		}
	}

	@Override
	@Transactional
	public BusinessNature save(BusinessNature businessNature) {
		return this.businessNatureRepository.save(businessNature);
	}

}
