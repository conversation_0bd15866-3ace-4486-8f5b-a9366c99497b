package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.model.ExtendAccessTokenDTO;
import my.com.mandrill.component.dto.model.RevokeRequestDTO;
import my.com.mandrill.component.dto.request.AuthServiceRequest;
import my.com.mandrill.component.dto.request.TokenSearchRequest;
import my.com.mandrill.component.dto.response.TokenServiceResponse;
import my.com.mandrill.component.repository.jpa.TokenRepository;
import my.com.mandrill.component.repository.jpa.TokenTransactionRepository;
import my.com.mandrill.component.repository.jpa.specification.TokenSpecification;
import my.com.mandrill.component.service.TokenService;
import my.com.mandrill.utilities.core.dto.model.UserActivityDTO;
import my.com.mandrill.utilities.feign.dto.GlobalSystemConfigurationDTO;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.HttpDetailDTO;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.exception.UnauthorizedException;
import my.com.mandrill.utilities.general.service.RedisService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TokenServiceImpl implements TokenService {

	private final TokenRepository tokenRepository;

	private final TokenTransactionRepository tokenTransactionRepository;

	private final ProxyFeignClient proxyFeignClient;

	private final BaseProperties baseProperties;

	private final RedisService redisService;

	private final FeatureFlagOutbound featureFlagOutbound;

	@Override
	public TokenServiceResponse createToken(AuthServiceRequest request, User user) {
		long refreshTokenInSecond = getValidToken(request.isRememberMe());

		Instant validity = Instant.now().plusSeconds(refreshTokenInSecond);
		Token token = Token.builder().user(user).deviceId(request.getDeviceId())
				.refreshToken(UUID.randomUUID().toString()).loginType(request.getLoginType())
				.accessType(request.getAccessType()).passcodeType(request.getPasscodeType())
				.rememberMe(request.isRememberMe()).refreshTokenValidity(validity).status(TokenStatus.ACTIVE)
				.platform(request.getClientPlatform()).build();
		if (isTransactionEnabled()) {
			Set<TokenTransaction> tokenTransactionSet = new HashSet<>();
			TokenTransaction tokenTransaction = createTokenTransaction(request.getHttpDetailDTO(), token);
			tokenTransactionSet.add(tokenTransaction);
			token.setTokenTransactions(tokenTransactionSet);
		}
		token = tokenRepository.save(token);

		Set<SimpleGrantedAuthority> grantedAuthorities = token.getUser().getAuthorities().stream()
				.filter(authority -> authority.getInstitution().getActive() && authority.getActive())
				.map(Authority::getPermissions).filter(Objects::nonNull).flatMap(Collection::stream)
				.map(Permission::getCode).map(SimpleGrantedAuthority::new).collect(Collectors.toSet());

		return TokenServiceResponse.builder().grantedAuthorities(grantedAuthorities).accessToken(token.getId())
				.refreshToken(token.getRefreshToken()).userRefNo(user.getRefNo()).userId(user.getId())
				.username(request.getUsername()).build();
	}

	@Transactional
	@Override
	public void createUserActivity(UserActivityDTO userActivityDTO) {
		if (isTransactionEnabled()) {
			tokenRepository.findById(userActivityDTO.getTokenId()).ifPresent(token -> tokenTransactionRepository
					.save(createTokenTransaction(userActivityDTO.getHttpDetailDTO(), token)));
		}
	}

	private boolean isTransactionEnabled() {
		return featureFlagOutbound.isFeatureEnabled(GlobalSystemConfigurationEnum.TOKEN_TRANSACTION_ENABLED);
	}

	@Transactional
	@Override
	public Page<TokenServiceResponse> findAllPaginate(TokenSearchRequest request, Pageable pageable) {
		Specification<TokenTransaction> spec = TokenSpecification.withFilters(request);
		return tokenTransactionRepository.findAll(spec, pageable)
				.map(MapStructConverter.MAPPER::toTokenServiceResponse);
	}

	@Transactional
	@Override
	public Token revokeToken(RevokeRequestDTO request) {
		Optional<Token> tokenOptional;
		if (StringUtils.isNotBlank(request.getAccessToken())) {
			tokenOptional = tokenRepository.findByIdAndStatus(request.getAccessToken(), TokenStatus.ACTIVE);
		}
		else {
			tokenOptional = tokenRepository.findByRefreshTokenAndStatus(request.getRefreshToken(), TokenStatus.ACTIVE);
		}

		if (tokenOptional.isPresent()) {
			Token token = tokenOptional.get();
			token.setStatus(TokenStatus.REVOKED);
			token.setRevokedReason(request.getRevokedReason());
			token.setRevokedDate(Instant.now());
			return tokenRepository.save(token);
		}
		throw new BusinessException(ErrorCodeGlobalEnum.ENTITY_NOT_FOUND);
	}

	@Override
	public void revokeAllToken(String reason) {
		boolean morePages = true;
		int pageNumber = 0;
		while (morePages) {
			Pageable pageable = PageRequest.of(pageNumber, baseProperties.getFeature().getRevokeBatchSize());
			Page<Token> tokenPage = tokenRepository.findByStatus(TokenStatus.ACTIVE, pageable);
			if (tokenPage.hasContent()) {
				List<Token> tokens = tokenPage.getContent();
				tokens.forEach(token -> {
					token.setStatus(TokenStatus.REVOKED);
					token.setRevokedReason(reason);
					token.setRevokedDate(Instant.now());
				});
				tokenRepository.saveAll(tokens);
			}

			morePages = tokenPage.hasNext();
			pageNumber++;
		}
	}

	@Override
	public List<Token> deleteToken(User user) {
		List<Token> tokens = tokenRepository.findAllByUser(user);
		if (CollectionUtils.isNotEmpty(tokens)) {
			tokenRepository.deleteAll(tokens);
		}
		return tokens;
	}

	@Override
	public void logoutToken(String accessToken) {
		Token token = tokenRepository.findByIdAndStatus(accessToken, TokenStatus.ACTIVE)
				.orElseThrow(ExceptionPredicate.tokenNotFound(accessToken));
		token.setStatus(TokenStatus.LOGOUT);
		tokenRepository.save(token);
	}

	@Override
	public List<TokenServiceResponse> revokedByUser(User user, ClientPlatformType platform, TokenStatus revokedStatus) {
		List<Token> tokens = tokenRepository.findByUserAndStatusAndPlatform(user, TokenStatus.ACTIVE, platform);
		return updateStatus(revokedStatus, tokens);
	}

	private List<TokenServiceResponse> updateStatus(TokenStatus status, List<Token> tokens) {
		if (CollectionUtils.isNotEmpty(tokens)) {
			tokens.forEach(token -> {
				token.setStatus(status);
				token.setRevokedDate(Instant.now());
			});
			return tokenRepository.saveAll(tokens).stream()
					.map(token -> TokenServiceResponse.builder().accessToken(token.getId()).build()).toList();
		}
		return new ArrayList<>();
	}

	@Override
	public List<TokenServiceResponse> revokedByUser(User user) {
		List<Token> tokens = tokenRepository.findAllByUserAndStatus(user, TokenStatus.ACTIVE);
		return updateStatus(TokenStatus.REVOKED_BY_UPDATED_USER, tokens);
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW, noRollbackFor = BusinessException.class)
	@Override
	public TokenServiceResponse extendToken(ExtendAccessTokenDTO request) {
		Optional<Token> refreshTokenOpt = tokenRepository.findByRefreshTokenAndStatus(request.getRefreshToken(),
				TokenStatus.ACTIVE);
		if (refreshTokenOpt.isEmpty()) {
			throw new UnauthorizedException();
		}
		Token token = refreshTokenOpt.get();
		token.setStatus(TokenStatus.EXPIRED);
		if (token.getRefreshTokenValidity().isBefore(Instant.now())) {
			tokenRepository.saveAndFlush(token);
			throw new UnauthorizedException();
		}
		tokenRepository.save(token);

		return this.createToken(
				AuthServiceRequest.builder().deviceId(token.getDeviceId()).rememberMe(token.isRememberMe())
						.passcodeType(token.getPasscodeType()).loginType(token.getLoginType())
						.accessType(token.getAccessType()).httpDetailDTO(request.getHttpDetailDTO()).build(),
				token.getUser());
	}

	private TokenTransaction createTokenTransaction(HttpDetailDTO httpDetailDTO, Token token) {
		return TokenTransaction.builder().token(token).user(token.getUser()).uri(httpDetailDTO.getUri())
				.ipAddress(httpDetailDTO.getIpAddress()).method(httpDetailDTO.getMethod())
				.service(httpDetailDTO.getService()).build();
	}

	// TODO: need to make sure how many second for refresh token validity
	private long getValidToken(boolean isRememberMe) {
		GlobalSystemConfigurationEnum sysConf = isRememberMe
				? GlobalSystemConfigurationEnum.REFRESH_TOKEN_VALIDITY_REMEMBER_ME_IN_SECOND
				: GlobalSystemConfigurationEnum.REFRESH_TOKEN_VALIDITY_IN_SECOND;
		return Long.parseLong(getGlobalSystemConfiguration(sysConf).getValue());
	}

	private GlobalSystemConfigurationDTO getGlobalSystemConfiguration(
			GlobalSystemConfigurationEnum systemConfiguration) {
		return redisService
				.getFromHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION, systemConfiguration.getCode(),
						GlobalSystemConfigurationDTO.class)
				.orElseGet(() -> proxyFeignClient.getCommonFeignClient()
						.getIntegrationGlobalSystemConfigurationByCode(systemConfiguration.getCode()));
	}

}
