<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <property name="now" value="now()" dbms="h2, mysql, mariadb,postgresql"/>

    <include file="liquibase/changelog/ai-component_20240402_weishun_0001_duplicate_table_product_platfrom_from_common_schema.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/ai-component_20240402_weishun_0002_duplicate_table_product_group_from_common_schema.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/ai-component_20240402_weishun_0003_duplicate_table_product_type_from_common_schema.xml"
             relativeToChangelogFile="false"/>

    <include file="liquibase/changelog/ai-component_20240611_weishun_0001_create_table_vehicle_application.xml"/>
    <include file="liquibase/changelog/ai-component_20240727_weishun_0001_add_product_platform_product_type.xml"/>
    <include file="liquibase/changelog/ai-component_20240806_weishun_0001_add_running_number.xml"/>
    <include file="liquibase/changelog/ai-component_20240806_weishun_0002_add_column_email_for_table_vehicle_application.xml"/>
    <include file="liquibase/changelog/ai-component_20240816_weishun_0001_create_table_vehicle_loan_calculation.xml"/>
    <include file="liquibase/changelog/ai-component_20240819_weishun_0001_alter_table_vehicle_loan_calculation_add_column_event_type.xml"/>
    <include file="liquibase/changelog/ai-component_20240822_weishun_0001_add_product_group_product_type.xml"/>
    <include file="liquibase/changelog/ai-component_20240904_weishun_0001_alter_table_vehicle_application_add_column_user_income.xml"/>
    <include file="liquibase/changelog/ai-component_20250225_monika_0001_add_product_group_product_type.xml"/>
    <include file="liquibase/changelog/ai-component_20250226_monika_0001_product_type_update.xml"/>
    <include file="liquibase/changelog/ai-component_20250227_monika_0001_product_type_and_group_insert.xml"/>
    <include file="liquibase/changelog/ai-component_20250502_monika_0001_alter_table_product_type_add_sequence.xml"/>
    <include file="liquibase/changelog/ai-component_20250519_deniarianto_0001_add_product_type_legal_credit_profile_renew_energy.xml"/>
    <include file="liquibase/changelog/ai-component_20250522_kuswandi_0001_add_product_type.xml"/>
    <include file="/liquibase/changelog/ai-component_20250526_deniarianto_0001_create_table_ai_migration.xml"/>
    <include file="/liquibase/changelog/ai-component_20250527_deniarianto_0001_add_bank_section_revamp.xml"/>
    <include file="/liquibase/changelog/ai-component_20250528_deniarianto_0001_remove_loan_child_from_group.xml"/>
    <include file="/liquibase/changelog/ai-component_20250529_deniarianto_0001_migrate_product.xml"/>
    <include file="/liquibase/changelog/ai-component_20250530_deniarianto_0001_hide_unnecessary_product_type.xml"/>
    <include file="/liquibase/changelog/ai-component_20250530_deniarianto_0002_sorting_product_homescreen_redesign.xml"/>
    <include file="/liquibase/changelog/ai-component_20250531_deniarianto_0001_fixing_credit_profile_missing_type.xml"/>
    <include file="/liquibase/changelog/ai-component_20250603_deniarianto_0001_add_personal_loan.xml"/>
    <include file="liquibase/changelog/ai-component_20250603_kuswandi_0001_add_product_type.xml"/>
</databaseChangeLog>
