package my.com.mandrill.utilities.feign.client;

import jakarta.validation.Valid;
import my.com.mandrill.utilities.feign.dto.*;
import my.com.mandrill.utilities.feign.dto.model.LoanPlusReportDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.request.BankReportRequest;
import my.com.mandrill.utilities.feign.dto.request.CreateManualLeadRequest;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.feign.dto.response.BankIncludedDTO;
import my.com.mandrill.utilities.feign.dto.response.LoanAggregateResponseDTO;
import my.com.mandrill.utilities.feign.dto.response.PaymentAccountDetailResponse;
import my.com.mandrill.utilities.feign.dto.response.UserInterestVaultResponse;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.BankDTO;
import my.com.mandrill.utilities.general.dto.BankDetailDTO;
import my.com.mandrill.utilities.general.dto.BankListDTO;
import my.com.mandrill.utilities.general.util.DateUtil;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@FeignClient("bank-component")
public interface BankFeignClient {

	@GetMapping("bank")
	BankResponse getBank();

	@GetMapping("bank/integration")
	List<BankDTO> getBanks(@RequestParam(required = false) AccountType accountType);

	@GetMapping("/bank/{id}")
	BankDTO getBankById(@PathVariable String id);

	@PostMapping("/loans/integration")
	LoanDTO loanIntegration(@RequestBody LoanDTO insuranceDTO);

	@GetMapping("/loans/integration")
	List<LoanDTO> getLoans(@RequestParam LoanTypeEnum loanType);

	@GetMapping("/loans/{entityName}/{entityId}")
	LoanDTO findLoanByEntityNameAndEntityId(@PathVariable EntityName entityName, @PathVariable String entityId);

	@PutMapping("/loans/integration/{id}")
	LoanDTO updateLoan(@RequestBody LoanDTO loanDTO, @PathVariable String id);

	@DeleteMapping("/loans/{id}")
	void deleteLoan(@PathVariable String id);

	@GetMapping("/loans/{id}")
	LoanDTO findLoanById(@PathVariable String id);

	@GetMapping("/loans")
	List<LoanDTO> findLoanAll(Sort sort);

	@PutMapping("bank/vault/link/{id}")
	void linkVault(@PathVariable String id, @Valid @RequestBody VaultLinkDTO vaultLinkDTO);

	@GetMapping("bank/vault/linked/{attachmentGroupId}")
	BankDetailVaultLinkDTO findLinkedVault(@PathVariable String attachmentGroupId);

	@GetMapping("bank/vault/unlinked")
	List<BankDetailVaultLinkDTO> getBankDetailWithAttachmentGroupIdNull();

	@GetMapping("bank/count")
	Long countBank(@RequestParam EntityName entityName);

	@GetMapping("loans/count")
	Long countLoan();

	@GetMapping("/bank/vault/is-linked/{attachmentGroupId}")
	Boolean existsByUserIdAndAttachmentGroupId(@PathVariable String attachmentGroupId);

	@GetMapping("/bank/integration/detailed-net-worth")
	List<DetailedNetWorthDTO> calculateDetailedNetWorth();

	@GetMapping("/bank-list/integration/issuer-codes")
	String getIssuerCodesByIsPartnerTrue();

	@GetMapping("/bank-list/private/issuer-codes")
	String getIssuerCodeByIsPartnerTruePrivate();

	@PostMapping("/user-interest-record/integration")
	List<UserInterestRecordDTO> userInterestRecord(@RequestBody BankReportRequest request);

	@GetMapping("/user-interest-record/integration/count")
	Long userInterestCount(@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate);

	@GetMapping("/bank-list/integration/issuer-codes/{issuerCode}")
	BankListDTO getBankByIssuerCode(@PathVariable String issuerCode);

	@GetMapping("/bank-list/private/issuer-codes/{issuerCode}/{productId}")
	BankListDTO getBankByIssuerCodeAndProductId(@PathVariable String issuerCode,
			@PathVariable(required = false) String productId);

	@GetMapping("loan-limits/count")
	Long countLoanLimit();

	@GetMapping("/user-interest-record/integration/redirect")
	List<UserInterestedRedirectDTO> userInterestReport(
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate);

	@GetMapping("/user-interest-record/integration/redirect/count")
	Long userInterestRedirectCount(@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate);

	@GetMapping("loan-limits/integration/count-by-date")
	long countLoanLimitByCreatedDate(@RequestParam Instant createdDateStart, @RequestParam Instant createdDateEnd);

	@GetMapping("loan-limits")
	LoanLimitDTO findLoanLimit();

	@GetMapping("bank-details/integration/count-card-added-via-campaign")
	long countCardAddedViaCampaign(@RequestParam Instant createdDateStart, @RequestParam Instant createdDateEnd,
			@RequestParam String advertisementId);

	@GetMapping("bank-details/{id}")
	BankDetailDTO findBankDetailById(@PathVariable String id,
			@RequestParam(required = false, defaultValue = "true") boolean populateReminder);

	@GetMapping("bank-details")
	List<BankDetailDTO> findBankDetails(@RequestParam List<AccountType> accountTypes);

	@GetMapping("loan-eligibilities/integration/report")
	List<LoanPlusReportDTO> exportLoanPlusDataReport(
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate startDate,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate endDate);

	@PostMapping("/bank-list/integration/bank-included")
	List<BankIncludedDTO> getBankListByListOfIssuerCode(@RequestBody List<String> issuerCodes);

	@GetMapping("loans/summary")
	LoanAggregateResponseDTO summaryLoan();

	@GetMapping("loans/summary/details")
	List<LoanDTO> findLoanDetails();

	@GetMapping("user-interest-record/integration/count-alliance-bank-campaign")
	Long countAllianceBankCampaign(@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate dateFrom,
			@RequestParam @DateTimeFormat(pattern = DateUtil.DATE_FORMAT) LocalDate dateTo,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone,
			@RequestParam(required = false) String issuerCode, @RequestParam(required = false) String productType,
			@RequestParam(required = false) UserInterestedSource source);

	@GetMapping("loans/integrations/linked-entity")
	List<String> getLinkedEntities(@RequestParam EntityName entityName);

	@PutMapping("loans/integrations/vault/link/{id}")
	void linkLoanVault(@PathVariable String id, @Valid @RequestBody VaultLinkDTO vaultLinkDTO);

	@GetMapping("user-interest-record/integration/{vaultId}/user-interest-record")
	String findUserInterestIssuerCodeByVaultId(@PathVariable String vaultId);

	@GetMapping("admin/user-interest/integration/{recordId}/vault-ids")
	UserInterestVaultResponse findVaultIdsByRecordId(@PathVariable String recordId);

	@GetMapping("bank-list/private/check-exception/{productId}")
	Boolean checkProductInException(@PathVariable String productId);

	@GetMapping("/admin/payment-account/private/get-payment-account/{userId}")
	PaymentAccountDetailResponse getPaymentAccount(@PathVariable String userId);

	@GetMapping("/user-interest-record/private/detail/{id}")
	UserInterestRecordRSMViewDTO findUserInterestedDetailId(@PathVariable String id);

	@GetMapping("bank-list/integration")
	List<ProviderDTO> getAllActiveBankList(Sort sort);

	@PostMapping("/user-interest-record/private/pagination")
	Page<UserInterestRecordRSMPaginationDTO> findRsmLead(@RequestParam int size, @RequestParam int page,
			@RequestParam String sort, @RequestBody RSMLeadRequest request);

	@PostMapping("/user-interest-record/private/report")
	List<UserInterestRecordRSMPaginationDTO> findRsmLeadReport(@RequestBody RSMLeadRequest request);

	@PutMapping("/user-interest-record/private/rsm-info")
	void updateUserInterestedRSMInfo(@RequestBody LeadRSMUpdateRequest request);

	@GetMapping("/user-interest-record/private/application-ids-except-user-ids")
	Set<String> findAllApplicationIdsExcludingUserIdsIn(@RequestParam Set<String> applicationIds,
			@RequestParam Set<String> userIds);

	@PostMapping("/admin/user-interest/private/integration/manual")
	void createManualLead(@Valid @RequestBody CreateManualLeadRequest createManualLeadRequest);

	@PostMapping("/admin/user-interest/private/integration/manual/redirect")
	void createManualLeadRedirect(@Valid @RequestBody CreateManualLeadRequest createManualLeadRequest);

}
