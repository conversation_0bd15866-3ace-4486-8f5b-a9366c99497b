package my.com.mandrill.component.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.dto.model.LoanDTO;
import my.com.mandrill.component.dto.request.CreateLoanV2;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.component.service.PopulateService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/v2/loans")
@RequiredArgsConstructor
public class LoanControllerV2 {

	private final ********************** loanIntegrationService;

	private final ValidationService validationService;

	private final CommonFeignClient commonFeignClient;

	private final PopulateService populateService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_CREATE)")
	public ResponseEntity<LoanDTO> create(@Valid @RequestBody CreateLoanV2 createLoanV2) {
		Loan loan = MapStructConverter.MAPPER.toLoan(createLoanV2);

		String userId = SecurityUtil.currentUserId();

		validationService.validateCreate(loan, userId);
		Loan result = loanIntegrationService.save(loan);
		commonFeignClient.calculateIntegrationKYLL();
		loanIntegrationService.sendDashboardActivity(result.getCreatedDate());

		loanIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.LIABILITIES, NetWorthType.LOANS,
				NetWorthSource.BANK);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toLoanDTO(result));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<LoanDTO>> findAll(Sort sort) {
		List<Loan> result = loanIntegrationService.findAllV2(SecurityUtil.currentUserId(), sort);
		return ResponseEntity.ok(result.stream().map(MapStructConverter.MAPPER::toLoanDTO).toList());
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<LoanDTO> findById(@PathVariable("id") String id) {
		Loan loan = loanIntegrationService.findByIdV2(id, SecurityUtil.currentUserId());
		return ResponseEntity.ok(MapStructConverter.MAPPER.toLoanDTO(loan));
	}

	@PutMapping("/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<LoanDTO> update(@Valid @RequestBody CreateLoanV2 request, @PathVariable String id) {
		String userId = SecurityUtil.currentUserId();
		Loan loan = MapStructConverter.MAPPER.toLoan(request);

		loan.setId(id);
		Loan existLoan = validationService.validateUpdateV2(loan, userId);
		Loan result = loanIntegrationService.save(existLoan);
		commonFeignClient.calculateIntegrationKYLL();

		loanIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.LIABILITIES, NetWorthType.LOANS,
				NetWorthSource.BANK);

		return ResponseEntity.ok(MapStructConverter.MAPPER.toLoanDTO(result));

	}

	@DeleteMapping("{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void delete(@PathVariable String id) {

		String userId = SecurityUtil.currentUserId();
		loanIntegrationService.deleteV2(id, userId);

		loanIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.LIABILITIES, NetWorthType.LOANS,
				NetWorthSource.BANK);

	}

}
