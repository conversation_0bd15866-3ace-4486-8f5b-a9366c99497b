package my.com.mandrill.component.service;

import my.com.mandrill.component.dto.request.PropertyInterestRequest;
import my.com.mandrill.component.dto.request.PropertySearchRequest;
import my.com.mandrill.component.dto.response.PurchasePropertyDetailResponse;
import my.com.mandrill.component.dto.response.PurchasePropertySearchResponse;
import my.com.mandrill.utilities.feign.dto.request.CreateManualLeadRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;

public interface PurchasePropertyIntgService {

	Page<PurchasePropertySearchResponse> searchProperty(PropertySearchRequest propertySearchRequest, String userId,
			Pageable pageable);

	List<PurchasePropertySearchResponse> myFavorites(Instant cursor, int limit);

	PurchasePropertyDetailResponse getProjectDetail(String id);

	void interest(PropertyInterestRequest request);

	void createManualLead(CreateManualLeadRequest createManualLeadRequest);

	void toggleLike(String projectId);

}
