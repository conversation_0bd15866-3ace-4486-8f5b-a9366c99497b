package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.User;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DeviceKeyRepository extends JpaRepository<DeviceKey, String> {

	List<DeviceKey> findByUser(@NonNull AppUser user, Sort sort);

	boolean existsByUserAndDeviceId(@NonNull AppUser user, @NonNull String deviceId);

	Optional<DeviceKey> findFirstByUserOrderByCreatedDateAsc(AppUser user);

	long countByUser(@NonNull AppUser user);

	Optional<DeviceKey> findByUserAndDeviceId(@NonNull AppUser user, @NonNull String deviceId);

}