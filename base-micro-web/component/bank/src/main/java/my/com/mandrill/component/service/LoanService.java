package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.dto.request.AttachEntityRequest;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.LoanTypeEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

public interface LoanService {

	Loan save(Loan loan);

	List<Loan> saveAll(List<Loan> loans);

	Loan findById(String id, String userId);

	List<Loan> findByUserId(String userId);

	Loan findByEntityNameAndEntityId(EntityName entityName, String entityId, String userId);

	Optional<Loan> findOptionalByEntityNameAndEntityId(EntityName entityName, String entityId, String userId);

	List<Loan> findAll(String userId, Sort sort);

	void delete(Loan loan);

	long count(String userId);

	List<Loan> findByUserIdAndTypeIn(String userId, Collection<LoanTypeEnum> types);

	Optional<Loan> findByIdAndUserId(String id, String userId);

	BigDecimal countTotalByType(String userId, LoanTypeEnum type);

	List<Loan> findLoanForKYLL(String userId);

	List<Loan> findDetached(String userId, LoanTypeEnum loanType);

	List<Loan> findAttachedLoan(String userId, EntityName entityName);

	void attachAttachmentGroupId(String id, String userId, String attachmentGroupId);

	void attachEntity(AttachEntityRequest request, String id, String userId);

	<T> T withDetachedCheck(String loanId, Supplier<T> executor);

	BigDecimal calculateLoanEndingBalance(Loan loan);

	Page<Loan> findAllOnGoingLoans(Pageable pageable);

}
