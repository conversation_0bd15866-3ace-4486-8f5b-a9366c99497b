package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.User;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;

public interface DeviceKeyService {

	List<DeviceKey> findAllDeviceKey(Sort sort, AppUser user);

	Device<PERSON>ey saveDeviceKey(DeviceKey deviceKey);

	SignatureChallenge saveSignatureChallenge(SignatureChallenge signatureChallenge);

	Optional<DeviceKey> findOptionalDeviceKeyByDeviceIdAndUser(String deviceId, AppUser user);

	DeviceKey findDeviceKeyByDeviceIdAndUser(String deviceId, AppUser user);

	SignatureChallenge findSignatureChallengeByIdAndDeviceKey(String id, DeviceKey deviceKey);

	void deleteOldestDevice<PERSON>ey(AppUser user);

	long countByUser(AppUser user);

	Optional<SignatureChallenge> findSignatureChallengeById(String signatureChallengeId);

}
