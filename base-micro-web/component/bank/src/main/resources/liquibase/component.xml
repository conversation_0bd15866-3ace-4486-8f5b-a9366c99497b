<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <include file="liquibase/changelog/bank-component_20230331_wuikeat_0001_create_table_for_bank_module.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230331_wuikeat_0002_insert_data_for_my_bank_picklist.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230404_wuikeat_0001_alter_entity_bank_list.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230407_jose_0001_update_data_url.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230411_wuikeat_0001_alter_entity_bank_detail.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230419_muhdlaziem_0001_add_loan_table.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230505_jose_0001_alter_table_bank_list.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230503_wuikeat_0001_alter_bank_detail_column_interest_rate.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230511_wuikeat_0001_alter_bank_detail.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230512_wuikeat_0001_alter_attachment_group_column.xml"
             relativeToChangelogFile="false"/>
    <include file="liquibase/changelog/bank-component_20230515_andy_0001_loan_update.xml"/>
    <include file="liquibase/changelog/bank-component_20230515_andy_0002_loan_update.xml"/>
    <include file="liquibase/changelog/bank-component_20230531_andy_0001_loan_add_label.xml"/>
    <include file="liquibase/changelog/bank-component_20230602_wuikeat_0001_loan_add_percentage.xml"/>
    <include file="liquibase/changelog/bank-component_20230606_andy_0001_add_account_type_loan.xml"/>
    <include file="liquibase/changelog/bank-component_20230607_muhdlaziem_0001_account_type_enum.xml"/>
    <include file="liquibase/changelog/bank-component_20230608_muhdlaziem_0001_delete_bank_account_type.xml"/>
    <include file="liquibase/changelog/bank-component_20230625_andy_0001_encryption_migration.xml"/>
    <include file="liquibase/changelog/bank-component_20230625_andy_0002_encryption_update.xml"/>
    <include file="liquibase/changelog/bank-component_20230703_wuikeat_0001_bank_db_constraint.xml"/>
    <include file="liquibase/changelog/bank-component_20230711_wuikeat_0001_add_card_product_in_bank_detail.xml"/>
    <include file="liquibase/changelog/bank-component_20230717_andy_0001_bank_list_is_partner.xml"/>
    <include file="liquibase/changelog/bank-component_20230720_andy_0001_bank_list_issuer_code.xml"/>
    <include file="liquibase/changelog/bank-component_20230714_maulana_0001_user_interest_record.xml"/>
    <include file="liquibase/changelog/bank-component_20230714_maulana_0002_alter_entity_bank_list.xml"/>
    <include file="liquibase/changelog/bank-component_20230720_maulana_0001_alter_bank_list.xml"/>
    <include file="liquibase/changelog/bank-component_20230720_maulana_0002_alter_user_interest_record.xml"/>
    <include file="liquibase/changelog/bank-component_20230728_maulana_0001_alter_user_interest.xml"/>
    <include file="liquibase/changelog/bank-component_20230912_muhdlaziem_0001_add_ref_no_user_interest_record.xml"/>
    <include file="liquibase/changelog/bank-component_20230914_andy_0001_bank_list_update.xml"/>
    <include file="liquibase/changelog/bank-component_20230918_andy_0001_bank_list_update.xml"/>
    <include file="liquibase/changelog/bank-component_20230922_andy_0001_bank_list_update.xml"/>
    <include file="liquibase/changelog/bank-component_20230930_andy_0001_bank_list_update.xml"/>
    <include file="liquibase/changelog/bank-component_20231011_andy_0001_bank_list_update.xml"/>
    <include file="liquibase/changelog/bank-component_20230912_wuikeat_0001_loan_limit_table.xml"/>
    <include file="liquibase/changelog/bank-component_20230927_maulana_0001_update_amount_for_loan_vehicle.xml"/>
    <include file="liquibase/changelog/bank-component_20231023_andy_0001_bank_list_update.xml"/>
    <include
            file="liquibase/changelog/bank-component_20231108_wuikeat_0001_add_is_bank_statement_ai_supported_column.xml"/>
    <include file="/liquibase/changelog/bank-component_20231127_muhdlaziem_0001_loan_eligibility_check_init.xml"/>
    <include
            file="/liquibase/changelog/bank-component_20231130_muhdlaziem_0001_loan_eligibility_add_phone_country.xml"/>
    <include file="/liquibase/changelog/bank-component_20231202_muhdlaziem_0001_finology_bank_list_mapping.xml"/>
    <include
            file="/liquibase/changelog/bank-component_20231202_muhdlaziem_0002_loan_eligibilty_pre_approval column.xml"/>
    <include file="/liquibase/changelog/bank-component_20231203_muhdlaziem_0001_use_existing_bank.xml"/>
    <include file="/liquibase/changelog/bank-component_20231203_muhdlaziem_0002_stp_bank_assignment.xml"/>
    <include file="liquibase/changelog/bank-component_20231125_muhdlaziem_0001_user_interested_nric.xml"/>
    <include file="liquibase/changelog/bank-component_20231130_loongyeat_0001_add_reapply_date_column.xml"/>
    <include file="liquibase/changelog/bank-component_20231210_muhdlaziem_0001_extend_dsr_percentage.xml"/>
    <include file="/liquibase/changelog/bank-component_20231213_muhdlaziem_0001_loan_eligibility_encryption.xml"/>
    <include file="/liquibase/changelog/bank-component_20231215_andy_0001_user_interest_update.xml"/>
    <include file="/liquibase/changelog/bank-component_20231215_muhdlaziem_0001_column-all-dsr-related.xml"/>
    <include
            file="/liquibase/changelog/bank-component_20231215_wuikeat_0001_add_advertisement_id_into_bank_detail.xml"/>
    <include file="/liquibase/changelog/bank-component_20231216_muhdlaziem_0001_add_net_salary_kyll.xml"/>
    <include file="/liquibase/changelog/bank-component_20231219_muhdlaziem_0001_is_epf_uploaded_flag.xml"/>
    <include file="/liquibase/changelog/bank-component_20231219_wuikeat_0001_add_bank_list_icon_url.xml"/>
    <include
            file="/liquibase/changelog/bank-component_20231220_wuikeat_0001_add_bank_list_icon_attachment_group_id.xml"/>
    <include file="/liquibase/changelog/bank-component_20231221_muhdlaziem_0001_running_number_tz.xml"/>
    <include file="/liquibase/changelog/bank-component_20231225_muhdlaziem_0001_kyll_remove_epf_flag.xml"/>
    <include file="/liquibase/changelog/bank-component_20240101_muhdlaziem_0001_running_number_refactor.xml"/>
    <include
            file="/liquibase/changelog/bank-component_20240122_wuikeat_0001_rename_bank_detail_reminder_to_isreminder.xml"/>
    <include file="/liquibase/changelog/bank-component_20240321_muhdlaziem_0001_add_balance_transfer_flag.xml"/>
    <include file="/liquibase/changelog/bank-component_20240328_muhdlaziem_0001_add_source.xml"/>
    <include
            file="/liquibase/changelog/bank-component_20240418_wuikeat_0001_add_user_ref_no_on_user_interest_record.xml"/>
    <include
            file="/liquibase/changelog/bank-component_20240423_wuikeat_0001_loan_eligibility_applicant_add_nric_and_ref_no.xml"/>
    <include
            file="/liquibase/changelog/bank-component_20240424_wuikeat_0001_loan_eligibility_applicant_sql_update_ref_no_nric.xml"/>
    <include file="liquibase/changelog/bank-component_20240429_weishun_0001_update_is_bank_statement_ai_supported.xml"/>
    <include file="liquibase/changelog/bank-component_20240513_wuikeat_0001_add_monthly_payment_due_date.xml"/>
    <include file="/liquibase/changelog/bank-component_20240601_andy_0001_partner_product_exception.xml"/>
    <include file="liquibase/changelog/bank-component_20240619_kuswandi_0001_credit_card_type.xml"/>
    <include file="liquibase/changelog/bank-component_20240619_kuswandi_0002_credit_card_type.xml"/>
    <include file="/liquibase/changelog/bank-component_20240623_maulana_0001_alter_loan.xml"/>
    <include file="/liquibase/changelog/bank-component_20240702_maulana_0001_drop_null_constraint_uj_loan_limit.xml"/>
    <include file="liquibase/changelog/bank-component_20240707_kuswandi_0001_refinance_calculation.xml"/>
    <include file="liquibase/changelog/bank-component_20240717_kuswandi_0001_adjust_card.xml"/>
    <include file="liquibase/changelog/bank-component_20240806_kuswandi_0001_alter_user_interest.xml"/>
    <include file="liquibase/changelog/bank-component_20240806_maulana_0001_user_interest_record_vault.xml"/>
    <include file="liquibase/changelog/bank-component_20240806_farhanah_0001_create_product_configuration.xml"/>
    <include file="liquibase/changelog/bank-component_20240808_farhanah_0001_create_product_config_template.xml"/>
    <include file="liquibase/changelog/bank-component_20240814_farhanah_0001_add_product_config_template.xml"/>
    <include file="liquibase/changelog/bank-component_20240812_andy_0001_update_bank_list_code.xml"/>
    <include file="liquibase/changelog/bank-component_20240819_farhanah_0001_rename_product_config_template.xml"/>
    <include file="liquibase/changelog/bank-component_20240822_kuswandi_0001_alter_product_config_template.xml"/>
    <include file="liquibase/changelog/bank-component_20240822_kuswandi_0002_alter_product_config.xml"/>
    <include file="liquibase/changelog/bank-component_20240823_kuswandi_0001_alter_product_config.xml"/>
    <include file="liquibase/changelog/bank-component_20240912_chooiyie_0001_alter_user_interest_record_url.xml"/>
    <include file="liquibase/changelog/bank-component_20241009_kuswandi_0001_table_user_interest_record_addon.xml"/>
    <include
            file="liquibase/changelog/bank-component_20240928_weishun_0001_add_loan_eligibility_lender_product_table.xml"/>
    <include file="liquibase/changelog/bank-component_20241015_kuswandi_0001_add_banklist.xml"/>
    <include file="liquibase/changelog/bank-component_20241013_monika_0001_table_bank_product_exception_add_platform.xml"/>
    <include file="liquibase/changelog/bank-component_20241016_kuswandi_0001_product-exception.xml"/>
    <include file="liquibase/changelog/bank-component_20241016_kuswandi_0002_product-configuration.xml"/>
    <include
            file="liquibase/changelog/bank-component_20241101_fadhlan_0001_alter_user_interest_record_income_amount.xml"/>
    <include
            file="liquibase/changelog/bank-component_20241101_fadhlan_0001_alter_user_interest_record_income_amount.xml"/>
    <include file="liquibase/changelog/bank-component_20241112_kuswandi_0001_alter_product_exception.xml"/>
    <include file="liquibase/changelog/bank-component_20241112_fadhlan_0001_add_juara_banklist.xml"/>
    <include file="liquibase/changelog/bank-component_20241115_kuswandi_0001_product_exception_remove.xml"/>
    <include file="liquibase/changelog/bank-component_20250115_deniarianto_0001_add_table_payment_account.xml"/>
    <include file="liquibase/changelog/bank-component_20250116_monika_0001_alter_payment_account_add_ic_number.xml"/>
    <include file="liquibase/changelog/bank-component_20250122_kuswandi_0001_alter_user_interestd_record.xml"/>
    <include file="liquibase/changelog/bank-component_20250123_deniarianto_0001_alter_payment_account_table.xml"/>
    <include file="liquibase/changelog/bank-component_20250127_kuswandi_0001_alter_user_interestd_record.xml"/>
    <include file="liquibase/changelog/bank-component_20250210_deniarianto_0001_create_sequence_number_payment_account.xml"/>
    <include file="liquibase/changelog/bank-component_20250210_deniarianto_0002_payment_account_attachment_id.xml"/>
    <include file="liquibase/changelog/bank-component_20250305_loongyeat_0001_bank_list_add_type_column.xml"/>
    <include file="liquibase/changelog/bank-component_20250519_monika_0001_loan_add_status_loan_balance.xml"/>
    <include file="liquibase/changelog/bank-component_20250609_diknes_0001_alter_user_interest_record.xml"/>
</databaseChangeLog>