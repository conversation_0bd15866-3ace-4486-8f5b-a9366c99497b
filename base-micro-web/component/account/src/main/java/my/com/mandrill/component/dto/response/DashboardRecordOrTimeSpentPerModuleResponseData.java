package my.com.mandrill.component.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.dto.DateDetailDTO;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DashboardRecordOrTimeSpentPerModuleResponseData implements Serializable {

	private DateDetailDTO dateFrom;

	private DateDetailDTO dateTo;

	private DateDetailDTO yearMonth;

	private long banksCount = 0L;

	private long loansCount = 0L;

	private long propertiesCount = 0L;

	private long vehiclesCount = 0L;

	private long utilitiesCount = 0L;

	private long insuranceCount = 0L;

	private long creditCardCount = 0L;

	private long remindersCount = 0L;

	private long finAnalysisCount = 0L;

	private long voucherCount = 0L;

	private long campaignCardCount = 0L;

	private long campaignLoanLimitCount = 0L;

	private long campaignAllianceBankCreditCardCount = 0L;

	private long mAndACount = 0L;

	private long solarooCount = 0L;

	private long eWillCount = 0L;

	private long campaignEWillCount = 0L;

	private long stashAwayCount = 0L;

	private long campaignStashAwayCount = 0L;

	private long milieuSolarCount = 0L;

	private long creditBureauCount = 0L;

	private long easiWillCount = 0L;

	private long ctosCount = 0L;

}
