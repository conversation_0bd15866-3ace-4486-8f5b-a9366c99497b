package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.LoanTypeEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface LoanRepository extends JpaRepository<Loan, String> {

	long countByUserId(@NonNull String userId);

	List<Loan> findByUserId(@NonNull String userId, Sort sort);

	List<Loan> findByUserId(@NonNull String userId);

	Optional<Loan> findByUserIdAndEntityNameAndEntityId(@NonNull String userId, @NonNull EntityName entityName,
			@NonNull String entityId);

	Optional<Loan> findByIdAndUserId(@NonNull String id, @NonNull String userId);

	List<Loan> findByUserIdAndTypeIn(@NonNull String userId, @NonNull Collection<LoanTypeEnum> types);

	@Query("""
			select sum(l.monthlyInstallment) from Loan l where l.type = :loanTypeEnum and l.userId = :userId
			""")
	BigDecimal countTotalAmountByLoanType(String userId, LoanTypeEnum loanTypeEnum);

	List<Loan> findAllByUserIdAndTypeInOrderByCreatedDateDesc(String userId, List<LoanTypeEnum> type);

	List<Loan> findByUserIdAndEntityIdAndType(@NonNull String userId, String entityId, @NonNull LoanTypeEnum loanType);

	List<Loan> findByUserIdAndEntityNameAndEntityIdIsNotNull(@NonNull String userId, @NonNull EntityName entityName);

	Optional<Loan> findFirstByUserIdAndAttachmentGroupId(String userId, String attachmentGroupId);

	@Modifying
	@Query("UPDATE Loan l set l.entityId = null, l.entityName = null where l.id = ?1")
	void setNullEntityIdAndEntityName(String loanId);

	@Query("""
			    SELECT l
			    FROM Loan l
			    WHERE l.paymentStatus IS NULL
			       OR l.paymentStatus = my.com.mandrill.utilities.general.constant.LoanPaymentStatus.ON_GOING
			""")
	Page<Loan> findAllNullOrOngoingStatus(Pageable pageable);

}