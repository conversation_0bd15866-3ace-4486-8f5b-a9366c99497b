package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.dto.response.CurrentUserInstitutionResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
// @JaversSpringDataAuditable
public interface InstitutionRepository extends JpaRepository<Institution, String> {

	Optional<Institution> findByIdAndActiveTrue(String id);

	List<Institution> findByParentInstitutionId(String parentId);

	@Query("SELECT i FROM Institution i WHERE LOWER(i.name) LIKE %:name% AND (:parentId IS NULL OR i.parentInstitution.id = :parentId)")
	Page<Institution> findAllByNameAndParentInstitutionId(Pageable pageable, String name, String parentId);

	@Query("SELECT i FROM Institution i WHERE i.path LIKE CONCAT(:path, '/', '%') AND i.active = TRUE")
	List<Institution> findAllChildInstitutionsByPathAndActiveTrue(String path);

	@Query("SELECT i FROM Institution i WHERE i.path LIKE CONCAT(:path, '/', '%') OR i.path like :path AND i.active = TRUE")
	List<Institution> findAllChildInstitutionsByPathAndActiveTrue(String path, Sort sort);

	@Query("SELECT i FROM Institution i WHERE (i.path LIKE CONCAT(:path, '/', '%') OR i.path = :path) AND i.active = TRUE "
			+ "AND (i.id IN :providerIds)")
	List<Institution> findAllChildInstitutionsByPathAndIdsAndActiveTrue(String path, List<String> providerIds,
			Sort sort);

	List<Institution> findByUsersUsernameAndActiveTrueOrderByTierAscNameAsc(@NonNull String username);

	boolean existsByName(String name);

	@Query("SELECT i FROM Institution i WHERE i.aiMapping LIKE %:aiMapping%")
	Optional<Institution> findByAiMapping(String aiMapping);

	List<Institution> findAllByAiMappingIsNotNull();

	List<Institution> findAllByPathContainingAndActiveTrue(String path);

	@Query("""
				SELECT new my.com.mandrill.component.dto.response.CurrentUserInstitutionResponse(i.id, i.name)
				FROM User u JOIN u.institutions i WHERE u.refNo = :refNo
			""")
	List<CurrentUserInstitutionResponse> getCurrentUserInstitutions(String refNo);

	@Query("""
			SELECT DISTINCT i.aiMapping FROM Institution i WHERE i.path LIKE :path AND i.aiMapping IS NOT NULL""")
	Set<String> findAiMappingByPath(String path);

	List<Institution> findByIdIn(List<String> ids);

	List<Institution> findByIdIn(List<String> ids, Sort sort);

}
