package my.com.mandrill.component.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import my.com.mandrill.utilities.core.audit.AuditSection;
import org.hibernate.Hibernate;

import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "product_group", uniqueConstraints = { @UniqueConstraint(columnNames = { "code" }) })
public class ProductGroup extends AuditSection {

	@Size(max = 255)
	@NotBlank
	@Column(name = "code", nullable = false)
	private String code;

	@Size(max = 255)
	@NotBlank
	@Column(name = "value", nullable = false)
	private String value;

	@Column(name = "sequence")
	private Integer sequence;

	@ToString.Exclude
	@EqualsAndHashCode.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "parent_id")
	private ProductGroup parent;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "parent")
	private Set<ProductGroup> subMenus;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "productGroup")
	private Set<ProductType> productTypes;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		ProductGroup identityType = (ProductGroup) o;
		return getId() != null && Objects.equals(getId(), identityType.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}
