package my.com.mandrill.component.exception;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.exception.ApiError;
import my.com.mandrill.utilities.general.exception.GlobalExceptionHandler;
import my.com.mandrill.utilities.general.util.TraceContextHelper;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class AccountExceptionHandler extends ResponseEntityExceptionHandler {

	private final TraceContextHelper traceContextHelper;

	@Override
	@NonNull
	public ResponseEntity<Object> handleMethodArgumentNotValid(@NonNull MethodArgumentNotValidException ex,
			@NonNull HttpHeaders headers, @NonNull HttpStatusCode status, @NonNull WebRequest request) {
		return GlobalExceptionHandler.handleMethodDefault(ex, headers, status, request);
	}

	@ExceptionHandler(InvalidOtpException.class)
	public ResponseEntity<ApiError> handleOtpException(InvalidOtpException invalidOtpException) {
		return buildResponseEntity(invalidOtpException.getApiError());
	}

	private ResponseEntity<ApiError> buildResponseEntity(ApiError apiError) {
		apiError.setRequestId(traceContextHelper.getRequestId());
		log.warn(
				"AccountExceptionHandler requestId={} | platform={} | code={} | status={} | message={} | debug message={}",
				traceContextHelper.getRequestId(), traceContextHelper.getPlatformClient(), apiError.getErrorCode(),
				apiError.getStatus(), apiError.getMessage(), apiError.getDebugMessage());
		return new ResponseEntity<>(apiError, apiError.getStatus());
	}

}
