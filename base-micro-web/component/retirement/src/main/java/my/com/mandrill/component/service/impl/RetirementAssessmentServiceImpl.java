package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.client.RetirementClient;
import my.com.mandrill.component.constant.RetirementAssessmentKey;
import my.com.mandrill.component.domain.UserRetirementAssessmentAnswer;
import my.com.mandrill.component.dto.request.client.RetirementAssessmentClientRequest;
import my.com.mandrill.component.service.RetirementAssessmentService;
import my.com.mandrill.component.service.UserRetirementAssessmentAnswerService;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RetirementAssessmentServiceImpl implements RetirementAssessmentService {

	private final RetirementClient retirementClient;

	private final UserRetirementAssessmentAnswerService userRetirementAssessmentAnswerService;

	@Override
	public ResponseEntity<String> findLatestRetirementAssessmentReport(String userId) {
		return retirementClient.findLatestRetirementAssessmentReport(userId);
	}

	@Override
	public ResponseEntity<String> findLatestRetirementAssessmentAnswer(String userId) {
		return retirementClient.findLatestRetirementAssessmentAnswer(userId);
	}

	@Override
	public ResponseEntity<String> processRetirementAssessment(
			RetirementAssessmentClientRequest retirementAssessmentClientRequest) {

		userRetirementAssessmentAnswerService.save(UserRetirementAssessmentAnswer.builder()
				.userId(retirementAssessmentClientRequest.getUserId()).key(RetirementAssessmentKey.ASSET_ARRANGEMENT)
				.value(retirementAssessmentClientRequest.getAssetsArrangements()).build());

		return retirementClient.processRetirementAssessment(retirementAssessmentClientRequest);
	}

}
