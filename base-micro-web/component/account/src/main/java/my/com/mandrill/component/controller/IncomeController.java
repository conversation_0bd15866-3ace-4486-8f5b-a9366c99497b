package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.EmploymentType;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.IncomeType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.component.dto.request.IncomeRequest;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.general.constant.Constant;
import my.com.mandrill.utilities.general.dto.request.ObjectRequest;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@Tag(name = "12-income")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("incomes")
public class IncomeController {

	private final EmploymentTypeService employmentTypeService;

	private final IncomeService incomeService;

	private final IncomeIntegrationService incomeIntegrationService;

	private final IncomeTypeService incomeTypeService;

	private final PopulateService populateService;

	private final CommonFeignClient commonFeignClient;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<IncomeDTO> create(@Valid @RequestBody IncomeRequest incomeRequest) {

		Income income = MapStructConverter.MAPPER.toIncome(incomeRequest);
		User user = new User(SecurityUtil.currentUserId());
		income = incomeService.create(income, user);
		income = incomeService.save(income);

		if (isSalaryAndFullTimeEmployee(incomeRequest.getIncomeType(), incomeRequest.getEmploymentType())) {
			commonFeignClient.calculateIntegrationKYLL();
		}

		if (incomeRequest.getIsReminder() != null && incomeRequest.getReminder() != null) {
			incomeIntegrationService.processReminder(income, incomeRequest.getIsReminder(),
					incomeRequest.getReminder());
		}

		return ResponseEntity.ok(MapStructConverter.MAPPER.toIncomeDTOModel(income));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<IncomeDTO>> findByUserAndIncomeType(@RequestParam(required = false) String incomeTypeId,
			@RequestParam(required = false) String employmentTypeId, Sort sort) {
		IncomeType incomeType = StringUtils.isNotBlank(incomeTypeId) ? incomeTypeService.findById(incomeTypeId) : null;
		EmploymentType employmentType = StringUtils.isNotBlank(employmentTypeId)
				? employmentTypeService.findById(employmentTypeId) : null;
		User user = new User(SecurityUtil.currentUserId());
		List<Income> result = incomeService.findByUserAndIncomeTypeAndEmploymentType(user, incomeType, employmentType,
				sort);
		return ResponseEntity.ok(result.stream().map(MapStructConverter.MAPPER::toIncomeDTOModel).toList());
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<IncomeDTO> findById(@PathVariable String id,
			@RequestParam(required = false, defaultValue = "true") boolean populateReminder) {
		User user = new User(SecurityUtil.currentUserId());
		Income income = incomeService.findByIdAndUser(id, user);

		IncomeDTO result = MapStructConverter.MAPPER.toIncomeDTOModel(income);
		if (populateReminder) {
			populateService.populateIncomeReminder(result);
		}
		return ResponseEntity.ok(result);
	}

	@GetMapping("salary")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<IncomeDTO> findByUserAndIncomeType(@RequestParam String employmentTypeId,
			@RequestParam(required = false, defaultValue = "true") boolean populateReminder) {
		EmploymentType employmentType = employmentTypeService.findById(employmentTypeId);

		Income income = incomeService.findUserIncomeSalary(SecurityUtil.currentUserId(), employmentType);

		IncomeDTO result = MapStructConverter.MAPPER.toIncomeDTOModel(income);
		if (populateReminder) {
			populateService.populateIncomeReminder(result);
		}
		return ResponseEntity.ok(result);
	}

	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<IncomeDTO> update(@PathVariable String id, @Valid @RequestBody IncomeRequest incomeRequest) {

		Income income = MapStructConverter.MAPPER.toIncome(incomeRequest);
		User user = new User(SecurityUtil.currentUserId());
		income = incomeService.update(id, income, user);
		income = incomeService.save(income);

		if (isSalaryAndFullTimeEmployee(incomeRequest.getIncomeType(), incomeRequest.getEmploymentType())) {
			commonFeignClient.calculateIntegrationKYLL();
		}

		if (incomeRequest.getIsReminder() != null) {
			incomeIntegrationService.processReminder(income, incomeRequest.getIsReminder(),
					incomeRequest.getReminder());
		}

		return ResponseEntity.ok(MapStructConverter.MAPPER.toIncomeDTOModel(income));
	}

	private boolean isSalaryAndFullTimeEmployee(ObjectRequest incomeType, ObjectRequest employmentType) {
		return Constant.SALARY_INCOME_TYPE_ID.equals(incomeType.getId())
				&& Constant.FULL_TIME_EMPLOYMENT_TYPE_ID.equals(employmentType.getId());
	}

	@DeleteMapping("{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void delete(@PathVariable String id) {
		User user = new User(SecurityUtil.currentUserId());
		incomeIntegrationService.delete(id, user);
	}

	@GetMapping("/integrations/current")
	public ResponseEntity<List<IncomeDTO>> getAllIncome() {
		List<IncomeDTO> result = incomeIntegrationService.getUserCurrentIncomes();
		return ResponseEntity.ok(result);
	}

	@ServiceToServiceAccess
	@GetMapping("/private/find-salary")
	public BigDecimal findSalaryIncome(@RequestParam String userId) {
		return incomeService.findIncome(userId);
	}

}
