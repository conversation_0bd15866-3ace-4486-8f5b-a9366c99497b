package my.com.mandrill.component.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.request.RetirementCalculationRequest;
import my.com.mandrill.component.dto.request.client.RetirementAssessmentClientRequest;
import my.com.mandrill.component.dto.response.RetirementCalculationResponse;
import my.com.mandrill.component.service.RetirementAssessmentService;
import my.com.mandrill.component.service.RetirementIntegrationService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.CurrentUserIdDTO;
import my.com.mandrill.utilities.feign.dto.UserDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

@Slf4j
@RestController
@RequestMapping("retirements")
@RequiredArgsConstructor
public class RetirementController {

	private final AccountFeignClient accountFeignClient;

	private final RetirementIntegrationService retirementIntegrationService;

	private final RetirementAssessmentService retirementAssessmentService;

	private final ValidationService validationService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final ObjectMapper objectMapper;

	@PostMapping("calculation")
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<RetirementCalculationResponse> calculateRetirement(
			@Valid @RequestBody RetirementCalculationRequest request) throws JsonProcessingException {

		log.info("Retirement Calculation Request : {}", request);

		CurrentUserIdDTO userDTO = accountFeignClient.getCurrentUserId();

		validationService.validateRetirementAge(request.getRetirementAge(), request.getLifeExpectancy());

		RetirementCalculationResponse response = retirementIntegrationService.processRetirementCalculation(request,
				userDTO.getId());

		if (!request.getCurrentAge().equals(userDTO.getAge())) {
			UserDTO updateUserAgeRequest = UserDTO.builder().id(userDTO.getId()).age(request.getCurrentAge()).build();
			kafkaTemplate.send(KafkaTopic.UPDATE_USER_TOPIC, objectMapper.writeValueAsString(updateUserAgeRequest));
		}

		return ResponseEntity.ok(response);
	}

	@GetMapping("calculation")
	public ResponseEntity<RetirementCalculationResponse> getLatestRetirementCalculationResult() {
		return ResponseEntity
				.ok(retirementIntegrationService.getLatestRetirementCalculationResult(SecurityUtil.currentUserId()));
	}

	@PostMapping("assessment-answers")
	public ResponseEntity<String> processRetirementAssessments(
			@Valid @RequestBody RetirementAssessmentClientRequest request) throws JsonProcessingException {

		request.setUserId(SecurityUtil.currentUserId());

		log.info("Retirement Assessment Client Request : {}", objectMapper.writeValueAsString(request));

		return retirementAssessmentService.processRetirementAssessment(request);
	}

	@GetMapping("assessment-answers")
	public ResponseEntity<String> getRetirementAssessmentAnswer() {
		return retirementAssessmentService.findLatestRetirementAssessmentAnswer(SecurityUtil.currentUserId());
	}

	@GetMapping("assessment-report")
	public ResponseEntity<String> getRetirementAssessmentReport() {
		return retirementAssessmentService.findLatestRetirementAssessmentReport(SecurityUtil.currentUserId());
	}

	@ServiceToServiceAccess
	@GetMapping("private/product-exclusion-tags")
	public Set<String> getProductExclusionTags() {
		return retirementIntegrationService.getProductExclusionTags(SecurityUtil.currentUserId());
	}

}
