package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.dto.model.LoanDTO;
import my.com.mandrill.component.dto.request.AttachEntityRequest;
import my.com.mandrill.component.dto.request.CreateLoanIntegration;
import my.com.mandrill.component.dto.request.NewLoanRequest;
import my.com.mandrill.component.dto.request.UpdateLoanIntegration;
import my.com.mandrill.component.dto.response.LoanAggregateResponse;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.component.service.LoanService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.VaultLinkDTO;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.LoanTypeEnum;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import my.com.mandrill.utilities.general.util.SpringSortComparator;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

@Tag(name = "04-loan")
@Slf4j
@RestController
@RequestMapping("loans")
@RequiredArgsConstructor
public class LoanController {

	private final ********************** loanIntegrationService;

	private final ValidationService validationService;

	private final LoanService loanService;

	private final CommonFeignClient commonFeignClient;

	@Hidden
	@PostMapping("integration")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<LoanDTO> integration(@Valid @RequestBody CreateLoanIntegration createLoan) {
		Loan loan = MapStructConverter.MAPPER.toLoan(createLoan);

		validationService.validateCreateIntegration(loan, SecurityUtil.currentUserId());
		Loan result = loanIntegrationService.save(loan);

		commonFeignClient.calculateIntegrationKYLL();
		return ResponseEntity.ok(MapStructConverter.MAPPER.toLoanDTO(result));
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<LoanDTO> findById(@PathVariable String id) {

		Loan result = loanService.findById(id, SecurityUtil.currentUserId());

		return ResponseEntity.ok(MapStructConverter.MAPPER.toLoanDTO(result));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<LoanDTO>> findAll(Sort sort) {

		List<Loan> result = loanService.findAll(SecurityUtil.currentUserId(), sort);

		return ResponseEntity.ok(result.stream().map(MapStructConverter.MAPPER::toLoanDTO).toList());
	}

	@Hidden
	@GetMapping("integration")
	public ResponseEntity<List<LoanDTO>> getLoans(@RequestParam LoanTypeEnum loanType) {

		List<Loan> result = loanService.findByUserIdAndTypeIn(SecurityUtil.currentUserId(), List.of(loanType));

		return ResponseEntity.ok(result.stream().map(MapStructConverter.MAPPER::toLoanDTO).toList());
	}

	@GetMapping("{entityName}/{entityId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<LoanDTO> findByEntityNameAndEntityId(@PathVariable EntityName entityName,
			@PathVariable String entityId) {

		Loan result = loanService.findByEntityNameAndEntityId(entityName, entityId, SecurityUtil.currentUserId());

		return ResponseEntity.ok(MapStructConverter.MAPPER.toLoanDTO(result));
	}

	@Hidden
	@PutMapping("integration/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<LoanDTO> updateIntegration(@Valid @RequestBody UpdateLoanIntegration updateLoan,
			@PathVariable String id) {
		Loan loan = MapStructConverter.MAPPER.toLoan(updateLoan);

		loan.setId(id);
		Loan result = loanIntegrationService
				.save(validationService.validateUpdateIntegration(loan, SecurityUtil.currentUserId()));

		commonFeignClient.calculateIntegrationKYLL();
		return ResponseEntity.ok(MapStructConverter.MAPPER.toLoanDTO(result));
	}

	@PutMapping("{id}/attach-entity")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void attachEntity(@Valid @RequestBody AttachEntityRequest request, @PathVariable String id) {
		loanService.attachEntity(request, id, SecurityUtil.currentUserId());
	}

	@PutMapping("{id}/detach-entity")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void detachEntity(@PathVariable String id) {
		Loan loan = loanService.findById(id, SecurityUtil.currentUserId());
		loan.setEntityId(null);
		loan.setEntityName(null);

		loanService.save(loan);
	}

	@DeleteMapping("{id}")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void delete(@PathVariable String id) {

		loanIntegrationService.delete(id, SecurityUtil.currentUserId());

	}

	@GetMapping("types")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<LoanTypeEnum.LoanTypeEnumDTO>> findAllLoanTypes(
			@RequestParam(required = false) Boolean active, Sort sort) {

		List<LoanTypeEnum.LoanTypeEnumDTO> result;
		if (Objects.isNull(active)) {
			result = Stream.of(LoanTypeEnum.values()).map(LoanTypeEnum::getObject).toList();
		}
		else {
			result = LoanTypeEnum.filterByActive(active);
		}

		try {
			Comparator<LoanTypeEnum.LoanTypeEnumDTO> comparator = null;
			for (Sort.Order order : sort) {
				if (order.getProperty().equals("name")) {
					comparator = SpringSortComparator.compare(order, comparator,
							Comparator.comparing(LoanTypeEnum.LoanTypeEnumDTO::getName));
				}
				else if (order.getProperty().equals("others")) {
					comparator = SpringSortComparator.compare(order, comparator,
							Comparator.comparing(LoanTypeEnum.LoanTypeEnumDTO::getOthers));
				}
			}
			if (comparator != null) {
				result = result.stream().sorted(comparator).toList();
			}
		}
		catch (Exception e) {
			result = Stream.of(LoanTypeEnum.values()).map(LoanTypeEnum::getObject).toList();
		}
		return ResponseEntity.ok(result);
	}

	@GetMapping("count")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Long> count() {
		return ResponseEntity.ok(loanService.count(SecurityUtil.currentUserId()));
	}

	@GetMapping("/summary")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<LoanAggregateResponse> countLoan() {
		return ResponseEntity.ok(loanIntegrationService.countTotalLoan(SecurityUtil.currentUserId()));
	}

	@GetMapping("/summary/details")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<LoanDTO>> findAllLoanForKYL() {
		List<Loan> loans = loanIntegrationService.findLoanForKYLL(SecurityUtil.currentUserId());
		return ResponseEntity.ok(loans.stream().map(MapStructConverter.MAPPER::toLoanDTO).toList());
	}

	@GetMapping("available-resources")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public List<LoanDTO> findAvailableResources(@RequestParam LoanTypeEnum loanType) {

		List<Loan> result = loanService.findDetached(SecurityUtil.currentUserId(), loanType);

		return result.stream().map(MapStructConverter.MAPPER::toLoanDTO).toList();
	}

	@GetMapping("/integrations/linked-entity")
	public List<String> getLinkedEntities(@RequestParam EntityName entityName) {
		return loanIntegrationService.findAttachedLoan(SecurityUtil.currentUserId(), entityName);
	}

	@Hidden
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("/integrations/vault/link/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void linkLoanVault(@PathVariable String id, @RequestBody VaultLinkDTO request) {
		loanService.attachAttachmentGroupId(id, SecurityUtil.currentUserId(), request.getAttachmentGroupId());
	}

	@PostMapping("/calculate-balance")
	public BigDecimal calculateUserBalance(@RequestBody NewLoanRequest newLoanRequest) {
		return loanIntegrationService.calculateNewLoanEndingBalance(newLoanRequest);
	}

}
