package my.com.mandrill.utilities.general.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SurveyAnswerTag {

	GROCERIES("Groceries"), SHOPPING("Shopping"), DINING("Dining"), TRAVEL("Traveling"), <PERSON>SH<PERSON>CK("Cashback"),
	REWARDS("Rewards"), BALANCE_TRANSFER("Balance Transfer"), NO_ANNUAL_FEES("No Annual Fee, Waived Annual Fee"),
	PETROL("Petrol, Utilities"), E_COMMERCE("E-Commerce"), ENTERTAINMENT("Entertainment"), EASY_PAYMENT("Easy Payment"),
	PREMIUM("Premium"), HIGH_CREDIT_LIMIT("High Credit Limit"), STRONG_CREDIT_SCORE("Strong Credit Score"),
	HIGH_INTEREST("High Interest"), ATM_BRANCHES("ATM Branches"), ONLINE_MOBILE_BANKING("Online Mobile Banking"),
	GOOD_INTEREST_CONVENIENCE("Good Interest - Convenience"), CURRENT_ACCOUNT("current-account"),
	SAVING_ACCOUNT("savings-account"), SHARIAH_COMPLIANT("Shariah Compliant"),
	CONVENTIONAL_ACCOUNT("Conventional Account"), DIGITAL_ONLY("Digital Only"), OPEN_TO_ANY("Open to any"),
	DEPOSIT_RANGE("Deposit Range"), INCOME_GROUP_B40("B40"), INCOME_GROUP_M40("M40"), INCOME_GROUP_T20("T20");

	private final String tag;

}
