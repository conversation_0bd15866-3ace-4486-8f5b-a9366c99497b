package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Property;
import my.com.mandrill.component.dto.model.PropertyDTO;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.PropertyRepository;
import my.com.mandrill.component.service.PropertyService;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.VaultLinkDTO;
import org.springframework.data.domain.Sort;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PropertyServiceImpl implements PropertyService {

	private final ObjectMapper objectMapper;

	private final PropertyRepository propertyRepository;

	@Override
	public Property findById(String id, String userId) {
		return propertyRepository.findByIdAndUserId(id, userId).orElseThrow(ExceptionPredicate.propertyNotFound(id));
	}

	@Override
	public Property findByAttachmentGroupId(String attachmentGroupId, String userId) {
		return propertyRepository.findByAttachmentGroupIdAndUserId(attachmentGroupId, userId);
	}

	@Override
	public List<Property> findByAttachmentGroupIdNull(String userId) {
		return propertyRepository.findByAttachmentGroupIdNullAndUserIdAndIsBrowsingFalse(userId);
	}

	@Override
	public long count(String userId) {
		return propertyRepository.countByUserIdAndIsBrowsingFalse(userId);
	}

	@Override
	public List<PropertyDTO> findByUserId(String userId, Sort sort) {
		List<Property> result = propertyRepository.findByUserId(userId, sort);
		return result.stream().map((property -> objectMapper.convertValue(property, PropertyDTO.class))).toList();
	}

	@Override
	@Transactional
	public Property save(Property property) {
		return propertyRepository.save(property);
	}

	@Override
	public Optional<Property> findOptionalById(String id, String userId) {
		return propertyRepository.findByIdAndUserId(id, userId);
	}

	@Transactional
	@Override
	public void delete(Property property) {
		propertyRepository.delete(property);
	}

	@Override
	public Boolean isPropertyExistByAddress(String address1, String address2, String address3, String userId) {
		return propertyRepository.existsByAddress1AndAddress2AndAddress3AndUserIdAndIsBrowsingFalse(address1, address2,
				address3, userId);
	}

	@Override
	@Transactional
	public void linkVault(Property property, VaultLinkDTO vaultLinkDTO) {
		property.setAttachmentGroupId(vaultLinkDTO.getAttachmentGroupId());
		save(property);
	}

	@Override
	public boolean existsByUserIdAndAttachmentGroupId(String userId, @NonNull String attachmentGroupId) {
		return propertyRepository.existsByUserIdAndAttachmentGroupId(userId, attachmentGroupId);
	}

	@Override
	public NetWorthDTO calculateNetWorthByUserId(String userId) {
		NetWorthDTO netWorthDTO = new NetWorthDTO();
		BigDecimal totalAssets = new BigDecimal(0);
		BigDecimal totalLiabilities = new BigDecimal(0);
		List<Property> properties = propertyRepository.findByUserId(userId, Sort.unsorted());
		for (Property property : properties) {
			if (property.getAverageMarketValue() != null) {
				totalAssets = totalAssets.add(property.getAverageMarketValue());
			}
			else if (property.getPurchaseValue() != null) {
				BigDecimal purchaseValue = new BigDecimal(property.getPurchaseValue());
				totalAssets = totalAssets.add(purchaseValue);
			}
		}
		netWorthDTO.setAssets(totalAssets);
		netWorthDTO.setLiabilities(totalLiabilities);
		return netWorthDTO;
	}

	@Override
	public List<Property> findByUserIdAndIsRentedAndIsBrowsingFalse(String userId, Boolean isRented) {
		return propertyRepository.findByUserIdAndIsRentedAndIsBrowsingFalse(userId, isRented);
	}

	@Override
	public boolean existsByIdAndUserId(String id, String userId) {
		return propertyRepository.existsByIdAndUserIdAndIsBrowsingFalse(id, userId);
	}

	@Override
	public List<PropertyDTO> findDetachedEntity(String userId, List<String> linkedEntity, Sort sort) {
		List<Property> data = propertyRepository.findByUserIdAndIdNotIn(userId, linkedEntity, sort);
		return data.stream().map(el -> objectMapper.convertValue(el, PropertyDTO.class)).toList();
	}

	@Override
	public List<Property> findTop5ByUserIdOrderByCreatedDateDesc(String userId) {
		return propertyRepository.findTop5ByUserIdOrderByCreatedDateDesc(userId);
	}

}
