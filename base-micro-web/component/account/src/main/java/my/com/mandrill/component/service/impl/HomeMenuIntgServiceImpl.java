package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.domain.HomeMenuInfo;
import my.com.mandrill.component.domain.HomeMenuProduct;
import my.com.mandrill.component.dto.model.HomeMenuDTO;
import my.com.mandrill.component.dto.request.HomeMenuProductRequest;
import my.com.mandrill.component.dto.request.HomeMenuRequest;
import my.com.mandrill.component.dto.request.HomeMenuV2Request;
import my.com.mandrill.component.dto.response.HomeMenuInfoResponse;
import my.com.mandrill.component.dto.response.HomeMenuProductResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.HomeMenuIntgService;
import my.com.mandrill.component.service.HomeMenuProductService;
import my.com.mandrill.component.service.HomeMenuService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.dto.AttachmentGroupDTO;
import my.com.mandrill.utilities.feign.dto.AttachmentGroupResponse;
import my.com.mandrill.utilities.feign.dto.AttachmentResponse;
import my.com.mandrill.utilities.general.constant.AttachmentSubtypeEnum;
import my.com.mandrill.utilities.general.constant.AttachmentTypeFileStorageEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.storage.util.LocalFileUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class HomeMenuIntgServiceImpl implements HomeMenuIntgService {

	private final HomeMenuService homeMenuService;

	private final CommonFeignClient commonFeignClient;

	private final HomeMenuProductService homeMenuProductService;

	private final ValidationService validationService;

	@Override
	public HomeMenuDTO save(HomeMenuRequest homeMenuRequest) {
		HomeMenu homeMenu = toEntity(homeMenuRequest);

		if (homeMenuRequest.getParentMenu() != null) {
			HomeMenu parent = homeMenuService.findById(homeMenuRequest.getParentMenu().getId());
			homeMenu.setParent(parent);
		}
		this.existByCode(homeMenu.getCode());

		return toDTO(homeMenuService.save(homeMenu));
	}

	private HomeMenu toEntity(HomeMenuRequest homeMenuRequest) {
		HomeMenu homeMenu = new HomeMenu();
		homeMenu.setName(homeMenuRequest.getName());
		homeMenu.setCode(homeMenuRequest.getCode());
		homeMenu.setDescription(homeMenuRequest.getDescription());
		homeMenu.setState(homeMenuRequest.getState());
		return homeMenu;
	}

	@Override
	public HomeMenuDTO toDTO(HomeMenu homeMenu) {
		HomeMenuDTO homeMenuDTO = new HomeMenuDTO();
		homeMenuDTO.setId(homeMenu.getId());
		homeMenuDTO.setName(homeMenu.getName());
		homeMenuDTO.setCode(homeMenu.getCode());
		homeMenuDTO.setActionGroup(homeMenu.getActionGroup());
		homeMenuDTO.setDescription(homeMenu.getDescription());
		homeMenuDTO.setState(homeMenu.getState());
		if (homeMenu.getParent() != null) {
			homeMenuDTO.setParent(
					HomeMenuDTO.builder().id(homeMenu.getParent().getId()).name(homeMenu.getParent().getName())
							.code(homeMenu.getParent().getCode()).state(homeMenu.getParent().getState()).build());
		}
		if (homeMenu.getHomeMenuMaps() != null) {
			homeMenuDTO.setGroupIds(homeMenu.getHomeMenuMaps().stream()
					.map(homeMenuMap -> homeMenuMap.getHomeMenuGroup().getId()).toList());
		}

		List<HomeMenuProductResponse> homeMenuProducts = findAllByHomeMenuCode(homeMenu.getCode());
		if (homeMenuProducts != null && !homeMenuProducts.isEmpty()) {
			homeMenuDTO.setHomeMenuProduct(homeMenuProducts);
		}

		return homeMenuDTO;
	}

	@Override
	public HomeMenuDTO findById(String id) {
		return toDTO(homeMenuService.findById(id));
	}

	@Override
	public void deleteById(String id) {
		// if want to delete all child by parent, need to add mapping one to many on
		// entity
		HomeMenu homeMenu = homeMenuService.findById(id);
		homeMenuService.delete(homeMenu);
	}

	private void existByCode(String code) {
		if (homeMenuService.existsByCode(code)) {
			throw new BusinessException(ErrorCodeEnum.HOME_MENU_CODE_IS_EXIST);
		}
	}

	@Override
	public HomeMenuDTO update(String id, HomeMenuRequest request) {
		HomeMenu existsData = homeMenuService.findById(id);
		existsData.setCode(request.getCode());
		existsData.setDescription(request.getDescription());
		existsData.setName(request.getName());
		existsData.setState(request.getState());

		if (request.getParentMenu() != null) {
			HomeMenu parent = homeMenuService.findById(request.getParentMenu().getId());
			existsData.setParent(parent);
		}
		return toDTO(homeMenuService.save(existsData));
	}

	@Override
	public Page<HomeMenu> findAll(String code, Pageable pageable) {
		return homeMenuService.findAll(code, pageable);
	}

	@Override
	public List<HomeMenuInfoResponse> findEnabledState(String name) {
		List<HomeMenuInfo> homeMenuInfos = homeMenuService.findEnabledState(name);
		List<HomeMenuInfoResponse> homeMenuInfoResponses = homeMenuInfos.stream().map(this::toResponse).toList();
		return homeMenuInfoResponses;
	}

	private HomeMenuInfoResponse toResponse(HomeMenuInfo homeMenuInfo) {
		HomeMenuInfoResponse homeMenuInfoResponse = new HomeMenuInfoResponse();
		homeMenuInfoResponse.setId(homeMenuInfo.getId());
		homeMenuInfoResponse.setName(homeMenuInfo.getName());
		homeMenuInfoResponse.setCode(homeMenuInfo.getCode());
		homeMenuInfoResponse.setDescription(homeMenuInfo.getDescription());
		homeMenuInfoResponse.setGroupIds(homeMenuService.findById(homeMenuInfo.getId()).getHomeMenuMaps().stream()
				.map(homeMenuMap -> homeMenuMap.getHomeMenuGroup().getId()).toList());
		return homeMenuInfoResponse;
	}

	public List<HomeMenuDTO> findWithoutParentId() {
		return homeMenuService.findWithoutParentId().stream().map(this::toDTO).toList();
	}

	@Override
	public Page<HomeMenu> findAllWithoutParentId(String code, Pageable pageable) {
		return homeMenuService.findAllWithoutParentId(code, pageable);
	}

	@Override
	public Page<HomeMenu> findAllWithParentId(String code, Pageable pageable) {
		return homeMenuService.findAllWithParentId(code, pageable);
	}

	@Override
	public HomeMenu saveHomeMenu(HomeMenuV2Request homeMenuV2Request) {
		HomeMenu homeMenu = toHomeMenuEntity(homeMenuV2Request);
		return homeMenuService.save(homeMenu);
	}

	@Override
	public Page<HomeMenu> findAllProducts(String code, Pageable pageable) {
		return homeMenuService.findAllProducts(code, pageable);
	}

	@Override
	public HomeMenu updateHomeMenu(HomeMenuV2Request homeMenuV2Request) {
		HomeMenu homeMenu = toHomeMenuEntity(homeMenuV2Request);
		return homeMenuService.save(homeMenu);
	}

	@Override
	public List<HomeMenuProductResponse> findAllByHomeMenuCode(String menuCode) {
		List<HomeMenuProduct> products = homeMenuProductService.findAllByHomeMenuCode(menuCode);
		return products.stream().map(product -> {
			HomeMenuProductResponse response = MapStructConverter.MAPPER.toHomeMenuProductResponse(product);
			List<HomeMenuProduct> subProducts = homeMenuProductService.findAllByParentId(product.getId());
			if (subProducts != null) {
				response.setSubProduct(MapStructConverter.MAPPER.toHomeMenuProductResponse(subProducts));
			}
			return response;
		}).collect(Collectors.toList());
	}

	private void uploadImage(HomeMenuProduct homeMenuProduct, AttachmentGroupDTO attachmentGroupDTO) {
		if (attachmentGroupDTO == null)
			return;

		attachmentGroupDTO.setAttachmentGroupId(homeMenuProduct.getAttachmentGroupId());
		attachmentGroupDTO.setClassName(HomeMenuProduct.class.getSimpleName());
		attachmentGroupDTO.setType(AttachmentTypeFileStorageEnum.HOME_MENU_PRODUCT_IMAGE.name());
		AttachmentGroupResponse attachmentGroupResponse = commonFeignClient.uploadAttachment(attachmentGroupDTO);
		if (attachmentGroupResponse == null)
			return;

		homeMenuProduct.setAttachmentGroupId(attachmentGroupResponse.getAttachmentGroupId());
		for (AttachmentResponse attachment : attachmentGroupResponse.getAttachments()) {
			StringBuilder url = new StringBuilder();
			if (attachmentGroupResponse.getPath() != null) {
				url.append(attachmentGroupResponse.getPath()).append(File.separator);
			}
			url.append(LocalFileUtil.formatActualFile(attachment.getId(), attachment.getName()));

			if (AttachmentSubtypeEnum.HOME_MENU_PRODUCT_IMAGE.equals(attachment.getSubtype())) {
				homeMenuProduct.setImageUrl(url.toString());
			}
			else if (AttachmentSubtypeEnum.HOME_MENU_PRODUCT_LOGO.equals(attachment.getSubtype())) {
				homeMenuProduct.setLogoUrl(url.toString());
			}
		}
	}

	private HomeMenu toHomeMenuEntity(HomeMenuV2Request homeMenuRequest) {
		HomeMenu menu = homeMenuService.findById(homeMenuRequest.getId());

		validationService.validateHomeMenuProductRequest(homeMenuRequest.getId(), homeMenuRequest.getHomeMenuProduct());
		homeMenuProductService.deleteByHomeMenu(menu);

		Set<HomeMenuProduct> products = homeMenuRequest.getHomeMenuProduct().stream()
				.map(request -> toHomeMenuProduct(request, menu)).collect(Collectors.toSet());

		menu.setHomeMenuProduct(products);
		return menu;
	}

	private HomeMenuProduct toHomeMenuProduct(HomeMenuProductRequest request, HomeMenu menu) {
		HomeMenuProduct product = MapStructConverter.MAPPER.toHomeMenuProduct(request);
		uploadImage(product, request.getImageAttachmentGroupDTO());
		product.setHomeMenu(menu);

		if (request.getSubProduct() != null && !request.getSubProduct().isEmpty()) {
			Set<HomeMenuProduct> subProducts = request.getSubProduct().stream()
					.map(subRequest -> toHomeMenuProduct(subRequest, menu, product)).collect(Collectors.toSet());
			product.setSubProducts(subProducts);
		}

		return product;
	}

	private HomeMenuProduct toHomeMenuProduct(HomeMenuProductRequest subRequest, HomeMenu menu,
			HomeMenuProduct parentProduct) {
		HomeMenuProduct subProduct = MapStructConverter.MAPPER.toHomeMenuProduct(subRequest);
		uploadImage(subProduct, subRequest.getImageAttachmentGroupDTO());

		subProduct.setHomeMenu(menu);
		subProduct.setParent(parentProduct);

		return subProduct;
	}

}
