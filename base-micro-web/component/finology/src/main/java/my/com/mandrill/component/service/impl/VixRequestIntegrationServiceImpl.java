package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.client.FinologyClient;
import my.com.mandrill.component.constant.RunningNumberModule;
import my.com.mandrill.component.domain.VehicleType;
import my.com.mandrill.component.domain.VixRequest;
import my.com.mandrill.component.dto.model.VixRequestDTO;
import my.com.mandrill.component.dto.request.VixCallRequest;
import my.com.mandrill.component.dto.response.FinologyVixResponse;
import my.com.mandrill.component.service.VehicleTypeService;
import my.com.mandrill.component.service.VixRequestIntegrationService;
import my.com.mandrill.component.service.VixRequestService;
import my.com.mandrill.component.service.VixResponseIntegrationService;
import my.com.mandrill.utilities.feign.client.VehicleFeignClient;
import my.com.mandrill.utilities.feign.dto.UpdateUserIdentityNumberByFinologyDTO;
import my.com.mandrill.utilities.feign.dto.VehichleUpdateRequest;
import my.com.mandrill.utilities.feign.dto.VehicleFinologyDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import static my.com.mandrill.utilities.general.util.FinologyUtil.PATTERN_FORMAT;
import static my.com.mandrill.utilities.general.util.FinologyUtil.hashPayload;

@Slf4j
@Service
@RequiredArgsConstructor
public class VixRequestIntegrationServiceImpl implements VixRequestIntegrationService {

	private final FinologyClient finologyClient;

	private final VixResponseIntegrationService vixResponseIntegrationService;

	private final RunningNumberUtil runningNumberUtil;

	private final ObjectMapper objectMapper;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final VixRequestService vixRequestService;

	private final VehicleTypeService vehicleTypeService;

	private final VehicleFeignClient vehicleFeignClient;

	@Value("${integration-finology.api-key}")
	private String apiKey;

	public Optional<String> getVehicleId(String vehicleRegistrationNo) {
		VehicleFinologyDTO vehicle = vehicleFeignClient.checkVehicleOwnByUser(vehicleRegistrationNo);
		if (Objects.nonNull(vehicle.getId())) {
			return Optional.of(vehicle.getId());
		}
		return Optional.empty();
	}

	@Override
	public FinologyVixResponse sendRequest(String userId, VixCallRequest vixCallRequest)
			throws NoSuchAlgorithmException, JsonProcessingException {
		return sendRequest(userId, vixCallRequest, this::getVehicleId);
	}

	@Override
	public FinologyVixResponse sendRequest(String userId, VixCallRequest vixCallRequest,
			Function<String, Optional<String>> getterVehicleId)
			throws NoSuchAlgorithmException, JsonProcessingException {
		Optional<String> vehicleIdOpt = getterVehicleId.apply(vixCallRequest.getVehicleRegistrationNo());

		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_FORMAT)
				.withZone(TimeConstant.DEFAULT_ZONE_ID);
		Instant now = Instant.now();
		String dateTimeNow = formatter.format(now);
		VixRequestDTO vixRequestDTO = new VixRequestDTO();
		vixRequestDTO.setRequestDateTime(dateTimeNow);
		vixRequestDTO.setPostcode(Strings.isBlank(vixCallRequest.getPostcode()) ? null : vixCallRequest.getPostcode());
		vixRequestDTO.setIdType(vixCallRequest.getIdTypeCode());
		vixRequestDTO.setIdentificationNo(vixCallRequest.getIdentificationNo());
		vixRequestDTO.setRefCode(
				runningNumberUtil.getLatestRunningNumber(RunningNumberModule.FINOLOGY_VEHICLE_RECORD.name(), false));
		vixRequestDTO.setVehicleRegistrationNo(vixCallRequest.getVehicleRegistrationNo());
		vixRequestDTO.setHashCode(hashPayload(apiKey, vixRequestDTO.getRefCode(), dateTimeNow,
				vixCallRequest.getVehicleRegistrationNo()));
		VixRequest vixRequest = save(userId, vixCallRequest.getVehicleTypeId(), vixRequestDTO);
		FinologyVixResponse response;
		log.info("request to vix call: {}", objectMapper.writeValueAsString(vixRequestDTO));
		try {
			response = finologyClient.sendRequest(vixRequestDTO);
			publishUpdateData(userId, vixCallRequest.getIdTypeCode(), vixCallRequest.getIdentificationNo());
		}
		catch (WebClientResponseException e) {
			log.info("error response from vix call: {}", e.getResponseBodyAsString());
			response = objectMapper.readValue(e.getResponseBodyAsString(), FinologyVixResponse.class);
		}
		log.info("response from vix call: {}", objectMapper.writeValueAsString(response));
		response.setIsOwned(vehicleIdOpt.isPresent());
		response.setVehicleId(vehicleIdOpt.orElse(null));

		vixResponseIntegrationService.save(userId, vixRequest, response);
		publishVehicleRenewalDateAndMarketValue(userId, response);
		return response;
	}

	@Override
	public VixRequest save(String userId, String vehicleTypeId, VixRequestDTO vixRequestDTO) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(PATTERN_FORMAT).withZone(ZoneId.systemDefault());

		VehicleType vehicleType = vehicleTypeService.findById(vehicleTypeId);

		VixRequest vixRequest = new VixRequest();
		vixRequest.setUserId(userId);
		vixRequest.setRefCode(vixRequestDTO.getRefCode());
		vixRequest.setRequestDateTime(Instant.from(formatter.parse(vixRequestDTO.getRequestDateTime())));
		vixRequest.setHashCode(vixRequestDTO.getHashCode());
		vixRequest.setVehicleRegistrationNo(vixRequestDTO.getVehicleRegistrationNo());
		vixRequest.setIdTypeCode(vixRequestDTO.getIdType());
		vixRequest.setIdentificationNo(vixRequestDTO.getIdentificationNo());
		vixRequest.setPostcode(vixRequestDTO.getPostcode());
		vixRequest.setVehicleType(vehicleType);
		return vixRequestService.save(vixRequest);

	}

	private void publishUpdateData(String userId, String idTypeCode, String value) throws JsonProcessingException {
		UpdateUserIdentityNumberByFinologyDTO data = new UpdateUserIdentityNumberByFinologyDTO();
		data.setId(userId);
		data.setIdentityType(idTypeCode);
		data.setValue(value);
		kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_USER_IDENTITY_NUMBER_TOPIC, userId,
				objectMapper.writeValueAsString(data));
	}

	private void publishVehicleRenewalDateAndMarketValue(String userId, FinologyVixResponse vixResponse)
			throws JsonProcessingException {
		if (isVixResponseSuccess(vixResponse)) {
			VehichleUpdateRequest request = new VehichleUpdateRequest();
			request.setUserId(userId);
			request.setVehicleId(vixResponse.getVehicleId());
			if (Objects.nonNull(vixResponse.getResponse().getVehicleDetails())
					&& StringUtils.isNotBlank(vixResponse.getResponse().getVehicleDetails().getMarketValue())) {
				request.setAverageMarketValue(
						new BigDecimal(vixResponse.getResponse().getVehicleDetails().getMarketValue()));
			}

			if (Objects.nonNull(vixResponse.getResponse().getNcdDetails())
					&& StringUtils.isNotBlank(vixResponse.getResponse().getNcdDetails().getCurrentNcdExpiryDate())) {
				request.setRoadTaxRenewalDate(
						LocalDate.parse(vixResponse.getResponse().getNcdDetails().getCurrentNcdExpiryDate()));
			}
			kafkaTemplate.send(KafkaTopic.FINOLOGY_UPDATE_VEHICLE_RENEWAL_DATE_AND_MARKET_VALUE_TOPIC, userId,
					objectMapper.writeValueAsString(request));
		}
	}

	private boolean isVixResponseSuccess(FinologyVixResponse vixResponse) {
		return vixResponse.getCode() == HttpStatus.OK.value() && Objects.nonNull(vixResponse.getResponse());
	}

}
