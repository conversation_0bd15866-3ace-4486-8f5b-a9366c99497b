package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.*;

import java.time.Instant;
import java.util.LinkedHashSet;
import java.util.Set;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "token")
@EqualsAndHashCode(callSuper = true)
public class Token extends AuditSection {

	@Column(name = "refresh_token")
	private String refreshToken;

	@Column(name = "refresh_token_validity")
	private Instant refreshTokenValidity;

	@Column(name = "revoked_date", columnDefinition = "DATETIME DEFAULT NOW()")
	private Instant revokedDate;

	@Column(name = "revoked_by")
	private String revokedBy;

	@Column(name = "revoked_reason")
	private String revokedReason;

	@Column(name = "status")
	@Enumerated(EnumType.STRING)
	private TokenStatus status;

	@Column(name = "device_id")
	private String deviceId;

	@Enumerated(EnumType.STRING)
	@Column(name = "login_type")
	private LoginTypeEnum loginType;

	@Enumerated(EnumType.STRING)
	@Column(name = "passcode_type")
	private PasscodeType passcodeType;

	@Enumerated(EnumType.STRING)
	@Column(name = "access_type")
	private AccessTypeEnum accessType;

	@Enumerated(EnumType.STRING)
	@Column(name = "platform")
	private ClientPlatformType platform;

	@Builder.Default
	@NotNull
	@Column(name = "remember_me", columnDefinition = "BOOLEAN DEFAULT FALSE", nullable = false)
	private boolean rememberMe = false;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "user_id", nullable = false)
	private User user;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "token", cascade = { CascadeType.ALL }, orphanRemoval = true)
	private Set<TokenTransaction> tokenTransactions = new LinkedHashSet<>();

}
