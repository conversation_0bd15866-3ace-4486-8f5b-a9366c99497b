package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.DeviceKeyRepository;
import my.com.mandrill.component.repository.jpa.SignatureChallengeRepository;
import my.com.mandrill.component.service.DeviceKeyService;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DeviceKeyServiceImpl implements DeviceKeyService {

	private final DeviceKeyRepository deviceKeyRepository;

	private final SignatureChallengeRepository signatureChallengeRepository;

	@Override
	public List<DeviceKey> findAllDeviceKey(Sort sort, AppUser user) {
		return deviceKeyRepository.findByUser(user, sort);
	}

	@Override
	@Transactional
	public DeviceKey saveDeviceKey(DeviceKey deviceKey) {
		return deviceKeyRepository.save(deviceKey);
	}

	@Override
	@Transactional
	public SignatureChallenge saveSignatureChallenge(SignatureChallenge signatureChallenge) {
		return signatureChallengeRepository.save(signatureChallenge);
	}

	@Override
	public Optional<DeviceKey> findOptionalDeviceKeyByDeviceIdAndUser(String deviceId, AppUser user) {
		return deviceKeyRepository.findByUserAndDeviceId(user, deviceId);
	}

	@Override
	public DeviceKey findDeviceKeyByDeviceIdAndUser(String deviceId, AppUser user) {
		return findOptionalDeviceKeyByDeviceIdAndUser(deviceId, user)
				.orElseThrow(ExceptionPredicate.deviceNotFound(deviceId));
	}

	@Override
	public SignatureChallenge findSignatureChallengeByIdAndDeviceKey(String id, DeviceKey deviceKey) {
		return signatureChallengeRepository.findByIdAndDeviceKey(id, deviceKey)
				.orElseThrow(ExceptionPredicate.signatureChallengeNotFound(id, deviceKey));
	}

	@Override
	@Transactional
	public void deleteOldestDeviceKey(AppUser user) {
		Optional<DeviceKey> deviceKey = deviceKeyRepository.findFirstByUserOrderByCreatedDateAsc(user);
		if (deviceKey.isPresent()) {
			long deleted = signatureChallengeRepository.deleteByDeviceKey(deviceKey.get());
			log.debug("{} signature challenges deleted.", deleted);
			deviceKeyRepository.delete(deviceKey.get());
			log.debug("{} device key", deviceKey.get().getId());
		}
	}

	@Override
	public long countByUser(AppUser user) {
		return deviceKeyRepository.countByUser(user);
	}

	@Override
	public Optional<SignatureChallenge> findSignatureChallengeById(String signatureChallengeId) {
		return signatureChallengeRepository.findById(signatureChallengeId);
	}

}
