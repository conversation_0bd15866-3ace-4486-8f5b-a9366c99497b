package my.com.mandrill.component.controller.admin;

import autovalue.shaded.com.google.common.collect.Streams;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.DashboardDateType;
import my.com.mandrill.component.constant.RequestedUserStatisticType;
import my.com.mandrill.component.constant.UserStatisticType;
import my.com.mandrill.component.dto.model.DateRangeAllDTO;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.util.DateUtil;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.LocalDate;
import java.time.Year;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "24-downloads-statistics-report")
@RequestMapping("/admin/downloads-statistics-report")
public class AdminDownloadsStatisticsReportController {

	private final DownloadsStatisticsReportService downloadsStatisticsReportService;

	private final ValidationService validationService;

	private final WeeklyUserStatisticsIntegrationService weeklyUserStatisticsIntegrationService;

	private final WeeklyUserTargetService weeklyUserTargetService;

	private final AccountSystemConfigurationService accountSystemConfigurationService;

	@GetMapping("accumulative-latest")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<DownloadsStatisticsReportResponse> getAccumulativeData(
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone) {
		DownloadsStatisticsReportResponse response = downloadsStatisticsReportService.getResultBetweenDate(
				DashboardDateType.APP_STARTING_DATE, LocalDate.now(ZoneId.of(timeZone)), ZoneId.of(timeZone));
		return ResponseEntity.ok(response);
	}

	@GetMapping("today")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<DownloadsStatisticsReportResponse> getTodayData(
			@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam Instant dateTimeFrom,
			@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam Instant dateTimeTo,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone) {

		DownloadsStatisticsReportResponse response = downloadsStatisticsReportService
				.getResultBetweenDateTime(dateTimeFrom, dateTimeTo, ZoneId.of(timeZone));

		return ResponseEntity.ok(response);

	}

	@GetMapping("daily-range")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<DownloadsStatisticsReportResponse> getDataInTheWeek(@RequestParam Year year,
			@RequestParam int weekNumber,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone,
			@RequestParam(required = false, defaultValue = "DESC") Sort.Direction direction) {

		validationService.validateWeekNumber(year, weekNumber);

		List<DownloadsStatisticsReportDailyResponse> dailyResult = downloadsStatisticsReportService.getDailyResult(year,
				weekNumber, ZoneId.of(timeZone));

		Long registeredUserTarget = weeklyUserTargetService.findByYearAndWeekNumber(year, weekNumber);

		DownloadsStatisticsReportResponse response = downloadsStatisticsReportService.getResultBetweenDate(year,
				weekNumber, ZoneId.of(timeZone), registeredUserTarget);

		if (direction.isAscending()) {
			Collections.reverse(dailyResult);
		}
		response.setDailyResult(dailyResult);

		return ResponseEntity.ok(response);
	}

	@GetMapping("os-download")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<OsDownloadsLastUpdatedResponse> getLastUpdatedData(
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone) {
		OsDownloadsLastUpdatedResponse response = downloadsStatisticsReportService
				.getOsDownloadsLastUpdated(ZoneId.of(timeZone));
		return ResponseEntity.ok(response);
	}

	@GetMapping("date-range-of-week")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<List<DateRangeAllDTO>> getDateRangeOfWeek(@RequestParam Year year,
			@RequestParam(required = false, defaultValue = "ASC") Sort.Direction direction) {
		List<DateRangeAllDTO> response = downloadsStatisticsReportService.getDateRangeOfWeek(year);
		if (direction.isDescending()) {
			Collections.reverse(response);
		}
		return ResponseEntity.ok(response);
	}

	@GetMapping("date-range-of-week-full")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<List<DateRangeAllDTO>> getDateRangeOfWeekFull(@RequestParam Year year,
			@RequestParam(required = false, defaultValue = "ASC") Sort.Direction direction) {
		List<DateRangeAllDTO> response = downloadsStatisticsReportService.getDateRangeOfWeekFull(year);
		if (direction.isDescending()) {
			Collections.reverse(response);
		}
		return ResponseEntity.ok(response);
	}

	@GetMapping("week-statistics")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<List<WeeklyUserStatisticsResponse>> getWeekStatisticsBy(
			@RequestParam(required = false) Set<Integer> weeks, @RequestParam Year year,
			@RequestParam RequestedUserStatisticType type,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone) {
		ZoneId zone = ZoneId.of(timeZone);

		List<WeeklyUserStatisticsResponse> response = switch (type) {
			case DOWNLOADS ->
				weeklyUserStatisticsIntegrationService.findForWeeks(weeks, year, UserStatisticType.DOWNLOADS, zone)
						.stream().map(MapStructConverter.MAPPER::toWeeklyUserDownloadsResponse).toList();
			case USER_REGISTERED_DELETED ->
				// Using zip is safe here since the underlying implementation for
				// WeeklyUserStatisticsIntegrationService.findForWeeks maps across
				// `weeks`,
				// so the length of both registered and deleted are guaranteed to be the
				// same since they share the same
				// `weeks`.
				Streams.zip(
						weeklyUserStatisticsIntegrationService
								.findForWeeks(weeks, year, UserStatisticType.USER_REGISTERED, zone).stream(),
						weeklyUserStatisticsIntegrationService
								.findForWeeks(weeks, year, UserStatisticType.USER_DELETED, zone).stream(),
						(registered, deleted) -> {
							WeeklyUserStatisticsResponse result = MapStructConverter.MAPPER
									.toWeeklyUserRegisteredResponse(registered);
							result.setDeleted(deleted.getValue());

							Long targetedRegistered = accountSystemConfigurationService.getTargetedRegistered();

							result.setRegisteredPercentage(downloadsStatisticsReportService
									.divideAndMultiplyByPercentage(registered.getValue(), targetedRegistered));

							return result;
						}).toList();

		};

		return ResponseEntity.ok(response);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@GetMapping("week-statistics/synchronize")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public void synchronizeWeekStatistics(@RequestParam(required = false) Year year,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone) {
		ZoneId zone = ZoneId.of(timeZone);
		LocalDate now = LocalDate.now();
		weeklyUserStatisticsIntegrationService.synchronize(DateUtil.getOrDefault(year, Year.of(now.getYear())), zone);
	}

	@GetMapping("monthly-target-vs-actual")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<List<MonthlyUserTargetAndActualResponse>> getMonthlyUserTargetVsActual(
			@RequestParam Year year,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone,
			@RequestParam(required = false, defaultValue = "ASC") Sort.Direction direction) {
		List<DateRangeAllDTO> dateRangeMonthly = downloadsStatisticsReportService.getMonthlyDateRange(year);
		List<MonthlyUserTargetAndActualResponse> responseList = downloadsStatisticsReportService
				.getRegisteredUserByDateRange(dateRangeMonthly, ZoneId.of(timeZone));
		if (direction.isDescending()) {
			Collections.reverse(responseList);
		}
		return ResponseEntity.ok(responseList);
	}

	@GetMapping("quarterly-summary")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ_DOWNLOADS_STATISTICS)")
	public ResponseEntity<List<DownloadsAndRegisteredUserResponse>> getQuarterlySummary(@RequestParam Year year,
			@RequestParam(required = false, defaultValue = TimeConstant.DEFAULT_TIMEZONE) String timeZone,
			@RequestParam(required = false, defaultValue = "ASC") Sort.Direction direction) {

		List<DateRangeAllDTO> dateRangeQuarterly = downloadsStatisticsReportService.getQuarterlyDateRange(year);

		List<DownloadsAndRegisteredUserResponse> responseList = downloadsStatisticsReportService
				.getDownloadsAndRegisteredUserByDateRange(dateRangeQuarterly, year, ZoneId.of(timeZone));
		if (direction.isDescending()) {
			Collections.reverse(responseList);
		}
		return ResponseEntity.ok(responseList);
	}

}
