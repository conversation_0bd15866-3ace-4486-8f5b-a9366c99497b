package my.com.mandrill.component.dto.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.LoanPaymentStatus;
import my.com.mandrill.utilities.general.constant.LoanTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Month;
import java.time.Year;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoanDTO implements Serializable {

	private String id;

	private BankListDTO provider;

	private Short duration;

	private Month repaymentStartMonth;

	@Schema(implementation = Short.class, example = "1997")
	@JsonFormat(shape = JsonFormat.Shape.NUMBER_INT, pattern = "yyyy")
	private Year repaymentStartYear;

	private BigDecimal monthlyInstallment;

	private BigDecimal interestRate;

	private EntityName entityName;

	private String entityId;

	private LoanTypeEnum type;

	private BigDecimal amount;

	private String label;

	private BigDecimal percentage;

	private String attachmentGroupId;

	private BigDecimal purchaseValue;

	private LoanPaymentStatus paymentStatus;

	private BigDecimal loanBalance;

}
