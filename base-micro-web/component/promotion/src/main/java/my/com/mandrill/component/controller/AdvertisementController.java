package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Advertisement;
import my.com.mandrill.component.dto.model.AdvertisementDTO;
import my.com.mandrill.component.dto.model.AdvertisementRequestDTO;
import my.com.mandrill.component.service.AdvertisementIntegrationService;
import my.com.mandrill.component.service.AdvertisementProductService;
import my.com.mandrill.component.service.AdvertisementService;
import my.com.mandrill.component.service.UserAdvertisementIntegrationService;
import my.com.mandrill.utilities.feign.dto.response.AdvertisementResponse;
import my.com.mandrill.utilities.general.constant.AdvertisementStatus;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "advertisement-controller")
@Slf4j
@RestController
@RequestMapping("/advertisements")
@RequiredArgsConstructor
public class AdvertisementController {

	private final AdvertisementService advertisementService;

	private final AdvertisementIntegrationService advertisementIntegrationService;

	private final UserAdvertisementIntegrationService userAdvertisementIntegrationService;

	private final AdvertisementProductService advertisementProductService;

	private final ObjectMapper objectMapper;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<AdvertisementResponse>> getListOfAdvertisementByActiveTrueAndDate(
			@RequestParam(required = false, defaultValue = "en") String language,
			@SortDefault.SortDefaults({ @SortDefault(sort = "seq", direction = Sort.Direction.ASC),
					@SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) }) Sort sort) {
		// Implementation
		List<Advertisement> results = userAdvertisementIntegrationService.findAvailable(sort,
				SecurityUtil.currentUserId());

		// Result
		return ResponseEntity.ok(results.stream()
				.map(advertisement -> advertisementIntegrationService.populateByLanguage(advertisement, language))
				.toList());
	}

	@GetMapping("active")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<AdvertisementResponse>> getListOfAdvertisementByActiveTrue(
			@RequestParam(required = false, defaultValue = "en") String language,
			@SortDefault.SortDefaults({ @SortDefault(sort = "seq", direction = Sort.Direction.ASC),
					@SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) }) Sort sort,
			@RequestParam(required = false) String platform) {

		return ResponseEntity.ok(advertisementIntegrationService.getActiveAdvertisements(language, platform, sort));
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<AdvertisementResponse> getAdvertisementByIdWithProduct(@PathVariable String id,
			@RequestParam(required = false, defaultValue = "en") String language,
			@RequestParam(required = false) String platform) {
		// Implementation
		Advertisement result = advertisementService.findById(id);
		AdvertisementRequestDTO advertisementRequestDTO = new AdvertisementRequestDTO();
		advertisementRequestDTO.setLanguage(language);
		advertisementRequestDTO.setPlatform(platform);
		// Result
		AdvertisementResponse advertisementResponse = advertisementIntegrationService
				.populateByProductWithDetails(result, advertisementRequestDTO);
		return ResponseEntity.ok(advertisementResponse);
	}

	@GetMapping("/active/{entityName}")
	public ResponseEntity<AdvertisementResponse> getLatestActiveCampaignByEntityName(
			@PathVariable EntityName entityName, @RequestParam(required = false, defaultValue = "en") String language,
			@RequestParam(required = false) String platform) {
		Advertisement result = advertisementService.findActiveCampaignByEntityName(entityName);
		AdvertisementRequestDTO advertisementRequestDTO = new AdvertisementRequestDTO();
		advertisementRequestDTO.setLanguage(language);
		advertisementRequestDTO.setPlatform(platform);

		AdvertisementResponse advertisementResponse = advertisementIntegrationService
				.populateByProductWithDetails(result, advertisementRequestDTO);

		return ResponseEntity.ok(advertisementResponse);
	}

	@GetMapping("/check-now-is-in-campaign-period-by-id")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Boolean> checkIsInCampaignPeriodBy(@RequestParam String id) {
		return ResponseEntity.ok(advertisementIntegrationService.checkNowIsInCampaignPeriodById(id));
	}

	@Hidden
	@GetMapping("/integration/find-by-code")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<AdvertisementDTO> getAdvertisementByCode(@RequestParam String code) {
		Advertisement response = advertisementService.findByCode(code);
		return ResponseEntity.ok(objectMapper.convertValue(response, AdvertisementDTO.class));
	}

	@GetMapping("/check-now-is-in-campaign-period-by-menu-code")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Boolean> checkNowIsInCampaignPeriodByMenuCode(@RequestParam String menuCode) {
		boolean isActive = advertisementService.checkNowIsInCampaignPeriodByMenuCode(menuCode);
		return ResponseEntity.ok(isActive);
	}

	@GetMapping("active/product/{productId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<AdvertisementResponse> getAdvertisementByActiveTrueAndProductId(
			@RequestParam(required = false, defaultValue = "en") String language, @PathVariable String productId) {
		List<String> advertisementIds = advertisementProductService.findByProductId(productId).stream()
				.map(app -> app.getAdvertisement().getId()).toList();

		if (!advertisementIds.isEmpty()) {
			Advertisement result = advertisementService
					.findFirstByStatusAndDeletedFalseAndIdIn(AdvertisementStatus.LIVE, advertisementIds);
			if (result != null) {
				return ResponseEntity.ok(advertisementIntegrationService.populateByLanguage(result, language));
			}
			else
				return ResponseEntity.ok(new AdvertisementResponse());
		}
		else {
			return ResponseEntity.ok(new AdvertisementResponse());
		}
	}

}
