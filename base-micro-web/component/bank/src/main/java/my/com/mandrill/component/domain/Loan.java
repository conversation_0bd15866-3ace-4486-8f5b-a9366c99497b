package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import my.com.mandrill.component.dto.request.ReminderIntegrationRequest;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.LoanPaymentStatus;
import my.com.mandrill.utilities.general.constant.LoanTypeEnum;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Month;
import java.time.Year;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "loan",
		uniqueConstraints = { @UniqueConstraint(columnNames = { "user_id", "entity_name", "entity_id" }) })
public class Loan extends AuditSection implements Serializable {

	@NotBlank
	@Size(max = 36)
	@Column(name = "user_id", nullable = false, length = 36)
	private String userId;

	@NotNull
	@ManyToOne(optional = false)
	@JoinColumn(name = "provider_id", nullable = false)
	private BankList provider;

	@NotNull
	@Column(name = "duration", nullable = false)
	private Short duration;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "repayment_start_month", nullable = false)
	private Month repaymentStartMonth;

	@NotNull
	@Column(name = "repayment_start_year", nullable = false)
	private Year repaymentStartYear;

	@NotNull
	@Digits(integer = 15, fraction = 2)
	@Column(name = "monthly_installment", nullable = false, precision = 15, scale = 2)
	private BigDecimal monthlyInstallment;

	@Digits(integer = 5, fraction = 2)
	@Column(name = "interest_rate", precision = 5, scale = 2)
	private BigDecimal interestRate;

	@Enumerated(EnumType.STRING)
	@Column(name = "entity_name")
	private EntityName entityName;

	@Size(max = 36)
	@Column(name = "entity_id", nullable = false, length = 36)
	private String entityId;

	@NotNull
	@Enumerated(EnumType.STRING)
	@Column(name = "type", nullable = false)
	private LoanTypeEnum type;

	@Digits(integer = 15, fraction = 2)
	@Column(name = "amount", precision = 15, scale = 2)
	private BigDecimal amount;

	@Max(100)
	@Digits(integer = 5, fraction = 2)
	@Column(name = "percentage", precision = 5, scale = 2)
	private BigDecimal percentage;

	@Transient
	private Boolean isReminder = false;

	@Transient
	private ReminderIntegrationRequest reminderIntegrationRequest;

	@ToString.Exclude
	@ManyToOne
	@JsonBackReference
	@JoinColumn(name = "bank_id")
	private Bank bank;

	@Size(max = 200)
	@Column(name = "label", length = 200)
	private String label;

	@Size(max = 36)
	@Column(name = "attachment_group_id", length = 36)
	private String attachmentGroupId;

	@Digits(integer = 15, fraction = 2)
	@Column(name = "loan_balance", precision = 15, scale = 2)
	private BigDecimal loanBalance;

	@Transient
	private BigDecimal purchaseValue;

	@Enumerated(EnumType.STRING)
	@Column(name = "payment_status")
	private LoanPaymentStatus paymentStatus;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		Loan loan = (Loan) o;
		return getId() != null && Objects.equals(getId(), loan.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}