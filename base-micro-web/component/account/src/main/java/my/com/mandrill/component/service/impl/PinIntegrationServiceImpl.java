package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.request.ChangePinRequest;
import my.com.mandrill.component.service.KeyRequestIntegrationService;
import my.com.mandrill.component.service.PasscodeProviderService;
import my.com.mandrill.component.service.PinIntegrationService;
import my.com.mandrill.component.service.UserIntegrationService;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class PinIntegrationServiceImpl implements PinIntegrationService {

	private final Map<String, PasscodeProviderService> passcodeProviderService;

	private final KeyRequestIntegrationService keyRequestIntegrationService;

	private final PasswordEncoder passwordEncoder;

	private final UserIntegrationService userIntegrationService;

	public UserKeyRequest validate(String pin, AppUser user, Supplier<UserKeyRequest> supplier) {
		log.info("validating pin...");
		passcodeProviderService.get(PasscodeType.PIN.getProvider()).match(user, pin);

		log.info("validating success, execute post process");
		return supplier.get();
	}

	@Override
	public void changePin(User user, ChangePinRequest request) {
		log.info("change pin...");
		keyRequestIntegrationService.withValidateKey(request.getKey(), RequestKeyType.VERIFICATION_CHANGE_PIN, () -> {
			log.info("save new pin to persistence");
			user.setPin(passwordEncoder.encode(request.getNewPin()));
			user.setLoginFailAttempt(0);
			return userIntegrationService.save(user);
		});
	}

}
