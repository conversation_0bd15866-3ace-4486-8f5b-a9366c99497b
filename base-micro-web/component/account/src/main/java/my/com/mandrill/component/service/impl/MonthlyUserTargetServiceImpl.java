package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.MonthlyUserTarget;
import my.com.mandrill.component.dto.model.MonthlyUserTargetDTO;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.MonthlyUserTargetRepository;
import my.com.mandrill.component.service.MonthlyUserTargetService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.BooleanUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Year;

@Slf4j
@Service
@RequiredArgsConstructor
public class MonthlyUserTargetServiceImpl implements MonthlyUserTargetService {

	private final MonthlyUserTargetRepository monthlyUserTargetRepository;

	@Override
	public boolean existsByYearAndMonth(Year year, int month) {
		return monthlyUserTargetRepository.existsByYearAndMonth(year, month);
	}

	@Override
	public Page<MonthlyUserTargetDTO> findAllPageable(Pageable pageable, Year year, Integer month) {
		return monthlyUserTargetRepository.findAllPageable(pageable, year, month)
				.map(MapStructConverter.MAPPER::toMonthlyUserTargetDTO);
	}

	@Override
	public MonthlyUserTargetDTO findById(String id) {
		MonthlyUserTarget monthlyUserTarget = monthlyUserTargetRepository.findById(id)
				.orElseThrow(ExceptionPredicate.monthlyUserTargetNotFound(id));
		return MapStructConverter.MAPPER.toMonthlyUserTargetDTO(monthlyUserTarget);
	}

	@Override
	@Transactional
	public MonthlyUserTarget save(MonthlyUserTarget monthlyUserTarget) {
		return monthlyUserTargetRepository.save(monthlyUserTarget);
	}

	@Override
	public MonthlyUserTarget update(String id, MonthlyUserTarget monthlyUserTarget) {
		MonthlyUserTarget currentRecord = monthlyUserTargetRepository.findById(id)
				.orElseThrow(ExceptionPredicate.monthlyUserTargetNotFound(id));

		Year newYear = monthlyUserTarget.getYear();
		int newMonth = monthlyUserTarget.getMonth();
		if (BooleanUtil.or(!currentRecord.getYear().equals(newYear), currentRecord.getMonth() != newMonth)) {
			if (monthlyUserTargetRepository.existsByYearAndMonth(newYear, newMonth)) {
				throw new BusinessException(ErrorCodeEnum.MONTHLY_USER_TARGET_EXISTS);
			}
		}
		currentRecord.setYear(newYear);
		currentRecord.setMonth(newMonth);
		currentRecord.setTarget(monthlyUserTarget.getTarget());
		currentRecord.setDescription(monthlyUserTarget.getDescription());

		return currentRecord;

	}

	@Override
	@Transactional
	public void deleteById(String id) {
		monthlyUserTargetRepository.deleteById(id);
	}

	@Override
	public Long findTargetByYearAndMonth(Year year, int month) {
		return monthlyUserTargetRepository.findTargetByYearAndMonth(year, month);
	}

}
