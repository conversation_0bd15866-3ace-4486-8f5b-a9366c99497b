package my.com.mandrill.component.integration.user;

import com.fasterxml.jackson.core.type.TypeReference;
import my.com.mandrill.component.dto.response.RefereeEarningResponse;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.*;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

public class ReferredFriendsIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private JSONUtil jsonUtil;

	@Autowired
	private ReferreRepository referreRepository;

	@Autowired
	private RsmDetailRepository rsmDetailRepository;

	@Autowired
	private RsmHeaderRepository rsmHeaderRepository;

	@Autowired
	private PointEarningRepository pointEarningRepository;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@Autowired
	private PointWithdrawalRepository pointWithdrawalRepository;

	@AfterEach
	public void cleanUp() {
		referreRepository.deleteAll();
		pointEarningRepository.deleteAll();
		pointTransactionRepository.deleteAll();
		pointWithdrawalRepository.deleteAll();
		rsmDetailRepository.deleteAll();
		rsmHeaderRepository.deleteAll();
	}

	@Test
	public void getReferredFriends_success() throws Exception {
		MvcResult mvcResult = mockMvc
				.perform(get("/account/v1/referred-friends").header("Authorization", "Bearer " + getUserSecretToken())
						.contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.status().isOk()).andReturn();

		Assertions.assertNotNull(mvcResult.getResponse().getContentAsString());
		List<RefereeEarningResponse> response = jsonUtil
				.convertValueFromJson(mvcResult.getResponse().getContentAsString(), new TypeReference<>() {
				});
		List<RefereeEarningResponse> expectation = getExpectation(new TypeReference<>() {
		});
		Assertions.assertNotNull(response);
		Assertions.assertNotNull(expectation);
		Assertions.assertEquals(expectation, response);
	}

}
