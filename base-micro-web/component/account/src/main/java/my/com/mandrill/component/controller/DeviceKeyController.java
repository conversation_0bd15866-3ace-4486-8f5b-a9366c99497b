package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.DeviceKey;
import my.com.mandrill.component.domain.SignatureChallenge;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.dto.model.DeviceKeyDTO;
import my.com.mandrill.component.dto.model.SignatureChallengeDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

@Tag(name = "00 - Device Registration")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("device-keys")
public class DeviceKeyController {

	private final UserService userService;

	private final DeviceKeyService deviceKeyService;

	private final ValidationService validationService;

	private final KeyRequestService keyRequestService;

	private final DeviceKeyIntegrationService deviceKeyIntegrationService;

	private final KeyRequestIntegrationService keyRequestIntegrationService;

	private final AppUserService appUserService;

	private final FeatureFlagOutbound featureFlagOutbound;

	@PostMapping("register/start")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<SignatureChallengeDTO> registerStart(@Valid @RequestBody DeviceRegisterStartRequest request) {
		AppUser appUser = appUserService.findByRefNo(SecurityUtil.currentUserLogin());
		DeviceKey deviceKey = MapStructConverter.MAPPER.toDeviceKey(request);
		SignatureChallenge signatureChallenge = deviceKeyIntegrationService.registerStart(deviceKey, appUser);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toSignatureChallengeDTO(signatureChallenge));
	}

	@PutMapping("register/finish")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<SignatureChallengeDTO> registerFinish(
			@Valid @RequestBody RegisterSignatureChallengeRequest request) {
		AppUser user = appUserService.findByRefNo(SecurityUtil.currentUserLogin());
		SignatureChallenge signatureChallenge = MapStructConverter.MAPPER.toSignatureChallenge(request);

		Supplier<SignatureChallenge> signatureChallengeSupplier = () -> deviceKeyIntegrationService
				.registerFinish(signatureChallenge, user);

		SignatureChallenge result;
		if (Objects.isNull(user.getPin())) {
			log.info("user does not have a PIN, skip the verification step key");
			result = signatureChallengeSupplier.get();
		}
		else {
			if (StringUtils.isBlank(request.getKey())) {
				log.error("key blank");
				throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
			}

			log.info("user has a PIN, verify the key");
			result = keyRequestIntegrationService.withValidateKey(request.getKey(),
					RequestKeyType.VERIFICATION_ENABLE_BIOMETRIC, signatureChallengeSupplier);
		}

		return ResponseEntity.ok(MapStructConverter.MAPPER.toSignatureChallengeDTO(result));
	}

	@PostMapping("assertion/start")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<SignatureChallengeDTO> assertionStart(@Valid @RequestBody ChallengeRequest request) {
		AppUser user = appUserService.findByRefNo(SecurityUtil.currentUserLogin());
		DeviceKey deviceKey = MapStructConverter.MAPPER.toDeviceKey(request);
		SignatureChallenge result = deviceKeyIntegrationService.assertionStart(deviceKey, user);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toSignatureChallengeDTO(result));
	}

	@PutMapping("assertion/finish")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<SignatureChallengeDTO> assertionFinish(
			@Valid @RequestBody AssertionSignatureChallengeRequest request) {
		AppUser user = appUserService.findByRefNo(SecurityUtil.currentUserLogin());
		SignatureChallenge signatureChallenge = MapStructConverter.MAPPER.toSignatureChallenge(request);
		SignatureChallenge result = deviceKeyIntegrationService.assertionFinish(signatureChallenge, user);
		return ResponseEntity.ok(MapStructConverter.MAPPER.toSignatureChallengeDTO(result));
	}

	@PostMapping("assertion/verify")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<KeyResponse> signatureVerification(@Valid @RequestBody KeySignatureChallengeRequest request) {
		log.info("assertion verify: {}", request.getRequestKeyType());
		validationService.validateSignatureVerificationRequestKey(request.getRequestKeyType());

		SignatureChallenge signatureChallenge = MapStructConverter.MAPPER.toSignatureChallenge(request);
		AppUser user = appUserService.findByRefNo(SecurityUtil.currentUserLogin());

		log.info("signature checking...");
		deviceKeyIntegrationService.assertionFinish(signatureChallenge, user);

		log.info("creating request key");
		int expiryInHours = featureFlagOutbound.getIntegerValue(GlobalSystemConfigurationEnum.KEY_REQUEST_VALID_HOURS);
		UserKeyRequest keyRequest = keyRequestService.createRequestKey(user.getUsername(), request.getRequestKeyType(),
				expiryInHours);

		KeyResponse keyResponse = new KeyResponse();
		keyResponse.setKey(keyRequest.getKeyValue());
		return ResponseEntity.ok(keyResponse);
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<DeviceKeyDTO>> findAllDeviceKey(Sort sort) {
		AppUser appUser = appUserService.findByRefNo(SecurityUtil.currentUserLogin());
		List<DeviceKey> deviceKeys = deviceKeyService.findAllDeviceKey(sort, appUser);
		return ResponseEntity.ok(deviceKeys.stream().map(MapStructConverter.MAPPER::toDeviceKeyDTO).toList());
	}

}
