package my.com.mandrill.component.client;

import my.com.mandrill.component.dto.request.clientRequest.ChatBotClientRequest;
import my.com.mandrill.component.dto.request.clientRequest.ChatBotFeedbackClientRequest;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import org.springframework.web.service.annotation.PutExchange;
import reactor.core.publisher.Flux;

@HttpExchange
public interface ChatBotClient {

	@PostExchange("chat")
	ResponseEntity<String> sendChat(@RequestBody ChatBotClientRequest request);

	@GetExchange("chat/{userId}")
	ResponseEntity<String> getLatestChat(@PathVariable String userId);

	@PutExchange("feedback/{sessionId}/{messageId}")
	ResponseEntity<String> sendFeedback(@PathVariable String sessionId, @PathVariable String messageId,
			@RequestBody ChatBotFeedbackClientRequest request);

	@PostExchange(value = "v2/chat", accept = MediaType.TEXT_EVENT_STREAM_VALUE)
	Flux<String> sendStreamingChat(@RequestBody ChatBotClientRequest request);

}
