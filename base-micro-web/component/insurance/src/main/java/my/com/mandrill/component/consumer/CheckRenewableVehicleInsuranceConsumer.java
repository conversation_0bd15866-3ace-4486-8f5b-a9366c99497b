package my.com.mandrill.component.consumer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Insurance;
import my.com.mandrill.component.service.InsuranceService;
import my.com.mandrill.utilities.feign.client.VehicleFeignClient;
import my.com.mandrill.utilities.feign.dto.ObjectRequest;
import my.com.mandrill.utilities.general.constant.InsuranceTypeEnum;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.model.VehicleDTO;
import my.com.mandrill.utilities.general.dto.request.CreateReminderVehicleInsuranceDTO;
import my.com.mandrill.utilities.general.dto.request.SyncVehicleRenewalDateRequest;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static my.com.mandrill.component.config.KafkaTopicConfig.GROUP;

@Slf4j
@Service
@RequiredArgsConstructor
public class CheckRenewableVehicleInsuranceConsumer {

	private final JSONUtil jsonUtil;

	private final InsuranceService insuranceService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final VehicleFeignClient vehicleFeignClient;

	@KafkaListener(topics = KafkaTopic.CHECK_RENEWABLE_VEHICLE_INSURANCE, groupId = GROUP,
			id = KafkaTopic.CHECK_RENEWABLE_VEHICLE_INSURANCE)
	public void checkRenewableInsurance(String message) {
		log.info("Start checking renewable insurance");

		List<Insurance> insurances = insuranceService.findExpiring(InsuranceTypeEnum.VEHICLE);
		List<ObjectRequest> vehicleIds = insurances.stream().map(v -> {
			ObjectRequest req = new ObjectRequest();
			req.setId(v.getEntityId());

			return req;
		}).toList();
		Map<String, VehicleDTO> vehicleMap = new HashMap<>();
		if (!vehicleIds.isEmpty()) {
			List<VehicleDTO> vehicles = vehicleFeignClient.findVehicleByIdIntegration(vehicleIds);
			vehicleMap = vehicles.stream().collect(Collectors.toMap(VehicleDTO::getId, Function.identity()));
		}

		List<CreateReminderVehicleInsuranceDTO> data = new ArrayList<>();
		for (Insurance insurance : insurances) {
			VehicleDTO vehicleDTO = vehicleMap.get(insurance.getEntityId());
			if (Objects.isNull(vehicleDTO)) {
				continue;
			}

			data.add(CreateReminderVehicleInsuranceDTO.builder().insuranceId(insurance.getId())
					.vehicleTypeName(vehicleDTO.getVehicleTypeName()).vehicleId(vehicleDTO.getId())
					.vehicleMake(vehicleDTO.getVixVehicleMake()).vehicleModel(vehicleDTO.getVixVehicleModel())
					.vehicleRegistrationNo(vehicleDTO.getVixVehicleRegistrationNo())
					.renewalDate(insurance.getRenewalDate()).userId(insurance.getUserId()).build());
		}
		log.info("Finish checking renewable insurance: {}", data.size());

		if (!data.isEmpty()) {
			kafkaTemplate.send(KafkaTopic.CREATE_REMINDER_VEHICLE_INSURANCE_EXPIRE, jsonUtil.convertToString(data));
		}
		for (CreateReminderVehicleInsuranceDTO syncAble : data) {
			kafkaTemplate.send(KafkaTopic.FINOLOGY_SYNC_RENEWAL_DATE_TOPIC,
					jsonUtil.convertToString(SyncVehicleRenewalDateRequest.builder().userId(syncAble.getUserId())
							.vehicleRegistrationNo(syncAble.getVehicleRegistrationNo())
							.vehicleId(syncAble.getVehicleId()).build()));
		}
	}

}
