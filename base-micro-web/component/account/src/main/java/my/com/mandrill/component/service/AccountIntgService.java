package my.com.mandrill.component.service;

import jakarta.validation.Valid;
import my.com.mandrill.component.dto.model.UserDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.CheckAccountResponse;
import my.com.mandrill.component.dto.response.KeyResponse;
import my.com.mandrill.component.dto.response.PreRegisterResponse;

public interface AccountIntgService {

	KeyResponse requestOtp(OTPRequest request);

	void register(SignUpRequest signUpRequest);

	CheckAccountResponse checkAccount(CheckAccountRequest checkAccountRequest);

	PreRegisterResponse preRegister(PreSignUpRequest signUpRequest);

	void finishRegister(FinishSignUpRequest request);

	void updateTncAgreement(@Valid TncAgreementRequest request);

	void createPin(PublicCreatePinRequest request);

	UserDTO getAccount();

}
