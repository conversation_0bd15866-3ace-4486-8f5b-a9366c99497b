buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath("io.spring.javaformat:spring-javaformat-gradle-plugin:0.0.35")
    }
}

plugins {
    id 'org.springframework.boot' version "${spring_boot_version}"
    id 'io.spring.dependency-management' version "${spring_dependency_version}"
    id 'java'
}

apply plugin: 'io.spring.javaformat'

group "${project_group}"
version "${project_version}"

sourceCompatibility = "${java_compatibility_version}"
targetCompatibility = "${java_compatibility_version}"

dependencies {
    implementation project(':utilities:general')

    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-security"
    implementation "org.springframework.boot:spring-boot-starter-data-jpa"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation 'org.springframework.boot:spring-boot-starter-webflux'

    implementation "org.springframework.cloud:spring-cloud-starter-openfeign:${spring_cloud_version}"

    compileOnly "org.projectlombok:lombok:${lombok_version}"
    annotationProcessor "org.projectlombok:lombok:${lombok_version}"
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-api:${springdoc_version}"

    implementation "org.springframework.cloud:spring-cloud-starter-kubernetes-client-all:${spring_cloud_kubernetes_version}"


}