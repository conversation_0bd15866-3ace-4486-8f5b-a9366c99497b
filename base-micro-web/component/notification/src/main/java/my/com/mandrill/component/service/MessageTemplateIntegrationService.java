package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.MessageTemplate;
import my.com.mandrill.component.dto.request.SavingGoalSharingTemplateRequest;
import my.com.mandrill.component.dto.response.GetMessageTemplateResponse;
import my.com.mandrill.component.dto.response.UserShareMessageHeaderResponse;

import java.util.List;

public interface MessageTemplateIntegrationService {

	GetMessageTemplateResponse processSavingGoalSavingTemplate(MessageTemplate messageTemplate,
			SavingGoalSharingTemplateRequest request);

	UserShareMessageHeaderResponse getShareMessageTemplate(String code);

	List<GetMessageTemplateResponse> getShareProductMessage(String userId, String productName, String productType);

}
