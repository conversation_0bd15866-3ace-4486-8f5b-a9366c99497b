package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Expense;
import my.com.mandrill.component.domain.ExpenseType;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExpenseDTO;
import my.com.mandrill.component.dto.model.ExpenseProjection;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExpenseRepository extends JpaRepository<Expense, String> {

	long deleteByUserAndIdNotIn(@Nullable User user, @Nullable Collection<String> ids);

	long deleteByUserAndId(@Nullable User user, @Nullable String id);

	@Modifying
	@Query("delete from Expense e where e.user.id in :userIds")
	void deleteByUserIds(@Param("userIds") List<String> userIds);

	Optional<Expense> findByIdAndUser(@NonNull String id, @NonNull User user);

	@Query("""
			select e from Expense e where e.user = :user
			and (:expenseType is null or e.expenseType = :expenseType)
			""")
	List<Expense> findByUserAndExpenseType(@NonNull User user, @NonNull ExpenseType expenseType, Sort sort);

	@Query("""
			select new my.com.mandrill.component.dto.model.ExpenseDTO(e.id, e.amount, e.active,
			new my.com.mandrill.component.dto.model.ExpenseTypeDTO(et.id, et.code, et.name), e.label)
			from Expense e join e.expenseType et
			join e.user u where u.refNo = :refNo
			""")
	List<ExpenseDTO> findByRefNo(@NonNull String refNo);

	@Query("""
			select e.amount as expenseAmount, et.name as expenseTypeName
			from Expense e join e.expenseType et
			where e.user.id = :userId
			""")
	List<ExpenseProjection> findProjectionByUserId(@NonNull String userId);

}
