package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.MessageTemplateParamKey;
import my.com.mandrill.component.domain.MessageTemplate;
import my.com.mandrill.component.domain.MessageTemplateHeader;
import my.com.mandrill.component.dto.request.SavingGoalSharingTemplateRequest;
import my.com.mandrill.component.dto.response.GetMessageTemplateResponse;
import my.com.mandrill.component.dto.response.UserShareMessageHeaderResponse;
import my.com.mandrill.component.dto.response.UserShareMessageResponse;
import my.com.mandrill.component.service.FreeMarkerTemplateService;
import my.com.mandrill.component.service.MessageTemplateHeaderService;
import my.com.mandrill.component.service.MessageTemplateIntegrationService;
import my.com.mandrill.component.service.MessageTemplateService;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.ChannelCodeEnum;
import my.com.mandrill.utilities.general.constant.MessageTemplateCodeEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MessageTemplateIntegrationServiceImpl implements MessageTemplateIntegrationService {

	private final FreeMarkerTemplateService freeMarkerTemplateService;

	private final MessageTemplateHeaderService messageTemplateHeaderService;

	private final MessageTemplateService messageTemplateService;

	private final ProxyFeignClient proxyFeignClient;

	@Override
	public GetMessageTemplateResponse processSavingGoalSavingTemplate(MessageTemplate messageTemplate,
			SavingGoalSharingTemplateRequest request) {

		GetMessageTemplateResponse messageTemplateResponse = MapStructConverter.MAPPER
				.toGetMessageTemplateResponse(messageTemplate);

		Map<String, Object> dataModel = Map.of(MessageTemplateParamKey.SAVING_GOAL_NAME.getKey(),
				request.getSavingGoalName());

		processMessageTemplate(messageTemplateResponse, dataModel);

		return messageTemplateResponse;

	}

	@Override
	public UserShareMessageHeaderResponse getShareMessageTemplate(String code) {
		MessageTemplateHeader header = messageTemplateHeaderService.getRSMParentMessageTemplate();

		UserShareMessageHeaderResponse response = MapStructConverter.MAPPER.toUserShareMessageHeaderResponse(header);

		List<UserShareMessageResponse> messageResponses = header.getMessageTemplates().stream()
				.filter(MessageTemplate::isActive).map(template -> mapTemplateToResponse(template, code))
				.collect(Collectors.toList());

		response.setUserShareMessageResponseList(messageResponses);
		return response;
	}

	private UserShareMessageResponse mapTemplateToResponse(MessageTemplate template, String code) {
		UserShareMessageResponse messageResponse = new UserShareMessageResponse();

		Map<String, Object> dataModel = Map.of(MessageTemplateParamKey.REFERRAL_CODE.getKey(), code);

		messageResponse.setCode(template.getCode().name());
		messageResponse.setSubject(template.getSubject());
		messageResponse.setContent(freeMarkerTemplateService.processTemplate(template.getCode().name(),
				template.getTemplate(), dataModel));
		messageResponse.setChannel(template.getChannel().name());
		return messageResponse;
	}

	private void processMessageTemplate(GetMessageTemplateResponse messageTemplate, Map<String, Object> dataModel) {

		String newTemplate = freeMarkerTemplateService.processTemplate(messageTemplate.getCode().name(),
				messageTemplate.getTemplate(), dataModel);
		messageTemplate.setTemplate(newTemplate);

		if (ChannelCodeEnum.EMAIL.equals(messageTemplate.getChannel()) && messageTemplate.getSubject() != null) {
			String newSubject = freeMarkerTemplateService.processTemplate(messageTemplate.getCode().name(),
					messageTemplate.getSubject(), dataModel);
			messageTemplate.setSubject(newSubject);
		}

	}

	@Override
	public List<GetMessageTemplateResponse> getShareProductMessage(String userId, String productName,
			String productType) {
		Optional<String> referralCode = Optional
				.ofNullable(proxyFeignClient.getMoneyXCoreFeignClient().existsReferralCodeByUserId(userId));
		MessageTemplateCodeEnum templateCode = MessageTemplateCodeEnum.SHARE_PRODUCT;
		if (referralCode.isPresent()) {
			templateCode = MessageTemplateCodeEnum.SHARE_PRODUCT_WITH_REFERRAL_CODE;
		}
		List<MessageTemplate> messageTemplates = messageTemplateService.findByActiveCode(templateCode);

		return messageTemplates.stream().map(template -> {
			GetMessageTemplateResponse response = MapStructConverter.MAPPER.toGetMessageTemplateResponse(template);
			Map<String, Object> dataModel = Map.of("productName", productName, "referralCode", referralCode.orElse(""),
					"productType", productType);
			processMessageTemplate(response, dataModel);
			return response;
		}).toList();
	}

}
