package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.UserSurvey;
import my.com.mandrill.component.dto.model.SurveyReportByActiveDTO;
import my.com.mandrill.component.dto.model.SurveyReportDTO;
import my.com.mandrill.component.dto.model.UserAiAnalyticsProfileDTO;
import my.com.mandrill.component.dto.request.SurveyHeaderCreateRequest;
import my.com.mandrill.component.dto.response.SurveyActiveFormResponse;
import my.com.mandrill.component.service.SurveyIntegrationService;
import my.com.mandrill.component.service.SurveyService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.dto.model.SurveyFormUserResultDTO;
import my.com.mandrill.utilities.general.constant.SurveyFormType;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("/survey")
@RequiredArgsConstructor
public class SurveyController {

	private final SurveyIntegrationService surveyIntegrationService;

	private final SurveyService surveyService;

	@PostMapping
	public ResponseEntity<String> processSurveyHeader(@Valid @RequestBody SurveyHeaderCreateRequest surveyHeaderRequest,
			String formId) {
		return ResponseEntity.ok(this.surveyIntegrationService.processSurveyHeaderV2(surveyHeaderRequest, formId));

	}

	@PostMapping("/{surveyHeaderId}/update-to-be-contacted")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public void updateToBeContacted(@PathVariable String surveyHeaderId,
			@RequestParam(required = false) boolean toBeContacted) {
		surveyService.updateToBeContacted(surveyHeaderId, toBeContacted);
	}

	@GetMapping
	public ResponseEntity<SurveyActiveFormResponse> getActiveQuestionnaire(
			@RequestParam(defaultValue = "WELCOME_APP") SurveyFormType formType) {

		return ResponseEntity.ok(surveyIntegrationService.getActiveSurveyFormResponse(formType));
	}

	@Hidden
	@GetMapping("/integration/export")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.REPORT_SURVEY_READ)")
	public ResponseEntity<Map<String, List<SurveyReportDTO>>> export(@RequestParam LocalDate startDate,
			@RequestParam LocalDate endDate, @RequestParam Set<String> formNameSet) {
		return ResponseEntity.ok(surveyIntegrationService.export(startDate, endDate, formNameSet));
	}

	@Hidden
	@GetMapping("/integration/export/by-active")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.REPORT_SURVEY_READ)")
	public ResponseEntity<Map<String, List<SurveyReportByActiveDTO>>> exportByActive(@RequestParam LocalDate startDate,
			@RequestParam LocalDate endDate, @RequestParam Set<String> formNameSet) {
		return ResponseEntity.ok(surveyIntegrationService.exportByActive(startDate, endDate, formNameSet));
	}

	@Hidden
	@GetMapping("/integration/active-by-name")
	public ResponseEntity<Map<String, Boolean>> checkSurveyFormActiveByName(@RequestParam Set<String> formName) {
		return ResponseEntity.ok(surveyIntegrationService.getSurveyFormCurrentActiveFlagByName(formName));
	}

	@Hidden
	@GetMapping("/integration/result")
	public SurveyFormUserResultDTO checkSurveyFormResult(@RequestParam SurveyFormType type) {
		return surveyIntegrationService.findFormSurveyResult(type, SecurityUtil.currentUserId());
	}

	@GetMapping("/pagination")
	@PreAuthorize("hasAuthority(@authorityPermission.REPORT_READ) && hasAuthority(@authorityPermission.REPORT_SURVEY_READ)")
	public ResponseEntity<Page<SurveyReportDTO>> export(@RequestParam LocalDate startDate,
			@RequestParam LocalDate endDate, @RequestParam Set<String> formNameSet, Pageable pageable) {
		return ResponseEntity
				.ok(surveyIntegrationService.getSurveyReportListing(startDate, endDate, formNameSet, pageable));
	}

	@GetMapping("salary-range-data")
	public ResponseEntity<BigDecimal> getSalaryRangeData() {
		return ResponseEntity.ok(surveyIntegrationService.getSalaryRangeData(SecurityUtil.currentUserId()));
	}

	@GetMapping("user-survey")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<UserSurvey> getUserSurveys(
			@RequestParam(defaultValue = "WELCOME_APP") SurveyFormType formType) {
		return ResponseEntity.ok(surveyService.findUserSurveyByUserIdAndSurveyFormType(formType));
	}

	/**
	 * Retrieves the current user's analytics data specifically for AI integration. This
	 * endpoint can be expanded in the future if additional analytics data is needed,
	 * reducing the number of feign client calls required by the AI service
	 * @return ResponseEntity containing the user's analytics profile with HTTP 200 (OK)
	 */
	@ServiceToServiceAccess
	@GetMapping("private/user/ai-profile")
	public ResponseEntity<UserAiAnalyticsProfileDTO> getUserAiAnalyticsProfile() {

		return ResponseEntity.ok(surveyIntegrationService.getUserAiAnalyticsProfile());
	}

}
