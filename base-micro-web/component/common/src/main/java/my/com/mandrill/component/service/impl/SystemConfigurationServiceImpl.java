package my.com.mandrill.component.service.impl;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.SystemConfiguration;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.jpa.SystemConfigurationRepository;
import my.com.mandrill.component.service.SystemConfigurationService;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.InstitutionDTO;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.SystemConfigurationEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.service.GlobalValidationService;
import my.com.mandrill.utilities.general.service.RedisService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Transactional(readOnly = true)
public class SystemConfigurationServiceImpl implements SystemConfigurationService {

	private final AccountFeignClient accountFeignClient;

	private final SystemConfigurationRepository systemConfigurationRepository;

	private final GlobalValidationService globalValidationService;

	private final RedisService redisService;

	public SystemConfigurationServiceImpl(AccountFeignClient accountFeignClient,
			SystemConfigurationRepository systemConfigurationRepository,
			GlobalValidationService globalValidationService, RedisService redisService) {
		this.accountFeignClient = accountFeignClient;
		this.systemConfigurationRepository = systemConfigurationRepository;
		this.globalValidationService = globalValidationService;
		this.redisService = redisService;
	}

	public Page<SystemConfiguration> getSystemConfigurations(Pageable page, String code, String institutionId) {
		return systemConfigurationRepository.findAllByCodeAndInstitutionId(page,
				globalValidationService.validateNullToLowerCase(code), institutionId);
	}

	public List<SystemConfiguration> getSystemConfigurationsByInstitutionId(String institutionId) {
		return systemConfigurationRepository.findAllByInstitutionId(institutionId);
	}

	public SystemConfiguration getSystemConfigurationById(String systemConfigurationId) {
		return systemConfigurationRepository.findById(systemConfigurationId)
				.orElseThrow(ExceptionPredicate.sysConfigByIdNotFound(systemConfigurationId));
	}

	@Transactional
	public SystemConfiguration updateSystemConfiguration(SystemConfiguration systemConfiguration) {
		SystemConfiguration existingSystemConfiguration = getSystemConfigurationById(systemConfiguration.getId());

		if (!existingSystemConfiguration.getCode().equalsIgnoreCase(systemConfiguration.getCode())
				|| !existingSystemConfiguration.getInstitutionId().equals(systemConfiguration.getInstitutionId())) {
			this.existsByCodeAndInstitutionId(systemConfiguration.getCode(), systemConfiguration.getInstitutionId());
		}

		// non-updatable field
		systemConfiguration.setInstitutionId(existingSystemConfiguration.getInstitutionId());

		systemConfiguration = systemConfigurationRepository.save(systemConfiguration);
		redisService.deleteFromHash(CacheKey.SYSTEM_CONFIGURATION, systemConfiguration.getCode());
		return systemConfiguration;
	}

	@Transactional
	public void deleteSystemConfigurationById(String systemConfigurationId) {
		SystemConfiguration systemConfiguration = getSystemConfigurationById(systemConfigurationId);

		if (systemConfiguration.getCode().equals(SystemConfigurationEnum.DEFAULT_INSTITUTION_TIER_MAX.getCode())) {
			throw new BusinessException(ErrorCodeEnum.DEFAULT_INSTITUTION_TIER_MAX_CANNOT_BE_DELETED);
		}

		systemConfigurationRepository.delete(systemConfiguration);
	}

	@Transactional
	public String getDefaultInstitutionId() {
		String result = "";
		Optional<SystemConfiguration> defaultInstitution = systemConfigurationRepository
				.findByCodeAndActiveTrue(SystemConfigurationEnum.DEFAULT_USER_INSTITUTION_ID.getCode());
		if (defaultInstitution.isPresent()) {
			result = defaultInstitution.get().getValue();
		}
		else {
			result = SystemConfigurationEnum.DEFAULT_USER_INSTITUTION_ID.getValue();
		}
		return result;
	}

	@Override
	@Transactional
	public SystemConfiguration createSystemConfiguration(SystemConfiguration systemConfiguration) {
		// Institution Existent Validation
		InstitutionDTO institutionDTO = accountFeignClient
				.getActiveInstitutionById(systemConfiguration.getInstitutionId());
		systemConfiguration.setInstitutionId(institutionDTO.getId());

		this.existsByCodeAndInstitutionId(systemConfiguration.getCode(), systemConfiguration.getInstitutionId());

		return systemConfigurationRepository.save(systemConfiguration);
	}

	@Override
	@Transactional
	public SystemConfiguration createSystemConfigurationIntegration(SystemConfiguration systemConfiguration) {
		return systemConfigurationRepository.save(systemConfiguration);
	}

	@Override
	public SystemConfiguration getSystemConfigurationByCodeAndInstitutionId(String code, String institutionId) {
		SystemConfiguration systemConfiguration = systemConfigurationRepository
				.findByCodeAndInstitutionIdAndActiveTrue(code, institutionId)
				.orElseThrow(ExceptionPredicate.sysConfigByInstitutionIdAndCodeNotFound(institutionId, code));
		redisService.putToHash(CacheKey.SYSTEM_CONFIGURATION, code, systemConfiguration);
		return systemConfiguration;
	}

	@Override
	public SystemConfiguration getSystemConfigurationByCodeAndInstitutionIdReturnDefault(String code,
			String institutionId) {

		Optional<SystemConfiguration> cachedSystemConfiguration = redisService
				.getFromHash(CacheKey.SYSTEM_CONFIGURATION, code, SystemConfiguration.class);

		if (cachedSystemConfiguration.isPresent()) {
			log.info("system configuration serve by cache");
			return cachedSystemConfiguration.get();
		}

		Optional<SystemConfiguration> systemConfiguration = systemConfigurationRepository
				.findByCodeAndInstitutionIdAndActiveTrue(code, institutionId);

		if (systemConfiguration.isPresent()) {
			redisService.putToHash(CacheKey.SYSTEM_CONFIGURATION, code, systemConfiguration.get());
			return systemConfiguration.get();
		}

		SystemConfigurationEnum systemConfigurationEnum = SystemConfigurationEnum.filterByCode(code)
				.orElseThrow(ExceptionPredicate.sysConfigNotFound(code));

		return SystemConfiguration.builder().code(systemConfigurationEnum.getCode()).institutionId(institutionId)
				.value(systemConfigurationEnum.getValue()).description(systemConfigurationEnum.getDescription())
				.active(true).build();
	}

	@Override
	public List<SystemConfiguration> getSystemConfigurations(String institutionId) {
		return systemConfigurationRepository.findAllByInstitutionId(institutionId);
	}

	private void existsByCodeAndInstitutionId(String code, String institutionId) {
		if (systemConfigurationRepository.existsByCodeAllIgnoreCaseAndInstitutionId(code, institutionId)) {
			throw new BusinessException(ErrorCodeEnum.SYSTEM_CONFIG_EXISTS_BY_CODE_AND_INSTITUTION_ID);
		}
	}

}
