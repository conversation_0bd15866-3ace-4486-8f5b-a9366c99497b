package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Vehicle;

import java.time.Instant;
import java.util.List;
import java.util.function.Function;

public interface VehicleIntegrationService {

	Vehicle save(Vehicle vehicle, Function<Vehicle, Void> postProcess);

	void delete(String id, String userId, Function<String, Void> postProcess);

	void sendDashboardActivity(Instant createdDate);

	List<Vehicle> findDetachedInsurance(String userId);

	List<Vehicle> findDetachedLoan(String userId);

	void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source);

}
