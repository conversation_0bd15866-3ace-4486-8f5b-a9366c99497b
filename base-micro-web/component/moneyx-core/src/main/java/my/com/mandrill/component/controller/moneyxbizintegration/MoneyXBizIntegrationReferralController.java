package my.com.mandrill.component.controller.moneyxbizintegration;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.dto.request.CreateReferralCodeRequest;
import my.com.mandrill.component.dto.request.GenericStringListRequest;
import my.com.mandrill.component.dto.request.UpdateReferralCodeRequest;
import my.com.mandrill.component.dto.response.BizReferralSummaryResponse;
import my.com.mandrill.component.dto.response.PointEarningResponse;
import my.com.mandrill.component.dto.response.RefereeEarningResponse;
import my.com.mandrill.component.dto.response.ReferralCodeResponse;
import my.com.mandrill.component.service.ReferralIntegrationService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import my.com.mandrill.utilities.general.dto.response.UserRefereeUpdateRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@RestController
@RequiredArgsConstructor
@RequestMapping("moneyxbiz-integration/referral")
public class MoneyXBizIntegrationReferralController {

	private final ReferralIntegrationService referralIntegrationService;

	@PublicAuth
	@SecurityRequirements
	@GetMapping("summary")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public BizReferralSummaryResponse getReferralSummary(@RequestParam String institutionId,
			@RequestParam SourceSystemEnum source, @RequestParam(required = false) Boolean shouldCheckReferralCode) {
		return referralIntegrationService.getBizSummary(institutionId, source, shouldCheckReferralCode);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("top-referees")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public Page<RefereeEarningResponse> getTopReferees(Pageable pageable, @RequestParam String institutionId,
			@RequestParam SourceSystemEnum source) {
		return referralIntegrationService.getBizTopReferees(institutionId, source, pageable)
				.map(MapStructConverter.MAPPER::toRefereeEarningResponse);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("earning-histories")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public Page<PointEarningResponse> getEarningHistory(
			@PageableDefault(sort = "id", direction = Sort.Direction.DESC) Pageable pageable,
			@RequestParam String institutionId, @RequestParam SourceSystemEnum source) {
		return referralIntegrationService.getBizEarningHistory(institutionId, source, pageable)
				.map(MapStructConverter.MAPPER::toPointEarningResponse);
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("referral-code/{referralCode}")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public ResponseEntity<ReferralCodeResponse> referralCode(@PathVariable String referralCode,
			@RequestParam SourceSystemEnum source) {
		return ResponseEntity.ok(referralIntegrationService.getReferralCode(referralCode, source));
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("referral-code")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public ResponseEntity<Map<String, ReferralCodeResponse>> getReferralCodeForUserIn(
			@RequestParam Set<String> userIds) {
		return ResponseEntity.ok(referralIntegrationService.getReferralCodeForUserIn(userIds));
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("referral-code-by-institution")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public ResponseEntity<Map<String, ReferralCodeResponse>> getReferralCodeForInstitution(
			@RequestParam String institutionId, @RequestParam SourceSystemEnum source) {
		return ResponseEntity.ok(referralIntegrationService.getReferralCodeForCompanyId(institutionId, source));
	}

	@PublicAuth
	@SecurityRequirements
	@GetMapping("referral-code-by-institution-set")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_READ_API)")
	public ResponseEntity<Map<String, ReferralCodeResponse>> getReferralCodeForInstitutionSet(
			@RequestParam Set<String> companyIds, @RequestParam SourceSystemEnum source) {
		return ResponseEntity.ok(referralIntegrationService.getReferralCodeForCompanyIdSet(companyIds, source));
	}

	@PublicAuth
	@SecurityRequirements
	@PostMapping("referral-code-by-institution-set")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_CREATE_API)")
	public ResponseEntity<Map<String, ReferralCodeResponse>> getReferralCodeForInstitutionSetPost(
			@RequestBody @Valid GenericStringListRequest companyIds, @RequestParam SourceSystemEnum source) {
		return ResponseEntity.ok(
				referralIntegrationService.getReferralCodeForCompanyIdSet(new HashSet<>(companyIds.getIds()), source));
	}

	@PublicAuth
	@SecurityRequirements
	@PostMapping("referral-code")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_CREATE_API)")
	public ResponseEntity<ReferralCodeResponse> createReferralCode(
			@Valid @RequestBody CreateReferralCodeRequest createReferralCodeRequest) {
		return ResponseEntity.ok(referralIntegrationService.createReferralCode(createReferralCodeRequest));
	}

	@PublicAuth
	@SecurityRequirements
	@PutMapping("referral-code/{companyId}")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_UPDATE_API)")
	public ResponseEntity<ReferralCodeResponse> updateReferralCodeById(@PathVariable String companyId,
			@Valid @RequestBody UpdateReferralCodeRequest request) {
		return ResponseEntity.ok(referralIntegrationService.updateReferralCodeByCompanyId(companyId, request));
	}

	@PublicAuth
	@SecurityRequirements
	@PostMapping("update-referee")
	@PreAuthorize("hasAuthority(@authorityPermission.MX_BIZ_CREATE_API)")
	public void updateReferee(@Valid @RequestBody UserRefereeUpdateRequest userRefereeUpdateRequest) {
		referralIntegrationService.updateRefereeFriend(userRefereeUpdateRequest);
	}

}
