package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Bank;
import my.com.mandrill.component.domain.BankDetail;
import my.com.mandrill.component.dto.model.BankDTO;
import my.com.mandrill.component.dto.request.BankRequest;
import my.com.mandrill.component.dto.response.BankResponse;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.*;
import my.com.mandrill.utilities.general.constant.AccountType;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Tag(name = "01-bank")
@Slf4j
@RestController
@RequestMapping("/bank")
@RequiredArgsConstructor
public class BankController {

	private final AccountFeignClient accountFeignClient;

	private final BankService bankService;

	private final ObjectMapper objectMapper;

	private final BankDetailService bankDetailService;

	private final BankDetailIntegrationService bankDetailIntegrationService;

	private final ValidationService validationService;

	private final BankIntegrationService bankIntegrationService;

	private final BankLoanCalculationService bankLoanCalculationService;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<BankResponse> getBank(@RequestParam(required = false) AccountType accountType,
			@RequestParam(required = false) String bankListId, Sort sort) {

		BigDecimal totalIncomeAmount = BigDecimal.ZERO;
		BigDecimal totalExpenseAmount = BigDecimal.ZERO;

		List<IncomeDTO> incomeDTOS = accountFeignClient.findByUserAndIncomeType("", Sort.unsorted().ascending());
		for (IncomeDTO incomeDTO : incomeDTOS) {
			totalIncomeAmount = totalIncomeAmount.add(incomeDTO.getMonthlyIncomeAmount());
		}

		List<ExpenseDTO> expenseDTOS = accountFeignClient.findByExpenseType("", Sort.unsorted().ascending());
		for (ExpenseDTO expenseDTO : expenseDTOS) {
			totalExpenseAmount = totalExpenseAmount.add(expenseDTO.getAmount());
		}

		BankResponse bankResponse = BankResponse.builder().totalIncomeAmount(totalIncomeAmount)
				.totalExpenseAmount(totalExpenseAmount).banks(bankIntegrationService
						.findUserBankDetails(SecurityUtil.currentUserId(), accountType, bankListId, sort))
				.build();
		return ResponseEntity.ok(bankResponse);
	}

	@Hidden
	@GetMapping("/integration")
	public ResponseEntity<List<BankDTO>> getBanks(@RequestParam(required = false) AccountType accountType, Sort sort) {

		List<BankDTO> banks = bankIntegrationService.findUserBankDetails(SecurityUtil.currentUserId(), accountType,
				null, sort);
		return ResponseEntity.ok(banks);
	}

	/**
	 * @deprecated since PRJA-1324
	 */
	@Deprecated(since = "PRJA-1324")
	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<BankDTO> getBankById(@PathVariable String id) {

		Bank bank = bankIntegrationService.findByIdAndUserId(id, SecurityUtil.currentUserId());

		return ResponseEntity.ok(
				bankDetailIntegrationService.processReminderResponse(objectMapper.convertValue(bank, BankDTO.class)));
	}

	/**
	 * @deprecated since PRJA-1324
	 */
	@Deprecated(since = "PRJA-1324")
	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<BankDTO> create(@Valid @RequestBody BankRequest request) {

		validationService.validateUserJourney(request.getUserJourneyId());
		Bank bank = bankIntegrationService.process(request, SecurityUtil.currentUserId(), null);

		bank.getBankDetails().forEach(bankDetail -> {
			if (Boolean.TRUE.equals(bankDetail.getIsReminder())) {
				bankIntegrationService.createOrUpdateReminder(bankDetail);
			}
			bankIntegrationService.sendDashboardActivity(bankDetail.getAccountType(), bank.getCreatedDate());
		});

		if (Objects.nonNull(request.getUserJourneyId())) {
			bankIntegrationService.completeUserJourney(bank.getId(), request.getUserJourneyId());
		}

		return ResponseEntity.ok(objectMapper.convertValue(bank, BankDTO.class));
	}

	/**
	 * @deprecated since PRJA-1324
	 */
	@Deprecated(since = "PRJA-1324")
	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<BankDTO> update(@PathVariable String id, @Valid @RequestBody BankRequest request) {

		Bank bank = bankIntegrationService.process(request, SecurityUtil.currentUserId(), id);

		bank.getBankDetails().forEach(bankDetail -> {
			if (Boolean.TRUE.equals(bankDetail.getIsReminder())) {
				bankIntegrationService.createOrUpdateReminder(bankDetail);
			}
		});

		return ResponseEntity.ok(objectMapper.convertValue(bank, BankDTO.class));
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@DeleteMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void delete(@PathVariable String id) {
		bankIntegrationService.delete(id, SecurityUtil.currentUserId());

		bankDetailIntegrationService.publishNetWorthTransactionEvent(SecurityUtil.currentUserId(),
				NetWorthMainType.ASSETS, NetWorthType.CASH_SAVINGS, NetWorthSource.BANK);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@DeleteMapping("/credit-card/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void deleteCreditCard(@PathVariable String id) {
		bankIntegrationService.deleteCreditCard(id, SecurityUtil.currentUserId());
	}

	@Hidden
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("vault/link/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void linkVault(@PathVariable String id, @RequestBody VaultLinkDTO request) {
		BankDetail bankDetail = bankDetailService.findById(id);
		bankIntegrationService.linkVault(bankDetail, request);
	}

	@Hidden
	@GetMapping("vault/linked/{attachmentGroupId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<BankDetailVaultLinkDTO> findLinkedVault(@PathVariable String attachmentGroupId) {
		return ResponseEntity.ok(MapStructConverter.MAPPER.toBankDetailVaultLinkDTO(
				bankDetailService.findByAttachmentGroupId(SecurityUtil.currentUserId(), attachmentGroupId)));

	}

	@Hidden
	@GetMapping("vault/unlinked")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<BankDetailVaultLinkDTO>> getBankDetailWithAttachmentGroupIdNull() {
		List<BankDetail> bankDetails = bankDetailService.findByAttachmentGroupIdNull(SecurityUtil.currentUserId());
		return ResponseEntity.ok()
				.body(bankDetails.stream().map(MapStructConverter.MAPPER::toBankDetailVaultLinkDTO).toList());
	}

	@GetMapping("count")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Long> count(@RequestParam EntityName entityName) {
		return ResponseEntity.ok(bankService.count(entityName, SecurityUtil.currentUserId()));
	}

	@Hidden
	@GetMapping("vault/is-linked/{attachmentGroupId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Boolean> existsByUserIdAndAttachmentGroupId(@PathVariable String attachmentGroupId) {
		return ResponseEntity.ok(
				bankDetailService.existsByUserIdAndAttachmentGroupId(SecurityUtil.currentUserId(), attachmentGroupId));
	}

	@Hidden
	@ResponseStatus(HttpStatus.OK)
	@GetMapping("/integration/detailed-net-worth")
	public List<DetailedNetWorthDTO> calculateDetailedNetWorth() {
		return bankLoanCalculationService.calculateDetailedNetWorthByUserId(SecurityUtil.currentUserId());
	}

}
