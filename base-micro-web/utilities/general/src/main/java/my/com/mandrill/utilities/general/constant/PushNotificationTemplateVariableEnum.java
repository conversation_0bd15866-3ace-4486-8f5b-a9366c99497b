package my.com.mandrill.utilities.general.constant;

import lombok.Getter;

@Getter
public enum PushNotificationTemplateVariableEnum {

	PAYMENT_ACCOUNT_APPROVAL_STATUS("paymentStatus"), REJECT_REASON("rejectReason"), EVENT_TIME_LIST("eventTimeList"),
	TENANT_NAME("tenantName"), E_ACCESS_NAME("eAccessName"), RSM_HEADER_COUNT("rsmHeaderCount"),
	ENDED_DATE("endedDate"), DAYS_LEFT("daysLeft");

	private final String value;

	PushNotificationTemplateVariableEnum(String value) {
		this.value = value;
	}

}
