package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.GlobalSystemConfiguration;
import my.com.mandrill.component.repository.jpa.GlobalSystemConfigurationRepository;
import my.com.mandrill.component.service.GlobalSystemConfigurationService;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.service.GlobalValidationService;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class GlobalSystemConfigurationServiceImpl implements GlobalSystemConfigurationService {

	private final GlobalSystemConfigurationRepository globalSystemConfigurationRepository;

	private final GlobalValidationService globalValidationService;

	private final ValidationServiceImpl validationServiceImpl;

	private final RedisService redisService;

	private final JSONUtil jsonUtil;

	@Override
	public Page<GlobalSystemConfiguration> getAllGlobalSystemConfigPagination(Pageable page, String code) {
		return globalSystemConfigurationRepository.findAllByCode(page,
				globalValidationService.validateNullToLowerCase(code));
	}

	@Override
	public GlobalSystemConfiguration getGlobalSystemConfigurationById(String globalSystemConfigurationId) {
		return globalSystemConfigurationRepository.findById(globalSystemConfigurationId)
				.orElseThrow(ExceptionPredicate.globalSysConfigByCodeNotFound(globalSystemConfigurationId));
	}

	@Override
	public GlobalSystemConfiguration update(GlobalSystemConfiguration globalSystemConfiguration) {
		GlobalSystemConfiguration existingGlobalSystemConfiguration = getGlobalSystemConfigurationById(
				globalSystemConfiguration.getId());

		if (!existingGlobalSystemConfiguration.getCode().equalsIgnoreCase(globalSystemConfiguration.getCode())) {
			this.validationServiceImpl.validateGlobalSysConfigCode(globalSystemConfiguration.getCode());
		}

		existingGlobalSystemConfiguration.setActive(globalSystemConfiguration.getActive());
		existingGlobalSystemConfiguration.setCode(globalSystemConfiguration.getCode());
		existingGlobalSystemConfiguration.setDescription(globalSystemConfiguration.getDescription());
		existingGlobalSystemConfiguration.setValue(globalSystemConfiguration.getValue());
		redisService.deleteFromHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION, globalSystemConfiguration.getCode());
		return existingGlobalSystemConfiguration;
	}

	@Override
	public GlobalSystemConfiguration getGlobalSystemConfigurationByCode(String code) {
		return globalSystemConfigurationRepository.findByCodeAndActiveTrue(code)
				.orElseThrow(ExceptionPredicate.globalSysConfigByCodeNotFound(code));
	}

	@Override
	public GlobalSystemConfiguration getGlobalSystemConfigurationByCodeReturnDefault(String code) {

		Optional<GlobalSystemConfiguration> cachedGlobalSystemConfiguration = redisService
				.getFromHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION, code, GlobalSystemConfiguration.class);

		if (cachedGlobalSystemConfiguration.isPresent()) {
			log.info("global system configuration serve by cache");
			return cachedGlobalSystemConfiguration.get();
		}

		Optional<GlobalSystemConfiguration> globalSystemConfiguration = globalSystemConfigurationRepository
				.findByCodeAndActiveTrue(code);

		if (globalSystemConfiguration.isPresent()) {
			redisService.putToHash(CacheKey.GLOBAL_SYSTEM_CONFIGURATION, code, globalSystemConfiguration.get());
			return globalSystemConfiguration.get();
		}

		GlobalSystemConfigurationEnum globalSystemConfigurationEnum = GlobalSystemConfigurationEnum.filterByCode(code)
				.orElseThrow(ExceptionPredicate.globalSysConfigByCodeNotFound(code));
		return GlobalSystemConfiguration.builder().code(globalSystemConfigurationEnum.getCode())
				.value(globalSystemConfigurationEnum.getValue())
				.description(globalSystemConfigurationEnum.getDescription()).active(true).build();
	}

	@Override
	public List<GlobalSystemConfiguration> getGlobalSystemConfigurations() {
		return globalSystemConfigurationRepository.findAll();
	}

	@Override
	@Transactional
	public GlobalSystemConfiguration save(GlobalSystemConfiguration globalSystemConfiguration) {

		return globalSystemConfigurationRepository.save(globalSystemConfiguration);
	}

	@Override
	public void processVersionControl(String apiMinVersionCode, String feMinVersionCode) {

		if (Integer
				.parseInt(getGlobalSystemConfigurationByCode(apiMinVersionCode).getValue().replace(".", "")) >= Integer
						.parseInt(getGlobalSystemConfigurationByCode(feMinVersionCode).getValue().replace(".", ""))) {
			throw new BusinessException(ErrorCodeGlobalEnum.INVALID_VERSION);
		}
	}

}
