package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component("pinPasscodeProvider")
@RequiredArgsConstructor
public class PinProviderService implements PasscodeProviderService {

	private final UserIntegrationService userIntegrationService;

	private final PasswordEncoder passwordEncoder;

	private final KeyRequestService keyRequestService;

	private final AccountService accountService;

	private final UserService userService;

	private final AppUserService appUserService;

	@Override
	public boolean match(AppUser appUser, String reqPassword) {
		userIntegrationService.failLoginCheck(appUser, true, PasscodeType.PIN);

		if (Objects.isNull(appUser.getPin()) || !passwordEncoder.matches(reqPassword, appUser.getPin())) {
			userIntegrationService.failLoginCheck(appUser, false, PasscodeType.PIN);
		}

		log.info("Login failed attempt: {}, device lock log: {}", appUser.getLoginFailAttempt(),
				appUser.getLoginFailDeviceLockLog());
		if (appUser.getLoginFailAttempt() > 0 || StringUtils.isNotBlank(appUser.getLoginFailDeviceLockLog())) {
			appUserService.resetFailedLogin(appUser);
		}
		return true;
	}

	@Override
	public UserKeyRequest reset(ResetPasscodeAware req, UsernameType usernameType) {
		String key = req.getKey() + "_" + req.getValue();
		UserKeyRequest keyInput = new UserKeyRequest();
		keyInput.setKeyValue(key);
		keyInput.setUsername(req.getUsername());
		accountService.validateUsernameType(keyInput, usernameType);

		Optional<UserKeyRequest> optionalKeyRequest = keyRequestService
				.findValidByUsernameAndType(keyInput.getUsernameRaw(), RequestKeyType.PIN_RESET);
		if (optionalKeyRequest.isEmpty()) {
			throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
		}
		UserKeyRequest keyRequest = optionalKeyRequest.get();

		accountService.validateOtpVerification(keyRequest, keyInput.getKeyValue());

		User user = userService.findByUsernameInputIgnoreCaseAndLoginType(keyRequest.getUsername(), LoginTypeEnum.USER,
				null);
		user.setPin(passwordEncoder.encode(req.getPassword()));
		user.setLoginFailAttempt(0);
		userIntegrationService.save(user);

		keyRequest.setCompletionDate(Instant.now());
		keyRequest.setStatus(RequestKeyStatus.COMPLETED);
		keyRequestService.save(keyRequest);

		return keyRequest;
	}

}
