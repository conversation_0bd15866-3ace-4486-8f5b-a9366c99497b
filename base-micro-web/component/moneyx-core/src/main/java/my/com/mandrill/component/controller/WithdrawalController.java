package my.com.mandrill.component.controller;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.dto.request.CreatePointWithdrawalRequest;
import my.com.mandrill.component.service.PointWithdrawalIntegrationService;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
public class WithdrawalController {

	private final PointWithdrawalIntegrationService pointWithdrawalIntegrationService;

	@GetMapping("/account/v1/withdrawal/has-pending")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public Boolean hasPendingWithdrawal() {
		return pointWithdrawalIntegrationService.isWithdrawalProcessExist(SecurityUtil.currentUserId());
	}

	@PostMapping(value = "payment/point/v1/withdrawal", consumes = MediaType.APPLICATION_JSON_VALUE)
	@ResponseStatus(HttpStatus.NO_CONTENT)
	public void pointWithdrawal(@Valid @RequestBody CreatePointWithdrawalRequest request) {

		pointWithdrawalIntegrationService.processPointWithdrawal(request, SecurityUtil.currentUserId());

	}

}
