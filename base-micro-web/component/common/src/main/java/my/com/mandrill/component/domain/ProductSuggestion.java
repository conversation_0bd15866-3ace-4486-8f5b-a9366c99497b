package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Table(name = "product_suggestion")
public class ProductSuggestion extends AuditSection {

	@NotBlank
	@Size(max = 100)
	@Column(nullable = false, length = 100)
	private String name;

	@Size(max = 500)
	private String description;

	@Column(name = "interest_rate", precision = 5, scale = 2)
	private BigDecimal interestRate;

	@Column(name = "active", columnDefinition = "BOOLEAN DEFAULT TRUE", nullable = false)
	private boolean active = true;

	@Column(name = "logo_url")
	private String logoUrl;

	@Column(name = "attachment_group_id", length = 36)
	private String attachmentGroupId;

	@ManyToOne
	@JoinColumn(name = "product_suggestion_category_id")
	private ProductSuggestionCategory productSuggestionCategory;

	@Column(name = "screen_route", length = 100)
	private String screenRoute;

	@Column(name = "type", length = 100)
	private String type;

	@Column(name = "exclusion_tag", length = 100)
	private String exclusionTag;

	@Column(name = "sequence")
	private Integer sequence = 0;

}
