package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.RsmClosingBalance;
import my.com.mandrill.component.dto.projection.ClosingBalanceProjection;
import my.com.mandrill.component.dto.response.BizPointSummaryFilterResponse;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

public interface ClosingBalanceService {

	BigDecimal findLatestClosingBalanceBySource(SourceSystemEnum source);

	Page<ClosingBalanceProjection> aggregateByMonthAndYearAndSource(Integer month, Integer year,
			SourceSystemEnum source, Pageable pageable);

	List<ClosingBalanceProjection> aggregateByMonthAndYearReportExport(Integer month, Integer year,
			SourceSystemEnum source);

	Optional<RsmClosingBalance> findCurrentDayClosingBalanceBySource(SourceSystemEnum source);

	Optional<RsmClosingBalance> findPreviousDayClosingBalanceBySource(SourceSystemEnum source);

	void updateBalanceRealtime(String id, BigDecimal pointAwarded, BigDecimal pointSpent, BigDecimal closingBalance);

	BizPointSummaryFilterResponse getAvailableTimeFilterForBiz();

	void updateTotalExpired(BigDecimal totalExpired, Instant closingDate, SourceSystemEnum source);

}
