package my.com.mandrill.utilities.general.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiProductResponse implements Serializable {

	@JsonProperty("card_type")
	private String cardType;

	private String entity;

	private String name;

	@JsonProperty("product_id")
	private String productId;

	private String type;

	private String logo;

	@JsonProperty("issuer_code")
	private String issuerCode;

	@JsonProperty("issuer_type")
	private String issuerType;

	private String description;

	@JsonProperty("product_link")
	private String productLink;

	@JsonProperty("apply_link")
	private String applyLink;

	@JsonProperty("highlights")
	private AiProductHighlightResponse highlights;

	private String screenRoute;

	private CommissionInfoResponse commission;

}
