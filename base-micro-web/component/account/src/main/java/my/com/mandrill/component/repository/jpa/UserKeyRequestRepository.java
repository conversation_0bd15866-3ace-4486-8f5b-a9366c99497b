package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserKeyRequestRepository extends JpaRepository<UserKeyRequest, String> {

	Optional<UserKeyRequest> findByUsernameAndKeyValueAndType(String username, String keyValue, RequestKeyType type);

	List<UserKeyRequest> findByStatusAndExpiresAtBefore(RequestKeyStatus status, Instant expiresAt);

	Optional<UserKeyRequest> findByKeyValueAndType(String keyValue, RequestKeyType type);

	Optional<UserKeyRequest> findByKeyValueAndTypeAndStatusAndExpiresAtAfter(String keyValue, RequestKeyType type,
			RequestKeyStatus status, Instant expiresAtAfter);

	Optional<UserKeyRequest> findFirstByUsernameAndTypeAndStatusOrderByCreatedDateDesc(String username,
			RequestKeyType type, RequestKeyStatus status);

	Optional<UserKeyRequest> findFirstByUsernameAndTypeAndStatusAndExpiresAtAfter(String usernameRaw,
			RequestKeyType type, RequestKeyStatus status, Instant expiresAtAfter);

}
