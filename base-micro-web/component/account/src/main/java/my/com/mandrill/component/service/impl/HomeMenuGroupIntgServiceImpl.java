package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.HomeMenuGroup;
import my.com.mandrill.component.dto.model.HomeMenuGroupDTO;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.HomeMenuGroupIntgService;
import my.com.mandrill.component.service.HomeMenuGroupService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class HomeMenuGroupIntgServiceImpl implements HomeMenuGroupIntgService {

	private final HomeMenuGroupService homeMenuGroupService;

	@Override
	public HomeMenuGroupDTO save(HomeMenuGroupDTO homeMenuGroupDTO) {
		this.existsByCode(homeMenuGroupDTO.getCode());
		HomeMenuGroup homeMenuGroup = toEntity(homeMenuGroupDTO);
		if (homeMenuGroupDTO.getParentGroup() != null) {
			HomeMenuGroup parent = homeMenuGroupService.findById(homeMenuGroupDTO.getParentGroup().getId());
			homeMenuGroup.setParent(parent);
		}
		return toDTO(homeMenuGroupService.save(homeMenuGroup));
	}

	private HomeMenuGroup toEntity(HomeMenuGroupDTO homeMenuGroupDTO) {
		HomeMenuGroup homeMenuGroup = new HomeMenuGroup();
		homeMenuGroup.setCode(homeMenuGroupDTO.getCode());
		homeMenuGroup.setNameEn(homeMenuGroupDTO.getNameEn());
		homeMenuGroup.setNameMy(homeMenuGroupDTO.getNameMy());
		homeMenuGroup.setNameCn(homeMenuGroupDTO.getNameCn());
		return homeMenuGroup;
	}

	@Override
	public HomeMenuGroupDTO toDTO(HomeMenuGroup homeMenuGroup) {
		HomeMenuGroupDTO homeMenuGroupDTO = new HomeMenuGroupDTO();
		homeMenuGroupDTO.setId(homeMenuGroup.getId());
		homeMenuGroupDTO.setCode(homeMenuGroup.getCode());
		homeMenuGroupDTO.setNameEn(homeMenuGroup.getNameEn());
		homeMenuGroupDTO.setNameMy(homeMenuGroup.getNameMy());
		homeMenuGroupDTO.setNameCn(homeMenuGroup.getNameCn());
		if (homeMenuGroup.getParent() != null) {
			HomeMenuGroup parent = homeMenuGroupService.findById(homeMenuGroup.getParent().getId());
			homeMenuGroupDTO.setParent(toDTO(parent));
		}
		return homeMenuGroupDTO;
	}

	@Override
	public HomeMenuGroupDTO findById(String id) {
		return toDTO(homeMenuGroupService.findById(id));
	}

	@Override
	public void deleteById(String id) {
		HomeMenuGroup homeMenuGroup = homeMenuGroupService.findById(id);
		homeMenuGroupService.delete(homeMenuGroup);
	}

	@Override
	public HomeMenuGroupDTO update(String id, HomeMenuGroupDTO homeMenuGroupDTO) {
		HomeMenuGroup existsData = homeMenuGroupService.findById(id);
		if (!existsData.getCode().equalsIgnoreCase(homeMenuGroupDTO.getCode())) {
			existsByCode(homeMenuGroupDTO.getCode());
		}
		HomeMenuGroup homeMenuGroup = toEntity(homeMenuGroupDTO);
		homeMenuGroup.setId(id);

		homeMenuGroup.setHomeMenuMaps(existsData.getHomeMenuMaps());

		if (homeMenuGroupDTO.getParentGroup() != null) {
			HomeMenuGroup parent = homeMenuGroupService.findById(homeMenuGroupDTO.getParentGroup().getId());
			homeMenuGroup.setParent(parent);
		}
		return toDTO(homeMenuGroupService.save(homeMenuGroup));
	}

	@Override
	public Page<HomeMenuGroup> findAll(String code, Pageable pageable) {
		return homeMenuGroupService.findAll(code, pageable);
	}

	private void existsByCode(String code) {
		if (homeMenuGroupService.existsByCode(code)) {
			throw new BusinessException(ErrorCodeEnum.HOME_MENU_GROUP_CODE_IS_EXIST);
		}
	}

}
