package my.com.mandrill.component.controller.admin;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.UserInterestRecord;
import my.com.mandrill.component.dto.model.UserInterestRecordDetailDTO;
import my.com.mandrill.component.dto.model.UserInterestRecordViewDTO;
import my.com.mandrill.component.dto.request.ReportServiceRequest;
import my.com.mandrill.component.dto.request.UpdateUserInterestRecordRequest;
import my.com.mandrill.component.service.UserInterestRecordIntegrationService;
import my.com.mandrill.component.service.UserInterestRecordService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.CurrentUserInstitutionsDTO;
import my.com.mandrill.utilities.feign.dto.request.CreateManualLeadRequest;
import my.com.mandrill.utilities.feign.dto.response.UserInterestVaultResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Tag(name = "admin-user-interest")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/user-interest")
public class AdminUserInterestController {

	private final AccountFeignClient accountFeignClient;

	private final ValidationService validationService;

	private final UserInterestRecordService userInterestRecordService;

	private final UserInterestRecordIntegrationService userInterestRecordIntegrationService;

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_INTEREST_LIST_READ)")
	public ResponseEntity<Page<UserInterestRecordViewDTO>> index(Pageable pageable,
			@RequestParam String currentInstitutionId, @RequestParam(required = false) LocalDate startDate,
			@RequestParam(required = false) LocalDate endDate, @RequestParam(required = false) String fullName,
			@RequestParam(required = false) Set<String> providerId, @RequestParam(required = false) String productType,
			@RequestParam(required = false) Set<String> productIds) {
		List<CurrentUserInstitutionsDTO> userInstitutions = accountFeignClient.getCurrentUserInstitutions();
		validationService.validateInstitution(currentInstitutionId, userInstitutions);
		Set<String> issuerCodes = accountFeignClient.getInstitutionAiMapping(currentInstitutionId);

		Page<UserInterestRecord> userInterestRecords = userInterestRecordService
				.findAll(ReportServiceRequest.builder().pageable(pageable).issuerCodes(issuerCodes).fullName(fullName)
						.productIds(productIds).providerIds(providerId).productType(productType).startDate(startDate)
						.isRedirect(false).endDate(endDate).build());
		return ResponseEntity.ok(userInterestRecords.map(MapStructConverter.MAPPER::toInterestRecordView));
	}

	@PutMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_INTEREST_LIST_UPDATE)")
	public void update(@Valid @RequestBody UpdateUserInterestRecordRequest request,
			@RequestParam String currentInstitutionId) {
		List<CurrentUserInstitutionsDTO> userInstitutions = accountFeignClient.getCurrentUserInstitutions();
		validationService.validateInstitution(currentInstitutionId, userInstitutions);
		UserInterestRecord userInterestRecord = userInterestRecordIntegrationService.findById(currentInstitutionId,
				request.getId());

		userInterestRecord.setStatus(request.getStatus());
		userInterestRecordService.save(userInterestRecord);
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_INTEREST_LIST_READ)")
	public ResponseEntity<UserInterestRecordDetailDTO> show(@PathVariable String id,
			@RequestParam String currentInstitutionId) {
		List<CurrentUserInstitutionsDTO> userInstitutions = accountFeignClient.getCurrentUserInstitutions();
		validationService.validateInstitution(currentInstitutionId, userInstitutions);

		UserInterestRecordDetailDTO detail = userInterestRecordIntegrationService.findDetail(currentInstitutionId, id);

		return ResponseEntity.ok(detail);
	}

	@Hidden
	@GetMapping("/integration/{recordId}/vault-ids")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_INTEREST_LIST_READ)")
	public UserInterestVaultResponse findVaultIdsByRecordId(@PathVariable String recordId) {
		return userInterestRecordIntegrationService.findVaultAndIssuerByRecordId(recordId);
	}

	@ServiceToServiceAccess
	@PostMapping("/private/integration/manual")
	public void createManualLead(@Valid @RequestBody CreateManualLeadRequest createManualLeadRequest) {
		userInterestRecordIntegrationService.createManualLead(createManualLeadRequest);
	}

	@ServiceToServiceAccess
	@PostMapping("/private/integration/manual/redirect")
	public void createManualLeadRedirect(@Valid @RequestBody CreateManualLeadRequest createManualLeadRequest) {
		userInterestRecordIntegrationService.createManualLeadRedirect(createManualLeadRequest);
	}

}
