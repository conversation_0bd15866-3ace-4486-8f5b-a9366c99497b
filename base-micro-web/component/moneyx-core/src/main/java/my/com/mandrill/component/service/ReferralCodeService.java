package my.com.mandrill.component.service;

import jakarta.validation.constraints.NotNull;
import my.com.mandrill.component.constant.RSMApplicationCancelReason;
import my.com.mandrill.component.constant.RSMReferralCodeStatus;
import my.com.mandrill.component.domain.ReferralCode;
import my.com.mandrill.component.dto.request.CreateReferralCodeRequest;
import my.com.mandrill.component.dto.request.UpdateReferralCodeRequest;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Sort;
import org.springframework.lang.NonNull;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ReferralCodeService {

	ReferralCode create(CreateReferralCodeRequest request, int codeSize);

	ReferralCode findOrCreate(String userId, SourceSystemEnum source, int codeSize);

	ReferralCode findByUserId(String userId);

	List<ReferralCode> findAllByUserIdIn(Set<String> userIds);

	List<ReferralCode> findAllByCompanyIdAndSource(String institutionId, SourceSystemEnum source);

	List<ReferralCode> findAllByCompanyIdSetAndSource(Set<String> companyIds, SourceSystemEnum source);

	Boolean existsReferralCode(String code, SourceSystemEnum source);

	Boolean existsReferralCode(String code);

	ReferralCode findByReferralCodeAndSource(String referralCode, SourceSystemEnum source);

	ReferralCode findByReferralCodeAndSource(String referralCode);

	ReferralCode save(ReferralCode referralCode);

	ReferralCode updateReferralStatusForInstitutionId(@NonNull String id, @NotNull RSMReferralCodeStatus status,
			RSMApplicationCancelReason cancellationReason);

	ReferralCode updateReferralCodeByCompanyId(@NonNull String companyId, @NonNull UpdateReferralCodeRequest request);

	Set<String> findAllCompanyIdsWhereStatusIn(@NonNull Sort sort, @NonNull SourceSystemEnum source,
			RSMReferralCodeStatus status, Instant dateFrom, Instant dateTo);

	void updateExpiredByUserIds(List<String> userIds);

	Optional<ReferralCode> getReferralCodeByUserId(String userId);

}
