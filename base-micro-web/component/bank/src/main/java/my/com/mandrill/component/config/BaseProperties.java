package my.com.mandrill.component.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "base")
public class BaseProperties {

	private final RequestUri requestUri = new RequestUri();

	private final CDN cdn = new CDN();

	private final Cache cache = new Cache();

	private final Kafka kafka = new Kafka();

	@Getter
	@Setter
	public static class RequestUri {

		private String openApiServer;

	}

	@Getter
	@Setter
	public static class CDN {

		private String host;

	}

	@Getter
	@Setter
	public static class Cache {

		private final Ehcache ehcache = new Ehcache();

		@Getter
		@Setter
		public static class Ehcache {

			private Long maxEntries;

			private Long timeToLiveSeconds;

		}

	}

	@Data
	public static class Kafka {

		private final Topic topic = new Topic();

	}

	@Data
	public static class Topic {

		private TopicConfig exceptionStacktrace = new TopicConfig(KafkaTopic.HIGH_PARTITIONS.getPartitions(),
				KafkaTopic.HIGH_PARTITIONS.getPartitions());

	}

	@Data
	@AllArgsConstructor
	public static class TopicConfig {

		private Integer partition;

		private Integer concurrency;

	}

}
