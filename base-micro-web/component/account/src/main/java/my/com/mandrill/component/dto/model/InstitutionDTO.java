package my.com.mandrill.component.dto.model;

import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serializable;

@Data
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class InstitutionDTO implements Serializable {

	private String id;

	@Size(max = 20)
	private String shortName;

	@Size(max = 100)
	private String name;

	private InstitutionDTO parentInstitution;

	@Size(max = 50)
	private String businessRegNo;

	@Size(max = 50)
	private String industry;

	@Size(max = 500)
	private String address;

	@Size(max = 20)
	private String phone;

	@Size(max = 50)
	private String fax;

	@Size(max = 100)
	private String email;

	@Size(max = 100)
	private String website;

	private Boolean active = false;

	private Integer tier;

	private String attachmentGroupId;

	private String url;

	private String secretKey;

}
