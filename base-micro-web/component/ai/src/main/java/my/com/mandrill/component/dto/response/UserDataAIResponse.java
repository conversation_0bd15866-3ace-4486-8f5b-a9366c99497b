package my.com.mandrill.component.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.feign.dto.BankResponse;
import my.com.mandrill.utilities.feign.dto.InsuranceDTO;
import my.com.mandrill.utilities.feign.dto.LoanDTO;
import my.com.mandrill.utilities.feign.dto.PropertyDTO;
import my.com.mandrill.utilities.general.dto.model.VehicleDTO;

import java.math.BigDecimal;
import java.util.List;

@Data
@Getter
@Setter
public class UserDataAIResponse {

	private String userId;

	@JsonProperty("user_income")
	private BigDecimal userIncome;

	@JsonProperty("user_applied_loan_amount")
	private BigDecimal userAppliedLoanAmount;

	@JsonProperty("user_age")
	private Integer userAge;

	private BankResponse bank;

	private List<VehicleDTO> vehicle;

	private List<PropertyDTO> property;

	private List<InsuranceDTO> insurance;

	private List<LoanDTO> loan;

	@JsonProperty("marital_status")
	private String maritalStatus;

	private String nationality;

	private String gender;

	private List<ExpenseResponse> expenses;

	@Data
	public static class ExpenseResponse {

		private String expenseTypeName;

		private BigDecimal expenseAmount;

	}

}
