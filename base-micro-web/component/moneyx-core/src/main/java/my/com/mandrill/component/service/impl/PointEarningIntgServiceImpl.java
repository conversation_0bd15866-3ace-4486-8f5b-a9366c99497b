package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.ChartOfAccountType;
import my.com.mandrill.component.constant.CoaTransactionMapping;
import my.com.mandrill.component.constant.PointTransactionStatus;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.domain.ChartOfAccount;
import my.com.mandrill.component.domain.PointEarning;
import my.com.mandrill.component.domain.PointTransaction;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class PointEarningIntgServiceImpl implements PointEarningIntgService {

	private final PointEarningService pointEarningService;

	private final PointTransactionService pointTransactionService;

	private final ClosingBalanceIntgService closingBalanceIntgService;

	private final ChartOfAccountService chartOfAccountService;

	private final RunningNumberUtil runningNumberUtil;

	private final FeatureFlagOutbound featureFlagOutbound;

	@Override
	@Transactional
	public List<PointEarning> disburse(Collection<String> ids, SourceSystemEnum source) {
		List<PointEarning> pointEarnings = pointEarningService.findByIdAndStatus(ids, PointEarningStatus.PENDING);
		Map<String, ChartOfAccount> coaMapping = chartOfAccountService.findChartOfAccountMapping();
		List<PointTransaction> transactions = new ArrayList<>();
		BigDecimal totalPointAwardedForRealtimeUpdate = BigDecimal.ZERO;
		int pointExpiryDuration = getPointExpiryDuration();
		for (PointEarning pointEarning : pointEarnings) {
			pointEarning.setStatus(PointEarningStatus.AWARDED);
			pointEarning.setPointDisbursementDate(Instant.now());
			transactions.addAll(createPointTransactions(pointEarning, coaMapping, pointExpiryDuration));

			if (isBizUserEarning(pointEarning)) {
				totalPointAwardedForRealtimeUpdate = totalPointAwardedForRealtimeUpdate
						.add(pointEarning.getPointAmount());
			}
		}
		if (totalPointAwardedForRealtimeUpdate.compareTo(BigDecimal.ZERO) > 0) {
			closingBalanceIntgService.calculateRealTimeClosingBalance(totalPointAwardedForRealtimeUpdate, null);
		}
		pointEarnings = pointEarningService.saveAll(pointEarnings);
		pointTransactionService.saveAll(transactions);
		return pointEarnings;
	}

	private int getPointExpiryDuration() {

		return featureFlagOutbound.getIntegerValue(GlobalSystemConfigurationEnum.RSM_POINT_EXPIRY_DURATION);
	}

	private boolean isBizUserEarning(PointEarning pointEarning) {
		return SourceSystemEnum.MXBIZ.equals(pointEarning.getSource()) && pointEarning.getUserId() != null;
	}

	private List<PointTransaction> createPointTransactions(PointEarning pointEarning,
			Map<String, ChartOfAccount> coaMapping, int pointExpiryDuration) {
		List<PointTransaction> pointTransactions = new ArrayList<>();
		for (CoaTransactionMapping mapping : pointEarning.getRsmDetailFocalType().getTypes()) {
			if (notApplicableToCreateTransaction(pointEarning, mapping)
					|| isNotValidNonReferralType(pointEarning, mapping)) {
				continue;
			}
			PointTransaction transaction = MapStructConverter.MAPPER.toPointTransaction(pointEarning);
			transaction.setApprovedDate(Instant.now());
			transaction.setName(getTransactionName(pointEarning, mapping));
			transaction.setStatus(PointTransactionStatus.SUCCESS);
			transaction.setType(mapping.getTransactionType());
			transaction.setChartOfAccount(coaMapping.get(mapping.getChartOfAccountType().getCode()));
			transaction.setSource(mapping.getSourceSystem());
			transaction.setRsmDetailFocalType(RSMFocalType.valueOf(mapping.getFocalType()));
			if (RSMFocalType.MONEYX.equals(transaction.getRsmDetailFocalType())) {
				transaction.setUserId(null);
				transaction.setCompanyId(null);
			}
			transaction.setExpiredDue(getPointExpiryDate(transaction.getApprovedDate(), pointExpiryDuration));
			pointTransactions.add(transaction);
		}
		return pointTransactions;
	}

	private boolean notApplicableToCreateTransaction(PointEarning pointEarning, CoaTransactionMapping mapping) {
		return !mapping.getRelationTypes().contains(pointEarning.getRsmScenario());
	}

	private boolean isNotValidNonReferralType(PointEarning pointEarning, CoaTransactionMapping mapping) {
		return RSMRelationType.NON_REFERRAL.equals(pointEarning.getRsmScenario())
				&& !pointEarning.getSource().equals(mapping.getSourceSystem());
	}

	private String getTransactionName(PointEarning pointEarning, CoaTransactionMapping coaTransactionMapping) {
		if (!pointEarning.getRsmDetailFocalType().equals(RSMFocalType.MONEYX)
				&& ChartOfAccountType.RSM_POINT_EARN.equals(coaTransactionMapping.getChartOfAccountType())) {
			return pointEarning.getPointName();
		}
		return coaTransactionMapping.getTransactionType().getName();
	}

	private Instant getPointExpiryDate(Instant awardedDate, int expiryDurationInMonths) {

		return awardedDate.atZone(TimeConstant.DEFAULT_ZONE_ID).plusMonths(expiryDurationInMonths)
				.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.of(23, 59, 59)).toInstant();

	}

}
