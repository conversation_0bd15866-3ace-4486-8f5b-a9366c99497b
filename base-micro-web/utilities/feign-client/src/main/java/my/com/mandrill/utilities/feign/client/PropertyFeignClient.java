package my.com.mandrill.utilities.feign.client;

import jakarta.validation.Valid;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.PropertyDTO;
import my.com.mandrill.utilities.feign.dto.PropertyStagingDTO;
import my.com.mandrill.utilities.feign.dto.VaultLinkDTO;
import my.com.mandrill.utilities.feign.dto.model.PropertyInterestExportDTO;
import my.com.mandrill.utilities.feign.dto.model.StandardObjectDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMViewDTO;
import my.com.mandrill.utilities.feign.dto.request.CreateManualLeadRequest;
import my.com.mandrill.utilities.feign.dto.request.DateRangeRequest;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@FeignClient("property-component")
public interface PropertyFeignClient {

	@GetMapping("v2/properties")
	List<PropertyDTO> getProperties();

	@GetMapping("properties/integration")
	List<PropertyDTO> getPropertiesForIntegration();

	@PostMapping("/property-staging")
	PropertyStagingDTO createPropertyStaging(@RequestBody PropertyStagingDTO propertyStagingDTO);

	@GetMapping("/property-staging")
	List<PropertyStagingDTO> getPropertyStaging();

	@GetMapping("v2/properties/{id}")
	PropertyDTO findById(@PathVariable String id);

	@GetMapping("/properties/check-address")
	boolean isPropertyExistByAddress(@SpringQueryMap PropertyStagingDTO propertyStagingDTO);

	@DeleteMapping("/property-staging/by-address")
	void deletePropertyStagingByAddress(@SpringQueryMap PropertyStagingDTO propertyStagingDTO);

	@DeleteMapping("property-staging/utility/{utilityId}")
	void deletePropertyStagingByUtilityId(@PathVariable String utilityId);

	@PutMapping("/properties/vault/link/{id}")
	void linkVault(@RequestBody VaultLinkDTO vaultLinkDTO, @PathVariable String id);

	@GetMapping("properties/vault/linked/{attachmentGroupId}")
	PropertyDTO findLinkedVault(@PathVariable String attachmentGroupId);

	@GetMapping("/properties/vault/unlinked")
	List<PropertyDTO> getPropertyWithAttachmentGroupIdNull();

	@GetMapping("/properties/count")
	Long count();

	@GetMapping("/properties/vault/is-linked/{attachmentGroupId}")
	Boolean existsByUserIdAndAttachmentGroupId(@PathVariable String attachmentGroupId);

	@GetMapping("/properties/integration/net-worth")
	NetWorthDTO calculateNetWorth();

	@GetMapping("/properties/rental")
	List<PropertyDTO> getPropertyByUserIdAndIsRentedAndIsBrowsingFalse(@RequestParam Boolean isRented);

	@GetMapping("/properties/{id}/exists")
	void existsById(@PathVariable String id);

	@GetMapping("/admin/v1/purchase-property/interest/export")
	List<PropertyInterestExportDTO> exportPropertyInterested(@SpringQueryMap DateRangeRequest query);

	@PutMapping("admin/v1/purchase-property/interest/private/rsm-info")
	void updatePurchasePropertyRSMInfo(@RequestBody LeadRSMUpdateRequest request);

	@GetMapping("admin/v1/purchase-property/interest/private/application-ids-except-user-ids")
	Set<String> findAllApplicationIdsExcludingUserIdsIn(@RequestParam Set<String> applicationIds,
			@RequestParam Set<String> userIdSet);

	@PostMapping("admin/v1/purchase-property/interest/private/pagination")
	Page<UserInterestRecordRSMPaginationDTO> findRsmLead(@RequestParam int size, @RequestParam int page,
			@RequestParam String sort, @RequestBody RSMLeadRequest request);

	@GetMapping("admin/v1/purchase-property/interest/private/detail/{id}")
	UserInterestRecordRSMViewDTO findRsmLeadDetail(@PathVariable String id);

	@PostMapping("admin/v1/purchase-property/interest/private/report")
	List<UserInterestRecordRSMPaginationDTO> findRsmLeadReport(@RequestBody RSMLeadRequest request);

	@GetMapping("developers/private/find")
	List<StandardObjectDTO> findDevelopers();

	@GetMapping("projects/private/find")
	List<StandardObjectDTO> findProjects(@RequestParam String developerId);

	@GetMapping("projects/private/find/by-project")
	List<StandardObjectDTO> findProjectsByIds(@RequestParam Set<String> projectId);

	@GetMapping("developers/private/find-by-ids")
	List<StandardObjectDTO> findAllDevelopersByIds(@RequestParam(required = false) Set<String> ids);

	@PostMapping("admin/v1/purchase-property/interest/private/integration/manual")
	void createManualLead(@Valid @RequestBody CreateManualLeadRequest createManualLeadRequest);

}
