package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.Token;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.request.MobileUserPaginationRequest;
import my.com.mandrill.component.dto.request.PublicUserDetailRequest;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.ExcelFileGenerator;
import my.com.mandrill.utilities.core.audit.AuditSection;
import my.com.mandrill.utilities.core.token.service.UserTokenService;
import my.com.mandrill.utilities.feign.dto.CountryDTO;
import my.com.mandrill.utilities.feign.dto.StateDTO;
import my.com.mandrill.utilities.feign.dto.model.FileS3ObjectDTO;
import my.com.mandrill.utilities.feign.dto.model.ReferralCodeDto;
import my.com.mandrill.utilities.feign.dto.request.DownloadEndRequest;
import my.com.mandrill.utilities.feign.dto.request.DownloadStartRequest;
import my.com.mandrill.utilities.feign.dto.request.EmailRequest;
import my.com.mandrill.utilities.feign.dto.response.EkycDetailResponse;
import my.com.mandrill.utilities.feign.dto.response.PaymentAccountDetailResponse;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import my.com.mandrill.utilities.general.util.HashUtil;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.UnaryOperator;

import static my.com.mandrill.utilities.general.constant.KafkaTopic.SEND_EMAIL_TOPIC;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserIntegrationServiceImpl implements UserIntegrationService {

	private static final String MALAYSIAN_PHONE_COUNTRY = "+60";

	private final AccountSystemConfigurationService accountSystemConfigurationService;

	private final UserService userService;

	private final AppUserService appUserService;

	private final PopulateService populateService;

	private final KeyRequestIntegrationService keyRequestIntegrationService;

	private final ProxyFeignClient proxyFeignClient;

	private final TokenService tokenService;

	private final UserTokenService userTokenService;

	private final ExcelFileGenerator excelFileGenerator;

	private final JSONUtil jsonUtil;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final IncomeService incomeService;

	@Override
	public void failLoginCheck(AppUser user, boolean loginStatus, PasscodeType passcodeType) {
		Long maxAttempt = accountSystemConfigurationService.getFailLoginAttempt(user);
		if (loginStatus) {
			// logic when password is correct
			if (user.getLoginFailAttempt() >= maxAttempt) {
				if (PasscodeType.PASSWORD.equals(passcodeType)) {
					throw new BusinessException(ErrorCodeEnum.LOGIN_ATTEMPT_EXCEED,
							StringUtils.defaultIfBlank(user.getFullName(), user.getRefNo()));

				}
				else if (PasscodeType.PIN.equals(passcodeType)) {
					throw new BusinessException(ErrorCodeEnum.PIN_ATTEMPT_EXCEED);
				}
			}
		}
		else {
			// logic when password is not correct
			if (user.getLoginFailAttempt() < maxAttempt) {
				user.setLoginFailAttempt(user.getLoginFailAttempt() + 1);
				appUserService.processFailedLogin(user);
			}
			if (PasscodeType.PIN.equals(passcodeType)) {
				long attemptRemaining = maxAttempt - user.getLoginFailAttempt();

				throw new BusinessException(ErrorCodeGlobalEnum.INVALID_PIN,
						Map.of("pinAttemptsRemaining", String.valueOf(attemptRemaining)), attemptRemaining);
			}
		}
	}

	@Override
	public void failLoginCheck(AppUser user, boolean loginStatus) {
		failLoginCheck(user, loginStatus, PasscodeType.PASSWORD);
	}

	@Override
	public AppUser sensitiveFieldUpdateCheck(AppUser user, String key, UnaryOperator<AppUser> saveFunc) {
		log.info("sensitive field update checking...");
		AppUser existing = appUserService.findByRefNo(user.getRefNo());
		RequestKeyType keyType = null;

		if (!StringUtils.equals(user.getEmail(), existing.getEmail())) {
			log.info("indicating update email...");
			keyType = RequestKeyType.VERIFICATION_CHANGE_EMAIL;
		}

		if (Objects.nonNull(keyType)) {
			log.info("key request checking...");
			if (StringUtils.isBlank(key)) {
				log.error("request key from user is null, go go tell them");
				throw new BusinessException(ErrorCodeEnum.INVALID_KEY);
			}

			return keyRequestIntegrationService.withValidateKey(key, keyType, () -> saveFunc.apply(user));
		}

		log.info("execute update user");
		return saveFunc.apply(user);
	}

	@Override
	public PublicUserDetailResponse getUserDetailAndKycStatus(PublicUserDetailRequest publicUserDetailRequest) {
		User user = userService.findByPhoneCountryAndPhoneNumberAndLoginTypeAndActiveTrue(
				publicUserDetailRequest.getPhoneCountry(), publicUserDetailRequest.getPhoneNumber(),
				LoginTypeEnum.USER);
		PublicUserDetailResponse publicUserDetailResponse = MapStructConverter.MAPPER.toUserDetailResponse(user);
		publicUserDetailResponse.setKycApproved(proxyFeignClient.getCommonFeignClient().getKYCStatus(user.getId()));

		ReferralCodeDto referralCodeDto = proxyFeignClient.getMoneyXCoreFeignClient()
				.getReferralCodeOfReferrerByReferredId(user.getId());
		publicUserDetailResponse
				.setReferralCode(Optional.ofNullable(referralCodeDto).map(ReferralCodeDto::getCode).orElse(null));
		BigDecimal userIncome = incomeService.findIncome(user.getId());
		publicUserDetailResponse.setIncomeAmount(userIncome);
		return publicUserDetailResponse;
	}

	@Override
	public AccountDetailResponse findDetailById(String id) {
		User user = userService.findById(id).orElseThrow(ExceptionPredicate.userNotFoundById(id));
		UserDetailResponse userDetailResponse = MapStructConverter.MAPPER.toInternalUserMobileResponse(user);
		userDetailResponse.setHasMobileReferralCode(hasMobileReferralCode(user));
		return AccountDetailResponse.builder().user(userDetailResponse).paymentAccount(getPaymentAccount(id))
				.ekyc(getEkycByUserId(user)).build();
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void deleteAdminUser(String id, String constitutionId) {
		User user = userService.findByIdAndInstitutionsIdAndLoginType(id, constitutionId, LoginTypeEnum.ADMIN);
		List<Token> tokens = tokenService.deleteToken(user);
		if (CollectionUtils.isNotEmpty(tokens)) {
			userTokenService.deleteByTokenIds(tokens.stream().map(AuditSection::getId).toList());
		}
		userService.delete(user);
	}

	private PaymentAccountDetailResponse getPaymentAccount(String id) {
		try {
			return proxyFeignClient.getBankFeignClient().getPaymentAccount(id);
		}
		catch (Exception ex) {
			log.warn("feature=get-payment-account, error:{}", ex.getMessage());
			return null;
		}
	}

	private EkycDetailResponse getEkycByUserId(User user) {
		try {
			EkycDetailResponse ekyc = proxyFeignClient.getCommonFeignClient().getEkycDetailByUser(user.getId());
			ekyc.setNric(user.getNric());
			return ekyc;
		}
		catch (Exception ex) {
			log.warn("feature=get-ekyc, error:{}", ex.getMessage());
			return null;
		}
	}

	private boolean hasMobileReferralCode(User user) {
		return user.getPhoneVerified() && MALAYSIAN_PHONE_COUNTRY.equals(user.getPhoneCountry())
				&& StringUtils.isNotBlank(user.getReferralCodeTermConditionVersion());
	}

	@Override
	public User save(User user) {

		// Validation to check if user is coming from public endpoints:
		// {/account/reset-password-mobile} should not populate
		CountryDTO countryDTO = null;
		StateDTO stateDTO = null;
		if (!SecurityUtil.isAnonymous()) {
			populateService.populateCountry(user);
			populateService.populateState(user);
		}

		return userService.save(user);
	}

	@Override
	public User findByIdAndInstitutionId(String id, String institutionId) {
		User user = userService.findByIdAndInstitutionsId(id, institutionId);
		// REMOVE 5/JAN because need to display all institution authority in edit screen
		// populateService.populateAuthoritiesByInstitution(user, institutionId);
		populateService.populateCountry(user);
		populateService.populateState(user);
		return user;
	}

	@Override
	public Page<User> findByInstitutionsIdAndLoginTypeAndFullName(Pageable pageable, String institutionId,
			LoginTypeEnum loginType, String fullName) {
		return userService.findByInstitutionsIdAndLoginTypeAndFullName(institutionId, loginType, fullName, pageable)
				.map(user -> {
					populateService.populateAuthoritiesByInstitution(user, institutionId);
					populateService.populateCountry(user);
					return user;
				});
	}

	@Async
	@Override
	public CompletableFuture<String> generateFileReport(MobileUserPaginationRequest request,
			CurrentUserIdResponse currentUser) {
		log.info("generating file report...");
		String filter = jsonUtil.convertToString(request);
		String id = proxyFeignClient.getCommonFeignClient()
				.startDownloadTransaction(DownloadStartRequest.builder().filter(filter)
						.filterHash(HashUtil.sha512Hex(filter)).generatedBy(currentUser.getRefNo())
						.type(DownloadTransactionType.USER_MOBILE).filterStartDate(request.getFilterStartDate())
						.filterEndDate(request.getFilterEndDate()).build());

		try {
			log.info("start generate file user record: {}", id);
			Page<UserDetailResponse> response = userService.findInternalMobileUser(Pageable.unpaged(), request);
			FileS3ObjectDTO s3Object = excelFileGenerator.userMobile(response.getContent());

			log.info("finish generate file user record");
			proxyFeignClient.getCommonFeignClient()
					.finishDownloadTransaction(DownloadEndRequest.builder().id(id).fileName(s3Object.getFilename())
							.fileSize(FileUtils.byteCountToDisplaySize(s3Object.getFileSize()))
							.basePath(s3Object.getBase()).key(s3Object.getKey()).status(DownloadTransactionStatus.READY)
							.finishedDate(Instant.now()).build());
			log.info("send notification to requester");
			EmailRequest emailRequest = EmailRequest.builder().to(List.of(currentUser.getEmail()))
					.templateName(TemplateNameEnum.FILE_GENERATION_DOWNLOAD_USER_MOBILE.getValue()).build();

			kafkaTemplate.send(SEND_EMAIL_TOPIC, jsonUtil.convertToString(emailRequest));

			return CompletableFuture.completedFuture(s3Object.getKey());
		}
		catch (Exception e) {
			log.error("error when generate file report user", e);
			proxyFeignClient.getCommonFeignClient().finishDownloadTransaction(
					DownloadEndRequest.builder().id(id).status(DownloadTransactionStatus.FAILED)
							.finishedDate(Instant.now()).error(ExceptionUtils.getStackTrace(e)).build());

			return CompletableFuture.completedFuture(null);
		}
	}

	@Override
	public List<String> findUserIdsByPermission(String permission) {
		return userService.findUserIdsByPermission(permission);
	}

	@Override
	public UserPersonalInformationResponse getUserPersonalInformation(String id) {
		User user = userService.findInternalMobileUserById(id);
		UserPersonalInformationResponse response = MapStructConverter.MAPPER.toUserPersonalInformationResponse(user);
		response.setCity(
				proxyFeignClient.getCommonFeignClient().getPostcodeByPostcode(user.getPostcode()).getCity().getName());

		return response;
	}

	@Override
	public UserPaymentInformationResponse getUserPaymentInformation(String id) {

		User user = userService.findInternalMobileUserById(id);
		UserPaymentInformationResponse response = MapStructConverter.MAPPER.toUserPaymentInformationResponse(user);
		response.setPaymentAccountDetailResponse(proxyFeignClient.getBankFeignClient().getPaymentAccount(user.getId()));
		return response;
	}

	@Override
	public UserEKycInformationResponse getUserEKycInformation(String id) {
		User user = userService.findInternalMobileUserById(id);
		UserEKycInformationResponse response = MapStructConverter.MAPPER.toUserEKycInformationResponse(user);
		response.setEkycDetailResponse(proxyFeignClient.getCommonFeignClient().getEkycDetailByUser(user.getId()));
		return response;
	}

}
