package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.Property;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PropertyRepository extends JpaRepository<Property, String> {

	long countByUserIdAndIsBrowsingFalse(@NonNull String userId);

	@Query("""
			select p from Property p where p.userId = :userId
			and  p.isBrowsing = false
			""")
	List<Property> findByUserId(String userId, Sort sort);

	Optional<Property> findByIdAndUserId(String id, String userId);

	Boolean existsByAddress1AndAddress2AndAddress3AndUserIdAndIsBrowsingFalse(String address1, String address2,
			String address3, String userId);

	Property findByAttachmentGroupIdAndUserId(String attachmentGroupId, String userId);

	List<Property> findByAttachmentGroupIdNullAndUserIdAndIsBrowsingFalse(String userId);

	boolean existsByUserIdAndAttachmentGroupId(String userId, @NonNull String attachmentGroupId);

	List<Property> findByUserIdAndIsRentedAndIsBrowsingFalse(@NonNull String userId, @NonNull Boolean isRented);

	boolean existsByIdAndUserIdAndIsBrowsingFalse(String id, String userId);

	List<Property> findByUserIdAndLoanId(@NonNull String userId, String loanId);

	List<Property> findByUserIdAndIdNotIn(@NonNull String userId, @NonNull List<String> ids, Sort sort);

	List<Property> findByUserIdAndInsuranceId(@NonNull String userId, String insuranceId);

	List<Property> findTop5ByUserIdOrderByCreatedDateDesc(@NonNull String userId);

}
