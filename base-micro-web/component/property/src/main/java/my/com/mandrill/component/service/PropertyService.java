package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Property;
import my.com.mandrill.component.dto.model.PropertyDTO;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.VaultLinkDTO;
import org.springframework.data.domain.Sort;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Optional;

public interface PropertyService {

	Property findById(String id, String userId);

	Optional<Property> findOptionalById(String id, String userId);

	List<PropertyDTO> findByUserId(String userId, Sort sort);

	Property save(Property property);

	void delete(Property property);

	Boolean isPropertyExistByAddress(String address1, String address2, String address3, String userId);

	void linkVault(Property property, VaultLinkDTO vaultLinkDTO);

	Property findByAttachmentGroupId(String attachmentGroupId, String userId);

	List<Property> findByAttachmentGroupIdNull(String userId);

	long count(String userId);

	boolean existsByUserIdAndAttachmentGroupId(String userId, @NonNull String attachmentGroupId);

	NetWorthDTO calculateNetWorthByUserId(String userId);

	List<Property> findByUserIdAndIsRentedAndIsBrowsingFalse(String userId, Boolean isRented);

	boolean existsByIdAndUserId(String id, String userId);

	List<PropertyDTO> findDetachedEntity(String userId, List<String> linkedEntities, Sort sort);

	List<Property> findTop5ByUserIdOrderByCreatedDateDesc(String userId);

}
