package my.com.mandrill.component.service.impl;

import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.domain.UserInterestedRedirect;
import my.com.mandrill.component.dto.UserInterestedRedirectForExportDTO;
import my.com.mandrill.component.dto.model.UserInterestedRedirectEntityAndCountDTO;
import my.com.mandrill.component.repository.UserInterestedRedirectRepository;
import my.com.mandrill.component.service.UserInterestedRedirectService;
import my.com.mandrill.utilities.feign.dto.request.LeadRSMUpdateRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import my.com.mandrill.utilities.general.exception.BusinessException;
import my.com.mandrill.utilities.general.exception.ExceptionPredicate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class UserUserInterestedRedirectImpl implements UserInterestedRedirectService {

	private final UserInterestedRedirectRepository userInterestedRedirectRepository;

	@Override
	@Transactional
	public UserInterestedRedirect save(UserInterestedRedirect userInterestedRedirect) {
		return userInterestedRedirectRepository.save(userInterestedRedirect);
	}

	@Override
	@Transactional
	public List<UserInterestedRedirect> save(List<UserInterestedRedirect> userInterestedRedirects) {
		return userInterestedRedirectRepository.saveAll(userInterestedRedirects);
	}

	@Override
	public List<UserInterestedRedirectEntityAndCountDTO> getEntityNameAndCount(Instant dateStart, Instant dateEnd) {
		return userInterestedRedirectRepository.getEntityNameAndCountBetweenCreatedDate(dateStart, dateEnd);
	}

	@Override
	public List<UserInterestedRedirectEntityAndCountDTO> getEntityNameAndCount(Instant dateStart, Instant dateEnd,
			List<String> campaignIdList) {
		return userInterestedRedirectRepository.getEntityNameAndCountBetweenCreatedDate(dateStart, dateEnd,
				campaignIdList);
	}

	@Override
	public UserInterestedRedirect findByCampaignIdAndUserId(String campaignId, String userId) {
		return userInterestedRedirectRepository.findFirstByCampaignIdAndUserIdOrderByCreatedDateDesc(campaignId,
				userId);
	}

	@Override
	public List<UserInterestedRedirectForExportDTO> findByEntityNameAndBetweenDate(EntityName entityName,
			Instant startDate, Instant endDate) {
		return userInterestedRedirectRepository.findAllByEntityNameForTotalClick(entityName, startDate, endDate);
	}

	@Override
	public List<UserInterestedRedirectForExportDTO> findByEntityNameAndBetweenDateForUniqueClick(EntityName entityName,
			Instant startDate, Instant endDate) {
		return userInterestedRedirectRepository.findAllByEntityNameForUniqueClick(entityName, startDate, endDate);
	}

	@Override
	public Page<UserInterestedRedirectForExportDTO> findByEntityNameAndBetweenDate(EntityName entityName,
			Instant startDate, Instant endDate, Pageable pageable) {
		return userInterestedRedirectRepository.findAllByEntityNameForTotalClick(entityName, startDate, endDate,
				pageable);
	}

	@Override
	public Page<UserInterestedRedirectForExportDTO> findByEntityNameAndBetweenDate(List<EntityName> entityName,
			Instant startDate, Instant endDate, Pageable pageable) {
		return userInterestedRedirectRepository.findAllByEntityNameForTotalClick(entityName, startDate, endDate,
				pageable);
	}

	@Override
	public List<UserInterestedRedirect> findByFullNameNullOrNricNullOrPhoneNumberNullOrEmailNullOrRefNoNull() {
		return userInterestedRedirectRepository.findByFullNameNullOrNricNullOrPhoneNumberNullOrEmailNullOrRefNoNull();
	}

	@Override
	@Transactional
	public void saveAll(List<UserInterestedRedirect> userInterestedRedirects) {
		userInterestedRedirectRepository.saveAll(userInterestedRedirects);
	}

	private Specification<UserInterestedRedirect> specificationUserInterestedRedirect(RSMLeadRequest request) {
		return (root, cq, cb) -> {
			List<Predicate> predicates = new ArrayList<>();
			if (StringUtils.isNotBlank(request.getRefNo())) {
				predicates.add(cb.like(root.get("applicationRefNo"), "%" + request.getRefNo() + "%"));
			}
			if (StringUtils.isNotBlank(request.getUserRefNo())) {
				predicates.add(cb.like(root.get("refNo"), "%" + request.getUserRefNo() + "%"));
			}
			if (StringUtils.isNotEmpty(request.getProductName())) {
				predicates.add(
						cb.like(cb.lower(root.get("productName")), "%" + request.getProductName().toLowerCase() + "%"));
			}
			if (CollectionUtils.isNotEmpty(request.getProviderIds())) {
				predicates.add(root.get("providerId").in(request.getProviderIds()));
			}
			if (CollectionUtils.isNotEmpty(request.getProductTypes())) {
				predicates.add(root.get("productType").in(request.getProductTypes()));
			}
			if (StringUtils.isNotEmpty(request.getFullName())) {
				predicates
						.add(cb.like(cb.lower(root.get("fullName")), "%" + request.getFullName().toLowerCase() + "%"));
			}
			if (CollectionUtils.isNotEmpty(request.getRsmRelation())) {
				predicates.add(root.get("rsmRelation").in(request.getRsmRelation()));
			}
			if (CollectionUtils.isNotEmpty(request.getEntityNames())) {
				predicates.add(root.get("entityName").in(request.getEntityNames()));
			}
			if (CollectionUtils.isNotEmpty(request.getRsmStatus())) {
				predicates.add(root.get("rsmStatus").in(request.getRsmStatus()));
			}
			if (CollectionUtils.isNotEmpty(request.getRsmCommissionAttached())) {
				predicates.add(root.get("rsmCommissionAttached").in(request.getRsmCommissionAttached()));
			}
			if (CollectionUtils.isNotEmpty(request.getRsmEligible())) {
				predicates.add(root.get("rsmEligible").in(request.getRsmEligible()));
			}
			if (Objects.nonNull(request.getDateType()) && Objects.nonNull(request.getStartDate())) {
				predicates.add(cb.greaterThan(root.get(request.getDateType().getFieldName()),
						request.getStartDate().atStartOfDay().atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant()));

			}
			if (Objects.nonNull(request.getDateType()) && Objects.nonNull(request.getEndDate())) {
				predicates.add(cb.lessThan(root.get(request.getDateType().getFieldName()),
						request.getEndDate().atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant()));
			}

			if (Objects.nonNull(request.getApplicationDate())) {
				final Instant startTimeFilter = request.getApplicationDate().atStartOfDay()
						.atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
				final Instant endTimeFilter = request.getApplicationDate().atTime(LocalTime.MAX)
						.atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
				predicates.add(cb.greaterThan(root.get("createdDate"), startTimeFilter));
				predicates.add(cb.lessThan(root.get("createdDate"), endTimeFilter));
			}
			return cb.and(predicates.toArray(new Predicate[0]));
		};
	}

	@Override
	public List<UserInterestedRedirect> findAll(RSMLeadRequest request) {
		return userInterestedRedirectRepository.findAll(this.specificationUserInterestedRedirect(request));
	}

	@Override
	public Page<UserInterestedRedirect> findAll(Pageable pageable, RSMLeadRequest request) {
		return userInterestedRedirectRepository.findAll(this.specificationUserInterestedRedirect(request), pageable);
	}

	@Override
	public UserInterestedRedirect findById(String id) {
		return userInterestedRedirectRepository.findById(id)
				.orElseThrow(ExceptionPredicate.userInterestedRecordNotFound(id));
	}

	@Override
	@Transactional
	public void updateRsmInfo(LeadRSMUpdateRequest request) {
		if (CollectionUtils.isNotEmpty(request.getIds())) {
			bulkUpdate(request);
			return;
		}
		singleUpdate(request);
	}

	private void bulkUpdate(LeadRSMUpdateRequest request) {
		if (userInterestedRedirectRepository.existsAllByIdInAndRsmStatusAndRsmCommissionAttachedIsTrue(request.getIds(),
				RSMStatus.SUCCESS)) {
			throw new BusinessException(ErrorCodeGlobalEnum.CANNOT_PERFORM_BULK_UPDATE_BECAUSE_SOME_SUCCESS);
		}
		userInterestedRedirectRepository.bulkUpdateStatusByIdIn(request.getStatus(), request.getIds());
	}

	private void singleUpdate(LeadRSMUpdateRequest request) {
		UserInterestedRedirect record = userInterestedRedirectRepository.findById(request.getId())
				.orElseThrow(ExceptionPredicate.userInterestedRecordNotFound(request.getId()));

		record.setRsmStatus(request.getStatus());
		record.setRsmCommissionAttached(request.isCommissionAttached());
		record.setRemarks(StringUtils.isNotBlank(request.getRemarks()) ? request.getRemarks() : record.getRemarks());
		userInterestedRedirectRepository.save(record);
	}

	@Override
	public Set<String> findAllApplicationIdsExcludingUserIdsIn(Set<String> applicationIds, Set<String> userIds) {
		if (applicationIds.isEmpty())
			return Set.of();

		return userInterestedRedirectRepository.findAllById(applicationIds).stream()
				.filter(u -> !userIds.contains(u.getUserId())).map(UserInterestedRedirect::getId)
				.collect(Collectors.toSet());
	}

}
