package my.com.mandrill.utilities.feign.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.DeliveryType;
import my.com.mandrill.utilities.general.constant.ReminderFrequency;
import my.com.mandrill.utilities.general.constant.ReminderType;

import java.io.Serializable;
import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode
public class ReminderResponse implements Serializable {

	private String id;

	private DeliveryType deliveryType;

	private ReminderType reminderType;

	private Instant startDate;

	private Instant endDate;

	private String title;

	private String note;

	private ReminderFrequency reminderFrequency;

	private ReminderDataResponse data;

	private Boolean isIntegration;

}