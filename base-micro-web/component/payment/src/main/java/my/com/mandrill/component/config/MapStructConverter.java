package my.com.mandrill.component.config;

import my.com.mandrill.component.domain.Merchant;
import my.com.mandrill.component.domain.RecurringCard;
import my.com.mandrill.component.domain.Transaction;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.component.dto.request.TransactionDefaultRequest;
import my.com.mandrill.component.dto.request.TransactionRequest;
import my.com.mandrill.utilities.feign.dto.model.PaymentTransactionDTO;
import my.com.mandrill.utilities.general.service.LazyLoadingAwareMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MapStructConverter extends LazyLoadingAwareMapper {

	MapStructConverter MAPPER = Mappers.getMapper(MapStructConverter.class);

	Transaction toTransaction(TransactionRequest transactionRequest);

	Transaction toTransaction(TransactionDefaultRequest transactionDefaultRequest);

	TransactionDTO toTransactionDTO(Transaction transaction);

	RecurringCardDTO toRecurringCardDTO(RecurringCard recurringCard);

	MerchantDTO toMerchantDTO(Merchant merchant);

	PaymentTransactionDTO toPaymentTransactionDTO(Transaction transaction);

	@Mapping(target = "transactionDatetime", source = "dateCompleted")
	@Mapping(target = "telcoOperator", source = "productDescription")
	@Mapping(target = "errorDescription", source = "responseDescription")
	TopUpTransactionDTO toTopUpTransactionDTO(Transaction transaction);

	@Mapping(target = "transactionDatetime", source = "dateCompleted")
	TopUpTransactionHistoryDTO toTopUpTransactionHistoryDTO(Transaction transaction);

	@Mapping(target = "transactionDatetime", source = "createdDate")
	my.com.mandrill.utilities.feign.dto.TopUpTransactionDTO toTopUpTransactionFeignDTO(Transaction transaction);

}
