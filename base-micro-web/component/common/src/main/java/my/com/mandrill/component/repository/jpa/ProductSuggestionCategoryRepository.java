package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.ProductSuggestionCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ProductSuggestionCategoryRepository extends JpaRepository<ProductSuggestionCategory, String> {

	@Query("SELECT c FROM ProductSuggestionCategory c " + "LEFT JOIN FETCH c.productSuggestions ps "
			+ "WHERE ps.active = true AND (:type IS NULL OR ps.type = :type)")
	List<ProductSuggestionCategory> findAllWithProductSuggestions(String type);

	@Query("SELECT c FROM ProductSuggestionCategory c " + "LEFT JOIN FETCH c.productSuggestions ps "
			+ "WHERE ps.active = true AND (:type IS NULL OR ps.type = :type) "
			+ "AND (ps.exclusionTag IS NULL OR ps.exclusionTag NOT IN :excludedTags)")
	List<ProductSuggestionCategory> findAllWithProductSuggestionWithExclusions(String type, Set<String> excludedTags);

}
