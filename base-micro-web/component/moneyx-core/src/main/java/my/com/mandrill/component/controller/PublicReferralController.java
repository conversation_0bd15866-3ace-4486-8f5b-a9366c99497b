package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.service.ReferralIntegrationService;
import my.com.mandrill.utilities.core.annotation.PublicAuth;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@PublicAuth
@RestController
@SecurityRequirements
@RequiredArgsConstructor
@RequestMapping("/v1/public/referral")
public class PublicReferralController {

	private final ReferralIntegrationService referralIntegrationService;

	@GetMapping("code-exists")
	public Boolean checkReferralCodeExists(@RequestParam String code) {
		return referralIntegrationService.existReferralCode(code);
	}

}
