package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.PropertyInterest;
import my.com.mandrill.utilities.general.constant.RSMStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Set;

@Repository
public interface PropertyInterestRepository
		extends JpaRepository<PropertyInterest, String>, JpaSpecificationExecutor<PropertyInterest> {

	@Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END " + "FROM PropertyInterest e "
			+ "WHERE e.id IN :ids AND (e.rsmStatus = :status OR e.rsmCommissionAttached = true)")
	boolean existsAllByIdInAndRsmStatusAndRsmCommissionAttachedIsTrue(Set<String> ids, RSMStatus status);

	@Modifying
	@Query("UPDATE PropertyInterest p SET p.rsmStatus = ?1 WHERE p.id IN ?2")
	void bulkUpdateStatusByIdIn(RSMStatus status, Set<String> ids);

}
