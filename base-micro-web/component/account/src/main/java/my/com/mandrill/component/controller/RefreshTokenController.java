package my.com.mandrill.component.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.dto.model.AuthenticateResultDTO;
import my.com.mandrill.component.dto.model.ExtendAccessTokenDTO;
import my.com.mandrill.component.service.TokenIntgService;
import my.com.mandrill.utilities.general.util.HttpRequestUtil;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "29-refresh-token")
@Slf4j
@RestController
@RequestMapping("/token")
@RequiredArgsConstructor
public class RefreshTokenController {

	private final TokenIntgService tokenIntgService;

	private final HttpRequestUtil httpRequestUtil;

	@ResponseStatus(HttpStatus.OK)
	@PostMapping(value = "/extends")
	public AuthenticateResultDTO extendRefreshToken(@RequestBody ExtendAccessTokenDTO request,
			HttpServletRequest httpServletRequest) {
		request.setHttpDetailDTO(httpRequestUtil.parseResource(httpServletRequest));
		return tokenIntgService.extendToken(request);
	}

}
