package my.com.mandrill.component.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import my.com.mandrill.component.domain.VixRequest;
import my.com.mandrill.component.dto.model.VixRequestDTO;
import my.com.mandrill.component.dto.request.VixCallRequest;
import my.com.mandrill.component.dto.response.FinologyVixResponse;

import java.security.NoSuchAlgorithmException;
import java.util.Optional;
import java.util.function.Function;

public interface VixRequestIntegrationService {

	FinologyVixResponse sendRequest(String userId, VixCallRequest vixCallRequest)
			throws NoSuchAlgorithmException, JsonProcessingException;

	FinologyVixResponse sendRequest(String userId, VixCallRequest vixCallRequest,
			Function<String, Optional<String>> getterVehicleId)
			throws NoSuchAlgorithmException, JsonProcessingException;

	VixRequest save(String userId, String vehicleTypeId, VixRequestDTO vixRequestDTO);

}
