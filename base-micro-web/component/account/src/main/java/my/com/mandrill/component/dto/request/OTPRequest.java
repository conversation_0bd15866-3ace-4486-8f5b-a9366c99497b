package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.UsernameType;
import my.com.mandrill.utilities.general.constant.DeliveryType;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.dto.HttpDetailDTO;

import java.io.Serializable;
import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OTPRequest implements Serializable {

	private String captchaToken;

	@NotBlank
	private String username;

	@NotNull
	private RequestKeyType requestKeyType;

	@NotNull
	private UsernameType usernameType;

	private DeliveryType deliveryType;

	private String referralCode;

	@JsonIgnore
	@Transient
	private HttpDetailDTO httpDetail;

	public DeliveryType getDeliveryType() {
		return Optional.ofNullable(deliveryType).orElse(DeliveryType.SMS);
	}

}
