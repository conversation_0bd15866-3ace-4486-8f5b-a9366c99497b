package my.com.mandrill.component.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Property;
import my.com.mandrill.component.dto.model.PropertyDTO;
import my.com.mandrill.component.dto.response.PropertyResponse;
import my.com.mandrill.component.service.PropertyIntegrationService;
import my.com.mandrill.component.service.PropertyService;
import my.com.mandrill.utilities.feign.dto.LoanDTO;
import my.com.mandrill.utilities.feign.dto.StateDTO;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import my.com.mandrill.utilities.feign.service.ProxyFeignClient;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.model.DashboardActivityMessage;
import my.com.mandrill.utilities.general.service.DashboardTriggerService;
import my.com.mandrill.utilities.general.service.KafkaSender;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class PropertyIntegrationServiceImpl implements PropertyIntegrationService {

	private final PropertyService propertyService;

	private final ProxyFeignClient proxyFeignClient;

	private final DashboardTriggerService dashboardTriggerService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final KafkaSender kafkaSender;

	@Override
	public void deleteV2(String id, String userId) {
		Property property = propertyService.findById(id, userId);
		propertyService.delete(property);
		proxyFeignClient.getCommonFeignClient().deleteUserJourneyByEntityName(EntityName.PROPERTY, id);
		kafkaTemplate.send(KafkaTopic.PROPERTY_DELETION, property.getId(), userId);
	}

	@Override
	public void populateAttachment(PropertyResponse propertyResponse) {
		propertyResponse.setAttachments(proxyFeignClient.getCommonFeignClient()
				.getAttachmentsByAttachmentGroupId(propertyResponse.getAttachmentGroupId()).getAttachments());
	}

	@Override
	public void populateState(PropertyResponse propertyResponse) {
		if (StringUtils.isNotBlank(propertyResponse.getStateId())) {
			try {
				propertyResponse
						.setState(proxyFeignClient.getCommonFeignClient().getState(propertyResponse.getStateId()));
			}
			catch (EntityNotFoundException e) {
				propertyResponse.setState(null);
				propertyResponse.setStateId(null);
			}
		}
	}

	@Override
	public void populateMonthlyInstallment(PropertyResponse propertyResponse) {
		try {
			propertyResponse.setMonthlyInstallment(proxyFeignClient.getBankFeignClient()
					.findLoanByEntityNameAndEntityId(EntityName.PROPERTY, propertyResponse.getId())
					.getMonthlyInstallment());
		}
		catch (EntityNotFoundException e) {
			propertyResponse.setMonthlyInstallment(null);
		}
	}

	@Override
	public void sendDashboardActivity(Instant createdDate) {
		DashboardActivityMessage dto = DashboardActivityMessage.builder().category(DashboardCategory.MODULE_RECORDS)
				.type(DashboardType.PROPERTIES).value(1L).createdDate(createdDate).build();

		dashboardTriggerService.send(dto);
	}

	@Override
	public Property create(Property property) {
		return propertyService.save(property);
	}

	@Override
	public List<PropertyDTO> findByUserId(String userId, Sort sort) {
		List<PropertyDTO> properties = propertyService.findByUserId(userId, sort);
		mapStateIntoProperty(properties);
		return properties;
	}

	@Override
	public List<PropertyDTO> findDetachedEntity(String userId, List<String> linkedEntity, Sort sort) {
		if (linkedEntity.isEmpty()) {
			return this.findByUserId(userId, sort);
		}
		List<PropertyDTO> properties = propertyService.findDetachedEntity(userId, linkedEntity, sort);
		mapStateIntoProperty(properties);
		return properties;
	}

	@Override
	public BigDecimal getCurrentUserLowestPropertyValue() {
		String userId = SecurityUtil.currentUserId();
		List<Property> properties = propertyService.findTop5ByUserIdOrderByCreatedDateDesc(userId);

		return properties.stream().map(Property::getPurchaseValue).filter(Objects::nonNull).map(BigDecimal::new)
				.min(Comparator.naturalOrder()).map(value -> value.multiply(BigDecimal.valueOf(0.9)))
				.orElse(BigDecimal.ZERO);
	}

	@Override
	public void publishNetWorthTransactionEvent(String userId, NetWorthMainType mainType, NetWorthType subType,
			NetWorthSource source) {

		BigDecimal totalPropertyValue = propertyService.calculateNetWorthByUserId(userId).getAssets();

		UpdateUserNetWorthTransactionRequest request = UpdateUserNetWorthTransactionRequest.builder().userId(userId)
				.mainType(mainType).subType(subType).amount(totalPropertyValue).source(source).build();

		kafkaSender.safeSend(KafkaTopic.UPDATE_USER_NET_WORTH_TRANSACTION_TOPIC, userId, request);
	}

	private void mapStateIntoProperty(List<PropertyDTO> properties) {
		HashMap<String, StateDTO> stateHashMap = new HashMap<>();
		Map<String, LoanDTO> loanMap = new HashMap<>();
		List<LoanDTO> loans = proxyFeignClient.getBankFeignClient().getLoans(LoanTypeEnum.HOME_LOANS);
		for (LoanDTO loan : loans) {
			if (loan.getEntityId() != null)
				loanMap.put(loan.getEntityId(), loan);
		}
		List<StateDTO> states = proxyFeignClient.getCommonFeignClient().getAllStates();

		for (StateDTO state : states) {
			stateHashMap.put(state.getId(), state);
		}
		for (PropertyDTO property : properties) {
			StateDTO state = stateHashMap.get(property.getStateId());
			if (state != null) {
				property.setState(state);
			}
			LoanDTO loan = loanMap.get(property.getId());
			if (loan != null) {
				property.setMonthlyInstallment(loan.getMonthlyInstallment());
			}
		}
	}

}
