package my.com.mandrill.component.service.impl;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Token;
import my.com.mandrill.component.dto.model.AuthenticateResultDTO;
import my.com.mandrill.component.dto.model.ExtendAccessTokenDTO;
import my.com.mandrill.component.dto.model.RevokeAllRequestDTO;
import my.com.mandrill.component.dto.model.RevokeRequestDTO;
import my.com.mandrill.component.dto.response.TokenServiceResponse;
import my.com.mandrill.component.service.TokenIntgService;
import my.com.mandrill.component.service.TokenService;
import my.com.mandrill.utilities.core.dto.model.JwtTokenRequest;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.core.token.service.UserTokenService;
import my.com.mandrill.utilities.feign.service.FeatureFlagOutbound;
import my.com.mandrill.utilities.general.constant.GlobalSystemConfigurationEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class TokenIntgServiceImpl implements TokenIntgService {

	private final TokenService tokenService;

	private final UserTokenService userTokenService;

	private final TokenProvider tokenProvider;

	private final FeatureFlagOutbound featureFlagOutbound;

	@Transactional
	@Override
	public void revokedToken(RevokeRequestDTO request) {
		Token token = tokenService.revokeToken(request);
		userTokenService.deleteByTokenIds(List.of(token.getId()));
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void revokedAllToken(RevokeAllRequestDTO request) {
		tokenService.revokeAllToken(request.getRevokedReason());
		userTokenService.deleteAllToken();
	}

	@Transactional
	@Override
	public void logoutToken(HttpServletRequest request) {
		String token = tokenProvider.getTokenFromRequest(request);
		tokenService.logoutToken(token);
		userTokenService.deleteByTokenIds(List.of(token));
	}

	@Override
	public AuthenticateResultDTO extendToken(ExtendAccessTokenDTO request) {
		TokenServiceResponse token = tokenService.extendToken(request);
		JwtTokenRequest jwtTokenRequest = JwtTokenRequest.builder().accessToken(token.getAccessToken())
				.grantedAuthorities(token.getGrantedAuthorities()).userRefNo(token.getUserRefNo())
				.userId(token.getUserId()).build();
		String accessToken = tokenProvider.createDefaultTokenV2(jwtTokenRequest);
		return AuthenticateResultDTO.builder().refreshToken(token.getRefreshToken()).accessToken(accessToken)
				.userId(token.getUserId()).build();
	}

}
