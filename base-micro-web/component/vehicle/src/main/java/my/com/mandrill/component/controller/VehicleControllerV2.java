package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.NetWorthMainType;
import my.com.mandrill.component.constant.NetWorthSource;
import my.com.mandrill.component.constant.NetWorthType;
import my.com.mandrill.component.domain.Vehicle;
import my.com.mandrill.component.dto.request.CreateVehicleRequest;
import my.com.mandrill.component.dto.request.VehicleRequest;
import my.com.mandrill.component.dto.response.VehicleResponse;
import my.com.mandrill.component.service.PopulateService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.component.service.VehicleIntegrationService;
import my.com.mandrill.component.service.VehicleService;
import my.com.mandrill.utilities.feign.client.AccountFeignClient;
import my.com.mandrill.utilities.feign.dto.VehicleFinologyDTO;
import my.com.mandrill.utilities.general.util.LambdaVoid;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("v2/vehicles")
@RequiredArgsConstructor
public class VehicleControllerV2 {

	private final AccountFeignClient accountFeignClient;

	private final PopulateService populateService;

	private final ValidationService validationService;

	private final VehicleService vehicleService;

	private final VehicleIntegrationService vehicleIntegrationService;

	private final ObjectMapper objectMapper;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<VehicleResponse> create(@Valid @RequestBody CreateVehicleRequest vehicleRequest) {

		String userId = SecurityUtil.currentUserId();

		Vehicle vehicle = new Vehicle();
		validationService.validateVehicle(vehicle, vehicleRequest, userId, LambdaVoid.supplierNothing());
		validationService.validateUserJourney(vehicleRequest.getUserJourneyId());
		Vehicle result = vehicleIntegrationService.save(vehicle, LambdaVoid.functionNothing());

		VehicleResponse response = MapStructConverter.MAPPER.toVehicleResponse(result);
		populateService.populateVehicleType(response);

		vehicleIntegrationService.sendDashboardActivity(result.getCreatedDate());

		vehicleIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.ASSETS,
				NetWorthType.VEHICLES, NetWorthSource.VEHICLE);

		return ResponseEntity.ok(response);
	}

	@PutMapping({ "{id}" })
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public ResponseEntity<VehicleResponse> update(@Valid @RequestBody VehicleRequest vehicleRequest,
			@PathVariable String id) {

		String userId = SecurityUtil.currentUserId();

		Vehicle existingVehicle = vehicleService.findById(id, userId);
		validationService.validateVehicle(existingVehicle, vehicleRequest, userId, LambdaVoid.supplierNothing());

		Vehicle result = vehicleIntegrationService.save(existingVehicle, LambdaVoid.functionNothing());

		VehicleResponse response = MapStructConverter.MAPPER.toVehicleResponse(result);
		populateService.populateVehicleType(response);

		vehicleIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.ASSETS,
				NetWorthType.VEHICLES, NetWorthSource.VEHICLE);

		return ResponseEntity.ok(response);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@DeleteMapping({ "{id}" })
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void delete(@PathVariable String id) {

		String userId = SecurityUtil.currentUserId();

		vehicleIntegrationService.delete(id, userId, LambdaVoid.functionNothing());

		vehicleIntegrationService.publishNetWorthTransactionEvent(userId, NetWorthMainType.ASSETS,
				NetWorthType.VEHICLES, NetWorthSource.VEHICLE);
	}

	@Hidden
	@PostMapping("/integration/finology")
	public ResponseEntity<VehicleFinologyDTO> createVehicleByFinologyData(
			@Valid @RequestBody VehicleFinologyDTO vehicleFinologyDTO) {
		vehicleFinologyDTO.setUserId(SecurityUtil.currentUserId());
		validationService.validateVehicle(vehicleFinologyDTO);
		Vehicle mapperResult = vehicleService.saveByFinology(vehicleFinologyDTO);
		Vehicle result = vehicleIntegrationService.save(mapperResult, LambdaVoid.functionNothing());

		return ResponseEntity.ok(objectMapper.convertValue(result, VehicleFinologyDTO.class));
	}

}
