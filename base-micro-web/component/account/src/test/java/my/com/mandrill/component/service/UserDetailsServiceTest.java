package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Authority;
import my.com.mandrill.component.domain.Permission;
import my.com.mandrill.component.repository.jpa.UserRepository;
import my.com.mandrill.component.service.impl.UserDetailsServiceImpl;
import my.com.mandrill.component.service.impl.UserServiceImpl;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class UserDetailsServiceTest {

	@InjectMocks
	UserDetailsServiceImpl userDetailsService;

	@Mock
	UserRepository userRepository;

	@InjectMocks
	AesCryptoUtil aesCryptoUtil;

	@Mock
	UserServiceImpl userService;

	Authority authorityMock;

	Set<Authority> authoritySetMock;

	User userSec;

	my.com.mandrill.component.domain.User userMock;

	Collection<GrantedAuthority> grantedAuthoritiesMock;

	GrantedAuthority grantedAuthorityMock;

	Set<Permission> permissionSetMock;

	Permission permissionMock;

	@BeforeEach
	void setup() {
		ReflectionTestUtils.setField(aesCryptoUtil, "AES_KEY", "NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc");
		ReflectionTestUtils.setField(aesCryptoUtil, "IV_KEY", "57ad289f-3def-4c");

		authorityMock = new Authority();
		permissionSetMock = new HashSet<>();
		permissionMock = new Permission();
		permissionSetMock.add(permissionMock);
		userMock = new my.com.mandrill.component.domain.User();
		userMock.setUsername("username");
		userMock.setPassword("password");
		authoritySetMock = new HashSet<>();
		grantedAuthorityMock = new SimpleGrantedAuthority("USER_ADMIN");
		authoritySetMock.add(authorityMock);
		userMock.setAuthorities(authoritySetMock);
		grantedAuthoritiesMock = new ArrayList<>();
		grantedAuthoritiesMock.add(grantedAuthorityMock);
		userSec = new User("username", "password", true, true, true, true, grantedAuthoritiesMock);
	}

	@Test
	void loadUserByUsername_Success() {
		Mockito.when(userService.findByUsernameAndProvider(any(), any())).thenReturn(userMock);

		UserDetails u = userDetailsService.loadUserByUsername("test");
		assertThat(u.getUsername()).isEqualTo("test");
	}

}
