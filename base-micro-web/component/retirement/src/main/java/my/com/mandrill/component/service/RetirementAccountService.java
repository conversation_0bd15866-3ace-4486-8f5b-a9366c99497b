package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.RetirementAccount;

import java.util.List;

public interface RetirementAccountService {

	RetirementAccount save(RetirementAccount retirementAccount);

	RetirementAccount findByIdAndUserId(String id, String userId);

	List<RetirementAccount> findAllByUserId(String userId);

	List<RetirementAccount> findAllByAccountTypeIdAndMonthlyContributionNotNull(String accountTypeId, long limit,
			long offset);

	boolean existsByUserIdAndAccountTypeIdAndRetirementProviderId(String userId, String accountTypeId,
			String providerId);

	boolean existsByIdAndUserId(String id, String userId);

	void deleteById(String id);

}
