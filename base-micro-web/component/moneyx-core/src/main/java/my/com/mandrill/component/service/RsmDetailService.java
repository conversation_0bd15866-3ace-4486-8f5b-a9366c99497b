package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.RsmDetail;
import my.com.mandrill.component.domain.RsmHeader;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface RsmDetailService {

	List<RsmDetail> findByHeader(List<String> ids);

	@Transactional
	void saveAll(List<RsmDetail> detail);

	@Transactional
	void deleteAllByHeader(RsmHeader header);

}
