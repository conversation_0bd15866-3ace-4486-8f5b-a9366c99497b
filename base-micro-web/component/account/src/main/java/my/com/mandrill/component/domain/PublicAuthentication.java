package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import lombok.*;
import my.com.mandrill.crypto.converter.ConfidentialDataConverter;
import my.com.mandrill.crypto.converter.SecretDataConverter;
import my.com.mandrill.utilities.core.audit.AuditSection;
import org.hibernate.annotations.Cascade;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "public_authentication", uniqueConstraints = { @UniqueConstraint(columnNames = { "identifier" }) })
public class PublicAuthentication extends AuditSection implements Serializable {

	@Convert(converter = SecretDataConverter.class)
	@Column(name = "api_key")
	private String apiKey;

	@Column(name = "identifier", nullable = false)
	private String identifier;

	@Column(name = "secret_key")
	@Convert(converter = ConfidentialDataConverter.class)
	private String secretKey;

	@EqualsAndHashCode.Exclude
	@ToString.Exclude
	@ManyToMany(fetch = FetchType.LAZY, cascade = { CascadeType.REFRESH })
	@JoinTable(name = "public_authentication_authority",
			joinColumns = { @JoinColumn(name = "public_authentication_id", nullable = false, updatable = false) },
			inverseJoinColumns = { @JoinColumn(name = "authority_id", nullable = false, updatable = false) })
	@Cascade({ org.hibernate.annotations.CascadeType.DETACH, org.hibernate.annotations.CascadeType.LOCK,
			org.hibernate.annotations.CascadeType.REFRESH, org.hibernate.annotations.CascadeType.REPLICATE })
	private Set<Authority> authorities = new HashSet<>();

}