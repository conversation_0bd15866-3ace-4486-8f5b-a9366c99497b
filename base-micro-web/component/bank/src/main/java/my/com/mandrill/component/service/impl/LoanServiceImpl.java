package my.com.mandrill.component.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.domain.Loan;
import my.com.mandrill.component.dto.request.AttachEntityRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.repository.jpa.LoanRepository;
import my.com.mandrill.component.service.LoanService;
import my.com.mandrill.component.util.LoanCalculator;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.LoanTypeEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Supplier;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class LoanServiceImpl implements LoanService {

	private final LoanRepository loanRepository;

	@Transactional
	@Override
	public Loan save(Loan loan) {
		return loanRepository.save(loan);
	}

	@Transactional
	@Override
	public List<Loan> saveAll(List<Loan> loans) {
		return loanRepository.saveAll(loans);
	}

	@Override
	public Loan findById(String id, String userId) {
		return loanRepository.findByIdAndUserId(id, userId).orElseThrow(ExceptionPredicate.loanNotFound(id));
	}

	@Override
	public Loan findByEntityNameAndEntityId(EntityName entityName, String entityId, String userId) {
		return findOptionalByEntityNameAndEntityId(entityName, entityId, userId)
				.orElseThrow(ExceptionPredicate.loanNotFound(entityName, entityId));
	}

	@Override
	public Optional<Loan> findOptionalByEntityNameAndEntityId(EntityName entityName, String entityId, String userId) {
		return loanRepository.findByUserIdAndEntityNameAndEntityId(userId, entityName, entityId);
	}

	@Override
	public List<Loan> findAll(String userId, Sort sort) {
		return loanRepository.findByUserId(userId, sort);
	}

	@Override
	@Transactional
	public void delete(Loan loan) {
		loanRepository.delete(loan);
	}

	@Override
	public long count(String userId) {
		return loanRepository.countByUserId(userId);
	}

	@Override
	public List<Loan> findByUserIdAndTypeIn(String userId, Collection<LoanTypeEnum> types) {
		return loanRepository.findByUserIdAndTypeIn(userId, types);
	}

	@Override
	public Optional<Loan> findByIdAndUserId(String id, String userId) {
		return loanRepository.findByIdAndUserId(id, userId);
	}

	@Override
	public List<Loan> findByUserId(String userId) {
		return loanRepository.findByUserId(userId);
	}

	@Override
	public BigDecimal countTotalByType(String userId, LoanTypeEnum type) {
		return loanRepository.countTotalAmountByLoanType(userId, type);
	}

	@Override
	public List<Loan> findLoanForKYLL(String userId) {
		List<LoanTypeEnum> loanTypeEnums = Arrays.asList(LoanTypeEnum.AUTO_LOANS, LoanTypeEnum.HOME_LOANS,
				LoanTypeEnum.PERSONAL_LOANS, LoanTypeEnum.EDUCATION_LOANS, LoanTypeEnum.ISLAMIC_FINANCING,
				LoanTypeEnum.OTHER_LOAN);
		return loanRepository.findAllByUserIdAndTypeInOrderByCreatedDateDesc(userId, loanTypeEnums);
	}

	@Override
	public List<Loan> findDetached(String userId, LoanTypeEnum loanType) {
		return loanRepository.findByUserIdAndEntityIdAndType(userId, null, loanType);
	}

	@Override
	public List<Loan> findAttachedLoan(String userId, EntityName entityName) {
		return loanRepository.findByUserIdAndEntityNameAndEntityIdIsNotNull(userId, entityName);
	}

	@Override
	@Transactional
	public void attachAttachmentGroupId(String id, String userId, String attachmentGroupId) {
		Optional<Loan> loanAttachment = loanRepository.findFirstByUserIdAndAttachmentGroupId(id, attachmentGroupId);
		if (loanAttachment.isPresent()) {
			Loan loan = loanAttachment.get();
			loan.setAttachmentGroupId(null);
			loanRepository.save(loan);
		}

		Optional<Loan> loanOptional = loanRepository.findByIdAndUserId(id, userId);
		if (loanOptional.isPresent()) {
			Loan loan = loanOptional.get();
			loan.setAttachmentGroupId(attachmentGroupId);

			loanRepository.save(loan);
		}
	}

	@Override
	@Transactional
	public void attachEntity(AttachEntityRequest request, String id, String userId) {
		Optional<Loan> loanOptional = loanRepository.findByUserIdAndEntityNameAndEntityId(userId,
				request.getEntityName(), request.getId());
		if (loanOptional.isPresent()) {
			throw new BusinessException(ErrorCodeEnum.ENTITY_HAS_BEEN_ATTACHED,
					Map.of("loanId", loanOptional.get().getId()));
		}

		Loan loan = findById(id, userId);
		loan.setEntityId(request.getId());
		loan.setEntityName(request.getEntityName());

		loanRepository.save(loan);
	}

	@Transactional
	public <T> T withDetachedCheck(String loanId, Supplier<T> executor) {
		try {
			return executor.get();
		}
		catch (EntityNotFoundException e) {
			loanRepository.setNullEntityIdAndEntityName(loanId);
			return null;
		}
	}

	@Override
	public BigDecimal calculateLoanEndingBalance(Loan loan) {
		LocalDate repaymentLocalDate = LocalDate.of(loan.getRepaymentStartYear().getValue(),
				loan.getRepaymentStartMonth(), 1);
		long completedPayments = LoanCalculator.calculateTotalPayments(repaymentLocalDate);
		BigDecimal endingBalance;
		if (LoanTypeEnum.HOME_LOANS.equals(loan.getType())) {
			endingBalance = new LinkedList<>(LoanCalculator.calculateMortgageAmortization(loan.getAmount(),
					loan.getMonthlyInstallment(), loan.getInterestRate(), repaymentLocalDate, loan.getDuration(),
					Math.toIntExact(completedPayments))).getLast().getEndingBalance();
		}
		else {
			endingBalance = LoanCalculator.calculateSimpleRepayment(loan.getAmount(), loan.getMonthlyInstallment(),
					loan.getInterestRate(), loan.getDuration(), Math.toIntExact(completedPayments));
		}
		return endingBalance.setScale(2, RoundingMode.HALF_UP);
	}

	@Override
	public Page<Loan> findAllOnGoingLoans(Pageable pageable) {
		return loanRepository.findAllNullOrOngoingStatus(pageable);

	}

}
