package my.com.mandrill.component.controller;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.dto.response.BalanceResponse;
import my.com.mandrill.component.dto.response.PointEarningResponse;
import my.com.mandrill.component.dto.response.RefereeEarningResponse;
import my.com.mandrill.component.dto.response.ReferralSummaryResponse;
import my.com.mandrill.component.service.ReferralCodeService;
import my.com.mandrill.component.service.ReferralIntegrationService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

@RestController
@RequiredArgsConstructor
public class ReferralController {

	private final ReferralIntegrationService referralIntegrationService;

	private final ReferralCodeService referralCodeService;

	@GetMapping("account/v1/referral-summary")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ReferralSummaryResponse referralSummary() {
		return referralIntegrationService.getSummary(SecurityUtil.currentUserId());
	}

	@ServiceToServiceAccess
	@GetMapping("account/v1/private/exists-referral-code")
	public ResponseEntity<Boolean> existReferralCode(@RequestParam String referralCode,
			@RequestParam SourceSystemEnum source) {
		return ResponseEntity.ok(referralCodeService.existsReferralCode(referralCode, source));
	}

	@GetMapping("account/v1/earning-histories")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public Page<PointEarningResponse> earningHistory(
			@PageableDefault(sort = "id", direction = Sort.Direction.DESC) Pageable pageable) {
		return referralIntegrationService
				.getEarningHistory(SecurityUtil.currentUserId(), SourceSystemEnum.MXAPP, pageable)
				.map(MapStructConverter.MAPPER::toPointEarningResponse);
	}

	@GetMapping("account/v1/referred-friends")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public List<RefereeEarningResponse> friendEarningData() {
		return referralIntegrationService.getFriendEarningData(SecurityUtil.currentUserId()).stream()
				.map(MapStructConverter.MAPPER::toRefereeEarningResponse).toList();
	}

	@GetMapping("account/v1/total-available-mxpoint")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public BigDecimal totalAvailablePoint() {
		return referralIntegrationService.getTotalAvailablePoint(SecurityUtil.currentUserId());
	}

	@GetMapping("account/v2/total-available-mxpoint")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public BalanceResponse totalAvailablePointV2() {
		return referralIntegrationService.getTotalAvailablePointV2(Collections.singleton(SecurityUtil.currentUserId()));
	}

	@ServiceToServiceAccess
	@GetMapping("account/v1/private/exists-by-user-id")
	public ResponseEntity<String> existsReferralCodeByUserId(@RequestParam String userId) {
		return ResponseEntity.ok(referralIntegrationService.getReferralCodeByUserId(userId));
	}

}
