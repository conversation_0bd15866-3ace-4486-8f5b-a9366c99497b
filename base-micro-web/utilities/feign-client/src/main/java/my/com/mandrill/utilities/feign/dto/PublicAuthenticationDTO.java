package my.com.mandrill.utilities.feign.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PublicAuthenticationDTO implements Serializable {

	private String id;

	private String apiKey;

	private String secretKey;

	private String identifier;

	private Instant createdDate;

	private Instant lastModifiedDate;

	private List<String> permissions;

}
