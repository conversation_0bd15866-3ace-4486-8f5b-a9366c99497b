package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.MonthlyUserTarget;
import my.com.mandrill.component.dto.model.MonthlyUserTargetDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Year;

public interface MonthlyUserTargetService {

	boolean existsByYearAndMonth(Year year, int month);

	Page<MonthlyUserTargetDTO> findAllPageable(Pageable pageable, Year year, Integer month);

	MonthlyUserTargetDTO findById(String id);

	MonthlyUserTarget save(MonthlyUserTarget monthlyUserTarget);

	MonthlyUserTarget update(String id, MonthlyUserTarget monthlyUserTarget);

	void deleteById(String id);

	Long findTargetByYearAndMonth(Year year, int month);

}
