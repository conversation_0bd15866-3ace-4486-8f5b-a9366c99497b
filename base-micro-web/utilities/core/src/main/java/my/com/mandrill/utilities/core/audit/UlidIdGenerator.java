package my.com.mandrill.utilities.core.audit;

import com.github.f4b6a3.ulid.UlidCreator;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

public class UlidIdGenerator implements IdentifierGenerator {

	public static final String generatorName = "ulidGenerator";

	@Override
	public Object generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
		return UlidCreator.getUlid().toString();
	}

	public static String generateUlid() {
		return UlidCreator.getUlid().toString();
	}

}
