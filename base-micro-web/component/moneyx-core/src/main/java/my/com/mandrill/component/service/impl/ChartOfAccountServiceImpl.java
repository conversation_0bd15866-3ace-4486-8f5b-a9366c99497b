package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.ChartOfAccount;
import my.com.mandrill.component.repository.ChartOfAccountRepository;
import my.com.mandrill.component.service.ChartOfAccountService;
import my.com.mandrill.utilities.general.constant.ErrorCodeGlobalEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChartOfAccountServiceImpl implements ChartOfAccountService {

	private final ChartOfAccountRepository chartOfAccountRepository;

	@Override
	public List<ChartOfAccount> findAll() {
		return chartOfAccountRepository.findAll();
	}

	@Override
	public ChartOfAccount findByAccountCode(String accountCode) {
		return chartOfAccountRepository.findByAccountCodeAndIsActiveTrue(accountCode)
				.orElseThrow(() -> new BusinessException(ErrorCodeGlobalEnum.CHART_OF_ACCOUNT_NOT_FOUND));
	}

	@Override
	public Map<String, ChartOfAccount> findChartOfAccountMapping() {
		return chartOfAccountRepository.findAllByIsActiveTrue().stream()
				.collect(Collectors.toMap(ChartOfAccount::getAccountCode, Function.identity()));
	}

}
