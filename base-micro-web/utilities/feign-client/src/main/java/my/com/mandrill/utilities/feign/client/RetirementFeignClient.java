package my.com.mandrill.utilities.feign.client;

import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Set;

@FeignClient("retirement-component")
public interface RetirementFeignClient {

	@GetMapping("/retirement/v1/accounts/integration/net-worth")
	NetWorthDTO calculateNetWorth();

	@GetMapping("/retirements/private/product-exclusion-tags")
	Set<String> getProductExclusionTags();

}
