package my.com.mandrill.component.service.impl;

import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.Expense;
import my.com.mandrill.component.domain.Income;
import my.com.mandrill.component.domain.Segment;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.service.BusinessRuleService;
import my.com.mandrill.utilities.general.util.BooleanUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
@Transactional(readOnly = true)
public class BusinessRuleServiceImpl implements BusinessRuleService {

	private static final String SEGMENT_CATEGORY_BEGINNER = "1";

	/*
	 * Business Rule for Showing Imbalance Income & Expense 1. Segment = 2 or 3 2. Expense
	 * > Income
	 */
	@Override
	public Boolean checkImbalanceIncomeExpense(User user) {
		Boolean isImbalanced = false;

		Segment segment = user.getSegment();
		if (segment != null && !segment.getCode().equals(SEGMENT_CATEGORY_BEGINNER)) {
			BigDecimal totalMonthlyIncome = BigDecimal.ZERO;
			Set<Income> incomes = user.getIncomes();
			for (Income income : incomes) {
				totalMonthlyIncome = totalMonthlyIncome.add(income.getMonthlyIncomeAmount());
			}

			BigDecimal totalMonthlyExpense = BigDecimal.ZERO;
			Set<Expense> expenses = user.getExpenses();
			for (Expense expense : expenses) {
				totalMonthlyExpense = totalMonthlyExpense.add(expense.getAmount());
			}

			if (totalMonthlyExpense.compareTo(BigDecimal.ZERO) > 0
					&& totalMonthlyExpense.compareTo(totalMonthlyIncome) > 0) {
				isImbalanced = true;
			}
		}
		return isImbalanced;
	}

}
