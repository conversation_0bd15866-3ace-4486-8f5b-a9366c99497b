package my.com.mandrill.utilities.general.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collection;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CollectionUtil {

	public static void addIgnoreBlank(Collection<String> collection, String string) {
		if (collection == null) {
			throw new NullPointerException("The collection must not be null");
		}
		if (StringUtils.isNotBlank(string)) {
			collection.add(string);
		}
	}

	public static boolean areAllEmpty(Collection<?>... collections) {
		return Arrays.stream(collections).allMatch(CollectionUtils::isEmpty);
	}

}
