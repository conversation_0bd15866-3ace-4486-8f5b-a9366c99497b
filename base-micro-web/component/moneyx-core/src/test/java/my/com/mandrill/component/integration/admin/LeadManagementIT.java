package my.com.mandrill.component.integration.admin;

import my.com.mandrill.component.domain.RsmCapture;
import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.*;
import my.com.mandrill.utilities.general.constant.ApplicationType;
import my.com.mandrill.utilities.general.constant.PointEarningStatus;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Optional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

public class LeadManagementIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private RsmHeaderRepository rsmHeaderRepository;

	@Autowired
	private RsmDetailRepository rsmDetailRepository;

	@Autowired
	private RsmCaptureRepository rsmCaptureRepository;

	@Autowired
	private PointEarningRepository pointEarningRepository;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@AfterEach
	public void tearDown() throws Exception {
		pointEarningRepository.deleteAll();
		pointTransactionRepository.deleteAll();
		rsmCaptureRepository.deleteAll();
		rsmDetailRepository.deleteAll();
		rsmHeaderRepository.deleteAll();
	}

	@Test
	public void getAllRSMLeadSuccess() throws Exception {
		mockMvc.perform(
				post("/admin/v1/lead").param("size", "30").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON).content("""
								{
								    "applicationType": "LEAD_GEN"
								}
								"""))
				.andExpect(MockMvcResultMatchers.jsonPath("$.content.length()").value(1))
				.andExpect(MockMvcResultMatchers.status().isOk());
	}

	@Test
	public void getAllRSMLeadAffiliateSuccess() throws Exception {
		mockMvc.perform(
				post("/admin/v1/lead").param("size", "30").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON).content("""
								{
								    "applicationType": "AFFILIATE"
								}
								"""))
				.andExpect(MockMvcResultMatchers.jsonPath("$.content.length()").value(1))
				.andExpect(MockMvcResultMatchers.jsonPath("$.content[0].applicationType").value("AFFILIATE"))
				.andExpect(MockMvcResultMatchers.status().isOk());
	}

	@Test
	public void getLeadDetailBankSuccess() throws Exception {
		mockMvc.perform(get("/admin/v1/lead/LEAD_GEN/4e51de60-a7b1-4d2c-8baa-e1abba5f04f5")
				.header("Authorization", "Bearer " + getUserSecretToken()).contentType(MediaType.APPLICATION_JSON))
				.andExpect(MockMvcResultMatchers.jsonPath("$.detail.id").value("4e51de60-a7b1-4d2c-8baa-e1abba5f04f5"))
				.andExpect(MockMvcResultMatchers.jsonPath("$.detail.applicationType")
						.value(ApplicationType.LEAD_GEN.getName()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.commissions").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("$.commissions.length()").value(1))
				.andExpect(
						MockMvcResultMatchers.jsonPath("$.commissions[0].headerId").value("01JJ8WKDV27BYFD1SB126KSGRP"))
				.andExpect(MockMvcResultMatchers.jsonPath("$.commissions[0].revenue").value(200000.00))
				.andExpect(MockMvcResultMatchers.jsonPath("$.commissions[0].details.length()").value(2))
				.andExpect(MockMvcResultMatchers.jsonPath("$.commissions[0].earnings").isArray())
				.andExpect(MockMvcResultMatchers.jsonPath("$.commissions[0].earnings.length()").value(2))
				.andExpect(MockMvcResultMatchers.status().isOk());
	}

	@Test
	public void attachCommissionSuccess() throws Exception {
		mockMvc.perform(
				post("/admin/v1/lead/attach-commission").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON).content("""
								{
								    "rsmHeaderId": "01JJ8WKDV27BYFD1SB126KSGRP",
								    "applicationType": "LEAD_GEN",
								    "applicationId": "4e51de60-a7b1-4d2c-8baa-e1abba5f04f5"
								}
								"""))
				.andExpect(MockMvcResultMatchers.status().isNoContent());
	}

	@Test
	public void disburseCommissionSuccess() throws Exception {
		mockMvc.perform(
				put("/admin/v1/lead/disburse-commission").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON).content(getRequest()))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		Optional<RsmCapture> rsmCapture = rsmCaptureRepository.findByApplicationIdAndRsmHeaderId(
				"4e51de60-a7b1-4d2c-8baa-e1abba5f04f5", "01JP9MTBH8W6SPSK8271K0F27V");
		rsmCapture.ifPresent(capture -> Assertions.assertTrue(capture.isConfirmed()));

		long totalPending = pointEarningRepository.countByApplicationIdAndStatus("4e51de60-a7b1-4d2c-8baa-e1abba5f04f5",
				PointEarningStatus.PENDING);
		long totalAwarded = pointEarningRepository.countByApplicationIdAndStatus("4e51de60-a7b1-4d2c-8baa-e1abba5f04f5",
				PointEarningStatus.AWARDED);
		Assertions.assertEquals(1, totalPending);
		Assertions.assertEquals(2, totalAwarded);
	}

	@Test
	public void deleteCommissionSuccess() throws Exception {
		mockMvc.perform(
				post("/admin/v1/lead/delete-commission").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON).content(getRequest()))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		boolean existCapture = rsmCaptureRepository.existsAllByApplicationIdAndRsmHeaderId(
				"4e51de60-a7b1-4d2c-8baa-e1abba5f04f5", "01JP9MTBH8W6SPSK8271K0F27V");
		Assertions.assertFalse(existCapture);
	}

	@Test
	public void deleteCommissionAlreadyConfirmed() throws Exception {
		mockMvc.perform(
				post("/admin/v1/lead/delete-commission").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON).content(getRequest()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value("CORE0011"))
				.andExpect(MockMvcResultMatchers.status().isBadRequest());

		boolean existCapture = rsmCaptureRepository.existsAllByApplicationIdAndRsmHeaderId(
				"4e51de60-a7b1-4d2c-8baa-e1abba5f04f5", "01JP9MTBH8W6SPSK8271K0F27V");
		Assertions.assertTrue(existCapture);
	}

	@Test
	public void bulkDeleteCommissionSuccess() throws Exception {
		mockMvc.perform(
				post("/admin/v1/lead/bulk-delete-commission").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON).content(getRequest()))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		boolean existCapture = rsmCaptureRepository.existsAllByApplicationIdAndRsmHeaderId(
				"4e51de60-a7b1-4d2c-8baa-e1abba5f04f5", "01JP9MTBH8W6SPSK8271K0F27V");
		Assertions.assertFalse(existCapture);
	}

	@Test
	public void bulkDeleteCommissionHeaderIdEmptySuccess() throws Exception {
		mockMvc.perform(
				post("/admin/v1/lead/bulk-delete-commission").header("Authorization", "Bearer " + getAdminSecretToken())
						.contentType(MediaType.APPLICATION_JSON).content(getRequest()))
				.andExpect(MockMvcResultMatchers.status().isNoContent());

		boolean existCapture = rsmCaptureRepository.existsAllByApplicationIdAndRsmHeaderId(
				"4e51de60-a7b1-4d2c-8baa-e1abba5f04f5", "01JP9MTBH8W6SPSK8271K0F27V");
		Assertions.assertFalse(existCapture);
	}

	@Test
	public void updateStatusSuccess() throws Exception {
		mockMvc.perform(post("/admin/v1/lead/update-status").header("Authorization", "Bearer " + getAdminSecretToken())
				.contentType(MediaType.APPLICATION_JSON).content(getRequest()))
				.andExpect(MockMvcResultMatchers.status().isNoContent());
	}

	@Test
	public void updateStatusNotAllowed() throws Exception {
		mockMvc.perform(post("/admin/v1/lead/update-status").header("Authorization", "Bearer " + getAdminSecretToken())
				.contentType(MediaType.APPLICATION_JSON).content(getRequest()))
				.andExpect(MockMvcResultMatchers.jsonPath("$.errorCode").value("CORE0014"))
				.andExpect(MockMvcResultMatchers.status().isBadRequest());
	}

}
