package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.domain.HomeMenuProduct;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface HomeMenuProductService {

	@Transactional
	void deleteByHomeMenu(HomeMenu homeMenu);

	List<HomeMenuProduct> findAllByHomeMenuCode(String menuCode);

	List<HomeMenuProduct> findAllByParentId(String parentId);

}
