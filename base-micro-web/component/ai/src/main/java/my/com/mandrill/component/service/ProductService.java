package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.ProductGroup;
import my.com.mandrill.component.domain.ProductPlatform;
import my.com.mandrill.component.domain.ProductType;
import my.com.mandrill.component.dto.model.ProductGroupDTO;
import my.com.mandrill.component.dto.model.ProductSimpleSelectionDTO;
import my.com.mandrill.component.dto.model.ProductSuggestionMatchDTO;
import my.com.mandrill.component.dto.request.*;
import my.com.mandrill.component.dto.response.GroupAPIResponse;
import my.com.mandrill.component.dto.response.clientresponse.ProductPageResponse;
import my.com.mandrill.utilities.feign.dto.request.LoanEligibilityLenderProductRequest;
import my.com.mandrill.utilities.general.constant.SurveyFormType;
import my.com.mandrill.utilities.general.dto.response.AiProductDetailResponse;
import my.com.mandrill.utilities.general.dto.response.AiProductResponse;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;

import java.util.Collection;
import java.util.List;

public interface ProductService {

	ResponseEntity<String> findProductList(String page, String size, String type);

	ResponseEntity<String> findProductFilters(String type, String recommendationType);

	AiProductDetailResponse findProductDetails(String type, String productName);

	ResponseEntity<String> findProductCompare(String type, String productName1, String productName2);

	ResponseEntity<String> findProductCategory(String group);

	ResponseEntity<String> findProduct(String type, String entityName, String cardType, String productName,
			String productId);

	ResponseEntity<String> findProductFilterLoanLimit(String type);

	ResponseEntity<String> findProductRefinance(String type, ProductLoanLimitRequest body, String page, String size);

	ResponseEntity<String> findProductSuggestionLoanLimit(String type, SuggestionLoanLimitRequest body, String page,
			String size);

	List<ProductGroupDTO> findAllProductGroups(Sort sort);

	List<ProductPlatform> findAllProductPlatform(Sort sort);

	List<ProductType> findAllProductTypeByPlatformAndGroup(String productPlatformCode, String productGroupCode,
			Boolean rsmActive, Sort sort);

	ProductPageResponse<List<AiProductResponse>> findProductSuggestion(String page, String size, String type,
			ProductSuggestionRequest body);

	ProductPageResponse<List<AiProductResponse>> findProductSuggestionNonLogin(String page, String size, String type,
			ProductSuggestionRequest body);

	ResponseEntity<String> findProductForYou(String size, ProductForYouRequest productForYouRequest);

	ResponseEntity<String> findAllProductGroupsAI();

	ResponseEntity<String> findLoanEligibilityPurchaseProducts(String type, LoanEligibilityLenderProductRequest body,
			String page, String size);

	ResponseEntity<String> findLoanEligibilityRefinanceProducts(String type, LoanEligibilityLenderProductRequest body,
			String page, String size);

	boolean isActiveProductTypeForPlatform(String code, String platformCode);

	ResponseEntity<String> findProductSuggestionForRetirement(RetirementProductSuggestionRequest request, String page,
			String size);

	List<ProductSuggestionMatchDTO> findProductSuggestionMatch(SurveyFormType type);

	List<ProductType> findDistinctByProductGroupIdAndProductPlatformCodeAndActive(String productGroupId,
			String productPlatformCode, Boolean active, Sort sort);

	List<ProductType> findDistinctByProductGroupIdsAndProductPlatformCodeAndActive(List<String> productGroupId,
			String productPlatformCode, Boolean active, Sort sort);

	ProductGroup findProductGroupById(String id);

	ProductType findProductTypeById(String id);

	List<ProductType> findActiveByGroupIdAndPlatform(Collection<String> productGroupId, String productPlatformCode);

	List<ProductType> findActiveProductTypeAndPlatform(Collection<String> productType, String productPlatformCode);

	List<ProductSimpleSelectionDTO> findProductSimpleSelection(String productType, String providerId,
			Boolean rsmActive);

	List<ProductGroupDTO> findProductGroupByValueIn(List<String> values, Sort sort);

	List<ProductGroupDTO> findProductGroupByIdIn(List<String> ids, Sort sort);

	List<ProductType> findProductTypeByIdIn(List<String> ids, Sort sort);

	List<ProductType> findByValueInAndProductPlatformCode(List<String> values, String platformCode, Sort sort);

	GroupAPIResponse findAllProductGroupByPlatformCode(String platformCode, Object o, Boolean rsmActive, Sort sort);

}
