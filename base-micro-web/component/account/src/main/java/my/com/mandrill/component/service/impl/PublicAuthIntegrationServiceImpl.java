package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.PublicAuthentication;
import my.com.mandrill.component.dto.request.PublicAuthenticationUpdateRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.service.PublicAuthIntegrationService;
import my.com.mandrill.component.service.PublicAuthenticationService;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PublicAuthIntegrationServiceImpl implements PublicAuthIntegrationService {

	private final PublicAuthenticationService publicAuthenticationService;

	@Override
	public PublicAuthentication update(String id, PublicAuthenticationUpdateRequest request) {
		PublicAuthentication publicAuthentication = publicAuthenticationService.findById(id);

		if (!publicAuthentication.getIdentifier().equals(request.getIdentifier())) {
			checkConstraint(request.getIdentifier());
		}

		publicAuthentication.setIdentifier(request.getIdentifier());
		publicAuthentication.setApiKey(request.getApiKey());
		publicAuthentication = publicAuthenticationService.save(publicAuthentication);
		return publicAuthentication;
	}

	@Override
	public void checkConstraint(String identifier) {
		if (publicAuthenticationService.existsByIdentifier(identifier)) {
			throw new BusinessException(ErrorCodeEnum.IDENTIFIER_EXISTED);
		}
	}

	@Override
	public List<PublicAuthentication> findAll() {
		return publicAuthenticationService.findAll();
	}

}
