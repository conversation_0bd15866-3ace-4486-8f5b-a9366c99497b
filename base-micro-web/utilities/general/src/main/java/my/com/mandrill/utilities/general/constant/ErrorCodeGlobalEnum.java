package my.com.mandrill.utilities.general.constant;

import lombok.Getter;
import my.com.mandrill.utilities.general.exception.ExceptionEnum;

@Getter
public enum ErrorCodeGlobalEnum implements ExceptionEnum {

	RUNTIME_EXCEPTION("GBL9999", "This is unexpected error. Contact <EMAIL>"),
	UNKNOWN_HOST_EXCEPTION("GBL0001", "This is unexpected error. Contact <EMAIL>"),
	INVALID_ARGUMENT("GBL0002", "This is unexpected error. Contact <EMAIL>"),
	// Access Related Error
	HTTP_MESSAGE_NOT_READABLE("GBL0003", "This is unexpected error. Contact <EMAIL>"),
	HTTP_ACCESS_DENIED("GBL0004", "This is unexpected error. Contact <EMAIL>"),
	WRONG_CREDENTIALS("GBL0005", "Incorrect credentials"),

	// DB Related Error
	CONSTRAINT_VIOLATION("GBL0006", "This is unexpected error. Contact <EMAIL>"),
	ENTITY_NOT_FOUND("GBL0007", "Record not found"),
	CANNOT_CREATE_TRANSACTION_EXCEPTION("GBL0016", "Connection timeout. Please try again later."),

	// Generic Error
	USER_NOT_VERIFIED("GBL0008", "This is unexpected error. Contact <EMAIL>"),
	NOT_SUPPORTED("GBL0009", "This is unexpected error. Contact <EMAIL>"),
	FAILED_TO_EXTRACT_PUBLIC_KEY("GBL0010", "This is unexpected error. Contact <EMAIL>"),
	FAILED_TO_VERIFY_SIGNATURE("GBL0011", "This is unexpected error. Contact <EMAIL>"),
	INVALID_PHONE_NUMBER("GBL0012", "Incorrect mobile number"),
	ACCOUNT_DELETED("GBL0013", "Account Deleted Recently",
			"The account deletion is in progress. If you wish to cancel the deletion, please send an <NAME_EMAIL> to reactivate the account."),
	FAILED_TO_FETCH_LOCK_EXCEPTION("GBL0014", "This is unexpected error. Contact <EMAIL>"),
	ACCOUNT_DOES_NOT_EXIST("GBL0015", "Account Does Not Exist",
			"We can’t find your account. Please check the mobile number or create a new account. If you recently deleted your account, email <EMAIL> to reactivate it."),
	INVALID_VERSION("GBL0016", "Update MoneyX App"),
	PIN_ALREADY_CREATED("GBL0017", "Password created! Use forget password to reset"),
	INVALID_PASSWORD("GBL0018", "Incorrect password. Click forgot password to reset password"),
	INVALID_PIN("GBL0019", "Incorrect password."), TOKEN_REVOKED("GBL0020", "Token revoked. Please re-login"),
	UNAUTHORIZED("GBL0020", "Unauthorized"), INVALID_REFERRAL_CODE("GBL0021", "Invalid Referral Code"),
	FAILED_TO_PROCESS_WITHDRAWAL("GBL0022", "Failed to process rsm withdrawal"),
	PAYMENT_ACCOUNT_NOT_APPROVED_YET("GBL0023", "Payment account not approved yet"),
	EKYC_NOT_SUCCESS_YET("GBL0024", "E-Kyc not success yet"),
	POINT_BALANCE_NOT_ENOUGH("GBL0025", "Point balance not enough"),
	NOT_VALID_STATUS_FOR_WITHDRAWAL_UPDATE("GLB0026", "Not valid status for withdrawal update"),
	MISSING_REASON_FOR_FAILED_WITHDRAWAL("GLB0027", "Missing reason for failed withdrawal"),
	CAN_ONLY_UPDATE_STATUS_FOR_PENDING_WITHDRAWAL("GLB0028",
			"Please select only request(s) that have \"Pending\" Points Withdrawal Status."),
	BALANCE_NOT_REACH_MINIMUM_REQUIREMENT_FOR_WITHDRAWAL_PROCESS("GLB0029",
			"Balance need reach minimum requirement for withdrawal process"),
	CHART_OF_ACCOUNT_NOT_FOUND("GLB0030", "Chart of Account not found"),
	TOO_MANY_REQUEST("GLB0031", "Too many request"),
	REQUEST_VERIFICATION_FAILED("GLB0032", "Your request could not be verified. Please try again"),
	CANNOT_PERFORM_BULK_UPDATE_BECAUSE_SOME_SUCCESS("GLB0033",
			"Bulk update failed because some records are already in SUCCESS status or already attached and cannot be modified.");

	/**
	 * don't change GBL0013 AND GBL0015 code or description, unless there is the
	 * requirement to change it [UPDATE] requirement
	 * <a href="https://mandrill.atlassian.net/browse/PRJA-2087">PRJA-2087</a>
	 */

	private final String code;

	private final String description;

	private final String title;

	ErrorCodeGlobalEnum(String code, String description) {
		this.code = code;
		this.description = description;
		this.title = null;
	}

	ErrorCodeGlobalEnum(String code, String title, String description) {
		this.code = code;
		this.title = title;
		this.description = description;
	}

}