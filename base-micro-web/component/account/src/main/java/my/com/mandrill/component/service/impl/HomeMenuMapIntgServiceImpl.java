package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.HomeMenuDirectionEnum;
import my.com.mandrill.component.constant.HomeMenuTypeEnum;
import my.com.mandrill.component.constant.HomeSectionEnum;
import my.com.mandrill.component.domain.HomeMenu;
import my.com.mandrill.component.domain.HomeMenuGroup;
import my.com.mandrill.component.domain.HomeMenuMap;
import my.com.mandrill.component.domain.HomeMenuProduct;
import my.com.mandrill.component.dto.model.HomeMenuDTO;
import my.com.mandrill.component.dto.model.HomeMenuGroupDTO;
import my.com.mandrill.component.dto.model.HomeMenuMapDTO;
import my.com.mandrill.component.dto.request.HomeMenuMapCreateRequest;
import my.com.mandrill.component.dto.request.HomeMenuMapUpdateSeqRequest;
import my.com.mandrill.component.dto.response.HomeMenuMapClientResponse;
import my.com.mandrill.component.dto.response.HomeMenuProductResponse;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.service.RedisService;
import my.com.mandrill.utilities.general.util.NumberUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class HomeMenuMapIntgServiceImpl implements HomeMenuMapIntgService {

	private final HomeMenuMapService homeMenuMapService;

	private final HomeMenuService homeMenuService;

	private final HomeMenuIntgService homeMenuIntgService;

	private final HomeMenuGroupService homeMenuGroupService;

	private final RedisService redisService;

	@Override
	public HomeMenuMap create(HomeMenuMapCreateRequest homeMenuMapDTO) {
		HomeMenu homeMenu = homeMenuService.findById(homeMenuMapDTO.getHomeMenuId());
		HomeMenuGroup homeMenuGroup = homeMenuGroupService.findById(homeMenuMapDTO.getHomeMenuGroupId());
		HomeMenuMap homeMenuMap = toEntity(homeMenuMapDTO);
		Integer latestGroupSeq = homeMenuMapService.findLatestGroupSequence(homeMenuMapDTO.getSection());
		if (latestGroupSeq != null) {
			Integer groupSeqByGroupMenu = homeMenuMapService
					.findGroupSequenceByHomeMenuGroup(homeMenuMapDTO.getSection(), homeMenuGroup);
			if (groupSeqByGroupMenu != null) {
				homeMenuMap.setGroupSequence(NumberUtil.incrementOrDefault(groupSeqByGroupMenu, 1, 0));
				Integer latestMenuSeq = homeMenuMapService.findLatestMenuSequence(homeMenuMapDTO.getSection(),
						homeMenuGroup);
				homeMenuMap.setMenuSequence(NumberUtil.incrementOrDefault(latestMenuSeq, 1, 0));
			}
			else {
				homeMenuMap.setGroupSequence(latestGroupSeq + 1);
			}
		}
		homeMenuMap.setHomeMenu(homeMenu);
		homeMenuMap.setHomeMenuGroup(homeMenuGroup);
		return homeMenuMapService.create(homeMenuMap);
	}

	private HomeMenuMap toEntity(HomeMenuMapCreateRequest homeMenuMapDTO) {
		HomeMenuMap homeMenuMap = new HomeMenuMap();
		homeMenuMap.setSection(homeMenuMapDTO.getSection());
		homeMenuMap.setGroupSequence(1);
		homeMenuMap.setMenuSequence(1);
		return homeMenuMap;
	}

	@Override
	public List<HomeMenuMapClientResponse> findBySectionEnum(HomeSectionEnum homeSectionEnum) {
		String cacheKey = CacheKey.HOME_MENU_MAP + homeSectionEnum.name();

		Optional<List<HomeMenuMapClientResponse>> cached = redisService.getFromValue(cacheKey, new TypeReference<>() {
		});
		if (cached.isPresent()) {
			log.info("home menu served by cache");
			return cached.get();
		}

		List<HomeMenu> allMenus = homeMenuService.findAllWithParent();
		Map<String, HomeMenuDTO> menuDTOMap = allMenus.stream()
				.collect(Collectors.toMap(HomeMenu::getId, this::toDTOOptimized));

		Map<String, List<HomeMenuDTO>> parentIdToChildren = menuDTOMap.values().stream()
				.filter(menu -> menu.getParent() != null)
				.collect(Collectors.groupingBy(menu -> menu.getParent().getId()));

		menuDTOMap.values().forEach(menu -> {
			List<HomeMenuDTO> children = parentIdToChildren.getOrDefault(menu.getId(), new ArrayList<>());
			children.sort(Comparator.comparingInt(c -> c.getSequence() != null ? c.getSequence() : Integer.MAX_VALUE));
			menu.setChild(children);
		});

		List<HomeMenuMap> homeMenuMaps = homeMenuMapService.findBySection(homeSectionEnum);

		Map<String, HomeMenuMapClientResponse> groupedResponses = homeMenuMaps.stream()
				.collect(Collectors.toMap(map -> map.getHomeMenuGroup().getId(), map -> {
					HomeMenuGroupDTO groupDTO = this.toDTOOptimized(map.getHomeMenuGroup());
					groupDTO.setSequence(map.getGroupSequence());

					HomeMenuDTO homeMenuDTO = menuDTOMap.get(map.getHomeMenu().getId());
					homeMenuDTO.setSequence(map.getMenuSequence());

					HomeMenuMapClientResponse response = new HomeMenuMapClientResponse();
					response.setMenuGroup(groupDTO);
					response.setSubmenus(new ArrayList<>(List.of(homeMenuDTO)));
					return response;
				}, (existing, incoming) -> {
					HomeMenuDTO homeMenuDTO = incoming.getSubmenus().get(0);
					existing.getSubmenus().add(homeMenuDTO);
					return existing;
				}, LinkedHashMap::new));

		List<HomeMenuMapClientResponse> result = new ArrayList<>(groupedResponses.values());
		result.sort(
				Comparator.comparingInt(resp -> resp.getMenuGroup() != null && resp.getMenuGroup().getSequence() != null
						? resp.getMenuGroup().getSequence() : Integer.MAX_VALUE));

		redisService.putToValue(cacheKey, result, Duration.ofMinutes(5));

		return result;
	}

	public HomeMenuDTO toDTOOptimized(HomeMenu homeMenu) {
		HomeMenuDTO dto = HomeMenuDTO.builder().id(homeMenu.getId()).name(homeMenu.getName()).code(homeMenu.getCode())
				.actionGroup(homeMenu.getActionGroup()).description(homeMenu.getDescription())
				.state(homeMenu.getState()).sequence(null).build();

		if (Objects.nonNull(homeMenu.getParent())) {
			dto.setParent(HomeMenuDTO.builder().id(homeMenu.getParent().getId()).name(homeMenu.getParent().getName())
					.code(homeMenu.getParent().getCode()).state(homeMenu.getParent().getState()).build());
		}

		if (CollectionUtils.isNotEmpty(homeMenu.getHomeMenuMaps())) {
			dto.setGroupIds(
					homeMenu.getHomeMenuMaps().stream().map(map -> map.getHomeMenuGroup().getId()).distinct().toList());
		}

		if (CollectionUtils.isNotEmpty(homeMenu.getHomeMenuProduct())) {
			List<HomeMenuProductResponse> productResponses = homeMenu.getHomeMenuProduct().stream()
					.filter(p -> p.getParent() == null).map(product -> {
						HomeMenuProductResponse response = MapStructConverter.MAPPER.toHomeMenuProductResponse(product);
						if (CollectionUtils.isNotEmpty(product.getSubProducts())) {
							List<HomeMenuProduct> sortedSubProducts = product.getSubProducts().stream()
									.sorted(Comparator.comparingInt(
											sp -> sp.getSequence() != null ? sp.getSequence() : Integer.MAX_VALUE))
									.toList();
							response.setSubProduct(
									MapStructConverter.MAPPER.toHomeMenuProductResponse(sortedSubProducts));
						}
						return response;
					}).toList();

			dto.setHomeMenuProduct(productResponses);
		}

		return dto;
	}

	public HomeMenuGroupDTO toDTOOptimized(HomeMenuGroup homeMenuGroup) {
		if (homeMenuGroup == null)
			return null;

		HomeMenuGroupDTO dto = new HomeMenuGroupDTO();
		dto.setId(homeMenuGroup.getId());
		dto.setCode(homeMenuGroup.getCode());
		dto.setNameEn(homeMenuGroup.getNameEn());
		dto.setNameMy(homeMenuGroup.getNameMy());
		dto.setNameCn(homeMenuGroup.getNameCn());

		if (homeMenuGroup.getParent() != null) {
			HomeMenuGroup parent = homeMenuGroup.getParent();
			HomeMenuGroupDTO parentDTO = new HomeMenuGroupDTO();
			parentDTO.setId(parent.getId());
			parentDTO.setCode(parent.getCode());
			parentDTO.setNameEn(parent.getNameEn());
			parentDTO.setNameMy(parent.getNameMy());
			parentDTO.setNameCn(parent.getNameCn());
			dto.setParent(parentDTO);
		}

		return dto;
	}

	@Override
	public Page<HomeMenuMap> findAll(HomeSectionEnum section, String menuCode, String groupCode, Pageable pageable) {
		return homeMenuMapService.findAll(section, menuCode, groupCode, pageable);
	}

	@Override
	public HomeMenuMap findById(String id) {
		return homeMenuMapService.findById(id);
	}

	@Override
	public void deleteProcess(String id) {
		HomeMenuMap homeMenuMap = homeMenuMapService.findById(id);

		// re-arrange menu sequence if deleted menu sequence have greater sequence
		List<HomeMenuMap> updateList = homeMenuMapService.findAllBySectionAndHomeMenuGroupAndMenuSequenceGreaterThan(
				homeMenuMap.getSection(), homeMenuMap.getHomeMenuGroup(), homeMenuMap.getMenuSequence());
		if (CollectionUtils.isNotEmpty(updateList)) {
			// Step 1: Sort the list by MenuSequence
			updateList.sort(Comparator.comparingInt(HomeMenuMap::getMenuSequence));

			// Step 2: Determine the start sequence
			int currentSeq = updateList.get(0).getMenuSequence() - 1;

			// Step 3: Reorder the sequences
			for (HomeMenuMap menuMap : updateList) {
				menuMap.setMenuSequence(currentSeq);
				currentSeq++; // Increment the sequence for the next item
			}
			homeMenuMapService.saveAll(updateList);
		}

		// re-arrange group sequence if deleted menu sequence have greater sequence
		List<HomeMenuMap> groupMenus = homeMenuMapService
				.findAllBySectionAndGroupSequenceGreaterThan(homeMenuMap.getSection(), homeMenuMap.getGroupSequence());
		if (CollectionUtils.isNotEmpty(groupMenus)) {
			// Step 1: Sort the list by MenuSequence
			groupMenus.sort(Comparator.comparingInt(HomeMenuMap::getGroupSequence));

			// Step 3: Reorder the sequences
			for (HomeMenuMap menuMap : groupMenus) {
				menuMap.setGroupSequence(menuMap.getGroupSequence() - 1);
			}
			homeMenuMapService.saveAll(groupMenus);
		}
		homeMenuMapService.delete(homeMenuMap);
	}

	@Override
	public HomeMenuMap update(String id, HomeMenuMapDTO homeMenuMapDTO) {
		HomeMenuMap existData = homeMenuMapService.findById(id);
		HomeMenu homeMenu = homeMenuService.findById(homeMenuMapDTO.getHomeMenuId());
		HomeMenuGroup homeMenuGroup = homeMenuGroupService.findById(homeMenuMapDTO.getHomeMenuGroupId());
		existData.setHomeMenu(homeMenu);
		existData.setHomeMenuGroup(homeMenuGroup);
		existData.setSection(homeMenuMapDTO.getSection());
		existData.setMenuSequence(homeMenuMapDTO.getMenuSequence());
		existData.setGroupSequence(homeMenuMapDTO.getGroupSequence());
		return homeMenuMapService.create(existData);
	}

	@Transactional
	@Override
	public HomeMenuMap updateSeq(String id, HomeMenuMapUpdateSeqRequest request) {
		HomeMenuMap existData = homeMenuMapService.findById(id);
		if (HomeMenuTypeEnum.MODULE_GROUP.equals(request.getType())) {
			if (HomeMenuDirectionEnum.UP.equals(request.getDirection())) {
				HomeMenuMap homeMenuPrev = homeMenuMapService.findHomeMenuBeforeGroupSequence(existData.getSection(),
						existData.getGroupSequence());
				swapGroupSequence(existData, homeMenuPrev);
			}
			else {
				HomeMenuMap homeMenuAfter = homeMenuMapService.findHomeMenuAfterGroupSequence(existData.getSection(),
						existData.getGroupSequence());
				swapGroupSequence(existData, homeMenuAfter);
			}
		}
		else {
			if (HomeMenuDirectionEnum.UP.equals(request.getDirection())) {
				HomeMenuMap homeMenuPrev = homeMenuMapService.findHomeMenuBeforeMenuSequence(existData.getSection(),
						existData.getHomeMenuGroup(), existData.getMenuSequence());
				swapMenuSequence(existData, homeMenuPrev);
			}
			else {
				HomeMenuMap homeMenuAfter = homeMenuMapService.findHomeMenuAfterMenuSequence(existData.getSection(),
						existData.getHomeMenuGroup(), existData.getMenuSequence());
				swapMenuSequence(existData, homeMenuAfter);
			}
		}
		return existData;
	}

	private void swapMenuSequence(HomeMenuMap existData, HomeMenuMap homeMenu) {
		if (homeMenu != null) {
			List<HomeMenuMap> updateList = new ArrayList<>();
			int tempMenuSeq = existData.getMenuSequence();
			existData.setMenuSequence(homeMenu.getMenuSequence());
			homeMenu.setMenuSequence(tempMenuSeq);
			updateList.add(existData);
			updateList.add(homeMenu);
			homeMenuMapService.saveAll(updateList);
		}
	}

	private void swapGroupSequence(HomeMenuMap existData, HomeMenuMap homeMenu) {
		if (homeMenu != null) {
			List<HomeMenuMap> existList = homeMenuMapService.findAllBySectionAndHomeMenuGroupAndGroupSequence(
					existData.getSection(), existData.getHomeMenuGroup(), existData.getGroupSequence());
			List<HomeMenuMap> swapList = homeMenuMapService.findAllBySectionAndHomeMenuGroupAndGroupSequence(
					homeMenu.getSection(), homeMenu.getHomeMenuGroup(), homeMenu.getGroupSequence());

			if (CollectionUtils.isNotEmpty(swapList) && CollectionUtils.isNotEmpty(existList)) {
				// Step 1: Extract the sequences from both lists
				int existSeq = existList.get(0).getGroupSequence();
				int swapSeq = swapList.get(0).getGroupSequence();

				// Step 2: Swap the sequences
				for (HomeMenuMap existMap : existList) {
					existMap.setGroupSequence(swapSeq);
				}

				for (HomeMenuMap swapMap : swapList) {
					swapMap.setGroupSequence(existSeq);
				}

				// Step 3: Save the updated entities back to the database
				homeMenuMapService.saveAll(existList);
				homeMenuMapService.saveAll(swapList);
			}
		}
	}

	@Override
	public List<HomeMenuProductResponse> findAllByHomeMenuCode(String menuCode) {
		return homeMenuIntgService.findAllByHomeMenuCode(menuCode);
	}

}
