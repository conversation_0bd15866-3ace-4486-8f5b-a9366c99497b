package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.utilities.feign.dto.SystemConfigurationDTO;

public interface AccountSystemConfigurationService {

	SystemConfigurationDTO createDefaultSystemConfiguration(Institution institution);

	Long getFailLoginAttempt(AppUser user);

	Integer getMaximumNumberOfDevicesPerUser(User user);

	Long getSignatureChallengeExpiredTime(User user);

	Long getTargetedRegistered();

}
