package my.com.mandrill.component.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;

import static my.com.mandrill.utilities.general.constant.KafkaTopic.PRE_PAID_TOP_UP_INBOX;
import static my.com.mandrill.utilities.general.constant.KafkaTopic.PRE_PAID_TOP_UP_REMINDER;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class KafkaTopicConfig {

	public static final String GROUP = "payment";

	public static final String PAYMENT_RESET_RUNNING_NUMBER = "payment-reset-running-number";

	@Bean
	public NewTopic resetRunningNumber() {
		return TopicBuilder.name(PAYMENT_RESET_RUNNING_NUMBER).partitions(KafkaTopic.LOW_PARTITIONS.getPartitions())
				.replicas(KafkaTopic.LOW_PARTITIONS.getReplicas()).build();
	}

	@Bean
	public NewTopic topUpPrePaidReminder() {
		return TopicBuilder.name(PRE_PAID_TOP_UP_REMINDER).partitions(KafkaTopic.LOW_PARTITIONS.getPartitions())
				.replicas(KafkaTopic.LOW_PARTITIONS.getReplicas()).build();
	}

	@Bean
	public NewTopic topUpPrePaidInbox() {
		return TopicBuilder.name(PRE_PAID_TOP_UP_INBOX).partitions(KafkaTopic.LOW_PARTITIONS.getPartitions())
				.replicas(KafkaTopic.LOW_PARTITIONS.getReplicas()).build();
	}

	@Bean
	public NewTopic topUpPreResolveFailed() {
		return TopicBuilder.name(KafkaTopic.RESOLVE_FAILED_PREPAID_TOP_UP)
				.partitions(KafkaTopic.LOW_PARTITIONS.getPartitions()).replicas(KafkaTopic.LOW_PARTITIONS.getReplicas())
				.build();
	}

}
