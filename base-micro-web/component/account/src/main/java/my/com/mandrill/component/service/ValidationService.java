package my.com.mandrill.component.service;

import my.com.mandrill.component.constant.DashboardDateType;
import my.com.mandrill.component.domain.EpfContribution;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.request.EpfContributionRequest;
import my.com.mandrill.component.dto.request.HomeMenuProductRequest;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.LocalDate;
import java.time.Year;
import java.time.YearMonth;
import java.util.List;
import java.util.Set;

public interface ValidationService {

	void validateCurrentLoginAndInstitution(String curIns);

	void validateDeleteAuthority(String authorityId);

	void validateDeleteSegment(String segmentId);

	void validateSegmentExist(String code, String name);

	void validateInstitutionExist(String name);

	void validateIncomeTypeExist(String code);

	void validateDeleteIncomeType(String incomeTypeId);

	Institution validateInstitution(String currentInstitutionId);

	void validateCreateAdminUser(User user);

	void validateUpdateAdminUser(User existingUser, User user);

	void validateAuthority(User existingUser, User user, String institutionId);

	void validateUpdateUser(User existingUser, User user);

	void validateEmailBlacklist(String email);

	void validateDateType(@NonNull DashboardDateType dateType, @Nullable LocalDate dateFrom, @Nullable LocalDate dateTo,
			@Nullable YearMonth yearMonth);

	void validateAge(Integer userAge);

	List<EpfContribution> validateEpfContribution(List<EpfContributionRequest> requests, User user);

	void validateRegister(User user);

	void validateDate(@NonNull LocalDate dateFrom, @NonNull LocalDate dateTo);

	void validateWeekNumber(@NonNull Year year, int weekNumber);

	void validateMonth(int month);

	void validatePasscodeVerificationRequestKey(RequestKeyType keyType);

	void validateSignatureVerificationRequestKey(RequestKeyType keyType);

	void validateLoginDeviceBinding(String userId, String deviceId);

	boolean getDeviceBindingValidationFlag();

	void validateRegisterDeviceBinding(String deviceId);

	void validateDeleteHomeMenu(String id);

	void validateDeleteHomeMenuGroup(String id);

	boolean hasDeviceBinding(String userId);

	void validateHomeMenuProductRequest(String homeMenuId, Set<HomeMenuProductRequest> homeMenuProductRequestSet);

}
