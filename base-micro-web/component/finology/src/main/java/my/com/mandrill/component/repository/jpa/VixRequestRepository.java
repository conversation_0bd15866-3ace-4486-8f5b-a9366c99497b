package my.com.mandrill.component.repository.jpa;

import my.com.mandrill.component.domain.VixRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface VixRequestRepository extends JpaRepository<VixRequest, String> {

	Optional<VixRequest> findByUserIdAndRefCode(String userId, String refCode);

	Optional<VixRequest> findFirstByUserIdAndVehicleRegistrationNoOrderByCreatedDateDesc(String userId, String vixNo);

}
