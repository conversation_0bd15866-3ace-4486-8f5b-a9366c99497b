package my.com.mandrill.component.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import my.com.mandrill.utilities.core.audit.AuditSection;
import org.hibernate.Hibernate;

import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "product_type", uniqueConstraints = { @UniqueConstraint(columnNames = { "code" }) })
public class ProductType extends AuditSection {

	@Size(max = 255)
	@NotBlank
	@Column(name = "code", nullable = false)
	private String code;

	@Size(max = 255)
	@NotBlank
	@Column(name = "value", nullable = false)
	private String value;

	@NotNull
	@Column(name = "active", nullable = false, columnDefinition = "BOOLEAN DEFAULT TRUE")
	private Boolean active = true;

	@Size(max = 200)
	@Column(name = "label", length = 200)
	private String label;

	@ManyToOne
	@JoinColumn(name = "product_group_id")
	private ProductGroup productGroup;

	@ManyToOne
	@JoinColumn(name = "product_platform_id")
	private ProductPlatform productPlatform;

	@Column(name = "sequence")
	private Integer sequence;

	@Column(name = "sequence_v2")
	private Integer sequenceV2;

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		ProductType identityType = (ProductType) o;
		return getId() != null && Objects.equals(getId(), identityType.getId());
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

}
