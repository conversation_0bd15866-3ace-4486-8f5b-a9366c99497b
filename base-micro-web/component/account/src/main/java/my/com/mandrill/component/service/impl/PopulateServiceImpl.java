package my.com.mandrill.component.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.ExpenseDTO;
import my.com.mandrill.component.dto.model.IncomeDTO;
import my.com.mandrill.component.service.PopulateService;
import my.com.mandrill.utilities.feign.client.CommonFeignClient;
import my.com.mandrill.utilities.feign.client.NotificationFeignClient;
import my.com.mandrill.utilities.feign.dto.CountryDTO;
import my.com.mandrill.utilities.feign.dto.ReminderResponse;
import my.com.mandrill.utilities.feign.dto.StateDTO;
import my.com.mandrill.utilities.general.constant.CacheKey;
import my.com.mandrill.utilities.general.constant.ReminderType;
import my.com.mandrill.utilities.general.service.RedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.swing.plaf.nimbus.State;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PopulateServiceImpl implements PopulateService {

	private final CommonFeignClient commonFeignClient;

	private final NotificationFeignClient notificationFeignClient;

	private final RedisService redisService;

	@Override
	public void populateCountry(User user) {
		if (Objects.nonNull(user.getCountryId())) {
			CountryDTO country = getCountryById(user.getCountryId());
			user.setCountry(country);
			user.setCountryId(country.getId());
		}
	}

	@Override
	public void populateState(User user) {
		if (Objects.nonNull(user.getStateId())) {
			StateDTO state = getStateById(user.getStateId());
			user.setState(state);
			user.setStateId(state.getId());
		}
	}

	@Override
	public void populateAuthoritiesByInstitution(User user, final String institutionId) {
		// only return authorities base on institution
		user.setAuthorities(user.getAuthorities().stream().filter(a -> institutionId.equals(a.getInstitution().getId()))
				.collect(Collectors.toSet()));
	}

	@Override
	public void populateIncomeReminder(IncomeDTO incomeDTO) {
		try {
			ReminderResponse response = notificationFeignClient.findByReminderTypeAndDataId(ReminderType.INCOME,
					incomeDTO.getId());
			incomeDTO.setIsReminder(true);
			incomeDTO.setReminder(response);
		}
		catch (EntityNotFoundException e) {
			incomeDTO.setIsReminder(false);
		}
	}

	@Override
	public void populateExpenseReminder(ExpenseDTO expenseDto) {
		try {
			ReminderResponse response = notificationFeignClient.findByReminderTypeAndDataId(ReminderType.EXPENSE,
					expenseDto.getId());
			expenseDto.setIsReminder(true);
			expenseDto.setReminder(response);
		}
		catch (EntityNotFoundException e) {
			expenseDto.setIsReminder(false);
		}
	}

	@Override
	public CountryDTO getCountryById(String countryId) {
		if (StringUtils.isBlank(countryId)) {
			return null;
		}
		return redisService.getFromHash(CacheKey.COUNTRY_CACHE, countryId, CountryDTO.class).orElseGet(() -> {
			CountryDTO country = commonFeignClient.getCountry(countryId);
			redisService.putToHash(CacheKey.COUNTRY_CACHE, countryId, country);
			return country;
		});
	}

	@Override
	public StateDTO getStateById(String stateId) {
		if (StringUtils.isBlank(stateId)) {
			return null;
		}
		return redisService.getFromHash(CacheKey.STATE_CACHE, stateId, StateDTO.class).orElseGet(() -> {
			StateDTO stateDTO = commonFeignClient.getState(stateId);
			redisService.putToHash(CacheKey.STATE_CACHE, stateId, stateDTO);
			return stateDTO;
		});
	}

}
