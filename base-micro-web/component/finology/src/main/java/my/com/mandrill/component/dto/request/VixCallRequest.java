package my.com.mandrill.component.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VixCallRequest implements Serializable {

	@NotBlank
	private String vehicleRegistrationNo;

	@NotBlank
	private String identificationNo;

	@NotBlank
	private String idTypeCode;

	@NotBlank
	private String vehicleTypeId;

	private String postcode;

}
