package my.com.mandrill.component.dto.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Optional;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RsmHeaderDetailDTO {

	private BigDecimal c2cRevenueDist = BigDecimal.ZERO;

	private BigDecimal b2cRevenueDist = BigDecimal.ZERO;

	private BigDecimal b2b2cRevenueDist = BigDecimal.ZERO;

	private BigDecimal b2bRevenueDist = BigDecimal.ZERO;

	private BigDecimal nonReferralRevenueDist = BigDecimal.ZERO;

	private String externalB2bC2cNonReferralRevenueDist;

	private String externalB2b2cRevenueDist;

	public BigDecimal getC2cRevenueDist() {
		return Optional.ofNullable(c2cRevenueDist).orElse(BigDecimal.ZERO);
	}

	public BigDecimal getB2cRevenueDist() {
		return Optional.ofNullable(b2cRevenueDist).orElse(BigDecimal.ZERO);
	}

	public BigDecimal getB2b2cRevenueDist() {
		return Optional.ofNullable(b2b2cRevenueDist).orElse(BigDecimal.ZERO);
	}

	public BigDecimal getB2bRevenueDist() {
		return Optional.ofNullable(b2bRevenueDist).orElse(BigDecimal.ZERO);
	}

	public BigDecimal getNonReferralRevenueDist() {
		return Optional.ofNullable(nonReferralRevenueDist).orElse(BigDecimal.ZERO);
	}

	public BigDecimal getNullC2cRevenueDist() {
		return c2cRevenueDist;
	}

	public BigDecimal getNullB2cRevenueDist() {
		return b2cRevenueDist;
	}

	public BigDecimal getNullB2b2cRevenueDist() {
		return b2b2cRevenueDist;
	}

	public BigDecimal getNullB2bRevenueDist() {
		return b2bRevenueDist;
	}

	public BigDecimal getNullNonReferralRevenueDist() {
		return nonReferralRevenueDist;
	}

}
