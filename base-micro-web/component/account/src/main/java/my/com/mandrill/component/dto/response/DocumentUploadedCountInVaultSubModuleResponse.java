package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DocumentUploadedCountInVaultSubModuleResponse implements Serializable {

	private long myFinance = 0L;

	private long myIdentity = 0L;

	private long myInsurance = 0L;

	private long myLegalDocs = 0L;

	private long myProperty = 0L;

	private long myUtility = 0L;

	private long myVehicle = 0L;

}
