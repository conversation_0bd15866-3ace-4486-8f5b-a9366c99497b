package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.Authority;
import my.com.mandrill.component.domain.Institution;
import my.com.mandrill.component.domain.User;
import my.com.mandrill.component.dto.model.AuthenticateResultDTO;
import my.com.mandrill.component.dto.request.SignInRequest;
import my.com.mandrill.component.service.impl.AuthServiceImpl;
import my.com.mandrill.utilities.ciphers.AesCryptoUtil;
import my.com.mandrill.utilities.core.dto.model.UserInfoData;
import my.com.mandrill.utilities.core.security.jwt.TokenProvider;
import my.com.mandrill.utilities.general.constant.AccessTypeEnum;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import my.com.mandrill.utilities.general.constant.PasscodeType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

	@InjectMocks
	AuthServiceImpl authService;

	@Mock
	AuthenticationManager authenticationManager;

	@Mock
	PasswordEncoder passwordEncoder;

	@Mock
	TokenProvider tokenProvider;

	@Mock
	UserService userService;

	@InjectMocks
	AesCryptoUtil aesCryptoUtil;

	Authentication mockAuthentication;

	SignInRequest mockSignInRequest;

	User mockUser;

	List<User> mockListUser;

	Authority mockAuthority;

	Institution mockInstitution;

	@BeforeEach
	void setup() {
		ReflectionTestUtils.setField(aesCryptoUtil, "AES_KEY", "NqhZ1t+GDdtaDXni2BNcDwRYIJ/T4mwc");
		ReflectionTestUtils.setField(aesCryptoUtil, "IV_KEY", "57ad289f-3def-4c");

		mockAuthentication = Mockito.mock(Authentication.class);
		mockUser = new User();
		mockUser.setPhoneVerified(true);
		mockAuthority = new Authority();
		mockInstitution = new Institution();
		mockInstitution.setActive(true);
		mockAuthority.setInstitution(mockInstitution);
		mockAuthority.setActive(true);
		Set<Authority> authoritySet = new HashSet<>();

		authoritySet.add(mockAuthority);
		mockUser.setAuthorities(authoritySet);
		mockUser.setDeleted(false);
		mockUser.setActive(true);
		mockUser.setId(UUID.randomUUID().toString());

		mockSignInRequest = new SignInRequest();
		mockSignInRequest.setUsername("user");
		mockSignInRequest.setPassword("password");

		mockListUser = new ArrayList<>();
		mockListUser.add(mockUser);
	}

	// @Test
	// void authenticateUser_Success() {
	// Mockito.when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
	// Mockito.when(mockAuthentication.getName()).thenReturn("valid");
	// UserInfoData mockUserInfo =
	// UserInfoData.builder().userId(UUID.randomUUID().toString()).refNo("userRefNo")
	// .build();
	//
	// Mockito.when(userService.getUserInfoByUsernameAndLoginType("valid",
	// LoginTypeEnum.USER))
	// .thenReturn(mockUserInfo);
	// Mockito.when(tokenProvider.createToken(any(), any(),
	// anyBoolean())).thenReturn("any");
	//
	// AuthenticateResultDTO result = authService.authenticateUser(mockSignInRequest,
	// AccessTypeEnum.EMAIL,
	// LoginTypeEnum.USER, PasscodeType.PASSWORD);
	// assertThat(result).isNotNull();
	// Assertions.assertEquals(mockUserInfo.getUserId(), result.getUserId());
	// Assertions.assertEquals("any", result.getAccessToken());
	// }
	//
	// @Test
	// void authenticateUser_ThrowInvalid() {
	// Mockito.when(authenticationManager.authenticate(any())).thenReturn(mockAuthentication);
	// Mockito.when(mockAuthentication.getName()).thenThrow(BadCredentialsException.class);
	//
	// Assertions.assertThrows(BadCredentialsException.class, () ->
	// authService.authenticateUser(mockSignInRequest,
	// AccessTypeEnum.EMAIL, LoginTypeEnum.USER, PasscodeType.PASSWORD));
	//
	// }

	@Test
	void processUserData_Success() {
		Mockito.when(userService.findByEmailAndLoginType(any(), any())).thenReturn(mockUser);

		User result = authService.processUserData(AccessTypeEnum.EMAIL.getCode(), LoginTypeEnum.USER, "username");

		assertThat(result).isEqualTo(mockUser);
	}

	@Test
	void processUserData_Success_1() {
		Mockito.when(userService.findByPhoneCountryAndPhoneNumberAndLoginType(any(), any(), any()))
				.thenReturn(mockUser);

		User result = authService.processUserData(AccessTypeEnum.MOBILE.name(), LoginTypeEnum.USER, "+628000000000");

		assertThat(result).isEqualTo(mockUser);
	}

	@Test
	void authenticateUserList_Success() {
		// Mockito.doNothing().when(userService).failLoginCheck(any(), anyBoolean());
		// Mockito.when(passwordEncoder.matches(any(), any())).thenReturn(true);
		//
		// List<String> result = authService.authenticateUser(mockUser, "password");
		// assertThat(result.size()).isEqualTo(0);
	}

	@Test
	void checkPassword_Success() {
		Mockito.when(passwordEncoder.matches(any(), any())).thenReturn(true);

		Assertions.assertDoesNotThrow(() -> authService.checkPassword(new User(), "password"));
	}

}
