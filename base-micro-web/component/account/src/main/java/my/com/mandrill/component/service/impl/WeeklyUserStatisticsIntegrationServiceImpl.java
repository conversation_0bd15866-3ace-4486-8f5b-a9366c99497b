package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.DashboardDateType;
import my.com.mandrill.component.constant.UserStatisticType;
import my.com.mandrill.component.domain.WeeklyUserStatistics;
import my.com.mandrill.component.dto.model.AppDownload;
import my.com.mandrill.component.service.*;
import my.com.mandrill.component.util.DateUtil;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class WeeklyUserStatisticsIntegrationServiceImpl implements WeeklyUserStatisticsIntegrationService {

	private final BigQueryService bigQueryService;

	private final WeeklyUserStatisticsService weeklyUserStatisticsService;

	private final DashboardActivityService dashboardActivityService;

	private final UserService userService;

	public List<WeeklyUserStatistics> findForWeeks(Set<Integer> requestedWeeks, Year year, UserStatisticType type,
			ZoneId zone) {
		int currentWeek = DateUtil.getNumberOfWeeksInYear(year);
		int currentYear = LocalDate.now().getYear();
		int startWeek = (year.equals(Year.of(DashboardDateType.APP_STARTING_DATE.getYear())))
				? DateUtil.getWeekNumberOfDate(DashboardDateType.APP_STARTING_DATE)
				: DateUtil.DASHBOARD_FIRST_WEEK_VALUE;

		Set<Integer> weeks = requestedWeeks;
		if (Objects.isNull(weeks)) {
			weeks = IntStream.rangeClosed(startWeek, currentWeek).boxed().collect(Collectors.toSet());
		}

		return weeks.stream().map(week -> {
			Optional<WeeklyUserStatistics> existingStatistics = weeklyUserStatisticsService
					.findByWeekAndYearAndType(week, year, type);

			DateUtil.DateRange dateRange = DateUtil.DateRange.from(week, year);
			LocalDate startDate = dateRange.from();
			LocalDate endDate = dateRange.to();

			boolean isCurrentYearAndWeek = year.getValue() == currentYear && week == currentWeek;

			// loongyeat: If the week is the current week, pull directly from BigQuery/DB
			// regardless, since the current
			// week's data can still change.
			return switch (type) {
				case DOWNLOADS -> {
					if (existingStatistics.isEmpty() || !existingStatistics.get().isSynchronized()) {
						AppDownload downloads = bigQueryService.countAppDownloadBetweenDate(startDate, endDate);
						WeeklyUserStatistics statistics = new WeeklyUserStatistics();
						existingStatistics
								.ifPresent(weeklyUserStatistics -> statistics.setId(weeklyUserStatistics.getId()));
						statistics.setWeek(week);
						statistics.setYear(year);
						statistics.setType(type);
						statistics.setSynchronized(!isCurrentYearAndWeek);

						Long install = downloads.getInstall();
						if (Objects.isNull(install))
							install = 0L;

						statistics.setValue(install);
						yield weeklyUserStatisticsService.save(statistics);
					}
					else {
						yield existingStatistics.get();
					}
				}
				/**
				 * <p>
				 * For case USER_REGISTERED and USER_DELETED, count the user directly in
				 * User table to get the most updated number.
				 * </p>
				 * @param UserStatisticType, startDateInstant, endDateInstant, zone
				 * @return WeeklyUserStatistics
				 * @since 2.4.0
				 *
				 */
				case USER_REGISTERED -> {
					Instant startDateInstant = startDate.atStartOfDay(zone).toInstant();
					Instant endDateInstant = endDate.atTime(LocalTime.MAX).atZone(zone).toInstant();

					// TODO: This repository call is from User table. Should move it
					// to UserRepository.
					long registeredUser = dashboardActivityService.countByDateBetweenAndLoginType(startDateInstant,
							endDateInstant, LoginTypeEnum.USER);

					WeeklyUserStatistics statistics = new WeeklyUserStatistics();
					statistics.setWeek(week);
					statistics.setYear(year);
					statistics.setType(type);
					statistics.setValue(registeredUser);

					yield statistics;
				}
				case USER_DELETED -> {
					Instant startDateInstant = startDate.atStartOfDay(zone).toInstant();
					Instant endDateInstant = endDate.atTime(LocalTime.MAX).atZone(zone).toInstant();

					long deletedUser = userService.countByDeletedAndLoginType(startDateInstant, endDateInstant,
							LoginTypeEnum.USER);

					WeeklyUserStatistics statistics = new WeeklyUserStatistics();
					statistics.setWeek(week);
					statistics.setYear(year);
					statistics.setType(type);
					statistics.setValue(deletedUser);

					yield statistics;
				}
			};
		}).toList();
	}

	@Async
	@Override
	public void synchronize(Year year, ZoneId zone) {
		int currentWeek = DateUtil.getNumberOfWeeksInYear(year);
		this.findForWeeks(IntStream.rangeClosed(1, currentWeek).boxed().collect(Collectors.toSet()), year,
				UserStatisticType.DOWNLOADS, zone);
	}

}
