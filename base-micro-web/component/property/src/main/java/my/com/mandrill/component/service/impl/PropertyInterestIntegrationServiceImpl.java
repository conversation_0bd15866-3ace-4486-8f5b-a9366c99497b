package my.com.mandrill.component.service.impl;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Developer;
import my.com.mandrill.component.domain.Project;
import my.com.mandrill.component.domain.ProjectUnit;
import my.com.mandrill.component.domain.PropertyInterest;
import my.com.mandrill.component.dto.model.PropertyInterestPageDTO;
import my.com.mandrill.component.service.PropertyInterestIntegrationService;
import my.com.mandrill.component.service.PropertyInterestService;
import my.com.mandrill.utilities.feign.dto.model.UserInterestRecordRSMPaginationDTO;
import my.com.mandrill.utilities.feign.dto.request.DateRangeRequest;
import my.com.mandrill.utilities.feign.dto.request.RSMLeadRequest;
import my.com.mandrill.utilities.general.constant.ApplicationType;
import my.com.mandrill.utilities.general.constant.TimeConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class PropertyInterestIntegrationServiceImpl implements PropertyInterestIntegrationService {

	private final PropertyInterestService propertyInterestService;

	@Override
	public Page<PropertyInterestPageDTO> find(DateRangeRequest query, Pageable pageable) {
		Instant startDate = Optional.ofNullable(query.getStartDate())
				.map(v -> v.atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant()).orElse(null);

		Instant endDate = Optional.ofNullable(query.getEndDate())
				.map(v -> v.atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant()).orElse(null);

		Page<PropertyInterest> data = propertyInterestService.findAll(startDate, endDate, pageable);
		return data.map(MapStructConverter.MAPPER::toPropertyInterestPageDTO);
	}

	@Override
	public List<PropertyInterestPageDTO> export(DateRangeRequest query) {

		Instant startDate = Optional.ofNullable(query.getStartDate())
				.map(v -> v.atStartOfDay(TimeConstant.DEFAULT_ZONE_ID).toInstant()).orElse(null);

		Instant endDate = Optional.ofNullable(query.getEndDate())
				.map(v -> v.atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant()).orElse(null);

		List<PropertyInterest> data = propertyInterestService.findAll(startDate, endDate);
		return data.stream().map(MapStructConverter.MAPPER::toPropertyInterestPageDTO).toList();
	}

	@Override
	public Page<UserInterestRecordRSMPaginationDTO> rsmLead(Pageable pageable, RSMLeadRequest request) {

		Specification<PropertyInterest> spec = this.specificationPropertyInterest(request);

		Page<PropertyInterest> interestPage = propertyInterestService.findAll(spec, pageable);

		return interestPage.map(record -> {
			UserInterestRecordRSMPaginationDTO result = MapStructConverter.MAPPER
					.toUserInterestRecordRSMPaginationDTO(record);

			result.setProductCategory(ApplicationType.PROPERTY.getName());
			result.setProductType(ApplicationType.PROPERTY.getName());
			result.setApplicationType(ApplicationType.PROPERTY);
			result.setApplicationTypeName(ApplicationType.PROPERTY.getName());
			return result;
		});

	}

	private Specification<PropertyInterest> specificationPropertyInterest(RSMLeadRequest request) {
		return (root, query, builder) -> {
			List<Predicate> predicates = new ArrayList<>();

			// Join table
			Join<PropertyInterest, Project> projectJoin = root.join("project", JoinType.LEFT);
			Join<Project, Developer> developerJoin = projectJoin.join("developer", JoinType.LEFT);
			Join<PropertyInterest, ProjectUnit> projectUnitJoin = root.join("projectUnit", JoinType.LEFT);

			// Filter
			if (StringUtils.isNotBlank(request.getUserRefNo())) {
				predicates.add(builder.like(builder.lower(root.get("userRefNo")),
						"%" + request.getUserRefNo().toLowerCase() + "%"));
			}

			if (StringUtils.isNotEmpty(request.getFullName())) {
				predicates.add(
						builder.like(builder.lower(root.get("name")), "%" + request.getFullName().toLowerCase() + "%"));
			}

			if (CollectionUtils.isNotEmpty(request.getRsmRelation())) {
				predicates.add(root.get("rsmRelation").in(request.getRsmRelation()));
			}

			if (CollectionUtils.isNotEmpty(request.getRsmStatus())) {
				predicates.add(root.get("rsmStatus").in(request.getRsmStatus()));
			}

			if (CollectionUtils.isNotEmpty(request.getRsmCommissionAttached())) {
				predicates.add(root.get("rsmCommissionAttached").in(request.getRsmCommissionAttached()));
			}

			if (CollectionUtils.isNotEmpty(request.getRsmEligible())) {
				predicates.add(root.get("rsmEligible").in(request.getRsmEligible()));
			}

			if (StringUtils.isNotEmpty(request.getRefNo())) {
				predicates.add(builder.like(root.get("applicationId"), "%" + request.getRefNo() + "%"));
			}

			if (StringUtils.isNotEmpty(request.getProductName())) {
				predicates.add(builder.like(builder.lower(projectJoin.get("name")),
						"%" + request.getProductName().toLowerCase() + "%"));
			}

			if (CollectionUtils.isNotEmpty(request.getProviderIds())) {
				predicates.add(developerJoin.get("id").in(request.getProviderIds()));
			}

			if (Objects.nonNull(request.getDateType()) && Objects.nonNull(request.getStartDate())) {
				predicates.add(builder.greaterThan(root.get(request.getDateType().getFieldName()),
						request.getStartDate().atStartOfDay().atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant()));
			}

			if (Objects.nonNull(request.getDateType()) && Objects.nonNull(request.getEndDate())) {
				predicates.add(builder.lessThan(root.get(request.getDateType().getFieldName()),
						request.getEndDate().atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant()));
			}

			if (Objects.nonNull(request.getApplicationDate())) {
				final Instant startTimeFilter = request.getApplicationDate().atStartOfDay()
						.atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
				final Instant endTimeFilter = request.getApplicationDate().atTime(LocalTime.MAX)
						.atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
				predicates.add(builder.greaterThan(root.get("createdDate"), startTimeFilter));
				predicates.add(builder.lessThan(root.get("createdDate"), endTimeFilter));
			}

			if (query.getResultType() != Long.class) {
				root.fetch("project", JoinType.LEFT);
				root.fetch("project", JoinType.LEFT).fetch("developer", JoinType.INNER);
				root.fetch("projectUnit", JoinType.LEFT);
			}

			return builder.and(predicates.toArray(new Predicate[0]));
		};
	}

	@Override
	public List<UserInterestRecordRSMPaginationDTO> rsmLeadReport(RSMLeadRequest request) {
		List<PropertyInterest> interestList = propertyInterestService
				.findAll(this.specificationPropertyInterest(request));

		return interestList.stream().map(interest -> {
			UserInterestRecordRSMPaginationDTO result = MapStructConverter.MAPPER
					.toUserInterestRecordRSMPaginationDTO(interest);

			result.setProductCategory(ApplicationType.PROPERTY.getName());
			result.setProductType(ApplicationType.PROPERTY.getName());
			result.setApplicationType(ApplicationType.PROPERTY);
			result.setApplicationTypeName(ApplicationType.PROPERTY.getName());

			BigDecimal incomeAmount = result.getIncomeAmount() == null ? null
					: result.getIncomeAmount().setScale(2, RoundingMode.HALF_UP);
			result.setIncomeAmount(incomeAmount);

			BigDecimal cashbackAmount = result.getCashbackAmount() == null ? null
					: result.getCashbackAmount().setScale(2, RoundingMode.HALF_UP);
			result.setCashbackAmount(cashbackAmount);

			return result;
		}).toList();

	}

}
