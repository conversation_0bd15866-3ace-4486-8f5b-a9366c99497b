package my.com.mandrill.component.service;

import lombok.NonNull;
import my.com.mandrill.component.constant.UserStatisticType;
import my.com.mandrill.component.domain.WeeklyUserStatistics;

import java.time.Year;
import java.util.Optional;

public interface WeeklyUserStatisticsService {

	Optional<WeeklyUserStatistics> findByWeekAndYearAndType(Integer week, Year year, UserStatisticType type);

	WeeklyUserStatistics save(WeeklyUserStatistics statistics);

}
