package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.BaseProperties;
import my.com.mandrill.component.domain.Transaction;
import my.com.mandrill.component.dto.response.GKashResponse;
import my.com.mandrill.component.repository.jpa.TransactionRepository;
import my.com.mandrill.component.service.TransactionService;
import my.com.mandrill.component.util.SignatureUtil;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.util.RequestUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@Primary
@RequiredArgsConstructor
@Transactional(readOnly = true)
@ConditionalOnProperty(name = "base.payment-gateway.g-kash.enabled", havingValue = "true")
public class GKashTransactionServiceImpl implements TransactionService {

	private final BaseProperties baseProperties;

	private final ObjectMapper objectMapper;

	private final TransactionRepository transactionRepository;

	@Override
	@Transactional
	public Transaction saveAndFlush(Transaction transaction) {
		return transactionRepository.saveAndFlush(transaction);
	}

	@Override
	@Transactional
	public Transaction save(Transaction transaction) {
		return transactionRepository.save(transaction);
	}

	@Override
	public Transaction findById(String id, String userId) {
		return transactionRepository.findByIdAndUserId(id, userId)
				.orElseThrow(my.com.mandrill.component.exception.ExceptionPredicate.transactionNotfound(id));
	}

	@Override
	public Transaction findByIdEntityId(String entityId) {
		return transactionRepository.findFirstByEntityId(entityId)
				.orElseThrow(my.com.mandrill.component.exception.ExceptionPredicate.transactionNotfound(entityId));
	}

	@Override
	public Transaction findById(String id) {
		return transactionRepository.findById(id)
				.orElseThrow(my.com.mandrill.component.exception.ExceptionPredicate.transactionNotfound(id));
	}

	@Override
	public List<Transaction> findAll(String userId, List<EntityName> entityNames, Sort sort) {
		return transactionRepository.findByUserIdAndEntityNameIn(userId, entityNames, sort);
	}

	@Override
	public RedirectView response(Map<String, String> response, RedirectAttributes redirectAttributes,
			HttpServletRequest request) {
		GKashResponse gKashResponse = objectMapper.convertValue(response, GKashResponse.class);

		Optional<Transaction> transaction = transactionRepository.findByRefNo(gKashResponse.getCartId());

		if (transaction.isPresent()) {
			try {
				if (SignatureUtil.hashResponseSignature(transaction.get()).equals(gKashResponse.getSignature())) {
					redirectAttributes.addAttribute(AttributeConstant.ID, transaction.get().getId());
					redirectAttributes.addAttribute(AttributeConstant.REF_NO, transaction.get().getRefNo());
					return new RedirectView(baseProperties.getPaymentGateway().getGKash().getResponseUrl());
				}
			}
			catch (Exception e) {
				log.error("Error happened during response: {}", e.getMessage());
			}
		}
		else {
			log.warn("Unknown Transaction. Request: {}:{}, Data: {}", RequestUtil.getIpAddress(request),
					RequestUtil.getHost(request), gKashResponse);
		}
		return null;
	}

	@Override
	public List<Transaction> findByEntityName(String userId, EntityName entityName, String entityId, Sort sort) {
		return transactionRepository.findByUserIdAndEntityNameAndEntityId(userId, entityName, entityId, sort);
	}

	@Override
	public Optional<Transaction> findByRefNo(String refNo) {
		return transactionRepository.findByRefNo(refNo);
	}

	@Override
	public Page<Transaction> findAllTransaction(EntityName entityName, LocalDate startDate, LocalDate endDate,
			Pageable pageable) {
		Instant startTime = getStartTime(startDate);
		Instant endTime = getEndTime(endDate);

		return transactionRepository
				.findAllByEntityNameAndStatusAndDateCompletedGreaterThanEqualAndDateCompletedLessThanEqual(entityName,
						Status.SUCCESS, startTime, endTime, pageable);
	}

	@Override
	public List<Transaction> findAllTransactionForReport(EntityName entityName, LocalDate startDate,
			LocalDate endDate) {
		Instant startTime = getStartTime(startDate);
		Instant endTime = getEndTime(endDate);

		return transactionRepository
				.findAllByEntityNameAndStatusAndDateCompletedGreaterThanEqualAndDateCompletedLessThanEqual(entityName,
						Status.SUCCESS, startTime, endTime);
	}

	@Override
	public List<Transaction> findAllTransactionForReport(EntityName entityName, LocalDate startDate, LocalDate endDate,
			List<Status> statuses) {
		Instant startTime = getStartTime(startDate);
		Instant endTime = getEndTime(endDate);

		return transactionRepository
				.findAllByEntityNameAndStatusInAndCreatedDateGreaterThanEqualAndCreatedDateLessThanEqual(entityName,
						statuses, startTime, endTime);
	}

	@Override
	public Long countTransaction(EntityName entityName, LocalDate startDate, LocalDate endDate) {
		Instant startTime = getStartTime(startDate);
		Instant endTime = getEndTime(endDate);

		return transactionRepository
				.countByEntityNameAndStatusAndDateCompletedGreaterThanEqualAndDateCompletedLessThanEqual(entityName,
						Status.SUCCESS, startTime, endTime);
	}

	@Override
	public List<Transaction> findAllPrepaidTopUpHistory(String transactionId, String userId) {
		return transactionRepository.findByUserIdAndEntityNameAndStatusAndIdIsNot(userId, EntityName.ATX,
				Status.SUCCESS, transactionId);
	}

	@Override
	public Page<Transaction> findAllTransaction(EntityName entityName, List<Status> statuses, LocalDate startDate,
			LocalDate endDate, Pageable pageable) {
		Instant startTime = getStartTime(startDate);
		Instant endTime = getEndTime(endDate);

		return transactionRepository
				.findAllByEntityNameAndStatusInAndDateCompletedGreaterThanEqualAndDateCompletedLessThanEqual(entityName,
						statuses, startTime, endTime, pageable);
	}

	private static Instant getEndTime(LocalDate endDate) {
		return endDate.atTime(LocalTime.MAX).atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
	}

	private static Instant getStartTime(LocalDate startDate) {
		return startDate.atStartOfDay().atZone(TimeConstant.DEFAULT_ZONE_ID).toInstant();
	}

	@Override
	public Page<Transaction> findByUserIdAndEntityName(String userId, EntityName entityName, List<Status> statuses,
			Pageable pageable) {
		return transactionRepository.findByUserIdAndEntityNameAndStatusIn(userId, entityName, statuses, pageable);
	}

	@Override
	public List<Transaction> findResolveAble(EntityName entityName, Instant pointer) {
		return transactionRepository.findByEntityNameAndStatusAndPaymentStatusAndCreatedDateAfter(entityName,
				Status.FAILED, PaymentStatus.PAID, pointer);
	}

}
