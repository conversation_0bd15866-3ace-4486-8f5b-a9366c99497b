package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DownloadsStatisticsReportResponse implements Serializable {

	private List<DownloadsStatisticsReportDailyResponse> dailyResult;

	private long totalInstallCount = 0L;

	private long totalUninstallCount = 0L;

	private long totalRegisteredUser = 0L;

	private long totalActiveUser = 0L;

	private BigDecimal registeredPercentage = BigDecimal.ZERO;

	private BigDecimal uninstallPercentage = BigDecimal.ZERO;

	private Long targetedRegistered;

}