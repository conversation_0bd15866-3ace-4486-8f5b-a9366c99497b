package my.com.mandrill.component.config;

import my.com.mandrill.component.domain.ai.fin_psych.UserPsychologyProfile;
import my.com.mandrill.component.domain.ai.fmf.UserSpendingCashAnalysis;
import my.com.mandrill.component.domain.ai.fmf.UserSummaryMonth;
import my.com.mandrill.component.domain.primary.JobStatus;
import my.com.mandrill.component.domain.primary.UserNetWorthTransaction;
import my.com.mandrill.component.dto.model.*;
import my.com.mandrill.utilities.feign.dto.request.UpdateUserNetWorthTransactionRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MapStructConverter {

	MapStructConverter MAPPER = Mappers.getMapper(MapStructConverter.class);

	UserPsychologyProfileDTO toUserPsychologyProfileDTO(UserPsychologyProfile userPsychologyProfile);

	SpendingTrendDTO toSpendingTrendDTO(UserSummaryMonth userSummaryMonth);

	SavingTrendDTO toSavingTrendDTO(UserSummaryMonth userSummaryMonth);

	IncomeChartDTO toIncomeChartDTO(UserSummaryMonth userSummaryMonth);

	ExpenseChartDTO toExpenseChartDTO(UserSummaryMonth userSummaryMonth);

	OpeningBalanceChartDTO toOpeningBalanceChartDTO(UserSummaryMonth userSummaryMonth);

	CashFlowAnalysisOverallDTO toCashFlowAnalysisOverallDTO(UserSummaryMonth userSummaryMonth);

	SpendingTrendDTO toSpendingTrendDTO(UserSpendingCashAnalysis userSpendingCashAnalysis);

	SavingTrendDTO toSavingTrendDTO(UserSpendingCashAnalysis userSpendingCashAnalysis);

	IncomeChartDTO toIncomeChartDTO(UserSpendingCashAnalysis userSpendingCashAnalysis);

	ExpenseChartDTO toExpenseChartDTO(UserSpendingCashAnalysis userSpendingCashAnalysis);

	OpeningBalanceChartDTO toOpeningBalanceChartDTO(UserSpendingCashAnalysis userSpendingCashAnalysis);

	CashFlowAnalysisByBankDTO toCashFlowAnalysisByBankDTO(UserSpendingCashAnalysis userSpendingCashAnalysis);

	JobStatusDTO toJobStatusDTO(JobStatus jobStatus);

	UserNetWorthTransaction toUserNetWorthTransaction(UpdateUserNetWorthTransactionRequest request);

}