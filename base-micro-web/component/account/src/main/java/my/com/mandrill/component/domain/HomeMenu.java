package my.com.mandrill.component.domain;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import my.com.mandrill.component.constant.HomeMenuStatusEnum;
import my.com.mandrill.utilities.core.audit.AuditSection;

import java.io.Serializable;
import java.util.Set;

@Getter
@Setter
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "home_menu", uniqueConstraints = { @UniqueConstraint(columnNames = { "code" }) })
public class HomeMenu extends AuditSection implements Serializable {

	@Column(name = "name", nullable = false)
	private String name;

	@Column(name = "code", nullable = false)
	private String code;

	@Column(name = "action_group", nullable = false)
	private String actionGroup;

	@Column(name = "description")
	private String description;

	@Enumerated(EnumType.STRING)
	private HomeMenuStatusEnum state;

	@ManyToOne
	@JoinColumn(name = "parent_id")
	private HomeMenu parent;

	@OneToMany(fetch = FetchType.EAGER, mappedBy = "homeMenu")
	@JsonBackReference(value = "homeMenu-section")
	@ToString.Exclude
	private Set<HomeMenuMap> homeMenuMaps;

	@OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true, mappedBy = "homeMenu")
	@ToString.Exclude
	private Set<HomeMenuProduct> homeMenuProduct;

}
