package my.com.mandrill.component.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.Property;
import my.com.mandrill.component.dto.model.PropertyDTO;
import my.com.mandrill.component.dto.request.PropertyStagingRequest;
import my.com.mandrill.component.dto.response.PropertyResponse;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.service.PropertyIntegrationService;
import my.com.mandrill.component.service.PropertyService;
import my.com.mandrill.utilities.feign.client.BankFeignClient;
import my.com.mandrill.utilities.feign.client.InsuranceFeignClient;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.ObjectRequest;
import my.com.mandrill.utilities.feign.dto.VaultLinkDTO;
import my.com.mandrill.utilities.general.constant.EntityName;
import my.com.mandrill.utilities.general.constant.InsuranceTypeEnum;
import my.com.mandrill.utilities.general.util.SecurityUtil;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

@Tag(name = "01-property")
@Slf4j
@RestController
@RequestMapping("/properties")
@RequiredArgsConstructor
public class PropertyController {

	private final PropertyService propertyService;

	private final PropertyIntegrationService propertyIntegrationService;

	private final ObjectMapper objectMapper;

	private final BankFeignClient bankFeignClient;

	private final InsuranceFeignClient insuranceFeignClient;

	@Hidden
	@GetMapping("integration")
	public ResponseEntity<List<PropertyDTO>> getPropertiesForIntegration(Sort sort) {
		List<PropertyDTO> result = propertyService.findByUserId(SecurityUtil.currentUserId(), sort);

		return ResponseEntity.ok(result);
	}

	@Hidden
	@GetMapping("check-address")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Boolean> isPropertyExistByAddress(@SpringQueryMap PropertyStagingRequest propertyRequest) {
		return ResponseEntity.ok(propertyService.isPropertyExistByAddress(propertyRequest.getAddress1(),
				propertyRequest.getAddress2(), propertyRequest.getAddress3(), SecurityUtil.currentUserId()));
	}

	@Hidden
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PutMapping("vault/link/{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void linkVault(@RequestBody VaultLinkDTO vaultLinkDTO, @PathVariable String id) {
		Property property = propertyService.findById(id, SecurityUtil.currentUserId());
		propertyService.linkVault(property, vaultLinkDTO);
	}

	@Hidden
	@GetMapping("vault/linked/{attachmentGroupId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<PropertyDTO> findLinkedVault(@PathVariable String attachmentGroupId) {
		Property property = propertyService.findByAttachmentGroupId(attachmentGroupId, SecurityUtil.currentUserId());
		return ResponseEntity.ok(objectMapper.convertValue(property, PropertyDTO.class));
	}

	@GetMapping("vault/unlinked")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<PropertyDTO>> getPropertyWithAttachmentGroupIdNull() {
		List<Property> properties = propertyService.findByAttachmentGroupIdNull(SecurityUtil.currentUserId());
		return ResponseEntity.ok(
				properties.stream().map(property -> objectMapper.convertValue(property, PropertyDTO.class)).toList());
	}

	@GetMapping("count")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Long> count() {
		return ResponseEntity.ok(propertyService.count(SecurityUtil.currentUserId()));
	}

	@Hidden
	@GetMapping("vault/is-linked/{attachmentGroupId}")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<Boolean> existsByUserIdAndAttachmentGroupId(@PathVariable String attachmentGroupId) {
		return ResponseEntity.ok(
				propertyService.existsByUserIdAndAttachmentGroupId(SecurityUtil.currentUserId(), attachmentGroupId));
	}

	@Hidden
	@GetMapping("/integration/net-worth")
	public ResponseEntity<NetWorthDTO> calculateNetWorth() {
		NetWorthDTO netWorthDTO = propertyService.calculateNetWorthByUserId(SecurityUtil.currentUserId());
		return ResponseEntity.ok(netWorthDTO);
	}

	@Hidden
	@GetMapping("rental")
	public ResponseEntity<List<PropertyDTO>> getPropertyByUserIdAndIsRentedAndIsBrowsingFalse(
			@RequestParam Boolean isRented) {
		List<Property> result = propertyService.findByUserIdAndIsRentedAndIsBrowsingFalse(SecurityUtil.currentUserId(),
				isRented);
		return ResponseEntity
				.ok(result.stream().map(property -> objectMapper.convertValue(property, PropertyDTO.class)).toList());
	}

	@Hidden
	@GetMapping("{id}/exists")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public void existsById(@PathVariable String id) {
		if (!propertyService.existsByIdAndUserId(id, SecurityUtil.currentUserId())) {
			throw ExceptionPredicate.propertyNotFound(id).get();
		}
	}

	@PutMapping("{id}/attach-insurance")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void attachInsurance(@Valid @RequestBody ObjectRequest attachInsuranceRequest, @PathVariable String id) {
		Property property = propertyService.findById(id, SecurityUtil.currentUserId());
		property.setInsuranceId(attachInsuranceRequest.getId());
		propertyService.save(property);
	}

	@GetMapping("available-resources/insurance")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<PropertyResponse>> findInsuranceAvailableResource(Sort sort) {
		List<String> attachedInsurance = insuranceFeignClient.getLinkedEntityIds(InsuranceTypeEnum.PROPERTY);
		List<PropertyDTO> properties = propertyIntegrationService.findDetachedEntity(SecurityUtil.currentUserId(),
				attachedInsurance, sort);

		return ResponseEntity.ok(properties.stream().map(MapStructConverter.MAPPER::toPropertyResponse).toList());
	}

	@PutMapping("{id}/attach-loan")
	@ResponseStatus(HttpStatus.NO_CONTENT)
	@PreAuthorize("hasAuthority(@authorityPermission.USER_UPDATE)")
	public void attachLoan(@Valid @RequestBody ObjectRequest attachLoanRequest, @PathVariable String id) {
		Property property = propertyService.findById(id, SecurityUtil.currentUserId());
		property.setLoanId(attachLoanRequest.getId());
		propertyService.save(property);
	}

	@GetMapping("available-resources/loan")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<List<PropertyResponse>> findLoanAvailableResources(Sort sort) {
		List<String> attachedLoan = bankFeignClient.getLinkedEntities(EntityName.PROPERTY);
		List<PropertyDTO> properties = propertyIntegrationService.findDetachedEntity(SecurityUtil.currentUserId(),
				attachedLoan, sort);

		return ResponseEntity.ok(properties.stream().map(MapStructConverter.MAPPER::toPropertyResponse).toList());
	}

	@GetMapping("lowest-amount")
	@PreAuthorize("hasAuthority(@authorityPermission.USER_READ)")
	public ResponseEntity<BigDecimal> getLowestAmount() {
		return ResponseEntity.ok(propertyIntegrationService.getCurrentUserLowestPropertyValue());
	}

}
