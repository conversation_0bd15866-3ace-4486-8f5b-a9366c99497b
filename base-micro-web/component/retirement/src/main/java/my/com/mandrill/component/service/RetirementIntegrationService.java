package my.com.mandrill.component.service;

import my.com.mandrill.component.dto.request.RetirementCalculationRequest;
import my.com.mandrill.component.dto.response.RetirementCalculationResponse;

import java.util.Set;

public interface RetirementIntegrationService {

	RetirementCalculationResponse processRetirementCalculation(RetirementCalculationRequest request, String userId);

	RetirementCalculationResponse getLatestRetirementCalculationResult(String userId);

	Set<String> getProductExclusionTags(String userId);

}
