package my.com.mandrill.utilities.general.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EkycStatus {

	NOT_STARTED("Not Started"), PENDING("Pending"), SUCCESS("Success"), FAIL("Fail"), RUNNING("Running"),

	/**
	 * This value is used solely for integration purposes (i.e. status from 3rd party
	 * service if we have passed an EKYC submission/attempt) and is never stored in DB.
	 * For enum values that are stored in DB, see
	 *
	 * @see NOT_STARTED
	 * @see PENDING
	 * @see SUCCESS
	 * @see FAIL
	 * @see RUNNING
	 */
	PASSED("Passed"),

	/**
	 * This value is used solely for integration purposes (i.e. status from 3rd party
	 * service if we have failed an EKYC submission/attempt) and is never stored in DB.
	 * For enum values that are stored in DB, see
	 *
	 * @see NOT_STARTED
	 * @see PENDING
	 * @see SUCCESS
	 * @see FAIL
	 * @see RUNNING
	 */
	FAILED("Failed");

	private final String name;

}
