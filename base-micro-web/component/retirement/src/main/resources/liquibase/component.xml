<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.11.xsd">

    <property name="now" value="now()" dbms="h2, mysql, mariadb,postgresql"/>

    <include
            file="liquibase/changelog/retirement-component_20241010_weishun_0001_create_table_retirement_calculation.xml"/>
    <include file="liquibase/changelog/retirement-component_20241107_weishun_0001_add_column_for_retirement_calculation.xml"/>
    <include file="liquibase/changelog/retirement-component_20241108_weishun_0001_create_table_retirement_coverage_copy_writing.xml"/>
    <include file="liquibase/changelog/retirement-component_20241112_weishun_0001_create_table_retirement_product_category.xml"/>
    <include file="liquibase/changelog/retirement-component_20241112_weishun_0002_create_table_retirement_product.xml"/>
    <include file="liquibase/changelog/retirement-component_20241119_weishun_0001_alter_table_retirement_product_add_column.xml"/>
    <include file="liquibase/changelog/retirement-component_20241206_weishun_0001_alter_table_retirement_product_modify_data_type.xml"/>
    <include file="liquibase/changelog/retirement-component_20241212_chooiyie_0001_create_table_retirement_account_type.xml"/>
    <include file="liquibase/changelog/retirement-component_20241217_chooiyie_0001_create_table_retirement_provider.xml"/>
    <include file="liquibase/changelog/retirement-component_20241216_weishun_0001_create_table_retirement_account.xml"/>
    <include file="liquibase/changelog/retirement-component_20241218_deni_0001_add_column_attachment_group_id_retirment_provider.xml"/>
    <include file="liquibase/changelog/retirement-component_20241219_deni_0001_remove_short_name_retirment_provider.xml"/>
    <include file="liquibase/changelog/retirement-component_20241218_chooiyie_0001_load_retirement_product_data.xml"/>
    <include file="liquibase/changelog/retirement-component_20250110_chooiyie_0001_update_retirement_product_data.xml"/>
    <include file="liquibase/changelog/retirement-component_20250113_weishun_0001_alter_table_retirement_coverage_copy_writing_add_column_title.xml"/>
    <include
            file="liquibase/changelog/retirement-component_20250520_weishun_0001_create_table_user_retirement_assessment_answer.xml"/>
    <include
            file="liquibase/changelog/retirement-component_20250521_weishun_0001_create_table_assessment_product_exclusion_rules.xml"/>
    <include
            file="liquibase/changelog/retirement-component_20250526_diknes_0001_alter_table_retirement_account_add_columns.xml"/>

</databaseChangeLog>
