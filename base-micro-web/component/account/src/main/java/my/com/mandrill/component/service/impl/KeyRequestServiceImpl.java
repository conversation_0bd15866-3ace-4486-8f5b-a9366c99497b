package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.RequestKeyStatus;
import my.com.mandrill.component.domain.UserKeyRequest;
import my.com.mandrill.component.repository.jpa.UserKeyRequestRepository;
import my.com.mandrill.component.service.KeyRequestService;
import my.com.mandrill.utilities.general.constant.RequestKeyType;
import my.com.mandrill.utilities.general.util.RandomUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class KeyRequestServiceImpl implements KeyRequestService {

	private final UserKeyRequestRepository userKeyRequestRepository;

	@Override
	public Optional<UserKeyRequest> findFirstByUsernameAndTypeAndStatusOrderByCreatedDateDesc(String usernameRaw,
			RequestKeyType requestKeyType, RequestKeyStatus requestKeyStatus) {
		return userKeyRequestRepository.findFirstByUsernameAndTypeAndStatusOrderByCreatedDateDesc(usernameRaw,
				requestKeyType, requestKeyStatus);
	}

	@Override
	@Transactional
	public UserKeyRequest save(UserKeyRequest keyRequest) {
		return userKeyRequestRepository.save(keyRequest);
	}

	@Override
	public Optional<UserKeyRequest> findValidByKeyValueAndType(String keyValueRaw, RequestKeyType requestKeyType) {
		return userKeyRequestRepository.findByKeyValueAndTypeAndStatusAndExpiresAtAfter(keyValueRaw, requestKeyType,
				RequestKeyStatus.PENDING, Instant.now());
	}

	@Override
	@Transactional
	public UserKeyRequest createRequestKey(String username, RequestKeyType requestKeyType, int expiryInHours) {
		log.info("creating request key user...");
		Instant initialDate = Instant.now();

		UserKeyRequest keyRequest = new UserKeyRequest();
		keyRequest.setUsername(username);
		keyRequest.setKeyValue(RandomUtil.generateAlphanumeric(15));
		keyRequest.setInitialDate(initialDate);
		keyRequest.setExpiresAt(initialDate.plus(expiryInHours, ChronoUnit.HOURS));
		keyRequest.setType(requestKeyType);
		keyRequest.setAttempt(0);
		keyRequest.setFailVerificationAttempt(0);
		return save(keyRequest);
	}

	@Override
	public Optional<UserKeyRequest> findValidByUsernameAndType(String usernameRaw, RequestKeyType requestKeyType) {
		return userKeyRequestRepository.findFirstByUsernameAndTypeAndStatusAndExpiresAtAfter(usernameRaw,
				requestKeyType, RequestKeyStatus.PENDING, Instant.now());
	}

}
